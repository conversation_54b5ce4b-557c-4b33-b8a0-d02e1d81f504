<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]"  :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px', 'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back" :style="{'color':isDark?'#ffffff':'#000000'}"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}, {'color':isDark?'#ffffff':'#000000'}]">
					用户登录
				</view>
				<!--  #ifdef H5 || APP-PLUS -->
				<!--  #endif -->
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="app-about">
			<view class="app-logo tn-flex justify-center" style="height: 84px;">
				<image :src="AppLogo" style="width:84px;height:84px" mode="widthFix"></image>
			</view>
		</view>
		<view class="user-form" v-if="loginType==0">
			<form>
				<view class="cu-form-group" style="border-radius: 60upx;">
					<input name="input" placeholder="用户名/手机号/邮箱 " v-model="userName"></input>
				</view>
				<view class="cu-form-group" style="border-radius: 60upx;">
					<input name="input" placeholder="用户密码" type="password" v-model="password"></input>
				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;">
					<view class="text-blue" @tap="toFoget">找回密码</view>
					<view class="text-blue" @tap="toRegister">立即注册</view>
				</view>
				<view class="user-btn flex flex-direction">
					<!-- #ifdef APP-PLUS || H5 -->
					<button class="cu-btn bg-blue margin-tb-sm lg" @tap="login">立即登录</button>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<button class="cu-btn bg-blue margin-tb-sm lg" @tap="usermodelVisible=true">立即登录</button>
					<button class="cu-btn bg-green margin-tb-sm lg" @tap="toRegister">注册新用户</button>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<button class="cu-btn bg-green margin-tb-sm lg" style="background-color: #3cc8c9;" @tap="qrcodeLogin"><text
						class="cuIcon-qrcode margin-right-xs"></text>扫码登录</button>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS || H5 -->
					<text class="text-right margin-top" style="font-size: 22upx;color:#808080;">
						<text style="margin-right: 10upx">
							<radio value="r1" :checked="isAgree" color="#3ccea7"
								style="transform:scale(0.7)" @tap="isAgree=!isAgree" />
						</text>我同意{{appname}}的<text class="text-blue" @tap="toAgreement">《用户协议》</text>
						和<text class="text-blue" @tap="toPrivacy">《隐私条款》</text>
					</text>
					<!-- #endif -->
				</view>
			</form>
		</view>
		<view class="user-form" v-if="loginType==1">
			<form>
				<view class="cu-form-group" style="border-radius: 60upx;">
					<input name="input" placeholder="请输入手机号" v-model="phone"></input>
				</view>
				<view class="cu-form-group" style="border-radius: 60upx;">
					<input name="input" v-model="code" placeholder="请输入验证码"></input>
					<view class="sendcode text-blue" v-if="show" @tap="SMSCode">发送</view>
					<view class="sendcode text-gray" v-if="!show">{{ times }}s</view>
				</view>
				<view class="user-btn flex flex-direction">
					<!-- #ifdef APP-PLUS || H5 -->
					<button class="cu-btn bg-blue margin-tb-sm lg" @tap="phoneLogin">一键登录或注册</button>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<button class="cu-btn bg-blue margin-tb-sm lg" @tap="usermodelVisible=true">一键登录或注册</button>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS || H5 -->
					<text class="text-right margin-top" style="font-size: 22upx;color:#808080;">
						<text style="margin-right: 10upx">
							<radio value="r1" :checked="isAgree" color="#3ccea7"
								style="transform:scale(0.7)" @tap="isAgree=!isAgree" />
						</text>我同意{{appname}}的<text class="text-blue" @tap="toAgreement">《用户协议》</text>
						和<text class="text-blue" @tap="toPrivacy">《隐私条款》</text>
					</text>
					<!-- #endif -->
				</view>
			</form>
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="api-login grid" :class="colClass">
			<view class="api-login-box" @tap="toQQlogin" v-if="qqlogin==1">
				<image src="../../static/icon_qq.png"></image>
			</view>
			<view class="api-login-box" @tap="toWexinlogin" v-if="wxlogin==1">
				<image src="../../static/icon_weixin.png"></image>
			</view>
			<view class="api-login-box" @tap="toWeibologin" v-if="wblogin==1">
				<image src="../../static/icon_weibo.png"></image>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<view class="api-login">
			<view class="api-login-box" @tap="toWexinlogin(true)" v-if="wxlogin==1">
				<image src="../../static/icon_weixin.png"></image>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef MP-QQ -->
		<view class="api-login">
			<view class="api-login-box" @tap="toQQlogin" v-if="qqlogin==1">
				<image src="../../static/icon_qq.png"></image>
			</view>
		</view>
		<!-- #endif -->
		<view class="user-foget" v-if="isPhone==1">
			<text v-if="loginType==0" @tap="loginType=1">验证码登录</text>
			<text v-if="loginType==1" @tap="loginType=0">密码登录</text>
		</view>

		<tn-popup v-model="usermodelVisible" mode="center" :borderRadius="23" :maskCloseable="false">
			<view style="padding: 30rpx 40rpx;">
				<view style="text-align: center;">用户及隐私协议</view>
				<view class="model-body" style="margin-top: 20rpx;">请问你是否同意<text class="text-blue"
						@tap="toAgreement">《用户及隐私协议》</text></view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button backgroundColor="#3ccea7" fontColor="#fff" @tap="yesBtn">同意</tn-button>
					<tn-button style="margin-left: 20rpx;" backgroundColor="#37bc99" fontColor="#fff"
						@tap="noBtn">不同意</tn-button>
				</view>
			</view>
		</tn-popup>

		<tn-popup v-model="modelVisible" mode="bottom" :borderRadius="23" :maskCloseable="false">
			<view style="padding: 30rpx 40rpx;">
				<view style="text-align: center;">温馨提示</view>
				<view class="model-body" v-html="logintext" style="margin-top: 20rpx;"></view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button style="margin-left: 20rpx;" backgroundColor="#3cc9a4" fontColor="#fff"
						@tap="okBtn">知道了</tn-button>
				</view>
			</view>
		</tn-popup>
		<view class="cu-modal" :class="modalName=='kaptcha'?'show':''">
			<view class="cu-dialog kaptcha-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">操作验证</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>
				<view class="kaptcha-form">
					<view class="kaptcha-image">
						<image :src="kaptchaUrl" mode="widthFix" @tap="reloadCode()"></image>
					</view>
					<view class="kaptcha-input">
						<input name="input" v-model="verifyCode" placeholder="请输入验证码"></input>
						<view class="cu-btn bg-blue" @tap="SMSCode">确定</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		mixins: [darkModeMixin],
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				userName: "",
				password: "",
				loginType: 0,
				isPhone: 0,
				qqlogin: 0,
				logintext: "",
				AppLogo: this.$API.GetAppLogo(),
				appname: this.$API.GetAppName(),
				adminemail: this.$API.GetAppEmail(),
				modelVisible: false,
				wxlogin: 0,
				wblogin: 0,
				times: 60,
				show: true,
				usermodelVisible: false,
				phone: "",
				code: "",
				isAgree: false,
				isWxLogin: false,
				modalName: null,
				kaptchaUrl: "",
				verifyCode: "",
				verifyLevel: 0,
				styleIndex: "",
				// 行为验证相关
				captchaPluginEnabled: false,
				captchaConfig: null,
				behaviorCaptchaData: null,
				




			}
		},
		onPullDownRefresh() {
			var that = this;

		},
		onShow() {
			var that = this;
			that.regConfig();
			that.getleiji()
			that.getset()
			uni.$emit('tOnLazyLoadReachBottom');
			// #ifdef APP-PLUS

			//plus.navigator.setStatusBarStyle("dark")
			// #endif

		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif

			that.kaptchaUrl = that.$API.getKaptcha();
			that.styleIndex = that.$API.GetStyleIndex();

			// 检查行为验证插件状态
			that.checkCaptchaPlugin();
		},
		computed: {
			colClass() {
				let count = this.qqlogin + this.wxlogin + this.wblogin;
				if (count === 3) {
					return 'col-3';
				} else if (count === 2) {
					return 'col-2';
				} else if (count === 1) {
					return 'col-1';
				} else {
					return '';
				}
			},
		},
		methods: {
			// 检查行为验证插件状态
			checkCaptchaPlugin() {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/getPluginStatus.php',
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							that.captchaPluginEnabled = res.data.data.overall_enabled;
							if (that.captchaPluginEnabled) {
								that.getCaptchaConfig();
							}
						}
					},
					fail: function() {
						// 插件不存在或网络错误，使用原有验证码
						that.captchaPluginEnabled = false;
					}
				});
			},

			// 获取行为验证配置
			getCaptchaConfig() {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/getCaptchaConfig.php',
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							that.captchaConfig = res.data.data;
						}
					}
				});
			},

			// 处理行为验证结果
			onBehaviorCaptchaSuccess(data) {
				this.behaviorCaptchaData = data;
				// 验证成功后继续登录流程
				this.performLogin();
			},

			// 显示行为验证
			showBehaviorCaptcha() {
				var that = this;
				if (!that.captchaConfig) {
					uni.showToast({
						title: "验证配置加载失败",
						icon: 'none'
					});
					return;
				}

				// 根据验证类型显示对应的验证界面
				switch (that.captchaConfig.captcha_type) {
					case 'geetest':
						that.showGeetestCaptcha();
						break;
					case 'cloudflare':
						that.showCloudflareCaptcha();
						break;
					case 'recaptcha':
						that.showRecaptchaCaptcha();
						break;
					default:
						uni.showToast({
							title: "不支持的验证类型",
							icon: 'none'
						});
				}
			},

			// 显示极验验证
			showGeetestCaptcha() {
				// 这里需要集成极验SDK
				uni.showToast({
					title: "极验验证功能开发中",
					icon: 'none'
				});
			},

			// 显示Cloudflare验证
			showCloudflareCaptcha() {
				// 这里需要集成Cloudflare Turnstile
				uni.showToast({
					title: "Cloudflare验证功能开发中",
					icon: 'none'
				});
			},

			// 显示reCAPTCHA验证
			showRecaptchaCaptcha() {
				// 这里需要集成reCAPTCHA
				uni.showToast({
					title: "reCAPTCHA验证功能开发中",
					icon: 'none'
				});
			},

			okBtn() {
				const nowDate = +new Date();
				uni.setStorageSync('modelView', nowDate);
				this.cancleBtn();
			},
			cancleBtn() {
				this.modelVisible = false;
			},
			getleiji() {
				var that = this;
				uni.request({
					url: that.$API.SPqqlogin(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.qqlogin = res.data.qqlogin;
						that.wxlogin = res.data.wxlogin;
						that.wblogin = res.data.wblogin;
					},
					fail(error) {
						console.log(error);
					}

				})
			},
			back() {
				localStorage.setItem('isqh', "true");
				console.log(22);
				uni.navigateBack({
					delta: 1
				});
			},
			getset() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.logintext = res.data.logintext;
						if (that.logintext != "") {
							that.modelVisible = true
						}
					},
					fail(error) {
						console.log(error);
					}
				})
			},
			reloadCode() {
				var that = this;
				var kaptchaUrl = that.$API.getKaptcha();
				var num = Math.ceil(Math.random() * 10);
				kaptchaUrl += "?" + num;
				that.kaptchaUrl = kaptchaUrl;
			},

			yesBtn() {
				this.usermodelVisible = false
				this.isAgree == true
				if(this.isWxLogin){
					this.toWexinlogin()
				}else{
					if (this.loginType == 1) {
						this.phoneLogin()
					} else {
						this.login()
					}
				}
				
			},
			noBtn() {
				this.usermodelVisible = false
				uni.showToast({
					title: "同意再来登录吧",
					icon: 'none',
				});
			},
			getCID() {
				var that = this;
				let cid = ''
				// #ifdef APP-PLUS
				let pinf = plus.push.getClientInfo();
				cid = pinf.clientid;
				if (cid) {
					that.setClientId(cid);
				}
				// #endif
			},
			hideModal(e) {
				this.modalName = null
			},
			setClientId(cid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('token')) {

					token = localStorage.getItem('token');
				} else {
					return false;
				}
				that.$Net.request({

					url: that.$API.setClientId(),
					data: {
						"clientId": cid,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {


						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			regConfig() {
				var that = this;
				//获取应用信息
				uni.request({

					url: that.$API.getAppinfo(),
					data: {
						"key": that.$API.getAppKey()
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.isPhone = res.data.data.isPhone;
							that.isInvite = res.data.data.isInvite;
							that.verifyLevel = res.data.data.verifyLevel;
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "获取应用配置信息失败！",
							icon: 'none'
						})
					}
				})
			},
			login() {
				var that = this;
				// #ifdef APP-PLUS || H5
				if (that.isAgree == false) {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				if (that.password == "" || that.userName == "") {
					uni.showToast({
						title: "请输入正确的参数",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}

				// 检查验证码
				if (that.verifyLevel > 0) {
					// 如果启用了行为验证插件，检查行为验证
					if (that.captchaPluginEnabled && that.captchaConfig && that.captchaConfig.enabled) {
						if (!that.behaviorCaptchaData) {
							// 触发行为验证
							that.showBehaviorCaptcha();
							return false;
						}
					} else {
						// 使用原有的图片验证码
						if (that.verifyCode == "") {
							that.modalName = 'kaptcha'
							return false
						}
					}
				}

				that.performLogin();
			},

			performLogin() {
				var that = this;
				var data = {
					name: that.userName,
					password: that.password,
				}

				// 添加验证码数据
				if (that.verifyLevel > 0) {
					if (that.captchaPluginEnabled && that.behaviorCaptchaData) {
						// 添加行为验证数据
						data.captcha_type = that.captchaConfig.captcha_type;
						data.captcha_data = JSON.stringify(that.behaviorCaptchaData);
					} else if (that.verifyCode) {
						// 添加图片验证码
						data.verifyCode = that.verifyCode;
					}
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.userLogin(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						
						if (res.data.code == 1) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							localStorage.setItem('userinfo', JSON.stringify(res.data.data));
							localStorage.setItem('token', res.data.data.token);
							that.getCID();
							var timer = setTimeout(function() {
								that.back();
								clearTimeout('timer')
							}, 1000)
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
						
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			toRegister() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/register'
				});
			},
			toFoget() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/foget'
				});
			},
			toQQlogin() {
				//QQ登陆
				//后端直接根据access_token来判断用户的唯一性。
				var that = this;
				// #ifdef APP-PLUS || H5
				if (that.isAgree == false) {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				uni.login({
					provider: 'qq',
					success: resp => {
						var js_code = resp.code;
						var access_token = "";
						// #ifdef APP-PLUS
						access_token = resp.authResult.access_token;
						// #endif
						uni.getUserInfo({
							provider: 'qq',
							success: function(infoRes) {

								var formdata = {
									nickName: infoRes.userInfo.nickname,
									//gender: infoRes.userInfo.gender == '男' ? 1 : 2,
									appLoginType: "qq",
									headImgUrl: infoRes.userInfo.figureurl_qq_2,
									// openId: infoRes.userInfo.openId,
									// accessToken: access_token
								};
								// #ifdef APP-PLUS
								formdata.openId = infoRes.userInfo.openId;
								formdata.accessToken = access_token,
									formdata.type = "app";
								// #endif
								// #ifdef MP-QQ
								formdata.type = "applets";
								formdata.js_code = js_code;
								// #endif
								uni.showLoading({
									title: "加载中"
								});
								that.$Net.request({

									url: that.$API.userApi(),
									data: {
										"params": JSON.stringify(that.$API
											.removeObjectEmptyKey(formdata))
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "get",
									dataType: 'json',
									success: function(res) {
										console.log(res)
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										if (res.data.code == 1) {
											uni.showToast({
												title: res.data.msg,
												icon: 'none'
											})
											localStorage.setItem('userinfo', JSON
												.stringify(res.data.data));
											localStorage.setItem('token', res.data.data
												.token);
											if (localStorage.getItem('userinfo')) {
												var userInfo = JSON.parse(localStorage
													.getItem('userinfo'));
												var uid = userInfo.uid;
											}
											uni.request({
												url: that.$API.SPQQbang(),
												method: 'GET',
												data: {
													uid: uid
												},
												dataType: "json",
												success(res) {
													uni.showToast({
														title: res.data
															.message,
														icon: 'none'
													});
												},
												fail(error) {
													console.log(error);
												}

											})
											//保存用户信息
											that.getCID();
											var timer = setTimeout(function() {
												that.back();
												clearTimeout('timer')
											}, 1000)
										}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
											uni.showToast({
												title: '程序授权异常，请联系管理员：'+that.adminemail,
												icon: 'none'
											})
										}else{
											uni.showToast({
												title: res.data.msg,
												icon: 'none'
											})
										}
									},
									fail: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络开小差了哦",
											icon: 'none'
										})
										uni.stopPullDownRefresh()
									}
								})

							}
						});
					},
					fail: err => {
						uni.showToast({
							title: '请求出错啦！',
							icon: 'none',
							duration: 3000
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
					}
				});
			},
			toWexinlogin(isMp = false) {
				var that = this;
				//微信登陆
				//后端直接根据unionId来判断用户的唯一性。
				// #ifdef APP-PLUS || H5
				if (that.isAgree == false) {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				// #ifdef MP-WEIXIN
				// 小程序环境下,先显示隐私政策弹窗
				if(isMp){
					that.isWxLogin = true;
					that.usermodelVisible = true;
					return;
				}
				that.isWxLogin = false;
				// #endif

				uni.login({
					provider: 'weixin',
					onlyAuthorize: true,
					success: res => {
						var js_code = res.code;
						console.log(JSON.stringify(res));
						//微信APP需要从微信接口获取
						// #ifdef APP-PLUS
						let formdata = {
							"type": "app",
							"js_code": js_code,
							"appLoginType": "weixin"
						}
						uni.showLoading({
							title: "加载中"
						});
						that.$Net.request({

							url: that.$API.userApi(),
							data: {
								"params": JSON.stringify(that.$API.removeObjectEmptyKey(formdata))
							},
							header: {
								'Content-Type': 'application/x-www-form-urlencoded'
							},
							method: "get",
							dataType: 'json',
							success: function(res) {
								setTimeout(function() {
									uni.hideLoading();
								}, 1000);
								
								if (res.data.code == 1) {

									//保存用户信息
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									localStorage.setItem('userinfo', JSON.stringify(res.data
										.data));
									localStorage.setItem('token', res.data.data.token);
									if (localStorage.getItem('userinfo')) {
										var userInfo = JSON.parse(localStorage.getItem(
											'userinfo'));
										var uid = userInfo.uid;
									}
									uni.request({
										url: that.$API.SPWXbang(),
										method: 'GET',
										data: {
											uid: uid
										},
										dataType: "json",
										success(res) {
											uni.showToast({
												title: res.data.message,
												icon: 'none'
											});
										},
										fail(error) {
											console.log(error);
										}

									})

									var timer = setTimeout(function() {
										that.back();
										clearTimeout('timer')
									}, 1000)
								}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
									uni.showToast({
										title: '程序授权异常，请联系管理员：'+that.adminemail,
										icon: 'none'
									})
								}else{
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
								}
							},
							fail: function(res) {
								setTimeout(function() {
									uni.hideLoading();
								}, 1000);
								uni.showToast({
									title: "网络开小差了哦",
									icon: 'none'
								})
								uni.stopPullDownRefresh()
							}
						})
						// #endif
						//微信小程序直接获取
						// #ifdef MP-WEIXIN
						uni.getUserInfo({
							provider: 'weixin',
							onlyAuthorize: true,
							success: function(infoRes) {
								console.log(JSON.stringify(infoRes));
								let formdata = {
									nickName: infoRes.userInfo.nickName,
									//gender: infoRes.userInfo.gender,
									appLoginType: "weixin",
									headImgUrl: infoRes.userInfo.avatarUrl,
									// openId: infoRes.userInfo.openId,
									// accessToken: infoRes.userInfo.unionId,
								};


								formdata.type = "applets";
								formdata.js_code = js_code;

								uni.showLoading({
									title: "加载中"
								});
								that.$Net.request({

									url: that.$API.userApi(),
									data: {
										"params": JSON.stringify(that.$API
											.removeObjectEmptyKey(formdata))
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "get",
									dataType: 'json',
									success: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										
										if (res.data.code == 1) {
											//保存用户信息
											uni.showToast({
												title: res.data.msg,
												icon: 'none'
											})
											localStorage.setItem('userinfo', JSON
												.stringify(res.data.data));
											localStorage.setItem('token', res.data.data
												.token);
											if (localStorage.getItem('userinfo')) {
												var userInfo = JSON.parse(localStorage
													.getItem('userinfo'));
												var uid = userInfo.uid;
											}
											uni.request({
												url: that.$API.SPWXbang(),
												method: 'GET',
												data: {
													uid: uid
												},
												dataType: "json",
												success(res) {
													uni.showToast({
														title: res.data
															.message,
														icon: 'none'
													});
												},
												fail(error) {
													console.log(error);
												}

											})
											that.getCID();
											var timer = setTimeout(function() {
												that.back();
												clearTimeout('timer')
											}, 1000)
										}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
											uni.showToast({
												title: '程序授权异常，请联系管理员：'+that.adminemail,
												icon: 'none'
											})
										}else{
											uni.showToast({
												title: res.data.msg,
												icon: 'none'
											})
										}
									},
									fail: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络开小差了哦",
											icon: 'none'
										})
										uni.stopPullDownRefresh()
									}
								})

							}
						});
						// #endif
					},
					fail: err => {
						console.log(err)
						uni.showToast({
							title: err.errMsg,
							icon: 'none',
							duration: 3000
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
					}
				});
			},
			toPrivacy() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/privacy'
				});
			},
			toAgreement() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/agreement'
				});
			},

			toWeibologin() {
				var that = this;
				//微博登陆
				//后端直接根据access_token来判断用户的唯一性。
				// #ifdef APP-PLUS || H5
				if (that.isAgree == false) {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				uni.login({
					provider: 'sinaweibo',
					success: res => {
						var access_token = '';
						access_token = res.authResult.access_token;
						uni.getUserInfo({
							provider: 'sinaweibo',
							success: function(infoRes) {
								var formdata = {
									nickName: infoRes.userInfo.nickname,
									//gender: infoRes.userInfo.gender == 'm' ? 1 : 2,
									headImgUrl: infoRes.userInfo.avatar_large,
									openId: infoRes.userInfo.id,
									accessToken: access_token,
									appLoginType: 'SINAWEIBO'

								};
								uni.showLoading({
									title: "加载中"
								});
								that.$Net.request({

									url: that.$API.userApi(),
									data: {
										"params": JSON.stringify(that.$API
											.removeObjectEmptyKey(formdata))
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "get",
									dataType: 'json',
									success: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										
										if (res.data.code == 1) {
											//保存用户信息
											uni.showToast({
												title: res.data.msg,
												icon: 'none'
											})
											localStorage.setItem('userinfo', JSON
												.stringify(res.data.data));
											localStorage.setItem('token', res.data.data
												.token);
											if (localStorage.getItem('userinfo')) {
												var userInfo = JSON.parse(localStorage
													.getItem('userinfo'));
												var uid = userInfo.uid;
											}
											uni.request({
												url: that.$API.SPWBbang(),
												method: 'GET',
												data: {
													uid: uid
												},
												dataType: "json",
												success(res) {
													uni.showToast({
														title: res.data
															.message,
														icon: 'none'
													});
												},
												fail(error) {
													console.log(error);
												}

											})
											that.getCID();
											var timer = setTimeout(function() {
												that.back();
												clearTimeout('timer')
											}, 1000)
										}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
											uni.showToast({
												title: '程序授权异常，请联系管理员：'+that.adminemail,
												icon: 'none'
											})
										}else{
											uni.showToast({
												title: res.data.msg,
												icon: 'none'
											})
										}
									},
									fail: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络开小差了哦",
											icon: 'none'
										})
										uni.stopPullDownRefresh()
									}
								})
							}
						});
					},
					fail: err => {
						uni.showToast({
							title: '请求出错啦！',
							icon: 'none',
							duration: 3000
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
					}
				});
			},
			SMSCode() {
				var that = this;

				if (that.phone == "") {
					uni.showToast({
						title: "请输入手机号",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}


				if (that.verifyLevel > 0) {
					if (that.verifyCode == "") {
						that.modalName = 'kaptcha'
						return false
					}
				}
				var data = {
					'phone': that.phone,
					'verifyCode': that.verifyCode
				}

				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.sendSMS(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.verifyCode = "";
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						
						if (res.data.code == 1) {
							that.getCode();
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}

					},
					fail: function(res) {
						that.modalName = null;
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			getCode() {
				this.show = false
				this.timer = setInterval(() => {
					this.times--
					if (this.times === 0) {
						this.show = true
						clearInterval(this.timer);
						this.times = 60;
					}
				}, 1000)
			},
			qrcodeLogin() {
				uni.navigateTo({
					url: '/pages/user/qrcodeLogin'
				});
			},
			phoneLogin() {
				var that = this;
				// #ifdef APP-PLUS || H5
				if (that.isAgree == false) {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				if (that.phone == "" || that.code == "") {
					uni.showToast({
						title: "请输入正确的参数",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}

				var data = {
					'phone': that.phone,
					"code": that.code
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.phoneLogin(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							//保存用户信息
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							localStorage.setItem('userinfo', JSON.stringify(res.data.data));
							localStorage.setItem('token', res.data.data.token);
							that.getCID();
							var timer = setTimeout(function() {
								that.back();
								clearTimeout('timer')
							}, 1000)
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			}

		}
	}
</script>

<style>
	.t-login {
		position: relative;
		width: 600rpx;
		margin: 0 auto;
		font-size: 28rpx;
		color: #000;
	}

	.t-login button {
		font-size: 28rpx;
		background: #5677fc;
		color: #fff;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 50rpx;
		box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
	}

	.t-login input {
		padding: 0 20rpx 0 120rpx;
		height: 90rpx;
		line-height: 90rpx;
		/* margin-bottom: 50rpx; */
		background: #f8f7fc;
		border: 1px solid #e9e9e9;
		font-size: 28rpx;
		border-radius: 50rpx;
	}

	.t-login .t-a {
		position: relative;
	}

	.t-login .t-a image {
		width: 60rpx;
		height: 40rpx;
		position: absolute;
		left: 40rpx;
		top: 28rpx;
		border-right: 2rpx solid #dedede;
		padding-right: 20rpx;
	}

	.t-login .t-b {
		text-align: left;
		font-size: 19px;
		color: #313131;
		padding: 35px 0 0px 0;
		font-weight: bold;
	}

	.t-login .t-c {
		position: absolute;
		right: 22rpx;
		top: 22rpx;
		background: #5677fc;
		color: #fff;
		font-size: 24rpx;
		border-radius: 50rpx;
		height: 50rpx;
		line-height: 50rpx;
		padding: 0 25rpx;
	}

	.t-login .t-d {
		text-align: center;
		color: #999;
		margin: 80rpx 0;
	}

	.t-login .t-e {
		text-align: center;
		width: 250rpx;
		margin: 80rpx auto 0;
	}

	.t-login .t-g {
		float: left;
		width: 100%;
	}

	.t-login .t-e image {
		width: 50rpx;
		height: 50rpx;
	}

	.t-login .t-f {
		text-align: center;
		margin: 200rpx 0 0 0;
		color: #666;
	}

	.t-login .t-f text {
		margin-left: 20rpx;
		color: #aaaaaa;
		font-size: 27rpx;
	}

	.t-login .uni-input-placeholder {
		color: #000;
	}

	.cl {
		zoom: 1;
	}

	.s1 {

		float: right;
	}

	.cl:after {
		clear: both;
		display: block;
		visibility: hidden;
		height: 0;
		content: '\20';
	}
	.cu-btn{
		border-radius: 50px;
	}
	
</style>