<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px', 'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back" :style="{'color':isDark?'#ffffff':'#000000'}"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}, {'color':isDark?'#ffffff':'#000000'}]">
					找回密码
				</view>
				<view class="action">
					
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="t-login" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
		<view class="t-b" :style="{'color':isDark?'#ffffff':'#000000'}">Hi，等你好久了~</view>
		</view>
		<view class="user-form" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<form>
				<block v-if="isEmail>0&&loginType==0">
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
					<input name="input" v-model="name" placeholder="请输入用户名或者邮箱" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
					<input name="input" v-model="code" placeholder="请输入邮箱验证码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
					<view class="sendcode text-blue" v-if="show" @tap="SendCode">发送</view>
					<view class="sendcode text-gray" v-if="!show">{{ times }}s</view>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
					<input name="input" v-model="password" type="password" placeholder="输入新密码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
					<input name="input" v-model="repassword" type="password" placeholder="再次输入密码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="user-btn flex flex-direction">
					<button class="cu-btn bg-blue margin-tb-sm lg" @tap="userFoget">确认修改</button>
				</view>
				</block>
				<block v-if="loginType==1">
					<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
						<input name="input" v-model="phone" placeholder="请输入手机号" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
					</view>
					<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
						<input name="input" v-model="code" placeholder="请输入短信验证码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
						<view class="sendcode text-blue" v-if="show" @tap="RegSendSms">发送</view>
						<view class="sendcode text-gray" v-if="!show">{{ times }}s</view>
					</view>
					<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
						<input name="input" v-model="password" type="password" placeholder="输入新密码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
					</view>
					<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
						<input name="input" v-model="repassword" type="password" placeholder="再次输入密码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
					</view>
					<view class="user-btn flex flex-direction">
						<button class="cu-btn bg-blue margin-tb-sm lg" @tap="userPhoneFoget">确认修改</button>
					</view>
				</block>
				<block v-if="isEmail==0&&loginType==0">
					<view class="t-b" :style="{'color':isDark?'#ffffff':'#000000'}">管理员已关闭找回功能，请联系管理员</view>
				</block>
			</form>
		</view>
		<tn-popup v-model="modelVisible" mode="bottom" :borderRadius="23" :maskCloseable="false">
				      <view style="padding: 30rpx 40rpx;" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						  <view style="text-align: center;" :style="{'color':isDark?'#ffffff':'#000000'}">温馨提示</view>
						  <view class="model-body" v-html="fogettext" style="margin-top: 20rpx;" :style="{'color':isDark?'#cccccc':'#666666'}"></view>
						  <view style="display: flex;justify-content: center;margin-top: 20rpx;">
						  	<tn-button style="margin-left: 20rpx;" :backgroundColor="isDark?'#2c2c2c':'#3cc9a4'" fontColor="#fff" @tap="okBtn">知道了</tn-button>
						  </view>
					  </view>
				</tn-popup>
		<view class="cu-modal" :class="modalName=='kaptcha'?'show':''">
			<view class="cu-dialog kaptcha-dialog" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="cu-bar justify-end" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
					<view class="content" :style="{'color':isDark?'#ffffff':'#000000'}">操作验证</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close" :style="{'color':isDark?'#ffffff':'#000000'}"></text>
					</view>
				</view>
				<view class="kaptcha-form" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
					<view class="kaptcha-image">
						<image :src="kaptchaUrl" mode="widthFix" @tap="reloadCode()"></image>
					</view>
					<view class="kaptcha-input">
						<input name="input" v-model="verifyCode" placeholder="请输入验证码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
						<view class="cu-btn bg-blue" v-if="loginType==0" @tap="SendCode">确定</view>
						<view class="cu-btn bg-blue" v-if="loginType==1" @tap="RegSendSms">确定</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	import darkModeMixin from '@/utils/darkModeMixin.js'
	export default {
		mixins: [darkModeMixin],
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
			AppStyle:this.$store.state.AppStyle,
				modelVisible: false,
				fogettext:"",
				times: 60,
				show:true,
				loginType: 1,
				isPhone: 0,
				name:"",
				phone:"",
				code:"",
				password:"",
				repassword:"",
				isEmail: 0,
				modalName:null,
				adminemail: this.$API.GetAppEmail(),
				kaptchaUrl:"",
				verifyCode:"",
				verifyLevel:0,
				// 行为验证相关
				captchaPluginEnabled: false,
				captchaConfig: null,
				behaviorCaptchaData: null,
				
			}
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onShow(){
			var that = this;
			that.getset();
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			that.kaptchaUrl = that.$API.getKaptcha();
			that.getConfig();

			// 检查行为验证插件状态
			that.checkCaptchaPlugin();
		},
		methods: {
			// 检查行为验证插件状态
			checkCaptchaPlugin() {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/getPluginStatus.php',
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							that.captchaPluginEnabled = res.data.data.overall_enabled;
							if (that.captchaPluginEnabled) {
								that.getCaptchaConfig();
							}
						}
					},
					fail: function() {
						that.captchaPluginEnabled = false;
					}
				});
			},

			// 获取行为验证配置
			getCaptchaConfig() {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/getCaptchaConfig.php',
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							that.captchaConfig = res.data.data;
						}
					}
				});
			},

			// 验证行为验证码
			verifyBehaviorCaptcha(callback) {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/checkCaptcha.php',
					method: 'POST',
					data: {
						captcha_type: that.captchaConfig.captcha_type,
						captcha_data: JSON.stringify(that.behaviorCaptchaData)
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							// 验证成功，执行回调
							callback && callback();
						} else {
							// 验证失败，清除验证数据并提示
							that.behaviorCaptchaData = null;
							uni.showToast({
								title: res.data ? res.data.msg : '验证失败',
								icon: 'none'
							});
						}
					},
					fail: function() {
						// 网络错误，回退到图片验证码
						that.behaviorCaptchaData = null;
						that.captchaPluginEnabled = false;
						uni.showToast({
							title: '验证服务异常，请使用图片验证码',
							icon: 'none'
						});
						if (that.verifyCode == "") {
							that.modalName = 'kaptcha';
						} else {
							callback && callback();
						}
					}
				});
			},

			// 显示行为验证
			showBehaviorCaptcha(action) {
				var that = this;
				that.currentAction = action; // 记录当前操作

				if (that.captchaConfig.captcha_type === 'geetest') {
					that.showGeetestCaptcha();
				} else if (that.captchaConfig.captcha_type === 'cloudflare') {
					that.showCloudflareCaptcha();
				} else if (that.captchaConfig.captcha_type === 'recaptcha') {
					that.showRecaptchaCaptcha();
				}
			},

			// 极验验证（占位符）
			showGeetestCaptcha() {
				// TODO: 集成极验SDK
				console.log('显示极验验证');
				// 模拟验证成功
				setTimeout(() => {
					this.onBehaviorCaptchaSuccess({
						geetest_challenge: 'mock_challenge',
						geetest_validate: 'mock_validate',
						geetest_seccode: 'mock_seccode'
					});
				}, 2000);
			},

			// Cloudflare验证（占位符）
			showCloudflareCaptcha() {
				// TODO: 集成Cloudflare Turnstile
				console.log('显示Cloudflare验证');
				// 模拟验证成功
				setTimeout(() => {
					this.onBehaviorCaptchaSuccess({
						'cf-turnstile-response': 'mock_cf_response'
					});
				}, 2000);
			},

			// reCAPTCHA验证（占位符）
			showRecaptchaCaptcha() {
				// TODO: 集成Google reCAPTCHA
				console.log('显示reCAPTCHA验证');
				// 模拟验证成功
				setTimeout(() => {
					this.onBehaviorCaptchaSuccess({
						'g-recaptcha-response': 'mock_recaptcha_response'
					});
				}, 2000);
			},

			// 处理行为验证结果
			onBehaviorCaptchaSuccess(data) {
				this.behaviorCaptchaData = data;
				// 根据当前操作执行相应的验证
				if (this.currentAction === 'SendCode') {
					this.verifyBehaviorCaptcha(() => this.performSendCode());
				} else if (this.currentAction === 'RegSendSms') {
					this.verifyBehaviorCaptcha(() => this.performRegSendSms());
				}
			},

			userPhoneFoget(){
				var that = this;
				if (that.phone == ""||that.code == ""||that.password == ""||that.repassword == "") {
					uni.showToast({
						title:"请输入正确的参数",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if (!/^1[3-9]\d{9}$/.test(that.phone)) {
					uni.showToast({
						title:"请输入合法的手机号",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false;
				}
				if(!that.validatePassword(that.password)){
					uni.showToast({
						title:"密码至少包含字母和数字，且长度必须大于5位",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if(that.password!=that.repassword){
					uni.showToast({
						title:"两次密码不一致",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				var data = {
					'phone':that.phone,
					'code':that.code,
					'password':that.password,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.userPhoneFoget(),
					data:{"params":JSON.stringify(that.$API.removeObjectEmptyKey(data))},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						
						if(res.data.code==1){
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							that.back();
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			RegSendSms() {
				var that = this;
				
				if (that.phone == "") {
					uni.showToast({
						title: "请输入手机号",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^1[3-9]\d{9}$/.test(that.phone)) {
					uni.showToast({
						title:"请输入合法的手机号",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false;
				}
				
				if (that.verifyLevel > 0) {
					// 如果启用了行为验证插件，使用行为验证
					if (that.captchaPluginEnabled && that.captchaConfig && that.captchaConfig.enabled) {
						if (!that.behaviorCaptchaData) {
							// 触发行为验证
							that.showBehaviorCaptcha('RegSendSms');
							return false;
						} else {
							// 先验证行为验证码，成功后再发送短信
							that.verifyBehaviorCaptcha(function() {
								that.performRegSendSms();
							});
							return false;
						}
					} else {
						// 使用原有的图片验证码
						if (that.verifyCode == "") {
							that.modalName = 'kaptcha'
							return false
						}
					}
				}

				that.performRegSendSms();
			},

			// 执行短信发送
			performRegSendSms() {
				var that = this;
				var data = {
					'phone': that.phone,
					'verifyCode': that.captchaPluginEnabled ? '' : that.verifyCode
				}

				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.sendSMS(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.verifyCode = "";
						that.behaviorCaptchaData = null; // 清除行为验证数据
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);

						if (res.data.code == 1) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							that.getCode();
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
				
					},
					fail: function(res) {
						that.modalName = null;
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			getset() {
			  var that = this;
			      uni.request({
			        url:that.$API.SPset(),
			        method:'GET',
			        data:{
			          id:1
			        },
			        dataType:"json",
			        success(res) {
					    that.fogettext = res.data.fogettext;
						if (that.fogettext != "") {
							that.modelVisible = true
						}
			        },
			        fail(error) {
			          console.log(error);
			        }
			      })
			},
			okBtn() {
							const nowDate = +new Date();
							uni.setStorageSync('modelView', nowDate);
							this.cancleBtn();
						},
			cancleBtn() {
							this.modelVisible = false;
						},
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			reloadCode(){
				var that = this;
				var kaptchaUrl = that.$API.getKaptcha();
				var num=Math.ceil(Math.random()*10);
				kaptchaUrl += "?"+num;
				that.kaptchaUrl = kaptchaUrl;
			},
			getConfig() {
				var that = this;
				//获取应用信息
				uni.request({
			
					url: that.$API.getAppinfo(),
					data: {
						"key": that.$API.getAppKey()
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.isEmail = res.data.data.isEmail;
							that.loginType = res.data.data.isPhone;
							that.isPhone = res.data.data.isPhone;
							that.verifyLevel = res.data.data.verifyLevel;
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "获取应用配置信息失败！",
							icon: 'none'
						})
					}
				})
			},
			hideModal(e) {
				this.modalName = null
			},
			PickerChange(e) {
				this.index = e.detail.value
			},
			validatePassword(password) {
			  // 至少包含一个字母和一个数字，长度必须大于6，可以包含特殊符号
			  const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]]{6,}$/;
			  return regex.test(password);
			},
			userFoget() {
				var that = this;
				if (that.name == ""||that.code == ""||that.password == ""||that.repassword == "") {
					uni.showToast({
						title:"请输入正确的参数",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if(!that.validatePassword(that.password)){
					uni.showToast({
						title:"密码至少包含字母和数字，且长度必须大于5位",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if(that.password!=that.repassword){
					uni.showToast({
						title:"两次密码不一致",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				var data = {
					'name':that.name,
					'code':that.code,
					'password':that.password,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.userFoget(),
					data:{"params":JSON.stringify(that.$API.removeObjectEmptyKey(data))},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						
						if(res.data.code==1){
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							that.back();
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			SendCode() {
				var that = this;
				if (that.name == "") {
					uni.showToast({
						title:"请输入用户名/邮箱",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if(that.verifyLevel>0){
					// 如果启用了行为验证插件，使用行为验证
					if (that.captchaPluginEnabled && that.captchaConfig && that.captchaConfig.enabled) {
						if (!that.behaviorCaptchaData) {
							// 触发行为验证
							that.showBehaviorCaptcha('SendCode');
							return false;
						} else {
							// 先验证行为验证码，成功后再发送邮件
							that.verifyBehaviorCaptcha(function() {
								that.performSendCode();
							});
							return false;
						}
					} else {
						// 使用原有的图片验证码
						if (that.verifyCode == "") {
							that.modalName = 'kaptcha'
							return false
						}
					}
				}
				that.performSendCode();
			},

			// 执行邮件发送
			performSendCode() {
				var that = this;
				var data = {
					'name':that.name,

				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.SendCode(),
					data:{
						"params":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						'verifyCode': that.captchaPluginEnabled ? '' : that.verifyCode
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.verifyCode = "";
						that.behaviorCaptchaData = null; // 清除行为验证数据
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);

						if(res.data.code==1){
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							that.getCode();
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
						
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			getCode() {
			  this.show = false
			  this.timer = setInterval(()=>{
				this.times--
				if(this.times===0){
				  this.show = true
				  clearInterval(this.timer);
				  this.times = 60;
				}
			  },1000)
			}
		}
	}
</script>

		<style>
					.img-a {
						position: absolute;
						width: 100%;
						top: -280rpx;
						right: -100rpx;
					}
					.img-b {
						position: absolute;
						width: 50%;
						bottom: 0;
						left: -50rpx;
						margin-bottom: -300rpx;
					}
					.t-login {
						position: relative;
						width: 600rpx;
						margin: 0 auto;
						font-size: 28rpx;
						color: #000;
					}
					
					.t-login button {
						font-size: 28rpx;
						background: #5677fc;
						color: #fff;
						height: 90rpx;
						line-height: 90rpx;
						border-radius: 50rpx;
						box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
					}
					
					.t-login input {
						padding: 0 20rpx 0 120rpx;
						height: 90rpx;
						line-height: 90rpx;
						/* margin-bottom: 50rpx; */
						background: #f8f7fc;
						border: 1px solid #e9e9e9;
						font-size: 28rpx;
						border-radius: 50rpx;
					}
					
					.t-login .t-a {
						position: relative;
					}
					
					.t-login .t-a image {
						width: 60rpx;
						height: 40rpx;
						position: absolute;
						left: 40rpx;
						top: 28rpx;
						border-right: 2rpx solid #dedede;
						padding-right: 20rpx;
					}
					
					.t-login .t-b {
						text-align: left;
						font-size: 19px;
						color: #313131;
						padding: 35px 0 0px 0;
						font-weight: bold;
					}
					
					.t-login .t-c {
						position: absolute;
						right: 22rpx;
						top: 22rpx;
						background: #5677fc;
						color: #fff;
						font-size: 24rpx;
						border-radius: 50rpx;
						height: 50rpx;
						line-height: 50rpx;
						padding: 0 25rpx;
					}
					
					.t-login .t-d {
						text-align: center;
						color: #999;
						margin: 80rpx 0;
					}
					
					.t-login .t-e {
						text-align: center;
						width: 250rpx;
						margin: 80rpx auto 0;
					}
					
					.t-login .t-g {
						float: left;
						width: 100%;
					}
					
					.t-login .t-e image {
						width: 50rpx;
						height: 50rpx;
					}
					
					.t-login .t-f {
						text-align: center;
						margin: 200rpx 0 0 0;
						color: #666;
					}
					
					.t-login .t-f text {
						margin-left: 20rpx;
						color: #aaaaaa;
						font-size: 27rpx;
					}
					
					.t-login .uni-input-placeholder {
						color: #000;
					}
					
					.cl {
						zoom: 1;
					}
					.s1{
							
							float: right;
						}
					.cl:after {
						clear: both;
						display: block;
						visibility: hidden;
						height: 0;
						content: '\20';
					}
					.cu-btn{
						border-radius: 50px;
					}
					.cu-form-group{
						border-radius: 50px;
					}
				</style>
		