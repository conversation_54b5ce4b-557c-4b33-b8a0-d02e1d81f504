# articleItemB.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/components/articleItemB.vue.md`
- **组件说明**：文章列表项的又一种样式，特点是包含操作按钮（如编辑、删除），可能用于"我的文章"或管理后台等场景。

---

<template>
	<view>
		<view v-if="item.isAds">
			<view class="tn-margin-bottom-sm"><!-- 默认-->
				<view class="article-shadow" @tap="goAds(item)">
					<view class="tn-flex">
						<view class="image-pic tn-margin-sm" style="margin: 16upx;"
							:style="'background-image:url('+item.img+')'">
							<view class="image-article">
							</view>
						</view>
						<text class="extra-count" style="z-index: 99;">广告</text>
						<view class="tn-margin-sm tn-padding-top-xs" style="width: 100%;">
							<view class="tn-text-lg tn-text-bold clamp-text-1 tn-text-justify">
								<text>{{replaceSpecialChar(item.name)}}</text>
							</view>
							<view class="tn-padding-top-xs" style="min-height: 90rpx;">
								<text class="tn-text-df tn-color-gray clamp-text-2 tn-text-justify">
									{{subText(item.intro,40)}}
								</text>
							</view>
							<view class="tn-margin-top-sm">
								<view class="justify-content-item tn-color-gray tn-text-center">
									<view class="ads-more tn-color-gray" @tap="goAds(item)">了解更多<text
											class="cuIcon-right"></text></view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-else-if="item.abcimg === 'able'||!item.abcimg">
			<view class="tn-margin-bottom-sm"><!-- 默认-->
				<view class="article-shadow" @tap="toInfo(item)">
					<view class="tn-flex">
						<block v-if="item.images.length != 0">
						<block v-if="item.category[0].slug == 'vip'">
							<view v-if="item.images.length > 0" class="image-pic tn-margin-sm"
								:style="'margin: 16upx;background-image:url('+vip_img+')'">
								<view class="image-article">
								</view>
							</view>
						</block>
						<block v-else>
							<view v-if="item.images.length > 0" class="image-pic tn-margin-sm" style="margin: 16upx;background-size: cover;"
								:style="'background-image:url('+item.images[0]+')'">
								<view class="image-article">
								</view>
							</view>
							<view v-else class="image-pic tn-margin-sm" style="margin: 16upx;"
								:style="'background-image:url('+no_img+')'">
								<view class="image-article">
								</view>
							</view>
						</block>
						</block>
						<view class="tn-padding-top-xs" style="width: 100%;" :style="item.images.length!=0?'margin: 16upx;':'margin: 34upx;'">
							<view class="tn-text-lg tn-text-bold clamp-text-1 tn-text-justify">
								<text>{{replaceSpecialChar(item.title)}}</text>
							</view>
							<view class="tn-padding-top-xs" style="min-height: 90rpx;">
								<text class="tn-text-df tn-color-gray clamp-text-2 tn-text-justify">
									{{subText(item.text,40)}}
								</text>
							</view>
							<view class="tn-flex tn-flex-row-between tn-flex-col-between tn-margin-top-sm">
								<view>
									<view class="justify-content-item tn-tag-content__item tn-text" style="margin-right: 15upx;" v-if="isTop">
										<text class="tn-tag-content__item--prefix tn-text-bold"
											style="padding-right: 0upx;">
											<text class="tn-icon-totop-simple tn-tag-content__item--prefix"
												style="color: #00BCD4;"></text>置顶</text>
									</view>
									<view class="justify-content-item tn-tag-content__item tn-text-sm tn-text-bold">
										{{item.category[0].name}}
									</view>
								</view>
								<view class="justify-content-item tn-color-gray tn-text-center">
									<text class="tn-icon-eye tn-color-gray" style="padding-right: 26rpx;"
										v-if="!isTop">{{formatNumber(item.views)}}</text>
									<text class="tn-icon-comment tn-color-gray"
										style="padding-right: 26rpx;">{{formatNumber(item.commentsNum)}}</text>
									<text class="tn-icon-like-lack tn-color-gray"
										>{{formatNumber(item.likes)}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-else-if="item.abcimg === 'mable'"><!-- 三图-->
			<view class="tn-margin-bottom-sm">
				<view class="article-shadow" @tap="toInfo(item)">
					<view style="padding: 4upx 10upx 0 10upx;">
						<tn-grid hoverClass="none" :col="3">
							<block v-if="item.category[0].slug == 'vip'">
								<view style="width: 33.3%;" v-if="item.images.length > 0">
									<view class="image-pic tn-margin-xs"
										:style="'background-image:url('+vip_img+');background-size: cover;'">
										<view class="image-article">
										</view>
									</view>
								</view>
								<view style="width: 33.3%;" v-if="item.images.length > 1">
									<view class="image-pic tn-margin-xs"
										:style="'background-image:url('+vip_img+');background-size: cover;'">
										<view class="image-article">
										</view>
									</view>
								</view>
								<view style="width: 33.3%;" v-if="item.images.length > 2">
									<view class="image-pic tn-margin-xs"
										:style="'background-image:url('+vip_img+');background-size: cover;'">
										<view class="image-article">
										</view>
									</view>
								</view>
							</block>
							<block v-else>
								<view style="width: 33.3%;" v-if="item.images.length > 0">
									<view class="image-pic tn-margin-xs"
										:style="'background-image:url(' + item.images[0] + ')'">
										<view class="image-article">
										</view>
									</view>

								</view>
								<view style="width: 33.3%;" v-if="item.images.length > 1">
									<view class="image-pic tn-margin-xs"
										:style="'background-image:url(' + item.images[1] + ')'">
										<view class="image-article">
										</view>
									</view>
								</view>
								<view style="width: 33.3%;" v-if="item.images.length > 2">
									<view class="image-pic tn-margin-xs"
										:style="'background-image:url(' + item.images[2] + ')'">
										<view class="image-article">
										</view>
									</view>
								</view>
							</block>
						</tn-grid>
					</view>
					<view class="tn-margin-sm tn-padding-bottom-sm">
						<view class="tn-text-lg tn-text-bold clamp-text-1 tn-text-justify">
							<text class="">{{replaceSpecialChar(item.title)}}</text>
						</view>

						<view class="tn-flex tn-flex-row-between tn-flex-col-between tn-margin-top-sm">
							<view class="justify-content-item tn-margin-right tn-text-sm tn-text-bold">
								<span class="tn-tag-content__item tn-margin-right-sm" v-if="isTop">
									<text class="tn-tag-content__item--prefix tn-text-bold">
										<text class="tn-icon-totop-simple tn-tag-content__item--prefix"
											style="color: #00BCD4;"></text>置顶</text></span>
								<span class="tn-tag-content__item"> {{item.category[0].name}}
								</span>
							</view>
							<!-- <view v-for="category in item.category" :key="category.id" class="justify-content-item tn-tag-content__item tn-margin-right tn-text-sm tn-text-bold">
			            <text class="tn-tag-content__item--prefix">#</text> {{category.name}}
			          </view> -->
							<view class="justify-content-item tn-color-gray tn-text-center">
								<text class="tn-icon-eye tn-color-gray"
									style="padding-right: 26rpx;">{{formatNumber(item.views)}}</text>
								<text class="tn-icon-comment tn-color-gray"
									style="padding-right: 26rpx;">{{formatNumber(item.commentsNum)}}</text>
								<text class="tn-icon-like-lack tn-color-gray"
									>{{formatNumber(item.likes)}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-else>
			<view class="tn-margin-bottom-sm"><!-- 大图 -->
				<view class="article-shadow" @tap="toInfo(item)">
					<block v-if="item.category[0].slug == 'vip'">
						<view class="image-pic"
							:style="'border-radius: 20rpx 20rpx 0 0;background-image:url('+vip_img_big+');background-size: cover;'"
							v-if="item.images.length > 0">
							<view class="image-design">
							</view>
						</view>
					</block>
					<block v-else>
						<view class="image-pic" style="border-radius: 20rpx 20rpx 0 0;" v-if="item.images.length > 0"
							:style="'background-image:url(' + item.images[0] + ')'">
							<view class="image-design">
							</view>
						</view>
					</block>

					<view class="tn-padding-left-sm tn-padding-right-sm tn-padding-bottom-sm">
						<view
							class="tn-text-lg tn-text-bold clamp-text-1 tn-text-justify tn-padding-top-sm tn-padding-bottom-sm">
							<text class="">{{replaceSpecialChar(item.title)}}</text>
						</view>
						<view class="tn-flex tn-flex-row-between tn-flex-col-between">
							<view class="tn-flex tn-flex-row-between tn-flex-col-between tn-flex-1">
								<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
									v-if="isTop">
									<text class="tn-tag-content__item--prefix tn-text-bold"
										style="padding-right: 0upx;">
										<text class="tn-icon-totop-simple tn-tag-content__item--prefix"
											style="color: #00BCD4;"></text>置顶</text>
								</view>
								<view
									class="justify-content-item tn-tag-content__item tn-margin-right tn-text-sm tn-text-bold">
									
									{{ item.category[0].name }}
								</view>
								<view class="justify-content-item tn-color-gray tn-text-right tn-flex-1">
									<text class="tn-icon-eye tn-color-gray"
										style="padding-right: 26rpx;">{{formatNumber(item.views)}}</text>
									<text class="tn-icon-comment tn-color-gray"
										style="padding-right: 26rpx;">{{formatNumber(item.commentsNum)}}</text>
									<text class="tn-icon-like-lack tn-color-gray"
										>{{formatNumber(item.likes)}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
			isTop: {
				type: Boolean,
				default: false
			},
			isDraft: {
				type: Boolean,
				default: false
			}
		},
		name: "articleItemB",
		data() {
			return {
				// #ifdef APP-PLUS || MP
				vip_img: "../../static/page/vip_img.png",
				vip_img_big: "../../static/page/vip_img_big.png",
				no_img: "../../static/page/pic.png",
				// #endif
				// #ifdef H5
				vip_img: "/h5/static/page/vip_img.png",
				vip_img_big: "/h5/static/page/vip_img_big.png",
				no_img: "/h5/static/page/pic.png",
				// #endif
			};
		},
		methods: {
			subText(text, num) {
			  text = text.replace(/vip(.*?)\/vip/g, "(该内容仅会员可见)");
			  text = text.replace(/audio(.*?)\/audio/g, "(该帖子包含音乐)");
			  text = text.replace(/video(.*?)\/video/g, "(该帖子包含视频)");
			  if (text.length > num) {
					return text.substring(0, num) + "..."
				} else {
					return text;
				}
			},
			replaceSpecialChar(text) {
				text = text.replace(/&quot;/g, '"');
				text = text.replace(/&amp;/g, '&');
				text = text.replace(/&lt;/g, '<');
				text = text.replace(/&gt;/g, '>');
				text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				return result;
			},
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},
			toInfo(data) {
				var that = this;
				if(!that.isDraft){
					uni.navigateTo({
						url: '/pages/contents/info?cid=' + data.cid + "&title=" + data.title
					});
				}
				
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
		}
	}
</script>

<style lang="scss" scoped>
	.pages-a {
		width: 100%;
		height: 100%;
		position: relative;
		// background-image: linear-gradient(to top, #4C3FAE 20%, #6E26BA 80%);
	}

	/* 底部安全边距 start*/
	.tn-tabbar-height {
		min-height: 120rpx;
		height: calc(140rpx + env(safe-area-inset-bottom) / 2);
		height: calc(140rpx + constant(safe-area-inset-bottom));
	}


	/* 底部tabbar假阴影 start*/
	.bg-tabbar-shadow {
		// background-image: repeating-linear-gradient(to top, rgba(0,0,0,0.1) 10rpx, rgba(255,255,255,0) , rgba(255,255,255,0));
		box-shadow: 0rpx 0rpx 400rpx 0rpx rgba(0, 0, 0, 0.25);
		position: fixed;
		bottom: calc(0rpx + env(safe-area-inset-bottom) / 2);
		bottom: calc(0rpx + constant(safe-area-inset-bottom));
		height: 60rpx;
		width: 100vw;
		z-index: 0;
	}

	/* 阴影 start*/
	.home-shadow {
		border-radius: 15rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
	}


	/* 自定义导航栏内容 start */
	.custom-nav {
		height: 100%;

		&__back {
			margin: auto 5rpx;
			font-size: 40rpx;
			margin-right: 10rpx;
			margin-left: 30rpx;
			flex-basis: 5%;
		}

		&__search {
			flex-basis: 60%;
			width: 100%;
			height: 100%;

			&__box {
				width: 100%;
				height: 70%;
				padding: 10rpx 0;
				margin: 0 30rpx;
				border-radius: 60rpx 60rpx 0 60rpx;
				font-size: 24rpx;
			}

			&__icon {
				padding-right: 10rpx;
				margin-left: 20rpx;
				font-size: 30rpx;
			}

			&__text {
				// color: #AAAAAA;
			}
		}
	}

	.extra-count {
		position: absolute;
		color: white;
		font-size: 14px;
		border-radius: 10upx;
		background-color: #00000080;
		margin-top: 20px;
		left: 60upx;
		width: 80upx;
		height: 40upx;
		display: flex;
		justify-content: center;
	}

	.logo-image {
		z-index: 9999 !important;
		width: 65rpx;
		height: 65rpx;
		position: relative;
	}

	.logo-pic {
		z-index: 9999 !important;
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border-radius: 50%;
	}

	/* 自定义导航栏内容 end */

	/* 旋转 */
	.png-sussuspension1 {
		animation: suspension1 12s ease-in-out infinite;
	}

	@keyframes suspension1 {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.tabs-fixed {
		position: fixed;
		top: 0;
		width: 100%;
		transition: all 0.25s ease-out;
		z-index: 1;
	}

	/* 轮播视觉差 start */
	.card-swiper {
		height: 360rpx !important;
		margin-top: -30rpx;
	}

	.card-swiper swiper-item {
		width: 750rpx !important;
		left: 0rpx;
		box-sizing: border-box;
		padding: 30rpx 30rpx 30rpx 30rpx;
		overflow: initial;
		// border: 1rpx solid blueviolet;
	}

	.card-swiper swiper-item .swiper-item {
		width: 100%;
		display: block;
		height: 100%;
		border-radius: 0rpx;
		transform: scale(0.85);
		transition: all 0.2s ease-in 0s;
		will-change: transform;
		overflow: hidden;
		border-radius: 10rpx;
		// border: 1rpx solid blue;
	}

	.card-swiper swiper-item.cur .swiper-item {
		transform: none;
		transition: all 0.2s ease-in 0s;
		will-change: transform;
	}

	.card-swiper swiper-item .swiper-item2 {
		margin-top: -370rpx;
		width: 620rpx;
		display: block;
		height: 515rpx;
		border-radius: 0rpx;
		transform: translate(240rpx, 0rpx) scale(0.45, 0.45);
		transition: all 0.6s ease 0s;
		will-change: transform;
		overflow: hidden;
		// border: 1rpx solid black;
	}

	.card-swiper swiper-item.cur .swiper-item2 {
		margin-top: -385rpx;
		width: 620rpx;
		transform: translate(210rpx, 0rpx) scale(0.62, 0.62);
		transition: all 0.6s ease 0s;
		will-change: transform;
	}

	.card-swiper swiper-item .swiper-item-text {
		margin-top: -300rpx;
		width: 100%;
		display: block;
		height: 50%;
		border-radius: 10rpx;
		transform: translate(0rpx, -40rpx) scale(0.7, 0.7);
		transition: all 0.6s ease 0s;
		will-change: transform;
		overflow: hidden;
	}

	.card-swiper swiper-item.cur .swiper-item-text {
		margin-top: -350rpx;
		width: 100%;
		transform: translate(0rpx, 0rpx) scale(0.9, 0.9);
		transition: all 0.6s ease 0s;
		will-change: transform;
	}

	.image-banner {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.image-banner image {
		width: 100%;
	}

	/* 轮播指示点 start*/
	.indication {
		z-index: 0;
		width: 100%;
		height: 36rpx;
		position: absolute;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		// border: 1rpx solid orangered;
	}

	.spot {
		// background-color: #FBBD12;
		top: -80rpx;
		opacity: 0.1;
		width: 30rpx;
		height: 8rpx;
		border-radius: 2rpx;
		margin: 0 6rpx !important;
		left: -238rpx;
		position: relative;
	}

	.spot.active {
		opacity: 1;
		width: 30rpx;
		// background-color: #FBBD12;
	}



	/* 资讯主图 start*/
	.image-article {
		border-radius: 8rpx;
		width: 200rpx;
		height: 200rpx;
		position: relative;
	}

	.image-pic {
		border: 1rpx solid #F8F7F8;
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border-radius: 10rpx;
	}

	.article-shadow {
		border-radius: 15rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
		background-color: #fff;
	}

	/* 文字截取*/
	.clamp-text-1 {
		-webkit-line-clamp: 1;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	.clamp-text-2 {
		-webkit-line-clamp: 2;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 35rpx;
			color: #1D2541;
			background-color: #F3F2F7;
			border-radius: 10rpx;
			font-size: 22rpx;
			padding: 5rpx 15rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}


	/* 大单图 start*/
	.tn-blogger-content2 {
		&__wrap {
			padding: 30rpx;
		}

		&__info {
			&__btn {
				margin-right: -12rpx;
				opacity: 0.5;
			}
		}

		&__label {
			&__item {
				line-height: 45rpx;
				padding: 0 20rpx;
				margin: 5rpx 18rpx 0 0;

				&--prefix {
					color: #00FFC8;
					padding-right: 10rpx;
				}
			}

			&__desc {
				line-height: 55rpx;
			}
		}

		&__main-image {
			box-shadow: 0rpx 5rpx 40rpx 0rpx rgba(43, 158, 246, 0.2);
			border-radius: 16rpx;

			&--1 {
				max-width: 690rpx;
				min-width: 690rpx;
				max-height: 400rpx;
				min-height: 400rpx;
			}

			&--2 {
				max-width: 260rpx;
				max-height: 260rpx;
			}

			&--3 {
				height: 212rpx;
				width: 100%;
			}
		}

		&__count-icon {
			font-size: 40rpx;
			padding-right: 5rpx;
		}
	}

	.image-design {
		padding: 140rpx 0rpx;
		font-size: 40rpx;
		font-weight: 300;
		position: relative;
	}

	.image-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border-radius: 10rpx;
	}

	/* 文章内容 end*/
</style>