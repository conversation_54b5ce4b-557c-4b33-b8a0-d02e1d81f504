<template>
	<view class="user" :class="$store.state.AppStyle" style="background-color: #f6f6f6;height: 100vh;">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					用户注册
				</view>
				<view class="action">

				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="t-login">
			<view class="t-b">Hi，欢迎加入{{appname}}~</view>
		</view>
		<view class="user-form">
			<form>
				<view class="cu-form-group" style="border-radius: 100rpx;">
					<input name="input" v-model="name" placeholder="请输入账号"></input>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;" v-if="loginType==0">
					<input name="input" v-model="mail"  placeholder="请输入邮箱"></input>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;" v-if="loginType==1">
					<input name="input" v-model="phone" placeholder="请输入手机号"></input>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;" v-if="isEmail>0&&loginType==0">
					<input name="input" v-model="code" placeholder="请输入邮箱验证码"></input>
					<view class="sendcode text-blue" v-if="show" @tap="RegSendCode">发送</view>
					<view class="sendcode text-gray" v-if="!show">{{ times }}s</view>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;" v-if="loginType==1">
					<input name="input" v-model="code" placeholder="请输入短信验证码"></input>
					<view class="sendcode text-blue" v-if="show" @tap="RegSendSms">发送</view>
					<view class="sendcode text-gray" v-if="!show">{{ times }}s</view>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;">
					<input name="input" v-model="password" type="text" placeholder="请输入密码"></input>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;">
					<input name="input" v-model="repassword" type="text" placeholder="再次输入密码"></input>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;" v-if="isInvite>0">
					<input name="input" v-model="inviteCode" type="text" placeholder="请输入邀请码 (必填)"></input>
					<view class="sendcode text-blue" v-if="BuyCodeUrl!=''" @tap="goBuyCode(BuyCodeUrl)">获取邀请码</view>
				</view>
				<view class="cu-form-group" style="border-radius: 100rpx;" v-else>
					<input name="input" v-model="inviteCode" type="text" placeholder="邀请码 (可留空)"></input>
				</view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view class="user-btn flex flex-direction">
					
					<button class="cu-btn bg-blue margin-tb-sm lg" v-if="loginType==0" @tap="userRegister">立即注册</button>
					<button class="cu-btn bg-blue margin-tb-sm lg" v-if="loginType==1" @tap="userRegisterByPhone">立即注册</button>
					<text class="text-right margin-top" style="font-size: 22upx;color:#808080;" @tap="getAgree">
						<text style="margin-right: 10upx">
							<radio value="r1" :checked="isAgreeck" color="#3ccea7"
								style="transform:scale(0.7)" />
						</text>我同意{{appname}}的<text class="text-blue" @tap="toAgreement">《用户协议》</text>
						和<text class="text-blue" @tap="toPrivacy">《隐私条款》</text>
					</text>
				</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="user-btn flex flex-direction">
					<button class="cu-btn bg-blue margin-tb-sm lg" @tap="usermodelVisible=true">立即注册</button>
					
				</view>
				<!-- #endif -->
				
			</form>
		</view>
		<tn-popup v-model="usermodelVisible" mode="center" :borderRadius="23" :maskCloseable="false">
			<view style="padding: 30rpx 40rpx;">
				<view style="text-align: center;">用户及隐私协议</view>
				<view class="model-body" style="margin-top: 20rpx;">请问你是否同意<text
							class="text-blue" @tap="toAgreement">《用户及隐私协议》</text></view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button backgroundColor="#3ccea7" fontColor="#fff"
						@tap="yesBtn">同意</tn-button>
					<tn-button style="margin-left: 20rpx;" backgroundColor="#37bc99" fontColor="#fff"
						@tap="noBtn">不同意</tn-button>
				</view>
			</view>
		</tn-popup>
		<tn-popup v-model="modelVisible" mode="bottom" :borderRadius="23" :maskCloseable="false">
			<view style="padding: 30rpx 40rpx;">
				<view style="text-align: center;">温馨提示</view>
				<view class="model-body" v-html="registertext" style="margin-top: 20rpx;"></view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button style="margin-left: 20rpx;" backgroundColor="#3cc9a4" fontColor="#fff"
						@tap="okBtn">知道了</tn-button>
				</view>
			</view>
		</tn-popup>
		<view class="cu-modal" :class="modalName=='kaptcha'?'show':''">
			<view class="cu-dialog kaptcha-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">操作验证</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>
				<view class="kaptcha-form">
					<view class="kaptcha-image">
						<image :src="kaptchaUrl" mode="widthFix" @tap="reloadCode()"></image>
					</view>
					<view class="kaptcha-input">
						<input name="input" v-model="verifyCode" placeholder="请输入验证码"></input>
						<view class="cu-btn bg-blue" v-if="loginType==0" @tap="RegSendCode">确定</view>
						<view class="cu-btn bg-blue" v-if="loginType==1" @tap="RegSendSms">确定</view>
					</view>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				usermodelVisible: false,
				times: 60,
				show: true,
				appname: this.$API.GetAppName(),
				modelVisible: false,
				name: "",
				BuyCodeUrl: this.$API.GetBuyInviteCodeUrl(),
				mail: "",
				code: "",
				password: "",
				phone: "",
				repassword: "",
				isEmail: 0,
				loginType: 0,
				isPhone: 0,
				isInvite: 0,
				inviteCode: "",
				isAgree: 'false',
				isAgreeck: false,
				registertext: "",
				modalName: null,
				kaptchaUrl: "",
				verifyCode: "",
				verifyLevel: 0,

			}
		},
		onPullDownRefresh() {
			var that = this;

		},
		onShow() {
			var that = this;
			// #ifdef APP-PLUS

			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			var that = this;
			if (localStorage.getItem('isAgree')) {
				that.isAgree = localStorage.getItem('isAgree');
				
			}else{
				that.isAgree = 'false'
			}
			if (that.isAgree=='true') {
				that.isAgreeck = true
			} else{
				that.isAgreeck = false
			}
			
			that.kaptchaUrl = that.$API.getKaptcha();
		},
		onLoad(res) {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = that.CustomBar;
			// #endif
			if (res.inviteCode) {
				that.inviteCode = res.inviteCode;
			}
			that.regConfig();
		},
		mounted() {
			this.getset()
		},
		methods: {
			getAgree(){
				var that = this;
				if (localStorage.getItem('isAgree')) {
					that.isAgree = localStorage.getItem('isAgree');
				}else{
					that.isAgree = 'false'
				}
				if (that.isAgree=='true') {
					that.isAgreeck = true
					uni.showToast({
						title: "已同意",
						icon: 'none',
						duration: 3000,
						position: 'center',
					});
				} else{
					that.isAgreeck = false
					that.toAgreement()
				}
			},
			goBuyCode(url) {
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
				// #ifdef MP-WEIXIN
				uni.showToast({
					title: "小程序无法转跳",
					icon: 'none'
				})
				// #endif
			},
			getset() {
			  var that = this;
			      uni.request({
			        url:that.$API.SPset(),
			        method:'GET',
			        dataType:"json",
			        success(res) {
					    that.registertext = res.data.registertext;
						if (that.registertext != "") {
							that.modelVisible = true
						}
			        },
			        fail(error) {
			          console.log(error);
			        }
			      })
			},
			okBtn() {
				this.modelVisible = false;
			},
			back() {
				localStorage.removeItem('isAgree');  
				uni.navigateBack({
					delta: 1
				});
			},
			hideModal(e) {
				this.modalName = null
			},
			PickerChange(e) {
				this.index = e.detail.value
			},
			validatePassword(password) {
			  // 至少包含一个字母和一个数字，长度必须大于6，可以包含特殊符号
			  const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]]{6,}$/;
			  return regex.test(password);
			},
			reloadCode() {
				var that = this;
				var kaptchaUrl = that.$API.getKaptcha();
				var num = Math.ceil(Math.random() * 10);
				kaptchaUrl += "?" + num;
				that.kaptchaUrl = kaptchaUrl;
			},
			userRegisterByPhone() {
				var that = this;
				// #ifdef APP-PLUS || H5
				if (that.isAgree=='false') {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				if (that.name == "" || that.phone == "" || that.password == "" || that.repassword == "") {
					uni.showToast({
						title: "请输入正确的参数",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^[A-Za-z0-9]+$/.test(that.name)) {
					uni.showToast({
						title: "账号只允许为数字和英文",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^1[3-9]\d{9}$/.test(that.phone)) {
					uni.showToast({
						title:"请输入合法的手机号",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false;
				}
				
				if(!that.validatePassword(that.password)){
					uni.showToast({
						title:"密码至少包含字母和数字，且长度必须大于5位",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if (that.password != that.repassword) {
					uni.showToast({
						title: "两次密码不一致",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				
				var data = {
					'name': that.name,
					'code': that.code,
					'password': that.password,
					'phone': that.phone,
					'inviteCode': that.inviteCode
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
			
					url: that.$API.userRegisterByPhone(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							localStorage.removeItem('isAgree');  
							if(res.data.msg=="注册成功"){
								var data = {
									name: that.name,
									password: that.password,
								}
								uni.showLoading({
									title: "加载中"
								});
								that.$Net.request({
								
									url: that.$API.userLogin(),
									data: {
										"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "post",
									dataType: 'json',
									success: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: res.data.msg,
											icon: 'none'
										})
										if (res.data.code == 1) {
											//保存用户信息
											localStorage.setItem('userinfo', JSON.stringify(res.data.data));
											localStorage.setItem('token', res.data.data.token);
											that.getCID();
											var timer = setTimeout(function() {
												that.back2();
												clearTimeout('timer')
											}, 1000)
										}
									},
									fail: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络开小差了哦",
											icon: 'none'
										})
										uni.stopPullDownRefresh()
									}
								})
							}else{
								that.back();
							}
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			back2() {
				localStorage.removeItem('isAgree');  
				uni.navigateBack({
					delta: 2
				});
			},
			getCID() {
				var that = this;
				let cid = ''
				// #ifdef APP-PLUS
				let pinf = plus.push.getClientInfo();
				cid = pinf.clientid;
				if (cid) {
					that.setClientId(cid);
				}
				// #endif
			},
			setClientId(cid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('token')) {
			
					token = localStorage.getItem('token');
				} else {
					return false;
				}
				that.$Net.request({
			
					url: that.$API.setClientId(),
					data: {
						"clientId": cid,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
			
			
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			userRegister() {
				var that = this;
				// #ifdef APP-PLUS || H5
				if (that.isAgree=='false') {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				if (that.name == "" || that.mail == "" || that.password == "" || that.repassword == "") {
					uni.showToast({
						title: "请输入正确的参数",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^[A-Za-z0-9]+$/.test(that.name)) {
					uni.showToast({
						title: "账号只允许为数字和英文",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				
				if(!that.validatePassword(that.password)){
					uni.showToast({
						title:"密码至少包含字母和数字，且长度必须大于5位",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if (that.password != that.repassword) {
					uni.showToast({
						title: "两次密码不一致",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				
				var data = {
					'name': that.name,
					'code': that.code,
					'password': that.password,
					'mail': that.mail,
					'inviteCode': that.inviteCode
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.userRegister(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							localStorage.removeItem('isAgree');  
							if(res.data.msg=="注册成功"){
								var data = {
									name: that.name,
									password: that.password,
								}
								uni.showLoading({
									title: "加载中"
								});
								that.$Net.request({
								
									url: that.$API.userLogin(),
									data: {
										"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "post",
									dataType: 'json',
									success: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: res.data.msg,
											icon: 'none'
										})
										if (res.data.code == 1) {
											//保存用户信息
											localStorage.setItem('userinfo', JSON.stringify(res.data.data));
											localStorage.setItem('token', res.data.data.token);
											that.getCID();
											var timer = setTimeout(function() {
												that.back2();
												clearTimeout('timer')
											}, 1000)
										}
									},
									fail: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络开小差了哦",
											icon: 'none'
										})
										uni.stopPullDownRefresh()
									}
								})
							}else{
								that.back();
							}
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			
			yesBtn(){
				this.usermodelVisible = false
				this.isAgree=='false'
				this.userRegister()
			},
			noBtn(){
				this.usermodelVisible = false
				uni.showToast({
					title: "同意再来注册吧",
					icon: 'none',
				});
			},
			RegSendSms() {
				var that = this;
				
				if (that.phone == "") {
					uni.showToast({
						title: "请输入手机号",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^1[3-9]\d{9}$/.test(that.phone)) {
					uni.showToast({
						title:"请输入合法的手机号",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false;
				}
				
				if (that.verifyLevel > 0) {
					if (that.verifyCode == "") {
						that.modalName = 'kaptcha'
						return false
					}
				}
				var data = {
					'phone': that.phone,
					'verifyCode': that.verifyCode
				}
				
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
				
					url: that.$API.sendSMS(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.verifyCode = "";
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							that.getCode();
						}
				
					},
					fail: function(res) {
						that.modalName = null;
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			RegSendCode() {
				var that = this;
				if (that.mail == "") {
					uni.showToast({
						title: "请输入邮箱",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (that.verifyLevel > 0) {
					if (that.verifyCode == "") {
						that.modalName = 'kaptcha'
						return false
					}
				}

				var data = {
					'mail': that.mail
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.RegSendCode(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						'verifyCode': that.verifyCode
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.verifyCode = "";
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							that.getCode();
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			regConfig() {
				var that = this;
				//获取应用信息
				uni.request({
			
					url: that.$API.getAppinfo(),
					data: {
						"key": that.$API.getAppKey()
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.isEmail = res.data.data.isEmail;
							that.isInvite = res.data.data.isInvite;
							that.loginType = res.data.data.isPhone;
							that.isPhone = res.data.data.isPhone;
							that.verifyLevel = res.data.data.verifyLevel;
							console.log(that.isInvite);
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "获取应用配置信息失败！",
							icon: 'none'
						})
					}
				})
			},
			getCode() {
				this.show = false
				this.timer = setInterval(() => {
					this.times--
					if (this.times === 0) {
						this.show = true
						clearInterval(this.timer);
						this.times = 60;
					}
				}, 1000)
			},
			toAgreement() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/agreement'
				});
			},
		}
	}
</script>

<style>
	
		.t-login {
			position: relative;
			width: 600rpx;
			margin: 0 auto;
			font-size: 28rpx;
			color: #000;
		}
		
		.t-login button {
			font-size: 28rpx;
			background: #5677fc;
			color: #fff;
			height: 90rpx;
			line-height: 90rpx;
			border-radius: 50rpx;
			box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
		}
		
		.t-login input {
			padding: 0 20rpx 0 120rpx;
			height: 90rpx;
			line-height: 90rpx;
			/* margin-bottom: 50rpx; */
			background: #f8f7fc;
			border: 1px solid #e9e9e9;
			font-size: 28rpx;
			border-radius: 50rpx;
		}
		
		.t-login .t-a {
			position: relative;
		}
		
		.t-login .t-a image {
			width: 60rpx;
			height: 40rpx;
			position: absolute;
			left: 40rpx;
			top: 28rpx;
			border-right: 2rpx solid #dedede;
			padding-right: 20rpx;
		}
		
		.t-login .t-b {
			text-align: left;
			font-size: 19px;
			color: #313131;
			padding: 35px 0 0px 0;
			font-weight: bold;
		}
		
		.t-login .t-c {
			position: absolute;
			right: 22rpx;
			top: 22rpx;
			background: #5677fc;
			color: #fff;
			font-size: 24rpx;
			border-radius: 50rpx;
			height: 50rpx;
			line-height: 50rpx;
			padding: 0 25rpx;
		}
		
		.t-login .t-d {
			text-align: center;
			color: #999;
			margin: 80rpx 0;
		}
		
		.t-login .t-e {
			text-align: center;
			width: 250rpx;
			margin: 80rpx auto 0;
		}
		
		.t-login .t-g {
			float: left;
			width: 100%;
		}
		
		.t-login .t-e image {
			width: 50rpx;
			height: 50rpx;
		}
		
		.t-login .t-f {
			text-align: center;
			margin: 200rpx 0 0 0;
			color: #666;
		}
		
		.t-login .t-f text {
			margin-left: 20rpx;
			color: #aaaaaa;
			font-size: 27rpx;
		}
		
		.t-login .uni-input-placeholder {
			color: #000;
		}
		
		.cl {
			zoom: 1;
		}
		.s1{
				
				float: right;
			}
		.cl:after {
			clear: both;
			display: block;
			visibility: hidden;
			height: 0;
			content: '\20';
		}
		.cu-btn{
			border-radius: 50px;
		}
		.cu-form-group{
			border-radius: 50px;
		}
</style>