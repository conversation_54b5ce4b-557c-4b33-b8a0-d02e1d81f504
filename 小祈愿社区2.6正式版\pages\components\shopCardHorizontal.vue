<template>
  <view class="shop-card-horizontal" :class="isDark?'dark':''" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}" @tap="shopInfo(item)">
    <image class="shop-img" :src="item.imgurl" mode="aspectFill"></image>
    <view class="shop-info">
      <view class="shop-title text-cut-2">{{ item.title || '无标题商品' }}</view>
      <view class="shop-price-sell flex-between">
        <text class="shop-price">￥{{ item.price }}</text>
        <text class="shop-sell" v-if="showFields.includes('sellNum')">销量: {{ item.sellNum }}</text>
        <text class="shop-sell" v-if="showFields.includes('num')">库存: {{ item.num }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import darkModeMixin from '@/utils/darkModeMixin.js'
export default {
  mixins: [darkModeMixin],
  name: 'shopCardHorizontal',
  props: {
    item: {
      type: Object,
      required: true
    },
    showFields: {
      type: Array,
      default: () => ['num', 'sellNum']
    }
  },
  methods: {
    shopInfo(data) {
      var that = this;
      uni.navigateTo({
        url: '/pages/shop/shopinfo?sid=' + data.id
      });
    }
  }
}
</script>

<style scoped>
.shop-card-horizontal {
  width: 260rpx;
  background: #fff;
  border-radius: 18rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.06);
  margin-right: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.shop-img {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
  border-radius: 18rpx 18rpx 0 0;
}
.shop-info {
  padding: 12rpx 16rpx 8rpx 16rpx;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.shop-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 4rpx;
}
.text-cut-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.shop-price-sell {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.shop-price {
  color: #e4393c;
  font-size: 26rpx;
  font-weight: bold;
}
.shop-sell {
  color: #888;
  font-size: 22rpx;
}
.shop-num, .shop-shop {
  color: #999;
  font-size: 22rpx;
}
</style> 