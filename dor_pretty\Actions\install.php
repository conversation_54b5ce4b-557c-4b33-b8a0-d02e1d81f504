<?php
initializeActions();

if ($installed == "true") {
    // 已安装过
    send_json(402, "已安装，无需再次安装");
} else {
    $msg = "";
    // ------------未安装过，开始执行安装--------------
    // 检查配置表是否存在
    $sql_1 = "SHOW TABLES LIKE 'Sy_Plugin_xqy_pretty_number_config'";
    $tcr = mysqli_query($connect, $sql_1);

    if (mysqli_num_rows($tcr) == 0) {
        // 创建配置表
        $sql_2 = "CREATE TABLE IF NOT EXISTS `Sy_Plugin_xqy_pretty_number_config` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `min_length` int(11) NOT NULL DEFAULT '8' COMMENT '最小长度',
            `max_length` int(11) NOT NULL DEFAULT '10' COMMENT '最大长度',
            `cooldown_days` int(11) NOT NULL DEFAULT '30' COMMENT '冷却期天数',
            `vip_min_length` int(11) NOT NULL DEFAULT '7' COMMENT 'VIP最小长度',
            `vip_max_length` int(11) NOT NULL DEFAULT '10' COMMENT 'VIP最大长度',
            `require_approval` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要审核',
            `black_gold_price` decimal(10,2) NOT NULL DEFAULT '99.00' COMMENT '黑金靓号价格',
            `change_price` decimal(10,2) NOT NULL DEFAULT '10.00' COMMENT '更换靓号基础价格',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='靓号配置表';";
        
        if (mysqli_query($connect, $sql_2)) {
            $msg .= "创建配置表成功。";
            
            // 插入默认配置
            $sql_3 = "INSERT INTO `Sy_Plugin_xqy_pretty_number_config` 
                    (min_length, max_length, cooldown_days, vip_min_length, vip_max_length, require_approval, black_gold_price) 
                    VALUES (8, 10, 30, 7, 10, 0, 99.00)";
            if (mysqli_query($connect, $sql_3)) {
                $msg .= "配置表数据插入成功。";
            } else {
                $msg .= "配置表数据插入失败：" . mysqli_error($connect) . "。";
            }
        } else {
            $msg .= "创建配置表失败：" . mysqli_error($connect) . "。";
        }
    } else {
        $msg .= "配置表已存在。";
    }

    // 检查靓号表是否存在
    $sql_4 = "SHOW TABLES LIKE 'Sy_Plugin_xqy_pretty_numbers'";
    $tcr = mysqli_query($connect, $sql_4);

    if (mysqli_num_rows($tcr) == 0) {
        // 创建靓号表
        $sql_5 = "CREATE TABLE `Sy_Plugin_xqy_pretty_numbers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `uid` int(11) NOT NULL COMMENT '用户ID',
            `number` varchar(20) NOT NULL COMMENT '靓号',
            `type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '靓号类型',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_uid` (`uid`),
            KEY `idx_number` (`number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if (mysqli_query($connect, $sql_5)) {
            $msg .= "创建靓号表成功。";
        } else {
            $msg .= "创建靓号表失败：" . mysqli_error($connect) . "。";
        }
    } else {
        $msg .= "靓号表已存在。";
    }

    // 检查冷却表是否存在
    $sql_6 = "SHOW TABLES LIKE 'Sy_Plugin_xqy_pretty_number_cooldowns'";
    $tcr = mysqli_query($connect, $sql_6);

    if (mysqli_num_rows($tcr) == 0) {
        // 创建冷却表
        $sql_7 = "CREATE TABLE `Sy_Plugin_xqy_pretty_number_cooldowns` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `uid` int(11) NOT NULL COMMENT '用户ID',
            `number` varchar(20) NOT NULL COMMENT '已注销的靓号',
            `deleted_at` timestamp NOT NULL COMMENT '注销时间',
            `cooldown_end` timestamp NOT NULL COMMENT '冷却期结束时间',
            `type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '靓号类型',
            `created_at` timestamp NOT NULL COMMENT '申请时间',
            PRIMARY KEY (`id`),
            KEY `idx_uid_cooldown` (`uid`, `cooldown_end`),
            KEY `idx_number` (`number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if (mysqli_query($connect, $sql_7)) {
            $msg .= "创建冷却表成功。";
        } else {
            $msg .= "创建冷却表失败：" . mysqli_error($connect) . "。";
        }
    } else {
        $msg .= "冷却表已存在。";
    }

    // 检查价格规则表是否存在
    $sql_8 = "SHOW TABLES LIKE 'Sy_Plugin_xqy_pretty_number_prices'";
    $tcr = mysqli_query($connect, $sql_8);

    if (mysqli_num_rows($tcr) == 0) {
        // 创建价格规则表
        $sql_9 = "CREATE TABLE `Sy_Plugin_xqy_pretty_number_prices` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `pattern` varchar(255) NOT NULL COMMENT '匹配模式',
            `price` decimal(10,2) NOT NULL COMMENT '价格',
            `description` varchar(255) DEFAULT NULL COMMENT '描述',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if (mysqli_query($connect, $sql_9)) {
            $msg .= "创建价格规则表成功。";
            
            // 插入默认价格规则
            $sql_10 = "INSERT INTO `Sy_Plugin_xqy_pretty_number_prices` 
                    (pattern, price, description) VALUES 
                    ('666', 66.00, '666靓号'),
                    ('888', 88.00, '888靓号'),
                    ('999', 99.00, '999靓号'),
                    ('520', 52.00, '520靓号'),
                    ('1314', 13.14, '1314靓号'),
                    ('5201314', 520.00, '5201314靓号')";
            if (mysqli_query($connect, $sql_10)) {
                $msg .= "价格规则表数据插入成功。";
            } else {
                $msg .= "价格规则表数据插入失败：" . mysqli_error($connect) . "。";
            }
        } else {
            $msg .= "创建价格规则表失败：" . mysqli_error($connect) . "。";
        }
    } else {
        $msg .= "价格规则表已存在。";
    }

    // 检查申请记录表是否存在
    $sql_11 = "SHOW TABLES LIKE 'Sy_Plugin_xqy_pretty_number_applications'";
    $tcr = mysqli_query($connect, $sql_11);

    if (mysqli_num_rows($tcr) == 0) {
        // 创建申请记录表
        $sql_12 = "CREATE TABLE `Sy_Plugin_xqy_pretty_number_applications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `uid` int(11) NOT NULL COMMENT '用户ID',
            `number` varchar(20) NOT NULL COMMENT '申请的靓号',
            `type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '靓号类型',
            `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0待审核,1通过,2拒绝',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
            `updated_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
            `admin_uid` int(11) DEFAULT NULL COMMENT '审核管理员ID',
            `remark` text DEFAULT NULL COMMENT '审核备注',
            PRIMARY KEY (`id`),
            KEY `idx_uid` (`uid`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        if (mysqli_query($connect, $sql_12)) {
            $msg .= "创建申请记录表成功。";
        } else {
            $msg .= "创建申请记录表失败：" . mysqli_error($connect) . "。";
        }
    } else {
        $msg .= "申请记录表已存在。";
    }

    
     // -------------------安装完成，以下勿动---------------------
    
    //【勿动】 将插件安装状态设置为已安装
    $currentDir = __DIR__;
    $configFile = str_replace('Actions', '', $currentDir) . 'config.ini';
    if (file_exists($configFile)) {
        $configData = parse_ini_file($configFile, true);
        $configData['plugin']['installed'] = "true";
        // 写回配置文件
        $newConfig = "";
        foreach ($configData as $section => $values) {
            $newConfig .= "[$section]\n";
            foreach ($values as $key => $value) {
                $newConfig .= "$key = \"$value\"\n";
            }
        }

        if (file_put_contents($configFile, $newConfig) !== false) {
            $msg .= "更新配置文件成功。";
        } else {
            $msg .= "更新配置文件失败。";
        }
    } else {
        $msg .= "配置文件不存在。";
    }
    $data['log'] = $msg;
    send_json(200, "插件安装完成", $data);
}