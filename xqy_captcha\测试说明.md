# xqy_captcha 行为验证插件测试说明

## 插件概述

xqy_captcha 是一个为 StarPro 社区系统开发的行为验证插件，支持第三方验证服务（极验、Cloudflare Turnstile、Google reCAPTCHA）作为传统图片验证码的可选替代方案。

## 功能特性

1. **完全兼容性**：插件启用时使用行为验证，禁用时自动回退到原有图片验证码
2. **多服务支持**：支持极验、Cloudflare、reCAPTCHA 三种主流验证服务
3. **前端集成**：已集成到登录、注册、忘记密码页面的验证流程
4. **后台管理**：提供完整的配置管理和状态监控界面

## 测试步骤

### 1. 插件安装测试

1. 将 `xqy_captcha` 文件夹上传到插件目录
2. 访问后台插件管理页面
3. 找到 "行为验证插件" 并点击安装
4. 检查数据库是否创建了 `Xqy_Plugin_captcha_config` 表
5. 验证插件状态显示为 "已安装"

### 2. 配置管理测试

1. 进入插件配置页面
2. 测试各种验证服务的配置：
   - 极验：输入 ID、Key、Secret
   - Cloudflare：输入 Site Key、Secret Key
   - reCAPTCHA：输入 Site Key、Secret Key
3. 保存配置并验证数据是否正确存储
4. 测试启用/禁用功能

### 3. 前端集成测试

#### 登录页面测试
1. 访问登录页面
2. 检查是否正确检测到插件状态
3. 如果插件启用，验证是否显示行为验证界面
4. 如果插件禁用，验证是否显示传统图片验证码
5. 测试验证成功和失败的处理逻辑

#### 注册页面测试
1. 访问注册页面
2. 测试邮箱验证码发送的验证流程
3. 测试短信验证码发送的验证流程
4. 验证行为验证与图片验证码的切换

#### 忘记密码页面测试
1. 访问忘记密码页面
2. 测试邮箱验证码发送功能
3. 测试短信验证码发送功能
4. 验证验证方式的正确切换

### 4. 兼容性测试

1. **插件禁用状态**：
   - 禁用插件后，所有页面应正常使用图片验证码
   - 验证原有功能不受影响

2. **网络异常处理**：
   - 模拟网络错误，验证是否正确回退到图片验证码
   - 检查错误提示是否友好

3. **配置错误处理**：
   - 测试错误的配置参数
   - 验证错误处理和提示机制

### 5. 性能测试

1. 测试插件对页面加载速度的影响
2. 验证验证服务的响应时间
3. 检查内存和资源使用情况

## 当前实现状态

### 已完成功能
- ✅ 插件基础结构
- ✅ 安装/卸载功能
- ✅ 配置管理界面
- ✅ 验证服务集成接口
- ✅ 前端页面集成（login.vue、register.vue、foget.vue）

### 待完善功能
- ⏳ 第三方验证服务的实际SDK集成（当前为占位符实现）
- ⏳ 验证服务的详细错误处理
- ⏳ 更多验证场景的支持

## 注意事项

1. **第三方服务配置**：需要在各验证服务官网申请相应的密钥
2. **HTTPS要求**：某些验证服务要求在HTTPS环境下使用
3. **域名配置**：需要在验证服务后台配置正确的域名
4. **备用方案**：始终保持图片验证码作为备用验证方式

## 故障排除

1. **插件无法安装**：检查文件权限和数据库连接
2. **验证失败**：检查第三方服务配置和网络连接
3. **页面报错**：检查浏览器控制台错误信息
4. **配置丢失**：检查数据库表和数据完整性

## 技术支持

如遇到问题，请检查：
1. 服务器错误日志
2. 浏览器控制台错误
3. 数据库表结构和数据
4. 第三方服务的配置和状态
