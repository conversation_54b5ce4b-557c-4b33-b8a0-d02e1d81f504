# prettyNumberItem.vue 文件分析

## 概述

`prettyNumberItem.vue` 组件用于显示指定用户的靓号（Pretty Number）。它首先检查靓号插件 (`dor_pretty`) 是否启用，若启用且提供了用户ID (`uid`)，则会请求后端获取该用户的靓号信息（号码和类型），并将其展示出来。组件还提供了点击复制靓号的功能。

## 文件信息
- **文件路径**：`APP前端部分/pages/components/prettyNumberItem.vue.md`
- **主要功能**：根据用户ID获取并展示用户的靓号，支持点击复制。

## 主要组成部分分析

### 1. Props
   - **`uid`**: 
     - 类型: `[Number, String]`
     - 是否必需: `true`
     - 说明: 要查询靓号的用户ID。
   - **`showLabel`**: 
     - 类型: `Boolean`
     - 默认值: `true`
     - 说明: (已定义但模板中未使用，原意图可能是控制是否显示"靓号"标签)。

### 2. 模板 (`<template>`)
   - **根元素 (`pretty-number-item`)**: 
     - 条件渲染: `v-if="isPluginEnabled && prettyNumber"`，即只有当靓号插件启用且成功获取到用户靓号时才显示。
   - **靓号容器 (`pretty-number-container`)**: 
     - 点击事件: `@click="copyNumber"`，复制靓号。
     - **靓号文本 (`pretty-number-value`)**: 
       - 应用动态类名 `:class="numberType"` (如 `normal`, `black_gold`)，用于区分不同类型的靓号样式。
       - **标签 (`number-label`)**: 固定显示文字"靓号"。
       - **号码 (`number-text`)**: 显示获取到的 `prettyNumber`。
     - **复制图标 (`copy-icon`)**: 显示 `tn-icon-copy` 图标。

### 3. 脚本 (`<script>`)
   - **`name`**: "prettyNumberItem"。
   - **`props`**: 定义了 `uid` 和 `showLabel`。
   - **`data`**: 
     - `prettyNumber`: `String | null` - 存储获取到的靓号号码。
     - `numberType`: `String` - 存储靓号类型 (如 `normal`, `black_gold`)，用于应用样式。
     - `isLoading`: `Boolean` - 请求进行中标记。
     - `isPluginEnabled`: `Boolean` - 标记靓号插件 (`dor_pretty`) 是否启用。
   - **`created()`**: 
     - 从 `uni.getStorageSync('getPlugins')` 检查 `dor_pretty` 插件是否启用，并设置 `isPluginEnabled`。
     - 如果插件启用且 `uid` 存在，调用 `getPrettyNumber()`。
   - **`watch`**: 
     - 监听 `uid` 变化，当 `uid` 变化且有值，并且插件启用时，调用 `getPrettyNumber()` 重新加载。
     - `immediate: true` 确保初始化时加载。
   - **`methods`**: 
     - **`getPrettyNumber()`**: 
       - 核心逻辑：获取用户靓号。
       - 包含加载中 (`isLoading`) 和插件启用 (`isPluginEnabled`) 及 `uid` 存在性检查。
       - 调用 `uni.request` 请求 `$API.PluginLoad('dor_pretty')`。
       - 请求参数: `{ plugin: 'dor_pretty', action: 'get_user_number', uid: this.uid }`。
       - **成功回调**: 
         - 如果 `res.data.code === 200` 且数据有效，则将 `res.data.data.number` 存入 `prettyNumber`，`res.data.data.type` 存入 `numberType`。
         - 触发 `number-loaded` 事件，传递获取到的靓号数据。
         - 如果 `res.data.code === 404` (用户无靓号)，设置 `prettyNumber = null`，触发 `number-loaded` 事件并传递 `null`。
         - 其他错误，打印错误信息，设置 `prettyNumber = null`，触发 `number-loaded` 事件并传递 `null`。
       - **失败回调**: 处理同上。
       - **完成回调**: 设置 `isLoading = false`。
     - **`copyNumber()`**: 
       - 如果 `prettyNumber` 存在，调用 `uni.setClipboardData` 复制靓号到剪贴板，并显示成功提示。

### 4. Emitted Events
   - **`number-loaded`**: 当获取靓号的请求完成（无论成功失败或有无靓号）时触发。参数为获取到的靓号数据对象 (`{ number: '...', type: '...' }`) 或 `null`。

### 5. 样式 (`<style lang="scss">`)
   - 定义了 `.pretty-number-item` 及其内部元素的样式。
   - 包含针对不同 `numberType` (`normal`, `black_gold`) 的特定样式，例如背景渐变、边框、阴影、文字颜色、动画效果等，用以区分普通靓号和特殊靓号（如黑金靓号）。
   - 包含一个 `sweep` 动画，用于 `black_gold` 类型的靓号文本，产生扫光效果。

## 总结与注意事项

-   `prettyNumberItem.vue` 是一个与靓号插件 (`dor_pretty`) 配套的前端展示组件。
-   它根据用户ID异步获取靓号信息，并根据靓号类型应用不同的视觉样式。
-   提供了基础的点击复制功能。
-   `showLabel` prop 已定义但未在模板中使用。
-   组件仅在插件启用且用户拥有靓号时才渲染，否则不显示任何内容。

## 后续分析建议

-   **`dor_pretty` 插件API**: 详细了解 `$API.PluginLoad('dor_pretty')` 接口的请求和响应规范，特别是 `numberType` 可能的值及其含义。
-   **父组件交互**: 查看使用 `prettyNumberItem` 的父组件如何响应 `number-loaded` 事件。
-   **样式定制**: 如果需要更多靓号类型或不同的视觉效果，需要扩展 `<style>` 部分的样式规则。
-   **未使用的 Prop**: 确认 `showLabel` 是否不再需要，或补充其在模板中的逻辑。 