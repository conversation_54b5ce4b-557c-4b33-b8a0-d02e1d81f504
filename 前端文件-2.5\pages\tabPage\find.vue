<template>
	<view :class="$store.state.AppStyle" v-if="tabCurTab==isLogin" style="background-color: #f6f6f6;height: 100%;">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}"
				v-if="tabCurTab==isLogin">
				<view class="user-rz">
					<tn-avatar style="margin-left: 16upx;" badgeBgColor="#ff4f4f" :src="avatarurl" :badge="noticeSumof"
						:badgeText="noticeSum" v-if="token!=''&&identifyCompany==1&&tabCurTab==isLogin"
						@tap="goMsg()"></tn-avatar>
					<tn-avatar style="margin-left: 16upx;font-size: 20upx;" backgroundColor="#f5f5f5" text="登录" v-else
						@tap="goUserInfo()"></tn-avatar>
					<image class="user-rz-icon-htop" :src="rzImg"
						v-if="token!=''&&identifyCompany==1&&tabCurTab==isLogin" mode="aspectFill"></image>
				</view>
				<view class="forumHome-header">

					<!-- 发现 -->
					<block v-if="Bannerswitch==0&&Hyperlinks==0&&Gallery==0&&Findtop==0&&Code==0">
						<text class="square-box" :class="squareid==1?'cur':''" @tap="setSquare(1)">动态</text>
					</block>
					<block v-else>
						<text class="square-box" :class="squareid==1?'cur':''" @tap="setSquare(1)">发现</text>
					</block>

					<!-- 圈子 -->
					<block v-if="tzof==1">
						<text class="square-box" :class="squareid==0?'cur':''" @tap="setSquare(0)">圈子</text>
					</block>
					<!-- #ifdef APP-PLUS || H5 -->
					<!-- 应用 -->
					<block v-if="sy_appbox">
						<text class="square-box" :class="squareid==2?'cur':''" @tap="setSquare(2)">应用</text>
					</block>
					<!-- #endif -->
					<!-- 文章 -->
					<block v-if="wzof==1">
						<text class="square-box" :class="squareid==3?'cur':''" @tap="setSquare(3)">文章</text>
					</block>
				</view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view class="action" @tap="toSearch" v-if="squareid!=2">
					<text class="cuIcon-search"></text>
				</view>
				<view class="action" @tap="toSet" v-if="squareid==2">
					<text class="tn-icon-install" style="font-size: 40upx;"></text>
				</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="action">
				</view>
				<!-- #endif -->
			</view>
		</view>
		<view :style="[{padding:(NavBar + 10) +  'px 10px 0px 10px'}]" style="background: #ffffff;"></view>
		<block v-if="squareid==0&&tabCurTab==isLogin">

			<forumIndex :sectionList="sectionList" :swiperList="swiperList" :radiusStyle="radiusStyle"
				:radiusBoxStyle="radiusBoxStyle" :fatherTitle="fatherTitle" :swiperStyle="swiperStyle"
				:swiperType="swiperType" :recommendSectionList="recommendSectionList" :recommendOf="recommendOf"
				:kuaijie="kuaijie">
			</forumIndex>
			<view class="loading" v-if="isLoading==0">
				<view class="loading-main">
					<image src="../../static/loading.gif"></image>
				</view>
			</view>
			<!--加载遮罩结束-->
			<view style="width: 100%; height: 100upx;background: #ffffff;"></view>
		</block>
		<block v-if="squareid==3&&tabCurTab==isLogin">
			<metas :topic="metaList">
			</metas>
			<view class="loading" v-if="isLoading==0">
				<view class="loading-main">
					<image src="../../static/loading.gif"></image>
				</view>
			</view>
			<!--加载遮罩结束-->
			<view style="width: 100%; height: 100upx;background: #ffffff;"></view>
		</block>
		<block v-if="squareid==1&&tabCurTab==isLogin">
			<view :class="{'data-box-2-banner': homeStyle==2}">
				<tn-swiper v-if="Bannerswitch==1&&adimage_sl!=0" :list="swiperList2" :effect3d="swiperStyle"
					:class="swiperStyle?'':'uni-swiper-slides-1'" style="padding: 0;" @click="swiperclick"
					:backgroundColor="swiperBgcolor" :height="swiperHeight" :effect3dPreviousSpacing="80"></tn-swiper>
			</view>


			<view class="data-box" :class="{'data-box-2': homeStyle==2}">
				<view class="bg-white" style="border-radius: 20upx;" v-if="Hyperlinks==1&&tabCurTab==isLogin">
					<tn-scroll-list :indicator="false">
						<view class="tn-flex">
							<view class="tn-flex-1 tn-padding-sm tn-margin-xs tn-radius"
								v-for="(item, index) in iconimg" :key="index" @tap="toLink(item.link,item.lgof)">
								<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center">
									<view
										class="icon5__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-shadow-blur"
										:style="'background-image: url(' + item.url + ')'"
										style="background-size:100% 100%;">
									</view>
									<view class="tn-color-black tn-text-center">
										<text class="tn-text-ellipsis">{{item.name}}</text>
									</view>
								</view>
							</view>
						</view>
					</tn-scroll-list>

				</view>
			</view>
			<view class="data-box" :class="{'data-box-2': homeStyle==2}" v-if="sy_gpt&&tabCurTab==isLogin">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text> AI大模型
					</view>
				</view>
				<view class="find-gpt">
					<view class="find-gpt-mian" @tap="goPage('/pages/plugins/sy_gpt/home')">
						<view class="find-gpt-ico">
							<image src="@/static/gpt.png" mode="widthFix"></image>
						</view>
						<view class="find-gpt-intro">
							<view class="find-gpt-intro-title">体验大模型，开启新时代</view>
							<text class="cu-btn sm bg-black">即刻出发</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 小祈愿AI显示 -->
			<view class="data-box" :class="{'data-box-2': homeStyle==2}" v-if="xqy_gpt&&tabCurTab==isLogin">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text> AI大模型
					</view>
				</view>
				<view class="find-gpt">
					<view class="find-gpt-mian" @tap="goPage('/pages/plugins/xqy_gpt/home')">
						<view class="find-gpt-ico">
							<image src="@/static/gpt.png" mode="widthFix"></image>
						</view>
						<view class="find-gpt-intro">
							<view class="find-gpt-intro-title">体验大模型，开启新时代</view>
							<text class="cu-btn sm bg-black">即刻出发</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 小祈愿短视频显示 -->
			<view class="data-box" :class="{'data-box-2': homeStyle==2}" v-if="xqy_video&&tabCurTab==isLogin">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text> 短视频
					</view>
				</view>
				<view class="find-gpt">
					<view class="find-gpt-mian" @tap="goPage('/pages/plugins/xqy_video/home')">
						<view class="find-gpt-ico">
							<text class="cuIcon-video" style="font-size: 120upx; color: #000000;"></text>
						</view>
						<view class="find-gpt-intro">
							<view class="find-gpt-intro-title">精彩短视频，分享生活点滴</view>
							<text class="cu-btn sm bg-black">即刻出发</text>
						</view>
					</view>
				</view>
			</view>
			<view class="data-box" :class="{'data-box-2': homeStyle==2}" v-if="Gallery==1&&tabCurTab==isLogin">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text> 活跃用户
					</view>
				</view>
				<view class="cu-list menu-avatar userList" style="padding-bottom: 20upx;">
					<view class="cu-item" v-for="(item,index) in userList" :key="index" @tap="toUserContents(item)">

						<view class="cu-avatar round lg" :style="item.style">
						</view>
						<view class="content">
							<view class="text-grey">
								<block v-if="item.screenName">{{item.screenName}}</block>
								<block v-else>{{item.name}}</block>
							</view>
							<view class="text-gray text-sm flex">
								<view class="text-cut">
									最近活跃:
									<text class="text-blue" v-if="item.posttime>0">{{formatDate(item.posttime)}}</text>
									<text class="text-blue" v-else>暂未活跃</text>
								</view>
							</view>
						</view>
						<view class="action goUserIndex">
							<view class="cu-btn bg-gradual-orange"
								style="font-size: 26upx;height: 55upx;border-radius: 100upx;">主页</view>

						</view>
					</view>
				</view>

			</view>
			<!--  #ifdef H5 || APP-PLUS -->
			<view class="data-box" :class="{'data-box-2': homeStyle==2}" v-if="shopof==1&&tabCurTab==isLogin">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text> 热销商品
					</view>
					<view class="action more" @tap="goPage('/pages/shop/shop')">
						<text>进入商城</text><text class="cuIcon-right"></text>
					</view>
				</view>
				<view class="tn-flex tn-flex-wrap">

					<block v-for="(item,index) in shopList" :key="index">
						<shopItem :item="item"></shopItem>
					</block>
				</view>
			</view>
			<!--  #endif -->
			<view class="data-box" :class="{'data-box-2': homeStyle==2}" v-if="wzof==1&&Findtop==1&&tabCurTab==isLogin">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text> 推荐文章
					</view>
					<view class="action more" @tap="toRecommend">
						<text>全部</text><text class="cuIcon-right"></text>
					</view>
				</view>
				<view class="cu-card article no-card">
					<block v-for="(item,index) in recommendList" :key="index" v-if="actStyle==1&&tabCurTab==isLogin">
						<articleItemA :item="item"></articleItemA>
					</block>
					<block v-for="(item,index) in recommendList" :key="index" v-if="actStyle==2&&tabCurTab==isLogin">
						<articleItemB :item="item"></articleItemB>
					</block>
				</view>
			</view>
			<view class="data-box" :class="{'data-box-2': homeStyle==2}" v-if="Code==1&&tabCurTab==isLogin">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text> 标签云
					</view>
					<view class="action more" @tap="toAlltag">
						<text>全部</text><text class="cuIcon-right"></text>
					</view>
				</view>
				<view class="tags">

					<text class="tags-box" v-for="(item,index) in tagList" :key="index"
						@tap='toCategoryContents("#"+item.name+"#",item.mid)'>
						# {{item.name}}
					</text>

				</view>
			</view>

			<view class="data-box" :class="{'data-box-2': homeStyle==2}">
				<view class="square-post">
					<view class="square-post-header">
						<view class="square-user" @tap="goUserInfo()">
							<tn-avatar :src="avatarurl" v-if="token!=''"></tn-avatar>
							<tn-avatar style="font-size: 20upx;" backgroundColor="#f5f5f5" text="登录" v-else
								@tap="goUserInfo()"></tn-avatar>
						</view>
						<view class="square-text" @tap="postSpace(0)">
							分享一下生活吧！
						</view>
					</view>
					<view class="square-post-btn grid col-2">
						<view class="square-post-btn-box" @tap="postSpace(0)">
							<text class="cuIcon-pic"></text>图文动态
						</view>
						<view class="square-post-btn-box" @tap="postSpace(4)">
							<text class="cuIcon-record"></text>视频动态
						</view>
					</view>
				</view>
			</view>
			<!-- <view class="square-data-type">
				<text :class="spaceDataType==0?'cur':''" @tap="setSpaceDataType(0)">只看关注</text>
				<text :class="spaceDataType==1?'cur':''" @tap="setSpaceDataType(1)">点赞最多</text>
				<text :class="spaceDataType==2?'cur':''" @tap="setSpaceDataType(2)">实时最新</text>
			</view> -->
			<block v-if="spaceList.length==0">

				<block v-if="spaceLoad">
					<view class="no-data">
						<text class="cuIcon-text"></text>

						暂时没有动态哦！
						<view class="text-center margin-top-sm">
							<text class="cu-btn bg-gradual-orange radius" @tap="postSpace(0)">立即发布</text>

						</view>
					</view>
				</block>
				<block v-else>
					<view class="dataLoad" v-if="!dataLoad">
						<image src="../../static/loading.gif"></image>
					</view>
				</block>
			</block>

			<spaceItem :spaceList="spaceList" :curIMG="curIMG"></spaceItem>
			<view class="load-more" @tap="loadMore" v-if="dataLoad&&chatList.length>0">
				<text>{{moreText}}</text>
			</view>
			<view style="width: 100%; height: 100upx;background: #f6f6f6;"></view>
			<!--加载遮罩-->
			<view class="loading" v-if="isLoading==0">
				<view class="loading-main">
					<image src="../../static/loading.gif"></image>
				</view>
			</view>
			<!--加载遮罩结束-->
		</block>
		<block v-if="squareid==2&&tabCurTab==isLogin">
			<!-- #ifdef APP-PLUS || H5 -->

			<block v-if="appLoad">
				<!-- 应用列表部分 -->
				<view class="u-wrap" :style="{ height: `calc(100vh - ${NavBar+10}px)` }">
					<view class="u-menu-wrap">
						<!-- 左侧菜单保持不变 -->
						<scroll-view scroll-y scroll-with-animation class="u-tab-view menu-scroll-view"
							:scroll-top="scrollTop">
							<view v-for="(item,index) in tabbar" :key="index" class="u-tab-item"
								:class="[current==index ? 'u-tab-item-active' : '']" :data-current="index"
								@tap.stop="swichMenu(index)">
								<text class="u-line-1">{{item.name}}</text>
							</view>
							<view style="width: 100%; height: 120upx;background: #ffffff00;"></view>
						</scroll-view>

						<!-- 右侧内容区只需要一个scroll-view -->
						<scroll-view scroll-y class="right-box" @scrolltolower="handleScrollToLower">
							<view class="page-view">
								<view class="class-item">
									<view class="filter-bar">
										<!-- 排序选择 -->
										<view class="filter-item" @tap="showOrderPicker">
											<text>{{orderText}}</text>
											<text class="cuIcon-unfold"></text>
										</view>

										<!-- 系统筛选 -->
										<view class="filter-item" @tap="showSystemFilter">
											<text>{{systemText}}</text>
											<text class="cuIcon-unfold"></text>
										</view>

										<!-- 类型筛选 -->
										<view class="filter-item" @tap="showTypeFilter">
											<text>{{typeText}}</text>
											<text class="cuIcon-unfold"></text>
										</view>
									</view>

									<!-- 添加加载动画 -->
									<view class="loading-container" v-if="loading">
										<u-loading mode="circle" size="36"></u-loading>
									</view>

									<view class="item-container" v-else>
										<block v-if="appList.length>0">
											<view class="app-box" style="padding: 10rpx 10rpx 0 10rpx;"
												v-for="(item, index) in appList" :key="index">
												<view class="app-box-body" @tap="toAppInfo(item.id)">
													<view class="app-box-logo">
														<u-image :src="item.logo" width="110rpx" height="110rpx"
															mode="aspectFill" :lazy-load="true" :fade="true"
															duration="450" border-radius="28rpx">
															<u-loading slot="loading"></u-loading>
														</u-image>
													</view>
													<view class="app-box-content">
														<view class="app-box-title text-cut">{{item.name}}</view>
														<view class="app-box-info">
															<text :style="{color: item.tagInfo.color}"
																:class="item.score>=3?'tn-icon-star-fill':'tn-icon-star'"></text>
															<text
																:style="{color: item.tagInfo.color}">{{item.score}}</text>
															<text>{{item.size}}</text>

														</view>
														<view class="app-box-tags">
															<text class="app-tag"
																:style="{backgroundColor: item.tagInfo.color}">{{item.tagInfo.text}}</text>
															<text>v{{item.version}}</text>
															<text
																:class="item.system=='ios'?'tn-icon-iphone':''"></text>
														</view>
													</view>
												</view>
												<view class="app-box-down" @tap="toAppInfo(item.id)">下载</view>
											</view>
										</block>
										<block v-else>
											<view class="margin-top-sm">
												<u-empty text="暂无数据" mode="data" icon-size="100"
													font-size="24"></u-empty>
											</view>

										</block>
										<!-- 底部加载更多 -->
										<view class="loading-more" v-if="loadStatus !== 'nomore'">
											<u-loadmore :status="loadStatus" :icon-type="'circle'"
												:load-text="loadMoreText" @loadmore="handleScrollToLower" />
										</view>
									</view>
								</view>
							</view>



							<view style="width: 100%; height: 120upx;background: #ffffff00;"></view>
						</scroll-view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="dataLoad" v-if="!dataLoad">
					<image src="../../static/loading.gif"></image>
				</view>
			</block>


			<!-- 修改排序选择器 -->
			<u-picker v-model="showOrder" :show="showOrder" :columns="[orderOptions]" @confirm="confirmOrder"
				@cancel="cancelOrder" mode="selector" :range="orderOptions" range-key="text"></u-picker>

			<!-- 系统筛选弹窗 -->
			<u-popup v-model="showSystem" mode="bottom" :mask-close-able="true" :safe-area-inset-bottom="true"
				@close="showSystem = false">
				<view class="filter-popup">
					<view class="filter-title">选择系统</view>
					<view class="filter-options">
						<view class="filter-option" v-for="(sys, index) in systemOptions" :key="index"
							:class="{'active': selectedSystem === sys.value}" @tap="selectSystem(sys.value)">
							{{sys.label}}
						</view>
					</view>
					<view class="filter-buttons">
						<view class="btn-reset" @tap="resetSystem">重置</view>
						<view class="btn-confirm" @tap="confirmSystem">确定</view>
					</view>
				</view>
			</u-popup>

			<!-- 类型筛选弹窗 -->
			<u-popup v-model="showType" mode="bottom" :mask-close-able="true" :safe-area-inset-bottom="true"
				@close="showType = false">
				<view class="filter-popup">
					<view class="filter-title">选择类型</view>
					<view class="filter-options">
						<view class="filter-option" v-for="(type, index) in typeOptions" :key="index"
							:class="{'active': selectedType === type.value}" @tap="selectType(type.value)">
							{{type.label}}
						</view>
					</view>
					<view class="filter-buttons">
						<view class="btn-reset" @tap="resetType">重置</view>
						<view class="btn-confirm" @tap="confirmType">确定</view>
					</view>
				</view>
			</u-popup>
			<!-- #endif -->
		</block>
		<!--加载遮罩结束-->
		<view class="full-noLogin" v-if="noLogin">
			<view class="full-noLogin-main">
				<view class="full-noLogin-text">
					您需要登录后才可以查看内容哦！
				</view>
				<view class="full-noLogin-btn">
					<view class="cu-btn bg-blue" @tap="goLogin()">
						立即登录
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import waves from '@/components/xxley-waves/waves.vue';

	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		props: {
			curPage: {
				type: Number,
				default: 0
			}
		},
		name: "find",
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				tabbar: [],
				scrollTop: 0, //tab标题的滚动条位置
				current: 0, // 预设当前项的值
				menuHeight: 0, // 左边菜单的高度
				menuItemHeight: 0, // 左边菜单item的高度
				sectionList: [],
				page: 1,
				swiperList: [],
				submitStatus5: false,
				submitStatus3: false,
				submitStatus2: false,
				submitStatus1: false,
				squareid: 0,
				isLogin: 1,
				metaList: [],
				adimage_sl: 0,
				tagList: [],
				recommendList: [],
				shopList: [],
				rzImg: this.$API.SPRz(),
				Rz: false,
				isvip: 0,
				userList: [],
				tabCurTab: 1,
				spaceDataType: 2,
				uid: 0,
				homeStyle: 0,
				swiperHeight: this.$API.SPfindSwiperHeight(),
				swiperBgcolor: "",
				actStyle: 0,
				Hyperlinks: 0,
				Gallery: 0,
				Findtop: 0,
				Code: 0,
				isLoading: 0,
				kuaijie: 2,
				radiusBoxStyle: 0,
				radiusStyle: 0,
				fatherTitle: 2,
				swiperStyle: false,
				swiperType: 0,
				recommendOf: 0,
				ads: "",
				wzof: 0,
				tzof: 0,
				shopof: 0,
				Bannerswitch: 0,
				searchText: "",
				iconimg: [],
				avatarurl: "../../static/user/avatar.png",
				dataLoad: false,
				spaceLoad: false,
				appLoad: false,
				recommendSectionList: [],
				swiperList2: [],
				chatList: [],
				oldChatList: [],
				identifyCompany: 0,
				curIMG: "",
				bannerAds: [],
				bannerAdsInfo: null,
				noticeSumof: false,
				noticeSum: 0,
				spaceList: [],
				token: "",
				isGetChat: null,
				userInfo: null,
				sy_gpt: false,
				noLogin: false,

				// 应用相关
				appList: [], // 应用列表
				limit: 15,
				loading: false,
				finished: false,
				tagMap: {
					1: {
						text: '搬运',
						color: '#7c72ff'
					},
					2: {
						text: '原创',
						color: '#19be6b'
					},
					3: {
						text: '金标',
						color: '#ff6600'
					},
					4: {
						text: '官方',
						color: '#2979ff'
					}
				},

				// 筛选相关
				showOrder: false,
				orderOptions: [{
						text: '最新投稿',
						value: 'created'
					},
					{
						text: '好评如潮',
						value: 'score'
					},
					{
						text: '讨论火热',
						value: 'commentsNum'
					},
					{
						text: '随便看看',
						value: 'random'
					}
				],
				selectedOrder: 'created',
				orderText: '最新投稿',

				// 系统筛选
				showSystem: false,
				systemOptions: [{
						label: '全部',
						value: ''
					},
					{
						label: 'Android',
						value: 'android'
					},
					{
						label: 'iOS',
						value: 'ios'
					}
				],
				selectedSystem: '',
				systemText: '系统',

				// 类型筛选
				showType: false,
				typeOptions: [{
						label: '全部',
						value: ''
					},
					{
						label: '搬运',
						value: '1'
					},
					{
						label: '原创',
						value: '2'
					},
					{
						label: '金标',
						value: '3'
					},
					{
						label: '官方',
						value: '4'
					}
				],
				selectedType: '',
				typeText: '类型',
				sy_appbox: false,

				// 修改加载状态的处理
				loadStatus: 'loadmore', // loadmore, loading, nomore
				loadMoreText: {
					loadmore: '上拉加载更多',
					loading: '正在加载...',
					nomore: '没有更多了'
				},
				xqy_video: false,
				xqy_gpt: false,
			}
		},
		onReady() {
			this.getMenuItemTop()
		},
		mounted() {
			var that = this;
			uni.$on('onShow', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				if (localStorage.getItem('getPlugins')) {
					var cachedPlugins = localStorage.getItem('getPlugins');
					if (cachedPlugins) {
						const pluginList = JSON.parse(cachedPlugins);
						that.sy_gpt = pluginList.includes('sy_gpt');
						that.xqy_video = pluginList.includes('xqy_video');
						that.xqy_gpt = pluginList.includes('xqy_gpt');
						// #ifdef APP-PLUS || H5
						that.sy_appbox = pluginList.includes('sy_appbox');
						// #endif
					}
				}
				if (that.homeStyle == 1) {
					that.swiperBgcolor = "#ffffff"
				} else {
					that.swiperBgcolor = "#f4f4f4"
				}
				console.log("触发Tab-" + data + "||页面下标" + that.curPage);
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.isvip = userInfo.isvip;
				}
				if (localStorage.getItem('token')) {

					that.token = localStorage.getItem('token');
				}
				uni.$emit('tOnLazyLoadReachBottom');
				that.allCache();
				if (localStorage.getItem('userinfo')) {
					that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.avatarurl = that.userInfo.avatar
					that.uid = that.userInfo.uid
				}
				if (localStorage.getItem('token')) {

					that.token = localStorage.getItem('token');
				}
				if (localStorage.getItem('identifyCompany')) {
					that.identifyCompany = localStorage.getItem('identifyCompany');
				}
				that.userStatus();
				that.unreadNum();

				if (that.noticeSum > 0 && that.noticeSum <= 99) {
					that.noticeSumof = true;
				} else if (that.noticeSum > 99) {
					that.noticeSumof = true;
					noticeSum = "99+"
				}
				if (localStorage.getItem('isinfoback')) {
					localStorage.removeItem('isinfoback');
				} else {
					that.loadingFun();
				}
			});

			uni.$on('onReachBottom', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				console.log("触发触底刷新");
				that.loadMore();

			});

			uni.$on('onPullDownRefresh', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				console.log("触发下拉刷新");
				that.getSet();
				that.loadingFun();
				that.userStatus();
				that.unreadNum();
			});

			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif


		},
		beforeDestroy() {
			var that = this;
			uni.$off('onReachBottom')
			uni.$off('onShow')
			uni.$off('onPullDownRefresh')

			clearInterval(that.isGetChat);
			that.isGetChat = null
		},
		created() {
			var that = this;

			if (that.homeStyle == 1) {
				that.swiperBgcolor = "#ffffff"
			} else {
				that.swiperBgcolor = "#f4f4f4"
			}
			that.getSet();
			that.getGonggao();
			that.getIconimg();
			that.getadimg();
			that.getSwiperList();
			if (that.isvip == 0) {
				that.getAdsCache();
			}
		},
		methods: {
			swiperclick(index) {
				//console.log('Clicked on index:', index);
				const data = this.swiperList2[index];
				this.goAds2(data.zt)

			},

			goAppInfo(item) {
				var that = this;
				uni.navigateTo({
					url: '/pages/plugins/sy_appbox/info'
				});
			},
			toLink(text, lgof) {
				var that = this;
				if (lgof == "true") {
					if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
						uni.showToast({
							title: "请先登录哦",
							icon: 'none'
						})
						return false;
					}
				}
				uni.navigateTo({
					url: text
				});
			},
			getSet() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.kuaijie = res.data.kuaijie;
						that.radiusBoxStyle = res.data.radiusBoxStyle;
						that.radiusStyle = res.data.radiusStyle;
						that.fatherTitle = res.data.fatherTitle;
						that.swiperStyle = res.data.swiperStyle2;
						that.tabCurTab = res.data.swiperinfo;
						that.swiperType = res.data.swiperType;
						that.recommendOf = res.data.recommendOf;
						that.actStyle = res.data.topStyle;
						that.homeStyle = res.data.homeStyle;
						that.wzof = res.data.wzof;
						that.tzof = res.data.tzof;
						that.shopof = res.data.shopof;
						localStorage.setItem('tabCurTab', that.tabCurTab);
					},
				})

			},
			getGonggao() {
				var that = this;
				uni.request({
					url: that.$API.SPgonggao(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.Hyperlinks = res.data.Hyperlinks;
						that.Gallery = res.data.Gallery;
						that.curIMG = res.data.videoimg;
						that.Findtop = res.data.Findtop;
						that.Bannerswitch = res.data.Bannerswitch;
						that.Code = res.data.Code;
					},
				})

			},
			chatFormatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				// 获取年月日时分秒值  slice(-2)过滤掉大于10日期前面的0
				var year = datetime.getFullYear();
				var month = ("0" + (datetime.getMonth() + 1)).slice(-2);
				var date = ("0" + datetime.getDate()).slice(-2);
				var hour = ("0" + datetime.getHours()).slice(-2);
				var minute = ("0" + datetime.getMinutes()).slice(-2);
				var time = year + "" + month + "" + date;

				var result = hour + ":" + minute;
				var curDate = new Date();
				var curYear = curDate.getFullYear(); //获取完整的年份(4位)
				var curMonth = ("0" + (curDate.getMonth() + 1)).slice(-2);
				var curDay = ("0" + curDate.getDate()).slice(-2); //获取当前日(1-31)
				var curTime = curYear + "" + curMonth + "" + curDay;
				if (year == curYear) {
					if (year == curYear) {
						if (date == curDay) {
							result = hour + ":" + minute;
						} else {
							result = month + "-" + date;
						}
					} else {
						result = month + "-" + date;
					}
				} else {
					result = month + "-" + date;
				}
				return result;
			},
			goMsg() {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.$emit('goMsg', 2);
			},
			goUserInfo() {

				var that = this;
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				uni.$emit('goUser', 0);
			},
			searchClose() {

				var that = this;
				that.searchText = "";
				that.page = 1;
				that.getUserList(false);
			},
			loadMore() {
				var that = this;
				that.moreText = "正在加载中...";
				that.isLoad = 1;
				if (that.squareid == 1) {
					that.getSpaceList(true);
				}
			},

			getImg() {
				return Math.floor(Math.random() * 35);
			},
			// 点击左边的栏目切换
			async swichMenu(index) {
				if (index == this.current) return; // 如果点击当前项则不处理

				this.current = index;
				this.page = 1;
				this.finished = false;
				this.loading = false;
				this.getAppList();
				if (this.menuHeight == 0 || this.menuItemHeight == 0) {
					await this.getElRect('menu-scroll-view', 'menuHeight');
					await this.getElRect('u-tab-item', 'menuItemHeight');
				}
				// 将菜单菜单活动item垂直居中
				this.scrollTop = index * this.menuItemHeight + this.menuItemHeight / 2 - this.menuHeight / 2;

			},
			// 获取一个目标元素的高度
			getElRect(elClass, dataVal) {
				new Promise((resolve, reject) => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.' + elClass).fields({
						size: true
					}, res => {
						// 如果节点尚未生成，res值为null，循环调用执行
						if (!res) {
							setTimeout(() => {
								this.getElRect(elClass);
							}, 10);
							return;
						}
						this[dataVal] = res.height;
					}).exec();
				})
			},
			toSet() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/setup'
				});
			},

			getIconimg() {
				var that = this;
				uni.request({
					url: that.$API.SPiconimg(),
					method: 'GET',
					dataType: "json",
					success(res) {
						if (res.data instanceof Array) {
							that.iconimg = res.data;
						} else {
							that.iconimg = Object.values(res.data);
						}
						localStorage.setItem('iconimg', JSON.stringify(that.iconimg));
					},
				});
			},
			arraysEqual(a, b) {
				if (a === b) return true;
				if (a == null || b == null) return false;
				if (a.length != b.length) return false;
				for (var c in a) {
					for (var d in b) {
						if (b[d].id == a[c].id) {
							if (b[d].lastTime != a[c].lastTime) {
								return false;
							}
						}

					}
				}
			},
			formatDate(datetime) {
				var now = new Date();
				var diff = now - new Date(datetime * 1000);
				var minuteDiff = Math.floor(diff / 60000);
				var hourDiff = Math.floor(diff / 3600000);
				var dayDiff = Math.floor(diff / 86400000);
				var weekDiff = Math.floor(dayDiff / 7);
				var monthDiff = Math.floor(diff / 2592000000);
				var yearDiff = Math.floor(diff / 31536000000);

				if (diff < 60000) {
					return Math.floor(diff / 1000) + "秒前";
				} else if (diff < 3600000) {
					return minuteDiff + "分钟前";
				} else if (hourDiff < 24) {
					return hourDiff + "小时前";
				} else if (dayDiff < 7 && dayDiff > 0) {
					return dayDiff + "天前";
				} else if (weekDiff > 0 && monthDiff <= 1) {
					return weekDiff + "周前";
				} else if (monthDiff > 1 && monthDiff < 12) {
					return monthDiff + "个月前";
				} else if (yearDiff >= 1) {
					return yearDiff + "年前";
				} else {
					return "刚刚";
				}
			},

			setSquare(type) {
				console.log("切换执行")
				var that = this;
				// if(reset){
				// 	that.page = 1;
				// }
				console.log("squareid" + that.squareid)
				that.squareid = type;
				clearInterval(that.isGetChat);
				that.isGetChat = null
				if (type == 0) {
					that.getadimg();
					that.getSectionList(false);
					that.getSwiperList();
				}
				if (type == 3) {
					that.getMetaList();
				}
				if (type == 2) {
					if (that.sy_appbox) {
						that.getSortList();
					}

				}
				if (type == 1) {
					that.getadimg();
					that.getSpaceList(false);
					that.getUserList();
					that.getShopList();
					that.getRecommend();
					that.getTagList();
				}
			},
			getAdsCache() {
				var that = this;
				if (localStorage.getItem('bannerAds')) {
					that.bannerAds = JSON.parse(localStorage.getItem('bannerAds'));

					var num = that.bannerAds.length;
					if (num > 0) {
						var rand = Math.floor(Math.random() * num);
						that.bannerAdsInfo = that.bannerAds[rand];
					}
				}
			},

			getSpaceList(isPage, isLogin) {
				var that = this;
				if (that.submitStatus1) {
					return false;
				}
				that.submitStatus1 = true;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				var page = that.page;
				if (isPage) {
					page++;
				}
				var data = {
					"status": 1
				}
				var spaceDataType = that.spaceDataType;
				var url = that.$API.followSpace();
				var order = "created";
				if (spaceDataType > 0) {
					url = that.$API.spaceList();
				}
				if (spaceDataType == 1) {
					order = "likes";
				}
				that.$Net.request({
					url: url,
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 10,
						"page": page,
						"order": order,
						"token": token
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						that.spaceLoad = true;
						that.isLoad = 0;
						that.moreText = "加载更多";
						if (!isPage) {
							that.dataLoad = true;
						}
						if (res.data.code == 1) {
							that.noLogin = false;
							var list = res.data.data;
							var spaceList = [];
							for (var i in list) {
								if (list[i].type == 0) {
									if (list[i].pic) {
										var pic = list[i].pic;
										list[i].picList = pic.split("||");
									} else {
										list[i].picList = [];
									}

								}
								if (list[i].type == 2) {
									if (list[i].forwardJson.pic) {
										var pic = list[i].forwardJson.pic;
										list[i].forwardJson.picList = pic.split("||");
									} else {
										list[i].forwardJson.picList = [];
									}

								}
							}
							spaceList = list;
							if (list.length > 0) {
								if (isPage) {
									that.page++;
									that.spaceList = that.spaceList.concat(spaceList);
								} else {
									that.spaceList = spaceList;
								}
								localStorage.setItem('spaceList', JSON.stringify(spaceList));
							} else {
								that.moreText = "没有更多动态了";
							}

						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true
								} else {
									that.getSpaceList(isPage, true);
								}
							}
						}
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						that.spaceLoad = true;
						that.moreText = "加载更多";
						that.isLoad = 0;
					}
				})
			},
			setSpaceDataType(type) {
				var that = this;
				that.spaceDataType = type;
				that.spaceLoad = false;
				that.page = 1;
				that.spaceList = [];
				that.getSpaceList(false);
			},
			userStatus() {
				var that = this;
				that.$Net.request({

					url: that.$API.userStatus(),
					data: {
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 0) {
							localStorage.removeItem('userinfo');
							localStorage.removeItem('token');
							that.token = "";
							that.userinfo = null;
						} else if (res.data.code == 1) {
							var myuid = res.data.data.uid
							that.avatarurl = res.data.data.avatar
							that.getRz(myuid)
							var userInfo = JSON.parse(localStorage.getItem('userinfo'));
							if (res.data.data.isvip) {
								userInfo.isvip = res.data.data.isvip;
							}
							if (res.data.data.vip) {
								userInfo.vip = res.data.data.vip;
							}
							if (res.data.data.avatar) {
								userInfo.avatar = res.data.data.avatar;
							}
							localStorage.setItem('userinfo', JSON.stringify(userInfo));
						}
					},
					fail: function(res) {
						console.log(res)
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			getRz(uid) {
				var that = this;
				that.$Net.request({

					url: that.$API.identifyStatus(),
					data: {
						"uid": uid
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						if (res.data.code == 1) {
							that.identifyCompany = res.data.data.identifyCompany;
							localStorage.setItem('identifyCompany', that.identifyCompany);
							console.log(that.identifyCompany);
						}
						var timer = setTimeout(function() {
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						console.log("网络开小差了哦");
						var timer = setTimeout(function() {
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			loadingFun() {
				var that = this;
				if (that.squareid == 0) {
					that.getSectionList(false);
					that.getSwiperList();
					that.getRecommend();
					that.getRecommendSectionList();
				}
				if (that.squareid == 1) {
					that.getadimg();
					that.getUserList();
					that.getShopList();
					that.getRecommend();
					that.getTagList();
				}
				if (that.squareid == 2) {
					if (that.sy_appbox) {
						that.getSortList();
					}

				}
				if (that.squareid == 3) {
					that.getMetaList();
				}
			},
			getadimg() {
				var that = this;
				uni.request({
					url: that.$API.SPadimg(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.adimage_sl = res.data.adimage_sl;

						// 清空之前的数据
						that.swiperList2 = [];

						// 动态设置swiperList2内容
						for (let i = 1; i <= that.adimage_sl; i++) {
							that.swiperList2.push({
								url: res.data['adimage' + i],
								zt: res.data['link_url' + i]
							});
						}
						localStorage.setItem('swiperList2', JSON.stringify(that.swiperList2));
					},
					fail: function(error) {
						console.log(error);
					}
				});
			},
			replaceSpecialChar(text) {
				text = text.replace(/&quot;/g, '"');
				text = text.replace(/&amp;/g, '&');
				text = text.replace(/&lt;/g, '<');
				text = text.replace(/&gt;/g, '>');
				text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			allCache() {
				var that = this;
				var timer = setTimeout(function() {
					that.isLoading = 1;
					clearTimeout('timer')
				}, 300)
				if (localStorage.getItem('tabCurTab')) {
					that.tabCurTab = localStorage.getItem('tabCurTab');
				}
				if (localStorage.getItem('identifyCompany')) {
					that.identifyCompany = localStorage.getItem('identifyCompany');
				} else {
					that.getRz()
				}
				if (localStorage.getItem('swiperList2')) {
					that.swiperList2 = JSON.parse(localStorage.getItem('swiperList2'));
				}
				if (localStorage.getItem('swiperList')) {
					that.swiperList = JSON.parse(localStorage.getItem('swiperList'));
				}
				if (localStorage.getItem('recommendSectionList')) {
					that.recommendSectionList = JSON.parse(localStorage.getItem('recommendSectionList'));
				}
				if (localStorage.getItem('sectionList')) {
					that.sectionList = JSON.parse(localStorage.getItem('sectionList'));
				}
				if (localStorage.getItem('iconimg')) {
					that.iconimg = JSON.parse(localStorage.getItem('iconimg'));
				}
				if (localStorage.getItem('userList')) {
					that.userList = JSON.parse(localStorage.getItem('userList'));
				}
				if (localStorage.getItem('find_shopList')) {
					that.shopList = JSON.parse(localStorage.getItem('find_shopList'));
				}
				if (localStorage.getItem('recommendList')) {
					that.recommendList = JSON.parse(localStorage.getItem('recommendList'));
				}
				if (localStorage.getItem('find_metaList')) {
					that.metaList = JSON.parse(localStorage.getItem('find_metaList'));
				}
				if (localStorage.getItem('find_tagList')) {
					that.tagList = JSON.parse(localStorage.getItem('find_tagList'));
				}
				if (localStorage.getItem('spaceList')) {
					that.spaceList = JSON.parse(localStorage.getItem('spaceList'));
				}
			},
			getDowns(downs) {
				var that = this;
				if (downs <= 999) {
					return downs;
				} else if (downs > 999 && downs <= 9999) {
					return (downs / 1000).toFixed(1) + "千";
				} else if (downs > 9999) {
					return (downs / 10000).toFixed(1) + "万";
				}
			},
			getRecommendSectionList(isLogin) {
				var that = this;
				if (that.submitStatus2) {
					return false;
				}
				that.submitStatus2 = true;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				var data = {
					"isrecommend": 1,
				}
				that.$Net.request({
					url: that.$API.sectionList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 8,
						"page": 1,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus2 = false;
						uni.stopPullDownRefresh()
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							that.recommendSectionList = res.data.data;
							localStorage.setItem('recommendSectionList', JSON.stringify(that
								.recommendSectionList));
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true
								} else {
									that.getRecommendSectionList(true);
								}
							}
						}
						var timer = setTimeout(function() {
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus2 = false;
						uni.stopPullDownRefresh()

					}
				})
			},
			getSectionList(isLogin) {
				var that = this;
				if (that.submitStatus3) {
					return false;
				}
				that.submitStatus3 = true;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				that.$Net.request({
					url: that.$API.sectionList(),
					data: {
						"limit": 50,
						"page": 1,
						"searchKey": that.searchText,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus3 = false;
						uni.stopPullDownRefresh()
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							var parentList = [];
							for (var i in list) {
								if (list[i].parent == 0) {
									list[i].subList = [];
									parentList.push(list[i]);
								}
							}
							for (var j in list) {
								if (list[j].parent != 0) {
									for (var p in parentList) {
										if (list[j].parent == parentList[p].id) {
											parentList[p].subList.push(list[j]);
										}
									}
								}
							}
							that.sectionList = parentList;
							localStorage.setItem('sectionList', JSON.stringify(that.sectionList));
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true
								} else {
									that.getSectionList(true);
								}
							}
						}
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus3 = false;
						uni.stopPullDownRefresh()
						that.moreText = "加载更多";
						that.isLoad = 0;
					}
				})
			},
			postSpace(type) {
				var that = this;
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/edit/articlePost'
					});
				} else if (type == 5) {
					uni.navigateTo({
						url: '/pages/edit/addshop'
					});
				} else {
					uni.navigateTo({
						url: '/pages/space/post?type=' + type
					});
				}
			},
			getSwiperList() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"isswiper": '1'
				}
				that.$Net.request({
					url: that.$API.postList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 5,
						"page": 1,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							var list = res.data.data;
							var swiper = [];
							if (list.length > 0) {
								for (var i in list) {
									if (list[i].images.length > 0) {
										var arr = {
											id: list[i].id,
											type: 'image',
											url: list[i].images[0],
											title: list[i].title,
											intro: that.subText(list[i].text, 20),
										}
										swiper.push(arr);
									}

								}
								that.swiperList = swiper.concat(that.swiperList2);
							} else {
								that.swiperList = that.swiperList2;
							}
							localStorage.setItem('swiperList', JSON.stringify(that.swiperList));
						}

					},
					fail: function(res) {

					}
				})
			},
			getRecommend(isLogin) {
				var that = this;
				if (that.submitStatus5) {
					return false;
				}
				that.submitStatus5 = true;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				var data = {
					"type": "post",
					"isrecommend": 1
				}
				that.$Net.request({
					url: that.$API.getContentsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 5,
						"page": 1,
						"order": "modified",
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus5 = false;
						if (res.data.code == 1) {
							that.noLogin = false;
							var list = res.data.data;
							if (list.length > 0) {

								that.recommendList = list;

							} else {
								that.recommendList = [];
							}
							localStorage.setItem('recommendList', JSON.stringify(that.recommendList));
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true
								} else {
									that.getRecommend(true);
								}
							}
						}
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus5 = false;
					}
				})
			},
			getMetaList() {
				var that = this;
				var data = {
					"type": "category",
					"parent": 0
				}
				that.$Net.request({
					url: that.$API.getMetasList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 40,
						"page": 1,
						"order": "order"
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {

								that.metaList = list;
								localStorage.setItem('find_metaList', JSON.stringify(that.metaList));
							}
						}
					},
					fail: function(res) {}
				})
			},
			goAds2(url) {
				var that = this;
				// #ifdef APP-PLUS
				plus.runtime.openWeb(url);
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			getTagList() {
				var that = this;
				var data = {
					"type": "tag",
					"isrecommend": "1"
				}
				that.$Net.request({
					url: that.$API.getMetasList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 50,
						"page": 1,
						"order": "count"
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {


								that.tagList = list;

								localStorage.setItem('find_tagList', JSON.stringify(that.tagList));
							}
						}
					},
					fail: function(res) {}
				})
			},
			toSearch() {
				var that = this;
				if (that.noLogin) {
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				uni.navigateTo({
					url: '/pages/contents/search'
				});
			},
			shopInfo(data) {
				var that = this;

				if (that.isAdmin) {
					return false
				} else {
					uni.navigateTo({
						url: '/pages/shop/shopinfo?sid=' + data.id
					});

				}

			},
			getShopList() {
				var that = this;
				var data = {
					"status": "1",
					"isView": "1"
				}
				that.$Net.request({
					url: that.$API.shopList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 6,
						"page": 1,
						"order": "sellNum"
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							that.shopList = list;
							localStorage.setItem('find_shopList', JSON.stringify(that.shopList));
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			getUserList() {
				var that = this;
				var token = ""
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;

				}
				that.$Net.request({
					url: that.$API.getUserList(),
					data: {
						"searchParams": "",
						"limit": 5,
						"page": 1,
						"order": "posttime",
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {

								var userList = [];
								for (var i in list) {
									var arr = list[i];
									arr.style = "background-image:url(" + list[i].avatar + ");"
									userList.push(arr);
								}
								that.userList = userList;
								localStorage.setItem('userList', JSON.stringify(that.userList));

							}
						}
					},
					fail: function(res) {}
				})
			},
			toCategoryContents(title, id) {
				var that = this;
				var type = "meta";
				uni.navigateTo({
					url: '/pages/contents/contentlist?title=' + title + "&type=" + type + "&id=" + id
				});
			},
			toTopContents(title, id) {
				var that = this;
				var type = "meta";
				uni.navigateTo({
					url: '/pages/contents/contentlist?title=' + title + "&type=top&id=" + id
				});
			},
			toInfo(data) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/info?cid=' + data.cid + "&title=" + data.title
				});
			},
			toAlltag() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/alltag'
				});
			},
			toAllcategory() {
				var that = this;
				uni.navigateTo({
					url: '/pages/contents/allcategory'
				});
			},
			goPage(url) {
				var that = this;

				uni.navigateTo({
					url: url
				});
			},
			toRecommend() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/recommend'
				});
			},
			toGroup() {
				var url = that.$API.GetGroupUrl();
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			toAds(url) {
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			unreadNum() {
				var that = this;
				if (localStorage.getItem('noticeSum')) {
					that.noticeSum = Number(localStorage.getItem('noticeSum'));
				}
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			goRegister() {
				uni.navigateTo({
					url: '/pages/user/register'
				});
			},
			goLogin() {
				uni.navigateTo({
					url: '/pages/user/login'
				});
			},
			toUserContents(data) {
				var that = this;
				var name = data.name;
				var title = data.name + "的信息";
				if (data.screenName) {
					title = data.screenName + " 的信息";
					name = data.screenName
				}
				var id = data.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
			subText(text, num) {
				if (text) {
					if (text.length > num) {
						text = text.substring(0, num);
						return text + "……";
					} else {
						return text;
					}
				} else {
					return "Ta还没有个人介绍哦"
				}
			},
			// 获取分区
			getSortList() {
				const that = this;
				that.$Net.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getSortList",
						"getsort_page": 1,
						"getsort_limit": 50,
						"getsort_order": "sort"
					},
					method: "GET",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 200) {
							// 添加"小编推荐"作为第一个分区
							that.tabbar = [{
								name: '小编推荐',
								slug: 'recommend',
								id: 0
							}, ...res.data.data];
						}
						that.getAppList();
					}
				});
			},

			// 获取应用列表
			getAppList(isPage = false) {

				const that = this;
				if (that.loading) return;

				if (!isPage) that.loading = true;
				that.loadStatus = 'loading';
				const page = isPage ? that.page + 1 : 1;

				// 构建筛选条件
				let conditions = {};
				if (that.selectedSystem != '') {
					conditions.system = that.selectedSystem
				}
				if (that.selectedType != '') {
					conditions.type = that.selectedType
				}
				if (that.current === 0) {
					conditions.istop = '1';
				} else if (that.tabbar[that.current]) {
					conditions.sort = String(that.tabbar[that.current].id);
				}

				that.$Net.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getAppList",
						"getapp_page": page,
						"getapp_limit": that.limit,
						"getapp_order": that.selectedOrder,
						"getapp_if": JSON.stringify(conditions)
					},
					method: "GET",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 200) {
							const data = res.data.data || [];
							const list = data.map(item => ({
								...item,
								tagInfo: that.tagMap[item.type] || {
									text: '未知',
									color: '#999'
								},
								size: that.formatSize(item.size)
							}));

							if (isPage) {
								// 加载更多时，使用数组拓展运算符合并数组
								that.appList = [...that.appList, ...list];
								that.page = page;
							} else {
								// 只有在初加载或刷新时才重置列表
								that.appList = list;
								that.page = 1;
							}

							that.finished = list.length < that.limit;
							that.loadStatus = that.finished ? 'nomore' : 'loadmore';
						}
						that.appLoad = true;
						that.loading = false;
					},
					fail: function() {
						that.appLoad = true;
						that.loading = false;
						that.loadStatus = 'loadmore';
					}
				});
			},

			// 触底加载
			handleScrollToLower() {
				console.log('触发触底加载');
				if (this.loadStatus === 'loadmore' && !this.loading && this.sy_appbox) {
					this.getAppList(true);
				}
			},

			// 筛选相关方法
			showOrderPicker() {
				this.showOrder = true;
			},

			confirmOrder(e) {
				const selectedOption = this.orderOptions[e[0]];
				if (selectedOption) {
					this.selectedOrder = selectedOption.value;
					this.orderText = selectedOption.text;
					this.showOrder = false;
					this.page = 1; // 重置页码
					if (this.sy_appbox) {
						this.getAppList(false); // 使用false参数重新加载
					}

				}
			},

			showSystemFilter() {
				this.showSystem = true;
			},

			confirmSystem() {
				const option = this.systemOptions.find(item => item.value === this.selectedSystem);
				this.systemText = option ? option.label : '系统';
				this.showSystem = false;
				this.page = 1; // 重置页码
				if (this.sy_appbox) {
					this.getAppList(false); // 使用false参数重新加载
				}
			},

			showTypeFilter() {
				this.showType = true;
			},

			confirmType() {
				const option = this.typeOptions.find(item => item.value === this.selectedType);
				this.typeText = option ? option.label : '类型';
				this.showType = false;
				this.page = 1; // 重置页码
				if (this.sy_appbox) {
					this.getAppList(false); // 使用false参数重新加载
				}
			},
			// 获取标签信息
			getTagInfo(item) {
				return this.tagInfoMap[item.type] || {
					text: '未知',
					color: '#999999'
				};
			},

			// 文件大小
			formatSize(size) {
				if (!size) return '未知大小';

				if (size >= 1024 * 1024) {
					return (size / (1024 * 1024)).toFixed(1) + 'GB';
				} else if (size >= 1024) {
					return (size / 1024).toFixed(1) + 'MB';
				} else {
					return size.toFixed(1) + 'KB';
				}
			},
			selectSystem(value) {
				this.selectedSystem = value;
			},
			resetSystem() {
				this.selectedSystem = '';
				this.systemText = '系统';
				this.showSystem = false;
				this.getAppList();
			},
			selectType(value) {
				this.selectedType = value;
			},
			resetType() {
				this.selectedType = '';
				this.typeText = '类型';
				this.showType = false;
				this.getAppList(false);
			},
			toAppInfo(id) {
				uni.navigateTo({
					url: '/pages/plugins/sy_appbox/info?id=' + id
				});
			},
			// 添加取消排序方法
			cancelOrder() {
				this.showOrder = false;
			},
		},
		// #ifdef APP-PLUS
		components: {
			waves
		},
		// #endif

		// #ifdef H5 || MP
		components: {
			waves
		},
		// #endif
	}
</script>

<style lang="scss" scoped>
	@import "@/static/styles/home.scss";

	body {
		background-color: #f6f6f6;
	}

	.user-rz {
		position: relative;
		display: inline-block;
	}

	.user-rz-icon {
		position: absolute;
		right: -6upx;
		bottom: -4upx;
		width: 44upx;
		height: 44upx;
	}

	.zhuce {
		background: #fff;
		border: 2upx solid #3cc9a4;
		color: #3cc9a4;
	}

	.data-box-2 {
		padding: 0;
		margin: 20upx 20upx;
		border-radius: 20upx;
		box-shadow: 0 0 50upx 0 rgba(0, 0, 0, .04);
	}

	.data-box-2 .cu-bar {
		border-radius: 20upx;
	}

	.data-box-2-banner {
		padding: 0;
		margin: 20upx 0;
		border-radius: 20upx;
		box-shadow: 0 0 50upx 0 rgba(0, 0, 0, .04);
	}

	.uni-swiper-dot.uni-swiper-dot-active {
		background-color: #00bcd4 !important;
		opacity: 0.8;
	}

	.uni-swiper-dot {
		border-radius: 50upx !important;
	}

	.uni-swiper-slides swiper-item {
		padding: 0 0;
		box-sizing: border-box;
	}

	.screen-swiper {
		min-height: 300upx;
	}

	.u-wrap {
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	.u-search-box {
		padding: 18rpx 30rpx;
	}

	.u-menu-wrap {
		flex: 1;
		display: flex;
		overflow: hidden;
	}

	.u-search-inner {
		background-color: rgb(234, 234, 234);
		border-radius: 100rpx;
		display: flex;
		align-items: center;
		padding: 10rpx 16rpx;
	}

	.u-search-text {
		font-size: 26rpx;
		color: $u-tips-color;
		margin-left: 10rpx;
	}

	.u-tab-view {
		width: 200rpx;
		height: 100%;
	}

	.u-tab-item {
		height: 110rpx; // 保持固定高度
		background: #f6f6f6;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		color: #444;
		font-weight: 400;
		line-height: 1;
	}

	.u-tab-item-active {
		position: relative;
		color: #000;
		font-size: 30rpx;
		font-weight: 600;
		background: #fff;
	}

	.u-tab-item-active::before {
		content: "";
		position: absolute;
		border-left: 4px solid #3cc9a4;
		height: 32rpx;
		left: 0;
		top: 39rpx;
		border-radius: 50rpx;
	}

	.u-tab-view {
		height: 100%;
		overflow: auto;
	}

	.right-box {
		background-color: rgb(250, 250, 250);
		overflow: auto;
	}

	.page-view {
		padding: 16rpx;
	}

	.class-item {
		margin-bottom: 30rpx;
		background-color: #fff;
		padding: 16rpx;
		border-radius: 16rpx;
	}

	.item-title {
		font-size: 26rpx;
		color: $u-main-color;
		font-weight: bold;
	}

	.item-menu-name {
		font-weight: normal;
		font-size: 24rpx;
		color: $u-main-color;
	}

	.thumb-box {
		width: 33.333333%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		margin-top: 20rpx;
	}

	.item-menu-image {
		width: 120rpx;
		height: 120rpx;
	}

	.app-box {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.app-box-body {
		flex: 1;
		display: flex;
		margin-right: 20rpx;
		min-width: 0; // 防止flex子元素溢出
		align-items: center;
	}

	.app-box-logo {
		width: 110rpx;
		height: 110rpx;
		flex-shrink: 0; // 防止图片缩小
	}

	.app-box-content {
		flex: 1;
		margin-left: 20rpx;
		min-width: 0; // 防止flex子元素溢出
	}

	.app-box-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 6rpx;
		width: 250rpx;
	}

	.text-cut {
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}

	.app-box-info {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 6rpx;

		text {
			margin-right: 10rpx;
		}
	}

	.app-box-tags {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		gap: 8rpx;
	}

	.app-tag {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		color: #ffffff;
		font-size: 24rpx;
	}

	.app-category-tag {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		background-color: #f5f5f5;
		color: #666666;
		font-size: 24rpx;
	}

	.app-box-down {
		background-color: #3cc9a4;
		color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 100rpx;
		white-space: nowrap; // 防止文字换行
		flex-shrink: 0; // 防止按钮缩小
	}

	.nav {
		white-space: nowrap;
		padding: 0 30rpx;
		height: 90rpx;
		border-bottom: 1px solid #f1f1f1;

		.cu-item {
			height: 90rpx;
			display: inline-block;
			line-height: 90rpx;
			margin: 0 30rpx;
			padding: 0 20rpx;

			&.cur {
				border-bottom: 4rpx solid #3cc9a4;
			}
		}
	}

	.app-box {
		margin-bottom: 20rpx;

		.app-box-tags {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			color: #666;
			font-size: 26rpx;

			.app-tag {
				padding: 4rpx 8rpx;
				border-radius: 8rpx;
				color: #ffffff;
				font-size: 20rpx;
			}

			.app-category-tag {
				padding: 4rpx 12rpx;
				border-radius: 8rpx;
				background-color: #f5f5f5;
				color: #666666;
				margin-right: 12rpx;
				font-size: 24rpx;
			}
		}
	}

	/* 筛选栏样式 */
	.filter-bar {
		display: flex;
		padding: 20rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.filter-item {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #333;
	}

	.filter-item .cuIcon-unfold {
		margin-left: 4rpx;
		font-size: 24rpx;
		color: #999;
	}

	/* 筛选弹窗样式 */
	.filter-popup {
		background: #fff;
		padding: 30rpx;
		border-radius: 20rpx 20rpx 0 0;
	}

	.filter-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.filter-options {
		display: flex;
		flex-wrap: wrap;
	}

	.filter-option {
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		border: 1px solid #eee;
		border-radius: 30rpx;
		margin: 0 20rpx 20rpx 0;
		font-size: 28rpx;
	}

	.filter-option.active {
		background: #3cc9a4;
		color: #fff;
		border-color: #3cc9a4;
	}

	.filter-buttons {
		display: flex;
		margin-top: 40rpx;
	}

	.btn-reset,
	.btn-confirm {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		margin: 0 20rpx;
		font-size: 30rpx;
	}

	.btn-reset {
		background: #f5f5f5;
		color: #666;
	}

	.btn-confirm {
		background: #3cc9a4;
		color: #fff;
	}

	.text-title-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	/* 添加加载动画相关样式 */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;

		.loading-text {
			font-size: 26rpx;
			color: #909399;
			margin-top: 20rpx;
		}
	}

	.loading-more {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx 0;

		.loading-more-text {
			font-size: 24rpx;
			color: #909399;
		}
	}

	.menu-scroll-view {
		height: 100%;
		overflow: auto;
	}
</style>