# forumItem.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/components/forumItem.vue.md`
- **组件说明**：此组件用于在帖子列表（如圈子详情页、我的帖子等）中展示单个帖子的摘要信息，通常包含标题、部分内容、作者、图片、标签、互动数据（点赞、评论、收藏）等，并提供操作按钮。

---

<template>
	<view class="forum-list-box">
		<!-- 			<image class="user-rz-icon-qz"  style="top:10rpx;width: 119rpx;height: 119rpx;max-width: initial!important;z-index:100;left:24rpx" v-if="fanstey_avatarframe"  :src="AvatarItem"></image> -->
		<view class="forum-list-content forum-shadow">
			<view class="user-rz-qz">
				<view class="forum-list-user">
					<view class="forum-avatar" @tap="toUserContents(item.userJson)"
						:style="{ backgroundImage: 'url(' + item.userJson.avatar + ')' }">
						<!-- <image v-if="fanstey_avatarframe" style="width: 100%;height: 100%;" :src="AvatarItem"></image> -->
						<!-- <tn-lazy-load :image="item.userJson.avatar" borderRadius="50%" height="90" mode="aspectFill"> 
						</tn-lazy-load> -->
						<!-- 小祈愿头像框 -->
						<image v-if="frameUrl && frameUid == item.userJson.uid" 
							  style="width:100%;height:100%;position:absolute;top:0;left:0;z-index:90;transform:scale(1.05);transform-origin:center center;" 
							  :src="frameUrl" 
							  mode="aspectFit"
							  lazy-load></image>
                        <!-- 小祈愿头像框 -->
					</view>
					<image class="user-rz-icon-qz" :src="rzImg" v-if="item.userJson.lvrz==1" mode="aspectFill" style="z-index: 100;"></image>

					<view class="forum-userinfo">
						<view class="forum-userinfo-name" :class="item.userJson.isvip>0?'name-vip':''">
							{{item.userJson.name}}
							<image v-if="item.userJson.isvip>0" :src="vipImg"
								style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;"
								mode="widthFix"></image>
							<image :src="lvImg+getLv(item.userJson.experience)+'.png'"
								style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 44upx;height: 22upx;"
								mode="widthFix"></image>
							<!-- 小祈愿勋章插件开始 -->
								<medal-item 
								:uid="item.userJson.uid"
								@medal-loaded="onMedalLoaded"
							/>
							 <!-- 小祈愿勋章插件结束 -->
						</view>

						<view class="forum-userinfo-date">
							{{formatDate(item.created)}} <text class="margin-left-sm" v-if="$API.localOf()">{{getLocal(item.userJson.local)}}</text>
						</view>
						<!-- <block v-if="item.isAds==0">
						<view class="cu-btn forum-follow-btn" v-if="item.isFollow==0"
							@tap="follow(1,item.userJson.uid)">
							<text class="cuIcon-add"></text>关注
						</view>
						<view class="cu-btn text-red forum-follow-btn isFollow" v-if="item.isFollow==1"
							@tap="follow(0,item.userJson.uid)">
							已关注
						</view>
					</block> -->
						<view class="forum-follow-btn" style="background-color: transparent;padding: 20upx 0;"
							v-if="item.isAds==0" @click="show = true">
							<span class="cuIcon-moreandroid"></span>
						</view>
						<tn-popup v-model="show" mode="bottom" :closeBtn="true" :height="myPurview>0?'50%':'20%'"
							:borderRadius="20">
							<view class="center-container tn-margin-top-xxl">
								<view class="">
									<block v-if="mySelf==false">
										<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
											v-if="item.isFollow==0" @tap="follow(1,item.userJson.uid)">
											<text class="tn-icon-my-add" style="margin-right: 5px;"></text>立即关注
										</view>
										<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
											v-if="item.isFollow==1" @tap="follow(0,item.userJson.uid)">
											<text class="tn-icon-my-reduce" style="margin-right: 5px;"></text>取消关注
										</view>
									</block>
									<!-- <view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toLink('../space/post?type=6&toid='+item.id)">
									<text class="tn-icon-send" style="margin-right: 5px;"></text>转发帖子
								</view> -->
									<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
										@tap="toLike(item.id)">
										<text :class="item.isLikes==1?'tn-icon-like-fill':'tn-icon-like-lack'"
											style="margin-right: 5px;"></text>
										<block v-if="item.isLikes!=1">
											点赞帖子
										</block>
										<block v-if="item.isLikes==1">
											已赞该帖
										</block>
									</view>
									<block v-if="myPurview > 0">
										<block v-if="myPurview >= 3">
											<block v-if="item.isrecommend==0">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toRecommend(item.id,1)">
													<text class="tn-icon-fire" style="margin-right: 5px;"></text>帖子加精
												</view>
											</block>
											<block v-if="item.isrecommend==1">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toRecommend(item.id,0)">
													<text class="tn-icon-fire-fill"
														style="margin-right: 5px;"></text>取消加精
												</view>
											</block>
											<block v-if="item.isTop==0">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toTop(item.id,1)">
													<text class="tn-icon-pushpin" style="margin-right: 5px;"></text>添加置顶
												</view>
											</block>
											<block v-if="item.isTop==1">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toTop(item.id,0)">
													<text class="tn-icon-pushpin-fill"
														style="margin-right: 5px;"></text>取消置顶
												</view>
											</block>

											<block v-if="item.isswiper==0">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toSwiper(item.id,1)">
													<text class="tn-icon-task" style="margin-right: 5px;"></text>设为轮播
												</view>
											</block>
											<block v-if="item.isswiper==1">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toSwiper(item.id,0)">
													<text class="tn-icon-task-fill"
														style="margin-right: 5px;"></text>取消轮播
												</view>
											</block>
										</block>
										<block v-if="myPurview >= 2">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toDelete(item.id)">
												<text class="tn-icon-delete" style="margin-right: 5px;"></text>删除帖子
											</view>
										</block>
										<block v-if="myPurview == 5">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toBan(item.userJson.uid)">
												<text class="tn-icon-prohibit" style="margin-right: 5px;"></text>封禁用户
											</view>
										</block>
										<block v-if="item.status==1">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toLock(item.id,2)">
												<text class="tn-icon-lock" style="margin-right: 5px;"></text>锁定帖子
											</view>
										</block>
										<block v-if="item.status==2">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toLock(item.id,1)">
												<text class="tn-icon-unlock" style="margin-right: 5px;"></text>解除锁定
											</view>
										</block>
									</block>
								</view>
							</view>
						</tn-popup>

					</view>
				</view>
			</view>
			<block v-if="item.isAds!=1">
				<view class="forum-list-text" @tap="goInfo(item.id)">
					<view class="forum-list-title text-content-1">
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.isTop==2">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-pushpin tn-tag-content__item--prefix"
									style="color: #d40000;"></text>置顶</text>
						</view>
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.isrecommend==1&&item.sectionJson.slug != 'vip'&&item.isTop!=2">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-pushpin tn-tag-content__item--prefix"
									style="color: #00BCD4;"></text>精华</text>
						</view>
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.sectionJson.slug == 'vip'">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-vip-diamond tn-tag-content__item--prefix"
									style="color: #df9319;"></text>VIP专享</text>
						</view>
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.sectionJson.slug == 'lv4'">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-vip tn-tag-content__item--prefix"
									style="color: #00b755;"></text>4级专享</text>
						</view>


						{{item.title}}
					</view>
					<view v-if="item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'" class="forum-list-intro text-content-2">
						{{subText(item.text)}}
					</view>
					<view v-if="item.sectionJson.slug == 'vip'" class="forum-list-intro text-content-2">
						该贴内容属于VIP专享！
					</view>
					<view v-if="item.sectionJson.slug == 'lv4'" class="forum-list-intro text-content-2">
						该贴内容属于等级专享！
					</view>
				</view>
				<block v-if="item.images.length==1&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="forum-media forum-one">
						<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
						</view>
					</view>
				</block>
				<block v-if="item.images.length==2&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-2 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-qz" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-pm" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
						</view>
					</view>
				</block>
				<block v-if="item.images.length==3&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-3 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-qz">
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
						</view>
						<view class="bg-img">
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-pm">
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
						</view>
					</view>
				</block>
				<block v-if="item.images.length==4&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-2 grid-square grid-square-1 grid-square-10">
						<view class="bg-img radius-q" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-p" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
						</view>

					</view>
					<view class="grid flex-sub col-2 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-z" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-m" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[3]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[3])"></tn-lazy-load>
						</view>
					</view>
				</block>
				<block v-if="item.images.length==5&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-3 grid-square grid-square-1 grid-square-10">
						<view class="bg-img radius-q">
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
						</view>
						<view class="bg-img">
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-p">
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
						</view>
					</view>
					<view class="grid flex-sub col-2 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-z" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[3]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-m" style="width: calc((92%)/2);">
							<tn-lazy-load :image="item.images[4]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
						</view>
					</view>
				</block>
				<block v-if="item.images.length>5&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-3 grid-square grid-square-1 grid-square-10">
						<view class="bg-img radius-q">
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
						</view>
						<view class="bg-img">
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-p">
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
						</view>
					</view>
					<view class="grid flex-sub col-3 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-z">
							<tn-lazy-load :image="item.images[3]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[3])"></tn-lazy-load>
						</view>
						<view class="bg-img">
							<tn-lazy-load :image="item.images[4]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[4])"></tn-lazy-load>
						</view>
						<view class="bg-img radius-m">
							<view v-if="item.images.length > 6" class="extra-count">
								<view class="cuIcon-add center-add"> {{ item.images.length-6 }}</view>
							</view>
							<tn-lazy-load :image="item.images[5]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[5])"></tn-lazy-load>
						</view>
					</view>
				</block>
				<block v-if="item.videos.length==1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="spaceVideo">
						<!--  #ifdef H5 || MP-->
						<video :src="item.videos[0].src" :poster="item.videos[0].poster"></video>
						<!--  #endif -->
						<!--  #ifdef APP-PLUS -->
						<block v-if="isIos">
							<view class="paceVideo2">
								<video :src="item.videos[0].src" http-cache="true" @play="playVedio(item.id)"
									:poster="item.videos[0].poster" loop @fullscreenchange="screenchange" :id="item.id"
									:title="item.title + ' - ' + item.userJson.name + ' | ' + $API.GetAppName()"></video>
							</view>
						</block>
						<block v-else>
							<view class="paceVideo2">
								<view class="spaceVideo-play" :style="{ 
						            backgroundImage: 'url(' + item.videos[0].poster + ')', 
						            backgroundSize: 'cover', 
						            backgroundRepeat: 'no-repeat', 
						            backgroundPosition: 'center center' 
						         }" @tap="goPlay(item.videos[0].src,item.title,item.userJson.name,item.id)">
									<text class="cuIcon-playfill"></text>
								</view>
							</view>
						</block>
						<!--  #endif -->
					</view>
				</block>
				<block v-if="item.sectionJson.slug == 'vip'">
					<view class="forum-media forum-one">
						<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
							<tn-lazy-load image="../../static/page/vip_img.png" @tap="goInfo(item.id)" mode="aspectFill"></tn-lazy-load>
						</view>
					</view>
				</block>
				<block v-if="item.sectionJson.slug == 'lv4'">
					<view class="forum-media forum-one">
						<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
							<tn-lazy-load image="../../static/page/lv_img.png" @tap="goInfo(item.id)" mode="aspectFill"></tn-lazy-load>
						</view>
					</view>
				</block>
			</block>
			<block v-if="item.isAds==1">
				<view class="forum-list-text" @tap="goAds(item)">
					<view class="forum-list-title">
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.isAds==1">
							<text class="tn-tag-content__item--prefix tn-text-bold" style="padding-right: 0upx;">
								广告</text>
						</view>
						{{item.name}}
					</view>
					<view class="forum-list-intro">
						{{item.intro}}
					</view>
				</view>
				<view class="forum-media forum-one">
					<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
						<tn-lazy-load :image="item.img" mode="aspectFill"
							@tap="previewImage(item.img,item.img)"></tn-lazy-load>
					</view>
				</view>
			</block>
			<block v-if="item.isAds!=1">
				<view
					class="article-content-btn article-list-btn flex justify-between tn-margin-top-sm tn-margin-right-lg">
					<view class="content-author flex align-center" style="margin-top: 0upx;line-height: 20upx;"
						@click="goSection(item.sectionJson.id)">
						<tn-tag :backgroundColor="'#ececec'" class="tn-text-bold" :fontColor="'#555'" shape="circle"
							padding="0 40rpx" width="100%">{{item.sectionJson.name}}</tn-tag>
					</view>
					<view class="flex align-center" style="color:#666">
						<view class="margin-left-lg" @tap="goInfo(item.id)">
							<span class="tn-icon-eye tn-color-gray" style="font-size: 40upx;"></span>
						</view>
						<text @tap="goInfo(item.id)"> {{item.views}} </text>
						<view class="margin-left-lg" @tap="goInfo(item.id)">
							<span class="tn-icon-comment tn-color-gray" style="font-size: 40upx;"></span>
						</view>
						<text @tap="goInfo(item.id)"> {{item.commentsNum}} </text>
						<view class="margin-left-lg" @tap="toLike(item.id)">
							<span class="tn-color-gray" :class="item.isLikes==1?'tn-icon-like-fill':'tn-icon-like-lack'"
								style="font-size: 40upx;"></span>
						</view>
						<text @tap="toLike(item.id)"> {{item.likes}}</text>
					</view>
				</view>
			</block>
		</view>
		<view class="videoPlay" v-if="isPlay">
			<view class="videoPlay-bg" @tap="isPlay=false">
				<view class="videoPlay-close" @tap="isPlay=true">
					<i class="cuIcon-close"></i>
				</view>
			</view>
			<video :src="curVideo" http-cache="true" @play="playVedio(videoid)" loop autoplay
				@fullscreenchange="screenchange" :id="videoid" :title="mp4title"></video>
		</view>
	</view>
</template>

<script>
	//#ifdef MP-QQ
	import TnLazyLoad from 'tuniao-ui/components/tn-lazy-load/tn-lazy-load';
	// #endif
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	import medalItem from './medalItem.vue'
	export default {
		components: {
			medalItem
		},
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
			myPurview: {
				type: Number,
				default: 0
			},
			mySelf: {
				type: Boolean,
				default: false
			},

		},
		name: "forumItem",

		data() {
			return {
				// danmuList: [{
				// 		text: '第 1s 出现的弹幕',
				// 		time: 1
				// 	},
				// 	{
				// 		text: '第 3s 出现的弹幕',
				// 		time: 3
				// 	}
				// ],
				vipImg: this.$API.SPvip(),
				lvImg: this.$API.SPLv(),
				show: false,
				isProcessing: false,
				rzImg: this.$API.SPRz(),
				identifyCompany: 0,
				frameUrl: "",
				frameUid: "",
				AvatarItem: "",
				fanstey_avatarframe: false,
				mp4title: "帖子包含的视频",
				mp4bt: "",
				mp4name: "",
				videoid: 0,
				isPlay: false,
				avatar: "",
				curVideo: "",
				identifyConsumer: 0,
				isIos: false,
				Rz: false,
				xqyFrameCache: {},
				frameCache: {},
				requestQueue: [],
				processingQueue: false
			};
		},
		mounted() {
			const that = this;
			uni.getSystemInfo({
				success: (res) => {
					if (res.platform == 'ios') {
						that.isIos = true;
					}
				}
			});
			that.avatar = "background-image:url(" + that.item.userJson.avatar + ");"
			if (localStorage.getItem('userinfo')) {

				that.userinfo = JSON.parse(localStorage.getItem('userinfo'));
			} else {
				that.userinfo = null;
				}
			// 获取已开启的插件列表 
			var cachedPlugins = localStorage.getItem('getPlugins');
				if (cachedPlugins) {
					const pluginList = JSON.parse(cachedPlugins);
				that.fanstey_avatarframe = pluginList.includes('xqy_avatar_frame');
				if (that.fanstey_avatarframe && that.item.userJson.uid) {
					that.loadUserFrame(that.item.userJson.uid);
					}
			}
			// 确保安全访问缓存
			try {
				// 初始化缓存
				if (typeof uni !== 'undefined' && uni.getStorageSync) {
					const cacheData = uni.getStorageSync('xqyFrameCache');
					if (cacheData) {
						that.xqyFrameCache = JSON.parse(cacheData);
					}
				}
			} catch (err) {
				console.error('初始化头像框缓存失败:', err);
				that.xqyFrameCache = {};
			}
		},
		watch: {
			'item.userJson.uid': {
				handler(newVal, oldVal) {
					if (newVal && newVal !== oldVal) {
						this.frameUrl = "";
						this.frameUid = "";
						if (this.fanstey_avatarframe) {
							this.loadUserFrame(newVal);
						}
					}
				},
				immediate: false
			}
		},
		methods: {
			processFrameRequestQueue() {
				if (this.processingQueue || this.requestQueue.length === 0) return;
				
				this.processingQueue = true;
				const nextUid = this.requestQueue.shift();
				
				// 检查缓存
				const cacheKey = `avatar_frame_${nextUid}`;
				const cachedData = localStorage.getItem(cacheKey);
				
				if (cachedData) {
					try {
						const data = JSON.parse(cachedData);
						if (data.timestamp && (Date.now() - data.timestamp < 5 * 60 * 1000)) {
							this.frameUrl = data.frameUrl || '';
							this.frameUid = nextUid;
							this.processingQueue = false;
							this.$nextTick(() => this.processFrameRequestQueue());
							return;
						}
					} catch (e) {
						// 缓存解析错误，继续获取新数据
					}
				}
				
				// 发起请求
				this.$Net.request({
					url: this.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": nextUid
					},
					method: "post",
					dataType: 'json',
					success: (res) => {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame) {
								this.frameUrl = wearingFrame.frame_url;
								this.frameUid = nextUid;
								
								// 缓存结果
								localStorage.setItem(cacheKey, JSON.stringify({
									frameUrl: wearingFrame.frame_url,
									timestamp: Date.now()
								}));
							} else {
								// 缓存空结果
								localStorage.setItem(cacheKey, JSON.stringify({
									frameUrl: '',
									timestamp: Date.now()
								}));
							}
						}
					},
					complete: () => {
						this.processingQueue = false;
						this.$nextTick(() => this.processFrameRequestQueue());
					}
				});
			},
			loadUserFrame(uid) {
				if (!uid) return;
				
				// 添加到请求队列
				if (!this.requestQueue.includes(uid)) {
					this.requestQueue.push(uid);
					this.processFrameRequestQueue();
				}
			},
			goPlay(url, title, name, id) {
				var that = this;
				that.curVideo = url;
				that.mp4bt = title;
				that.mp4name = name;
				that.mp4title = that.mp4bt + ' - ' + that.mp4name + ' | ' + that.$API.GetAppName();
				that.videoid = id
				that.isPlay = true;

			},

			subText(text) {
				text = text.replace(/vip(.*?)\/vip/g, "(该内容仅会员可见)");
				text = text.replace(/audio(.*?)\/audio/g, "(该帖子包含音乐)");
				text = text.replace(/video(.*?)\/video/g, "(该帖子包含视频)");
				return text;
			},
			goSection(id) {
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/home?id=' + id
				});
			},
			playVedio(id) {
				this.videoContext = uni.createVideoContext(String(id), this);
				uni.getSystemInfo({
					success: (res) => {
						if (res.platform !== 'ios') {
							this.videoContext.requestFullScreen();
							this.videoContext.play();
						} else {
							//ios避免自动重复执行
							if (this.isProcessing) {
								return;
							}

							this.isProcessing = true;
							this.videoContext.requestFullScreen();
							setTimeout(() => {
								this.videoContext.play();
							}, 500);
							setTimeout(() => {
								this.isProcessing = false;
							}, 800);
							// #ifdef APP-PLUS
							plus.screen.lockOrientation('landscape-primary');
							// #endif

						}
					}
				});

			},
			screenchange(e) {
				if (!e.detail.fullScreen) {
					this.videoContext.stop()
					this.isPlay = false
					setTimeout(() => {
						if (this.isIos) {
							// #ifdef APP-PLUS
							plus.screen.lockOrientation('portrait-primary');
							// #endif
						}
					}, 500);

				}
			},
			getLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var lv = that.$API.getLever(i);
				return lv;
			},
			follow(type, uid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					that.show = false
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				that.item.isFollow = type;
				var data = {
					token: token,
					touid: uid,
					type: type,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.follow(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						that.show = false
						if (res.data.code == 1) {
							if (type == 1) {
								if (localStorage.getItem('userinfo')) {
									that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
									that.uid = that.userInfo.uid;

									uni.request({
										url: that.$API.SPguanzhu(),
										method: 'GET',
										data: {
											uid: that.uid,
										},
										dataType: "json",
										success(res) {},
										fail() {
											setTimeout(function() {
												uni.hideLoading();
											}, 1000);
											uni.showToast({
												title: "网络不太好哦",
												icon: 'none'
											})
										}


									})
								}
							} else {
								if (localStorage.getItem('userinfo')) {
									that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
									that.uid = that.userInfo.uid;

									uni.request({
										url: that.$API.SPquguan(),
										method: 'GET',
										data: {
											uid: that.uid,
										},
										dataType: "json",
										success(res) {},
										fail() {
											setTimeout(function() {
												uni.hideLoading();
											}, 1000);
											uni.showToast({
												title: "网络不太好哦",
												icon: 'none'
											})
										}


									})
								}
							}
							that.item.isFollow = type;
						} else {
							that.item.isFollow = 0;
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			toLike(id) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				if (that.item.isLikes == 1) {
					uni.showToast({
						title: "你已经点赞过了",
						icon: 'none'
					});
					return false;
				} else {
					that.item.isLikes = 1;
				}

				that.item.likes += 1;
				var data = {
					token: token,
					id: id,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.postLikes(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 0) {
							that.item.isLikes = 1;
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			toSwiper(id, type) {
				var that = this;

				var typeText = "确定要添加帖子轮播吗？";
				if (type == 0) {
					typeText = "确定要取消帖子轮播吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							that.item.isswiper = type;
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postSwiper(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 0) {
										that.item.isswiper = 0;
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toTop(id, type) {
				var that = this;

				var typeText = "确定要置顶帖子吗？";
				if (type == 0) {
					typeText = "确定要取消置顶帖子吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							that.item.isTop = type;
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postTop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									that.show = false
									if (res.data.code == 0) {
										that.item.isTop = 0;

									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toLock(id, type) {
				var that = this;

				var typeText = "确定要锁定帖子吗？";
				if (type == 1) {
					typeText = "确定要取消锁定帖子吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {

							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postLock(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 1) {
										that.item.status = type;
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toRecommend(id, type) {
				var that = this;

				var typeText = "确定要加精帖子吗？";
				if (type == 0) {
					typeText = "确定要取消帖子加精吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							that.item.isrecommend = type;
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postRecommend(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 0) {
										that.item.isrecommend = 0;
										that.show = false
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toDelete(id) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"token": token
				}
				uni.showModal({
					title: '确定要删除该文章吗',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postDelete(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 1) {
										uni.request({
											url: that.$API.SPglyremove(),
											method: 'GET',
											data: {
												id: id,
											},
											dataType: "json",
											success(res) {},
											fail() {
												setTimeout(function() {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}


										})
										that.page = 1;
										that.moreText = "加载更多";
										that.isLoad = 0;
										that.getPostList();
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toBan(uid) {
				if (uid == 0) {
					uni.showToast({
						title: "该用户不存在",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/manage/banuser?uid=' + uid
				});
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var now = new Date();
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);

				var timeDiff = now - datetime;
				var seconds = Math.floor(timeDiff / 1000);
				var minutes = Math.floor(timeDiff / (1000 * 60));
				var hours = Math.floor(timeDiff / (1000 * 60 * 60));
				var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
				var months = Math.floor(days / 30);

				var result = "";

				if (seconds < 60) {
					result = seconds + "秒前";
				} else if (minutes < 60) {
					result = minutes + "分钟前";
				} else if (hours < 24) {
					result = hours + "小时前";
				} else if (days < 7) {
					result = days + "天前";
				} else if (year === now.getFullYear()) {
					result = month + "月" + date + "日";
				} else {
					result = year + "年" + month + "月" + date + "日";
				}

				return result;
			},

			getLocal(local) {
				var that = this;
				if (local && local != '') {
					var local_arr = local.split("|");
					if (!local_arr[3] || local_arr[3] == 0) {
						return local_arr[2];
					} else {
						return local_arr[3];
					}

				} else {
					return "未知"
				}

			},
			previewImage(imageList, image) {
				console.log(imageList);
				//预览图片
				uni.previewImage({
					urls: imageList,
					current: image
				});
			},
			goModerators(id) {
				var that = this;
				uni.redirectTo({
					url: '/pages/forum/moderators?id=' + id
				});
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			goInfo(id) {
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/info?id=' + id
				});
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			toUserContents(data) {
				var that = this;
				var name = data.name;
				var title = data.name + "的信息";
				var id = data.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
			//加载勋章
			onMedalLoaded(medals) {
			}
		}
	}
</script>
<style lang="scss" scoped>
	@import "@/static/styles/home.scss";

	.forum-avatar {
		position: relative;
		border-radius: 50%;
		display: inline-block;
		background-size: contain;
		background-repeat: no-repeat;
	}

	.forum-avatar image {
		width: 100upx !important;
		height: 100upx;
	}

	/* 文章内容 start*/
	.blogger {
		&__item {
			padding: 30rpx;
		}

		&__author {
			&__btn {
				margin-right: -12rpx;
				padding: 0 20rpx;
			}
		}

		&__desc {
			line-height: 55rpx;

			&__label {
				padding: 0 20rpx;
				margin: 0rpx 18rpx 0 0;

				&--prefix {
					color: #00FFC8;
					padding-right: 10rpx;
				}
			}

			&__content {}
		}

		&__content {
			margin-top: 18rpx;
			padding-right: 18rpx;

			&__data {
				line-height: 46rpx;
				text-align: justify;
				overflow: hidden;
				transition: all 0.25s ease-in-out;

			}

			&__status {
				margin-top: 10rpx;
				font-size: 26rpx;
				color: #82B2FF;
			}
		}

		&__main-image {
			border-radius: 16rpx;

			&--1 {
				max-width: 100%;
				max-height: 360rpx;
			}

			&--2 {
				max-width: 260rpx;
				max-height: 260rpx;
			}

			&--3 {
				height: 212rpx;
				width: 100%;
			}
		}

		&__count-icon {
			font-size: 40rpx;
			padding-right: 5rpx;
		}

		&__ad {
			width: 100%;
			height: 500rpx;
			transform: translate3d(0px, 0px, 0px) !important;

			::v-deep .uni-swiper-slide-frame {
				transform: translate3d(0px, 0px, 0px) !important;
			}

			.uni-swiper-slide-frame {
				transform: translate3d(0px, 0px, 0px) !important;
			}

			&__item {
				position: absolute;
				width: 100%;
				height: 100%;
				transform-origin: left center;
				transform: translate3d(100%, 0px, 0px) scale(1) !important;
				transition: transform 0.25s ease-in-out;
				z-index: 1;

				&--0 {
					transform: translate3d(0%, 0px, 0px) scale(1) !important;
					z-index: 4;
				}

				&--1 {
					transform: translate3d(13%, 0px, 0px) scale(0.9) !important;
					z-index: 3;
				}

				&--2 {
					transform: translate3d(26%, 0px, 0px) scale(0.8) !important;
					z-index: 2;
				}
			}

			&__content {
				border-radius: 40rpx;
				width: 640rpx;
				height: 500rpx;
				overflow: hidden;
			}

			&__image {
				width: 100%;
				height: 100%;
			}
		}
	}

	/* 文章内容 end*/
	.tn-margin-top-xxl {
		margin-top: 60upx;
	}

	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}


	.text-content-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.text-content-2 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.extra-count {
		position: absolute;
		background-color: #0000005c;
		color: white;
		z-index: 100;
		font-size: 20px;
		font-weight: bold;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.user-rz-qz {
		position: relative;
	}

	.user-rz-icon-qz {
		position: absolute;
		left: 58upx;
		bottom: -6upx;
		width: 36upx;
		height: 36upx;
	}

	.forum-shadow {
		border-radius: 20rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.075);
	}

	.sy-text-lg {
		font-size: 30upx;
	}

	.radius-q {
		border-radius: 20upx 0 0 0;
	}

	.radius-p {
		border-radius: 0 20upx 0 0;
	}

	.radius-m {
		border-radius: 0 0 20upx 0;
	}

	.radius-z {
		border-radius: 0 0 0 20upx;
	}

	.radius-qz {
		border-radius: 20upx 0 0 20upx;
	}

	.radius-pm {
		border-radius: 0 20upx 20upx 0;
	}

	.spaceVideo-play {
		position: relative;
		border-radius: 20px;
		z-index: 1;
	}

	.spaceVideo-play::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 20px;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1;
	}

	.cuIcon-playfill {
		position: relative;
		z-index: 2;
	}
</style>