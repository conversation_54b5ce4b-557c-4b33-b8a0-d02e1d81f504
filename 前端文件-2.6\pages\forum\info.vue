<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :class="isDark?'dark':'bg-white'" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px','background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action">
					<text class="cuIcon-back" @tap="back"></text>
					<!--  #ifdef MP-WEIXIN -->
					<text class="cuIcon-moreandroid" @click="popup(),commentsAdd = false"></text>
					<!--  #endif -->
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					<view class="section-sub" style="line-height: 30px;">
						<text style="font-size:30upx">帖子详情</text>
					</view>

				</view>
				<view class="action info-btn">
					<!--  #ifdef H5 || APP-PLUS -->

					<text class="cuIcon-moreandroid" @click="popup(),commentsAdd = false"></text>
					<!--  #endif -->
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>

		<tn-popup v-model="show" mode="bottom" :zIndex="500" :closeBtn="true" :height="myPurview>0?'50%':'20%'"
			:borderRadius="20">
			<view class="center-container tn-margin-top-xxl">
				<view class="">
					<block v-if="authorId!=uid">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="toJb(title)">
							<text class="tn-icon-warning" style="margin-right: 5px;"></text>举报帖子
						</view>

						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="isFollow==0"
							@tap="follow(1,userJson.uid)">
							<text class="tn-icon-my-add" style="margin-right: 5px;"></text>立即关注
						</view>
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="isFollow==1"
							@tap="follow(0,userJson.uid)">
							<text class="tn-icon-my-reduce" style="margin-right: 5px;"></text>取消关注
						</view>
					</block>
					<!-- <view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
						@tap="toLink('../space/post?type=6&toid='+id)">
						<text class="tn-icon-send" style="margin-right: 5px;"></text>转发帖子
					</view> -->
					<block v-if="authorId==uid||myPurview >= 1">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" @tap="toEdit(id)">
							<text class="tn-icon-edit-write" style="margin-right: 5px;"></text>编辑帖子
						</view>
					</block>
					<block v-if="authorId==uid||myPurview >= 2">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="toDelete(id)">
							<text class="tn-icon-delete" style="margin-right: 5px;"></text>删除帖子
						</view>
					</block>
					<block v-if="myPurview > 0">
						<block v-if="myPurview >= 3">
							<block v-if="isrecommend==0">
								<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toRecommend(id,1)">
									<text class="tn-icon-fire" style="margin-right: 5px;"></text>帖子加精
								</view>
							</block>
							<block v-if="isrecommend==1">
								<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toRecommend(id,0)">
									<text class="tn-icon-fire-fill" style="margin-right: 5px;"></text>取消加精
								</view>
							</block>
							<block v-if="isTop==0">
								<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toTop(id,1)">
									<text class="tn-icon-pushpin" style="margin-right: 5px;"></text>添加板块置顶
								</view>
							</block>
							<block v-if="isTop==0">
								<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toTop(id,2)">
									<text class="tn-icon-moments" style="margin-right: 5px;"></text>添加全局置顶
								</view>
							</block>
							<block v-if="isTop==1||isTop==2">
								<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toTop(id,0)">
									<text class="tn-icon-pushpin-fill" style="margin-right: 5px;"></text>取消置顶
								</view>
							</block>

							<block v-if="isswiper==0">
								<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toSwiper(id,1)">
									<text class="tn-icon-task" style="margin-right: 5px;"></text>设为轮播
								</view>
							</block>
							<block v-if="isswiper==1">
								<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toSwiper(id,0)">
									<text class="tn-icon-task-fill" style="margin-right: 5px;"></text>取消轮播
								</view>
							</block>
						</block>
						<block v-if="myPurview == 5">
							<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
								@tap="toBan(userJson.uid)">
								<text class="tn-icon-prohibit" style="margin-right: 5px;"></text>封禁用户
							</view>
						</block>
						<block v-if="status==1">
							<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
								@tap="toLock(id,2)">
								<text class="tn-icon-lock" style="margin-right: 5px;"></text>锁定帖子
							</view>
						</block>
						<block v-if="status==2">
							<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
								@tap="toLock(id,1)">
								<text class="tn-icon-unlock" style="margin-right: 5px;"></text>解除锁定
							</view>
						</block>
					</block>
				</view>
			</view>
		</tn-popup>
		<view style="margin-top: 35upx;margin-left: 15upx;margin-right: 15upx;">
			<view class="info">
				<view>
					<view class="info-title" style="border-radius: 20upx 20upx 0 0;">
						{{title}}
					</view>
					<view class="forum-author" style="position: relative;">
							
						<view class="forum-list-user" @tap="toUserInfo(userJson)">
							<view class="forum-avatar tn-margin-bottom-sm user-rz user-avatar-container"
							style="background-size: 100% 100%;border-radius: 50%;position: relative;"
							:style="{ backgroundImage: 'url(' + userJson.avatar + ')' }">
							<!-- 头像框 -->
							<image v-if="frameUrl" class="avatar-frame" :src="frameUrl" mode="aspectFit" @error="onFrameLoadError"></image>
							<image class="user-rz-icon" width="34upx" height="34upx" :src="rzImg" mode="aspectFill"
								v-if="lvrz==1"></image>
						</view>
							<view class="forum-userinfo">
								<view class="forum-userinfo-name">
									<text :class="userJson.isvip>0?'name-vip':''">
{{userJson.name || "用户已注销"}}
										<image v-if="userJson.isvip>0" :src="vipImg"
											style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;"
											mode="widthFix"></image>

										<image :src="lvImg+getLv(userJson.experience)+'.png'"
											style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 45upx;height: 22upx;"
											mode="widthFix"></image>
										<!-- 勋章组件 -->
										<medal-item :uid="userJson.uid" @medal-loaded="onMedalLoaded"></medal-item>
									</text>

								</view>
								<view class="forum-userinfo-date">
									{{formatDate(created)}} <text
										class="margin-left-sm" v-if="$API.localOf()">{{getLocal(userJson.local)}}</text>
								</view>
								<!-- <view class="cu-btn xs forum-follow" isFollow>
							<text class="cuIcon-add"></text>
							关注
						</view> -->
							</view>
						</view>
					</view>
					<image class="info-jh" src="../../static/page/001.gif" v-if="isrecommend==1"></image>
				</view>
				<view class="info-content">
					<block v-if="token">
						<block v-if="isforumLoading">
							加载中...
						</block>
						<block v-else>
							<block v-if="sectionInfo.slug == 'vip' && isvip <= 0">
								<view class="no-data">
									<text class="cuIcon-text"></text>
									该帖子仅VIP才能查看
									<view class="text-center margin-top-sm">
										<text class="cu-btn bg-blue margin-left-sm radius" style="border-radius: 50px;"
											@tap="gobuyvip()">立即开通</text>
									</view>
								</view>
							</block>
							<block v-else-if="sectionInfo.slug == 'lv4' && experience < 200">
								<view class="no-data">
									<text class="cuIcon-text"></text>
									该帖子需达到4级才能查看
									<view class="text-center margin-top-sm">
										<text class="cu-btn bg-blue margin-left-sm radius" style="border-radius: 50px;"
											@tap="gorenwu()">做任务升级</text>
									</view>
								</view>
							</block>
							<block v-else>
								<mp-html :content="html" :selectable="true" :show-img-menu="true" :scroll-table="true"
									:markdown="isMd == 1" :lazyLoad="false" />
							</block>
						</block>
					</block>

					<block v-else>
						<block v-if="sectionInfo.slug == 'vip' || sectionInfo.slug == 'lv4'">
							<view class="no-data">
								<text class="cuIcon-community"></text>
								请先登录哦！
								<view class="text-center margin-top-sm">
									<text class="cu-btn bg-blue radius" style="border-radius: 50px;"
										@tap="goLogin()">登录</text>
									<text class="cu-btn bg-blue margin-left-sm" style="border-radius: 50px;"
										@tap="goRegister()">注册</text>
								</view>
							</view>
						</block>
						<block v-else>
							<mp-html :content="html" :selectable="true" :show-img-menu="true" :scroll-table="true"
								:markdown="isMd == 1" :lazyLoad="false" />
						</block>
					</block>

					<view class="shop-value" v-if="shopValue!=''">
						<view class="shop-value-title">
							付费内容
						</view>
						<block v-if="shopIsMd==1">
							<mp-html :content="shopValue" :selectable="true" :show-img-menu="true" :scroll-table="true"
								:markdown="true" />
						</block>
						<block v-if="shopIsMd==0">
							<mp-html :content="shopValue" :selectable="true" :show-img-menu="true" :scroll-table="true"
								:markdown="false" />
						</block>

					</view>
					<view class="content-shop text-center" v-if="shopValue==''">
						<view class="cu-card article no-card" v-for="(item,index) in shopList" :key="index">
							<!--付费阅读-->
							<block v-if="item.type==4">
								<view class="text-left">
									<text class="left-title"></text>付费后查看
								</view>
								<view class="tool-price" v-if="isBuy==0">
									<text class="text-red text-bold">{{item.price}} {{currencyName}}</text><text
										class="margin-left-sm text-sm">会员价</text><text
										class="text-yellow text-bold">{{parseInt(item.price * item.vipDiscount)}}{{currencyName}}</text>
								</view>
								<br />
								<view class="tool-price" v-if="isBuy==1" @tap="toShopValue(item.id,item.type)">
									<text class="cu-btn bg-blue" style="border-radius: 50upx;">查看订单详情</text>
								</view>
								<view class="tool-price" v-if="isBuy==0" @tap="shopBuy(item.id,item.type)">
									<text class="cu-btn bg-blue" style="border-radius: 50upx;">立即购买</text>
								</view>
							</block>
						</view>
					</view>
				</view>

				<view class="reward-log" v-if="rewardLog.length > 0&&dsstyle==1">
					<view class="reward-log-main">
						<tn-avatar-group :lists="rewardLog" :dsof="true" :badge="true" badgeBgColor="white" txNum="7"
							badgeColor="#333" :rewardTotal="rewardTotal"></tn-avatar-group>
					</view>
					<view class="reward-log-btn" @tap="goReward(id)">
						<text class="cuIcon-more"></text>
					</view>

				</view>
				<view class="bg-white flex justify-center tn-padding-top-lg tn-padding-bottom-lg"
					style="border-radius: 0 0 20upx 20upx;">
					<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
						<view class="section-sub cu-btn" @click="goSection(sectionInfo.id)">
							<block v-if="sectionInfo.pic&&sectionInfo.pic!=''">
								<image class="section-ico" :src="sectionInfo.pic">{{sectionInfo.name}}</image>
							</block>
							<block v-else>
								<image class="section-ico bg-blue">{{sectionInfo.name}}</image>
							</block>

						</view>
						

					</view>
				</view>
				<view class="ads-banner tn-margin-top-sm tn-margin-bottom-sm" style="border-radius:20upx;"
					v-if="bannerAdsInfo!=null">
					<image :src="bannerAdsInfo.img" mode="widthFix" @tap="goAds(bannerAdsInfo)"></image>
				</view>
				<view class="data-box" style="border-radius: 20upx;">
					<view class="cu-bar bg-white" style="border-radius: 20upx 20upx 0 0;">
						<view class="action data-box-title" @click="comPx = true">
							<text v-if="isOnlyUser==0">全部评论</text>
							<text v-if="isOnlyUser==1">只看楼主</text>
							<text class="tn-icon-down"
								style="margin-left: 10upx;font-size: 20upx;font-weight: bold;"></text>
						</view>

						<tn-popup v-model="comPx" mode="bottom" :zIndex="500" :closeBtn="true" height="20%"
							:borderRadius="20">
							<view class="center-container tn-margin-top-xxl">
								<view class="">
									<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
										@tap="setOnlyUser(0)" :class="isOnlyUser==0?'comPxOn':'comPxOff'">
										全部评论<text class="tn-icon-success-circle" style="margin-left: 5px;"
											v-if="isOnlyUser==0"></text>
									</view>
									<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
										@tap="setOnlyUser(1)" :class="isOnlyUser==1?'comPxOn':'comPxOff'">
										只看楼主<text class="tn-icon-success-circle" style="margin-left: 5px;"
											v-if="isOnlyUser==1"></text>
									</view>
								</view>
							</view>
						</tn-popup>
						<view class="comNum-right" v-if="commentsNum>0">
							共<text>{{formatNumber(commentsNum)}}条</text>
						</view>
					</view>
					<view class="no-data" v-if="commentsList.length==0">
						快来抢沙发吧！
					</view>
					<view class="cu-card dynamic no-card info-comment"
						style="margin-top: 20upx;border-radius: 0 0 20upx 20upx;">
						<block v-for="(item,index) in commentsList" :key="index" v-if="commentsList.length>0">
							<postCommentItem :item="item" :isContent="true" @coAdd="showCommentsAdd" @coDel="regetComm"></postCommentItem>
						</block>
					</view>

					<!-- <view class="load-more" @tap="loadMore" v-if="commentsList.length>0">
					<text>{{moreText}}</text>
				</view> -->
					<view style="height: 100upx"></view>
				</view>
			</view>
		</view>
		<view class="info-operate-bg" :class="isShare?'show':''" @tap="isShare=false"></view>
		<view class="info-operate" :class="isShare?'show':''">
			<view class="info-operate-main grid col-2">
				<view class="index-sort-box">
					<view class="index-sort-main" @tap="copyShare">
						<view class="index-sort-i" style="background: rgb(158 158 158 / 20%)">
							<text class="tn-icon-link" style="color:  rgba(19, 19, 19);"></text>
						</view>
						<view class="index-sort-text">
							复制帖子链接
						</view>
					</view>
				</view>
				<view class="index-sort-box">
					<view class="index-sort-main" @tap="ToShare">
						<view class="index-sort-i" style="background: rgb(158 158 158 / 20%)">
							<text class="tn-icon-share-triangle" style="color: rgba(19, 19, 19);"></text>
						</view>
						<view class="index-sort-text">
							分享到其他应用
						</view>
					</view>
				</view>
			</view>


		</view>
		<!-- #ifdef APP-PLUS || H5 -->
		
		<u-popup v-model="commentsAdd" mode="bottom" height="64%" z-index="999" border-radius="40">
			<view>
				<form>
					<view class="cu-form-group">
						<textarea maxlength="5000" :adjust-position="false" :placeholder="coTitle" v-model="pltext"
							placeholder-class="textarea-placeholder"></textarea>
					</view>
					<view class="pl-tool-box">
						<view class="pl-tool-box-2">
							<view class="pl-tool">
								<text class="tn-icon-image" @tap="upimgf()"></text>
							</view>
						</view>
						<view>
							<tn-button @tap="commentsadd" blockTime class="pl-btn" height="54rpx" padding="0 22rpx" :plain="true"
								:border="true" shape="round" :backgroundColor="isDark?'#2c2c2c':'none'" fontColor="#525252">
								发送
							</tn-button>
						</view>
						
					</view>
					<view style="padding: 10rpx 30rpx;border-top: 1px solid #eee;">
						<block v-if="upimg">
						 <u-upload
						      ref="uUpload"
							  del-bg-color="#00000080"
							   :show-progress="true"
						      :action="uploadUrl" 
						      :form-data="{token: token}" 
						      :max-count="9" 
						      :auto-upload="true" 
						      :before-upload="beforeUpload" 
						      @on-success="handleSuccess"
							  @on-remove="handleRemove"
							  @on-uploaded="alluploaded"
							  @on-choose-complete="choosecomplet"
						    />
						</block>
					</view>
				</form>
				<!-- 添加支撑元素 -->
				<view :style="{'background-color':isDark?'#2c2c2c':'#f6f6f6', 'height':'900rpx'}"></view>
			</view>
		</u-popup>
		<!-- #endif -->
		<view class="info-footer grid col-2" :style="{'padding-bottom': paddingBottomHeight + 'upx'}">
			<view class="info-footer-input" :style="shareof!=1&&dsof!=1?'width: 60%;':'width: 50%;'">
				<!-- #ifdef APP-PLUS || H5 -->
				<view class="info-input-box" @tap="showCommentsAdd('pl')">
					善语结善缘, 恶言伤人心
				</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="info-input-box" @tap="commentsAddMp(title,0,0,uid)">
					善语结善缘, 恶言伤人心
				</view>
				<!-- #endif -->
				
			</view>
			<view class="info-footer-btn" :style="shareof!=1&&dsof!=1?'width: 40%;':'width: 50%;'"
				style="display: flex;justify-content: space-around;">
				<span class="user-rz" v-if="dsof!=1||shareof!=1"><text class="tn-icon-eye"></text><text class="foot-num"
						v-if="views>0">{{formatNumber(views)}}</text></span>
				<!-- #ifdef APP-PLUS || H5 -->
				<span class="user-rz"><text class="tn-icon-comment" @tap="showCommentsAdd('pl')"></text><text
						class="foot-num" v-if="commentsNum>0">{{formatNumber(commentsNum)}}</text></span>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<span class="user-rz"><text class="tn-icon-comment" @tap="commentsAddMp(title,0,0,uid)"></text><text
						class="foot-num" v-if="commentsNum>0">{{formatNumber(commentsNum)}}</text></span>
				<!-- #endif -->
				
				<span class="user-rz" v-if="isLikes==0"><text class="tn-icon-praise" @tap="toLike(id)"></text><text
						class="foot-num" v-if="likes>0">{{formatNumber(likes)}}</text></span>
				<span class="user-rz" v-else><text class="tn-icon-praise-fill text-blue" @tap="toLike(id)"></text><text
						class="foot-num" v-if="likes>0">{{formatNumber(likes)}}</text></span>
				<text class="tn-icon-star" @tap="toMark(1),isMark=1" v-if="isMark==0"></text>
				<text class="tn-icon-star-fill text-orange" @tap="toMark(0),isMark=0" v-else></text>
				<!-- <text class="cuIcon-recharge"  @tap="toReward"></text> -->
				<text class="tn-icon-money" v-if="dsof==1" @tap="dsShow=true"></text>
				<text class="tn-icon-share-circle" v-if="shareof==1" @tap="isShare=!isShare"></text>
			</view>
		</view>
		<tn-popup v-model="dsShow" mode="bottom" :zIndex="500" :closeBtn="true" height="25%" :borderRadius="20">
			<view class="grid col-3 padding-sm tn-margin-top-xxl"
				style="display: flex;justify-content: center;margin-top: 40rpx;">
				<view v-for="(item,index) in checkbox" class="padding-xs" :key="index">
					<button class="cu-btn cyan lg block" :class="item.checked?'bg-cyan':'line-cyan'"
						@tap="ChooseCheckbox(index)"> {{item.num}}{{currencyName}}
						<view class="cu-tag sm round" :class="item.checked?'bg-white text-cyan':'bg-cyan'"
							v-if="item.hot">HOT</view>
					</button>
				</view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button :backgroundColor="isDark?'#2c2c2c':'#1cbbb4'" style="padding: 0 60upx;" fontColor="#fff"
						@tap="toReward(),dsShow=false">立即打赏</tn-button>
				</view>
			</view>
		</tn-popup>
		<view class="cu-modal" style="z-index: 1999;" :class="modalName=='kaptcha'?'show':''">
			<view class="cu-dialog kaptcha-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">操作验证</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>
				<view class="kaptcha-form">
					<view class="kaptcha-image">
						<image :src="kaptchaUrl" mode="widthFix" @tap="reloadCode()"></image>
					</view>
					<view class="kaptcha-input">
						<input name="input" v-model="verifyCode" placeholder="请输入验证码"></input>
						<view class="cu-btn bg-blue" @tap="commentsadd">确定</view>
					</view>
				</view>
			</view>
		</view>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
		<view class="full-noLogin" v-if="isuserlogin">
			<view class="full-noLogin-main">
				<view class="full-noLogin-text">
					您需要登录后才可以查看内容哦！
				</view>
				<view class="full-noLogin-btn">
					<view class="cu-btn bg-blue" @tap="goLogin()">
						立即登录
					</view>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
	import mpHtml from '@/components/mp-html/mp-html'
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import medalItem from '../components/medalItem.vue'
	// #ifdef APP-PLUS
	import owo from '../../static/app-plus/owo/OwO.js'
	// #endif
	// #ifdef H5
	import owo from '../../static/h5/owo/OwO.js'
	// #endif
	// #ifdef MP
	var owo = [];
	// #endif
	export default {
		mixins: [darkModeMixin],
		components: {
			mpHtml,
			medalItem
		},
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				paddingBottomHeight: 0, //苹果X以上手机底部适配高度
				userMedals: null, // 用户勋章数据
				id: 0,
				isuserlogin: false,
				title: "",
				html: "",
				dsShow: false,
				status: 1,
				token: "",
				isrecommend: 0,
				topicList: [],
				comPx: false,
				isTop: 0,
				isswiper: 0,
				uid: 0,
				isMd: 0,
				uploaded: false,
				submitStatus1: false,
				vipImg: this.$API.SPvip(),
				commentsList: [],
				moreText: "加载更多",
				page: 1,
				shareof: 0,
				dsstyle: 1,
				sectionId: 0,
				dsof: 0,
				isLoad: 0,
				created: 0,
				currencyName: "",
				isComment: 0,
				isMark: 0,
				isLikes: 0,
				isShare: false,
				isShare: false,
				modalName: null,
				commentsNum: 0,
				AvatarItem: "",
				authorId: null,
				userJson: {
					"name": "未知用户",
					"avatar": null,
				},
				sectionInfo: {
					"sulg": "null",
					"name": "未知圈子",
					"pic": "",
				},
				checkbox: [{
					value: 0,
					name: '5金币',
					num: 5,
					checked: false,
					hot: false,
				}, {
					value: 1,
					name: '10金币',
					num: 10,
					checked: false,
					hot: false,
				}, {
					value: 2,
					name: '30金币',
					num: 30,
					checked: false,
					hot: false,
				}, {
					value: 3,
					name: '50金币',
					num: 50,
					checked: false,
					hot: false,
				}, {
					value: 4,
					name: '100金币',
					num: 100,
					checked: false,
					hot: false,
				}, {
					value: 5,
					name: '200金币',
					num: 200,
					checked: false,
					hot: false,
				}],

				rewardLog: [],
				rewardTotal: 0,
				rzImg: this.$API.SPRz(),
				lvImg: this.$API.SPLv(),
				isOnlyUser: 0,
				show: false,
				isvip: 0,
				experience: 0,
				isFollow: 0,
				frameUrl: null, // 用户头像框URL
				fanstey_avatarframe: false, // 头像框插件状态

				isLoading: 0,

				myPurview: 0,
				isforumLoading: 1,
				bannerAdsInfo: null,
				likes: 0,
				views: 0,
				shopList: [],
				shopID: 0,
				vipDiscount: 0,
				vipPrice: 0,
				scale: 0,
				lvrz: 0,
				smrz: 0,
				isBuy: 0,
				shopValue: "",
				chooesed: false,
				commentsAdd: false,
				submitStatus: false,
				coid: 0,
				kaptchaUrl: "",
				verifyCode: "",
				verifyLevel: 0,
				pltext: "",
				coTitle: "",
				pic: "",
				upimg:false,
				uploadUrl: this.$API.upload(),
			}
		},
		// #ifdef MP
		onShareAppMessage(res) {
			var that = this;
			var appname = that.$API.GetAppName();
			if (res.from === 'button') {
				// 来自页面内分享按钮
			}
			if (res.from === 'menu') {
				// 来自页面内分享按钮
			}
			var data = {
				title: that.title + ' - ' + appname,
				path: '/page/forum/info?id=' + that.id
			}
			if (that.images.lenght > 0) {
				data.imageUrl = that.images[0];
			}

		},
		onShareTimeline() {
			var that = this;
			var appname = that.$API.GetAppName();
			var data = {
				title: that.title + ' - ' + appname,
				path: '/page/forum/info?id=' + that.id
			}
			if (that.images.lenght > 0) {
				data.imageUrl = that.images[0];
			}

			return data;
		},
		// #endif
		onShow() {
			var that = this;
			that.page = 1;
			if (localStorage.getItem('isEdit')) {
				var isEdit = localStorage.getItem('isEdit');
				if (isEdit == 'true') {
					that.getInfo();
				}
				localStorage.removeItem('isEdit');
			}
		},
		onLoad(res) {
			var that = this;

			uni.getSystemInfo({
				success: function(res) {
					let model = ['X', 'XR', 'XS', '11', '12', '13', '14', '15'];
					console.log("当前设备型号：" + res.model)
					model.forEach(item => {

						//适配iphoneX以上的底部，给tabbar一定高度的padding-bottom
						if (res.model.indexOf(item) != -1 && res.model.indexOf('iPhone') != -1) {
							that.paddingBottomHeight = 40;
						}
					})
				}
			});
			that.userStatus();
			
			if (res.id) {
				that.id = res.id;
				console.log("当前帖子id：" + that.id);
				var cachedPlugins = localStorage.getItem('getPlugins');
				if (cachedPlugins) {
					const pluginList = JSON.parse(cachedPlugins);
					// 检查插件是否存在于插件列表中 
					
				}
				
				that.getInfo();
				that.getIsCommentsList(that.id);
				that.getRewardLog(that.id);
				that.getCommentsList(false, that.id);

			}
			that.getAdsCache();
			if (localStorage.getItem('userinfo')) {
				var userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.uid = userInfo.uid;
			}
			if (localStorage.getItem('token')) {
				that.token = localStorage.getItem('token');
			}
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
		},
		onReachBottom() {
			//触底后执行的方法，比如无限加载之类的
			var that = this;
			that.loadMore();
		},
		mounted() {
			const that = this;
			that.getopset()
			var cachedPlugins = localStorage.getItem('getPlugins');
			if (cachedPlugins) {
				const pluginList = JSON.parse(cachedPlugins);
				// 检查插件是否存在于插件列表中 
				this.fanstey_avatarframe = pluginList.includes('xqy_avatar_frame');
			}
			
		},
		methods: {
			upimgf(){
				var that = this;
				that.upimg = true;
			},
			beforeUpload(index, list) {
			  return true; 
			},
			regetComm(){
				var that = this;
				that.getCommentsList(false, that.id);
			},
			handleRemove(index) {
			  let urls = this.pic.split('||');
			  if (index >= 0 && index < urls.length) {
			    urls.splice(index, 1);
			    
			    this.pic = urls.join('||');
			
			  } else {
			  }
			},
			handleSuccess(data, index, lists) {
			  if (data.code === 1) {
				const url = data.data.url; 
				this.pic += (this.pic ? '||' : '') + url;
			  } else {
				  uni.showToast({
				  	title: data.msg ,
				  	icon: 'none',
				  });
				  console.log(data.msg);
			  }
			},
			alluploaded(lists, name){
				setTimeout(function() {
					uni.hideLoading();
				}, 500);
				uni.showToast({
					title: '上传完成' ,
					icon: 'success',
					duration: 1000,
				});
				this.uploaded = true
			},
			choosecomplet(lists, name){
				this.uploaded = false
				this.chooesed = true
			},
			commentsAddMp(title, coid, reply, uid) {
				var that = this;
 
				if (!localStorage.getItem('userinfo')) {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				} else {
					var id = that.id;
					uni.navigateTo({
						url: '/pages/forum/reply?postid=' + id + "&coid=" + coid + "&title=" + title +
							"&isreply=" + reply + "&uid=" + uid
					});
				}
 
			},
			commentsadd() {
				var that = this;
				if (that.submitStatus) {
					return false;
				}
				that.submitStatus = true;
				if (that.token == "") {
					uni.showToast({
						title: "请先登录",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					var timer = setTimeout(function() {
						uni.navigateTo({
							url: '/pages/user/login'
						});
						clearTimeout('timer')
					}, 1000)
					return false
				}
				if(!that.uploaded&&that.chooesed){
					uni.showToast({
						title: '请等待图片上传完成',
						icon: 'none',
						duration: 1000,
					});
					that.submitStatus = false;
					return false
				}
				if (that.verifyLevel > 1) {
					if (that.verifyCode == "") {
						that.modalName = 'kaptcha'
						return false
					}
				}
				
				if (that.pic === '' && that.pltext.length < 4) {
					uni.showToast({
						title: '文字评论不少于4字',
						icon: 'none',
						duration: 1000,
					});
					that.submitStatus = false;
					that.modalName = null;
					that.verifyCode = "";
					return false
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
			
					url: that.$API.postComments(),
					data: {
						"postid":that.id,
						"token": that.token,
						"text": that.pltext,
						"pic": that.pic,
						"parent":that.coid,
						'verifyCode': that.verifyCode
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.submitStatus = false;
						that.modalName = null;
						that.verifyCode = "";
						that.pltext = "";
						that.upimg = false;
						that.chooesed = false;
						that.uploaded = true;
						that.pic = "";
						that.coid = 0;
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
			
						if (res.data.code == 1) {
							uni.request({
								url:that.$API.SPpinglun(),
								method:'GET',
								data:{
									uid:that.uid,
								},
								dataType:"json",
								success(res) {
								},
								fail() {
									
									setTimeout(function () {
										uni.hideLoading();
									}, 500);
									uni.showToast({
										title: "网络不太好哦",
										icon: 'none'
									})
								}
							})
							that.getCommentsList(false, that.id);
							that.commentsAdd = false;
						}
					},
					fail: function(res) {
						that.submitStatus = false;
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			showCommentsAdd(type, author, coid){
				var that = this;
				if (that.token == "") {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				} 
				if(type=='pl'){
					that.coTitle = "输入评论内容"
					that.commentsAdd = true;
				}
				if(type=='hf'){
					that.commentsAdd = true;
					that.coTitle = "回复：@"+ author
					that.coid = coid
				}
				
			},
			
			getopset() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.currencyName = res.data.assetsname;
						that.dsstyle = res.data.dsstyle;
						that.dsof = res.data.dsof;
						that.shareof = res.data.shareof;
					},
					fail(error) {
						console.log(error);
					}

				})
			},

			getLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var lv = that.$API.getLever(i);
				return lv;
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var now = new Date();
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);

				var timeDiff = now - datetime;
				var seconds = Math.floor(timeDiff / 1000);
				var minutes = Math.floor(timeDiff / (1000 * 60));
				var hours = Math.floor(timeDiff / (1000 * 60 * 60));
				var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
				var months = Math.floor(days / 30);

				var result = "";

				if (seconds < 60) {
					result = seconds + "秒前";
				} else if (minutes < 60) {
					result = minutes + "分钟前";
				} else if (hours < 24) {
					result = hours + "小时前";
				} else if (days < 7) {
					result = days + "天前";
				} else if (year === now.getFullYear()) {
					result = month + "月" + date + "日";
				} else {
					result = year + "年" + month + "月" + date + "日";
				}

				return result;
			},
			
			getAdsCache() {
				var that = this;

				if (localStorage.getItem('bannerAds')) {
					that.bannerAds = JSON.parse(localStorage.getItem('bannerAds'));

					var num = that.bannerAds.length;
					if (num > 0) {
						var rand = Math.floor(Math.random() * num);
						that.bannerAdsInfo = that.bannerAds[rand];
					}
				}
			},
			loadMore() {
				var that = this;
				that.moreText = "正在加载中...";
				if (that.isLoad == 0) {
					that.getCommentsList(true, that.id);
				}

			},
			backHome() {
				uni.redirectTo({
					url: '/pages/home/<USER>'
				});
			},
			back() {
				const pages = getCurrentPages()
				if (pages.length === 1) {
					uni.redirectTo({
						url: '/pages/home/<USER>'
					})
				} else {
					uni.navigateBack({
						delta: 1
					});
				}
			},
			showModal(e) {
				this.modalName = e.currentTarget.dataset.target
			},
			hideModal(e) {
				this.modalName = null
			},
			popup() {
				var that = this;
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				that.show = true;

			},
			markExpand(text) {
				var that = this;
				//视频
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|type=video"[^>]*?>/g,
					(match, src, poster) => {
						return `<div style="border-radius:10px"><video src="${src}" poster="${poster}" object-fit width="100%" style="border-radius:10px" /></div>`;
					}
				);
				//超链接
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|text=([^"]+)\|type=url"[^>]*?>/g,
					(match, src, poster, text) => {
						return `<div><a href="${src}">${text}</a></div>`;
					}
				);
				//音乐
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|name=([^"]+)\|author=([^"]+)\|type=audio"[^>]*?>/g,
					(match, src, poster, name, author) => {
						return `<div><audio src="${src}" poster="${poster}" name="${name}" author="${author}" loop width="100%"></audio></div>`;
					}
				);
				//附件
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|pw=([^"]+)\|name=([^"]+)\|type=file"[^>]*?>/g,
					(match, src, poster, pw, name) => {
						var tqm = ''
						if (pw == '无') {
							tqm = ''
						} else {
							tqm = '提取码：' + pw
						}
						return `
						<div style='background: #f0f0f0;width:100%;padding:15px 15px;color:#666666;border:solid 1px black;box-sizing: border-box;border-radius: 20px;word-break:break-all;'/>
							<div style="display: flex;justify-content: space-between;align-items: center;">
								<div>
									<div style="font-size: 16px;font-weight: bold;color: black;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">${name}</div>
									<div style="font-size: 15px;">${tqm}</div>
								</div>
								<a href='${src}' style="color:#000000">
								<div>
									<span style='font-size:30px' class='tn-icon-download-simple' />
								</div>
								</a>
							</div>
						</div>
						`;
					}
				);
				// 替换视频标签
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|type=video"[^>]*?>/g,
					(match, src, poster) => {
						return `<div style="border-radius:10px"><video src="${src}" poster="${poster}" object-fit width="100%" style="border-radius:10px" /></div>`;
					}
				);
				//评论可见
				if (that.isComment == 1) {
					text = text.replace(
						/\[hide\]([\s\S]*?)\[\/hide\]/g,
						(match, content) => {
							return `
							<div style='width:100%;padding:15px 15px;color:#666666;border:solid 1px #3cc9a4;box-sizing: border-box;border-radius: 20px;word-break:break-all;'>
								<p>${content}
							</div>
							`;
						}
					);
				} else {
					text = text.replace(/\[hide(([\s\S])*?)\[\/hide\]/g,
						"<div style='width:100%;padding:15px 15px;background:#cbffea;color:#3cc9a4;border:solid 1px #3cc9a4;box-sizing: border-box;border-radius: 20px;text-align: center;'>该内容评论后显示！</div>"
					);
				}
				if (that.isvip > 0) {
					text = text.replace(
						/\[vip\]([\s\S]*?)\[\/vip\]/g,
						(match, content) => {
							return `
					        <div style='width:100%;padding:15px 15px;color:#666666;border:solid 1px #fb7299;box-sizing: border-box;border-radius: 20px;word-break:break-all;'>
					            <p>${content}
					        </div>
					        `;
						}
					);
				} else {
					text = text.replace(
						/\[vip\]([\s\S]*?)\[\/vip\]/g,
						"<div style='width:100%;padding:15px 15px;background:#ffecee;color:#fb7299;border:solid 1px #fb7299;box-sizing: border-box;border-radius: 20px;text-align: center;'>该内容仅会员可查看！</div>"
					);
				}
				//表情包
				// #ifdef APP-PLUS || H5
				var owoList = that.owoList;
				for (var i in owoList) {

					if (that.replaceSpecialChar(text).indexOf(owoList[i].data) != -1) {
						text = that.replaceAll(that.replaceSpecialChar(text), owoList[i].data, "<img src='" + owoList[i]
							.icon + "' class='tImg' />")

					}
				}
				// #endif
				//链接自动识别
				text = text.replace(/(?![^<>]*>)(src=['"]|poster=['"]|https?:\/\/[^\s'"<>]+)/gi, function(match, group1) {
					if (group1 && !/(src=['"]|poster=['"])/i.test(group1)) {
						var lastChar = match.charAt(match.length - 1);
						if (!/['"<>]/.test(match.charAt(4)) && !/[^a-zA-Z0-9\/]/.test(lastChar)) {
							return '<a href="' + match + '" target="_blank">' + match + '</a>';
						} else {
							return match;
						}
					} else {
						return match;
					}
				});
				return text;
			},
			markHtml(text) {
				var that = this;
				//下面奇怪的代码是为了解决可执行代码区域问题
				text = that.replaceAll(text, "@!!!", "@@@@");

				text = that.replaceAll(text, "!!!", "");
				text = that.replaceAll(text, "@@@@", "@!!!");
				text = that.markExpand(text);

				//text = text.replace(/(?<!\r)\n(?!\r)/g, "\n\n");
				//兼容垃圾的Safari浏览器
				text = text.replace(/([^\r])\n([^\r])/g, "$1\n\n$2");
				text = that.replaceAll(text, "||rn||", "\n\n");
				return text;

			},
			getUserLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			getUserLvStyle(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle = "color:#fff;background-color: " + rankStyle[i];
				return userlvStyle;
			},
			replaceAll(string, search, replace) {
				return string.split(search).join(replace);
			},

			markCommentHtml(text) {
				var that = this;
				// #ifdef APP-PLUS || H5
				var owoList = that.owoList;
				for (var i in owoList) {

					if (that.replaceSpecialChar(text).indexOf(owoList[i].data) != -1) {
						text = that.replaceAll(that.replaceSpecialChar(text), owoList[i].data, "<img src='/" + owoList[i]
							.icon + "' class='tImg' />")

					}
				}
				// #endif
				return text;
			},
			goLogin() {
				uni.navigateTo({
					url: '/pages/user/login'
				});
			},
			goRegister() {
				uni.navigateTo({
					url: '/pages/user/register'
				});
			},
			quillHtml(text) {
				var that = this;
				text = that.replaceAll(text, "hljs", "hl");
				text = that.replaceAll(text, "ql-syntax", "hl-pre");
				text = that.markExpand(text);
				return text;
			},
			getInfo() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": that.id,
					"token": token
				}
				that.$Net.request({
					url: that.$API.postInfoForum(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						that.isLoading = 1;
						uni.stopPullDownRefresh();
						if (res.data.code == 1) {
							that.title = res.data.data.title;
							that.userJson = res.data.data.userJson;
							that.commentsNum = res.data.data.commentsNum;
							that.isFollow = res.data.data.isFollow;
							that.created = res.data.data.created;
							that.isLikes = res.data.data.isLikes;
							that.isMark = res.data.data.isMark;
							that.lvrz = res.data.data.lvrz;
							that.smrz = res.data.data.smrz;
							that.likes = res.data.data.likes;
							that.views = res.data.data.views;
							that.status = res.data.data.status;
							that.images = res.data.data.images;
							if (res.data.data.isMd == 1) {
								that.html = that.markHtml(res.data.data.text);
							} else {
								that.html = that.quillHtml(res.data.data.text);
							}
							var sid = res.data.data.sid;
							that.getShopList(sid);
							that.authorId = res.data.data.authorId;
							that.isrecommend = res.data.data.isrecommend;
							that.isTop = res.data.data.isTop;
							that.isswiper = res.data.data.isswiper;
							that.isMd = res.data.data.isMd;
							that.sectionId = res.data.data.section;
							var section = res.data.data.section;
							that.getSectionInfo(section);
							that.userPurview();
							
							// 如果头像框插件启用，加载用户头像框
							if (that.fanstey_avatarframe && that.authorId) {
								that.loadUserFrame(that.authorId);
							}
						}
					},
					fail: function(res) {
						that.isLoading = 1;
						uni.stopPullDownRefresh();
					}
				})
			},
			getSectionInfo(id) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				that.$Net.request({

					url: that.$API.sectionInfo(),
					data: {
						"id": id,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.sectionInfo = res.data.data;
							if(that.sectionInfo.sulg!="null"){
								that.isforumLoading = 0;
							}
						}

					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toJb(title) {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/help?type=text&title=' + title
				});
			},
			// 加载用户头像框
			loadUserFrame(uid) {
				if (!uid || !this.fanstey_avatarframe) return;
				
				const that = this;
				that.$Net.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": uid
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame) {
								that.frameUrl = wearingFrame.frame_url;
							}
						}
					}
				});
			},
			
			// 头像框加载错误处理
			onFrameLoadError() {
				console.error('头像框加载失败');
				this.frameUrl = null;
			},
			
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},
			setOnlyUser(type) {
				var that = this;
				that.isOnlyUser = type;
				that.page = 1;
				var id = that.id
				that.getCommentsList(false, that.id);
				that.comPx = false;
			},
			getIsCommentsList(id) {
				var that = this;
				var token = "";
				var uid = 0;
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
					uid = userInfo.uid;
				}
				var data = {
					"forumid": id,
					"uid": uid,
				}
				that.$Net.request({
					url: that.$API.postCommentList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 2,
						"page": 1,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								that.isComment = 1;
								that.getInfo();

							}

						}

					},
					fail: function(res) {

					}
				})
			},

			goSection(id) {
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/home?id=' + id
				});
			},
			getCommentsList(isPage, id, isLogin) {
				var that = this;
				if (that.submitStatus1) {
					return false;
				}
				that.submitStatus1 = true;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				var data = {
					"forumid": id,
				}
				if (that.isOnlyUser == 1) {
					data.uid = that.userJson.uid;
				}
				var page = that.page;
				if (isPage) {
					page++;
				}
				that.$Net.request({
					url: that.$API.postCommentList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 10,
						"page": page,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						uni.stopPullDownRefresh();
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								var commentsList = [];
								for (var i in list) {
									var arr = list[i];
									arr.style = "background-image:url(" + list[i].userJson.avatar + ");"
									commentsList.push(arr);
								}
								if (isPage) {
									that.page++;
									that.commentsList = that.commentsList.concat(commentsList);
								} else {
									that.commentsList = commentsList;
								}

							} else {
								that.moreText = "没有更多评论了";
								if (that.page == 1 && !isPage) {
									that.commentsList = [];
								}

							}

						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.isuserlogin = true
								} else {
									that.getCommentsList(isPage, that.id, true);
								}
							}
						}

					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						uni.stopPullDownRefresh();
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						that.isLoad = 0;
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)

						that.moreText = "加载更多";
					}
				})
			},
			toUserInfo(data) {
				var that = this;
				var name = data.name;
				var title = data.name + "的信息";
				if (data.screenName) {
					title = data.screenName + " 的信息";
					name = data.screenName
				}
				var id = data.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
			toLike(id, index) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				if (that.isLikes == 1) {
					uni.showToast({
						title: "你已经点赞过了",
						icon: 'none'
					});
					return false;
				} else {
					that.isLikes = 1;
				}

				var data = {
					token: token,
					id: id,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.postLikes(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 0) {
							that.isLikes = 0;
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			toMark(type) {

				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": that.id,
					"token": token,
					"type": type
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.postMark(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code != 1) {
							that.getInfo();
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			ChooseCheckbox(j) {
				let items = this.checkbox;
				for (let i = 0, lenI = items.length; i < lenI; ++i) {
					this.checkbox[i].checked = false;
				}
				this.checkbox[j].checked = !this.checkbox[j].checked;
			},
			toReward() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var rewardList = that.checkbox;
				var num = 10;
				for (var i in rewardList) {
					if (rewardList[i].checked) {
						num = rewardList[i].num;
					}
				}
				var data = {
					"id": that.id,
					"num": num,
					"token": token
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.postReward(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						that.hideModal();
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							uni.request({
								url: that.$API.SPdashang(),
								method: 'GET',
								data: {
									uid: that.uid,
								},
								dataType: "json",
								success(res) {},
								fail() {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络不太好哦",
										icon: 'none'
									})
								}


							})
							uni.showToast({
								title: "成功打赏 " + num + " " + that.currencyName,
								icon: 'none'
							})
						}

					},
					fail: function(res) {
						that.hideModal();
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			getRewardLog(id) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				that.$Net.request({
					url: that.$API.postRewardList(),
					data: {
						"limit": 15,
						"page": 1,
						"id": id,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						if (res.data.code == 1) {
							that.rewardLog = res.data.data;
							var list = res.data.data;
							var rewardTotal = 0;
							for (var i in list) {
								rewardTotal = rewardTotal + list[i].num;
							}
							that.rewardTotal = rewardTotal;
						}
					},
					fail: function(res) {
						uni.stopPullDownRefresh();
					}
				})
			},
			goReward(id) {
				var that = this;

				uni.navigateTo({
					url: '/pages/forum/rewardLog?id=' + id
				});
			},
			ToShare() {

				var that = this;
				var linkRule = that.$API.GetforumStar();
				var appname = that.$API.GetAppName()
				var url = linkRule.replace("{id}", that.id);
				// #ifdef APP-PLUS
				uni.shareWithSystem({
					href: url,
					summary: that.title + ' | ' + appname,
					success() {
						// 分享完成，请注意此时不一定是成功分享

					},
					fail() {
						// 分享失败
					}
				});
				// #endif
				// #ifdef H5
				var text = that.title + ' - ' + appname + " | 链接：" + url
				that.ToCopy(text);
				// #endif
				that.isShare = false
			},
			toSearch() {
				var that = this;

				uni.redirectTo({
					url: '/pages/contents/search'
				});
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			getLocal(local) {
				var that = this;
				if (local && local != '') {
					var local_arr = local.split("|");
					if (!local_arr[3] || local_arr[3] == 0) {
						return local_arr[2];
					} else {
						return local_arr[3];
					}

				} else {
					return "未知"
				}

			},
			
			follow(type, uid, index) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				var data = {
					token: token,
					touid: uid,
					type: type,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.follow(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						that.show = false;
						if (type == 1) {
							that.isFollow = 1
						} else {
							that.isFollow = 0
						}
						if (res.data.code == 0) {
							if (type == 1) {
								uni.request({
									url: that.$API.SPguanzhu(),
									method: 'GET',
									data: {
										uid: that.uid,
									},
									dataType: "json",
									success(res) {},
									fail() {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络不太好哦",
											icon: 'none'
										})
									}


								})
							} else {
								uni.request({
									url: that.$API.SPquguan(),
									method: 'GET',
									data: {
										uid: that.uid,
									},
									dataType: "json",
									success(res) {},
									fail() {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络不太好哦",
											icon: 'none'
										})
									}


								})
							}
						}
						if (res.data.code == 0) {
							// that.getInfo();

						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					},

				})
			},

			userStatus() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				that.$Net.request({

					url: that.$API.userStatus(),
					data: {
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 0) {

							if (that.userInfo != null) {}
							localStorage.removeItem('userinfo');
							localStorage.removeItem('token');
						} else {
							that.isvip = res.data.data.isvip;
							that.experience = res.data.data.experience;
							
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			userPurview() {
				var that = this;
				var uid = 0;
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					uid = userInfo.uid;
				}
				var data = {
					"uid": uid,
				}
				that.$Net.request({
					url: that.$API.userPurview(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {


						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								for (var i in list) {
									if (list[i].sectionId == that.sectionId) {
										that.myPurview = list[i].purview;
									}
								}

							}
							if (localStorage.getItem('userinfo')) {
								var myInfo = JSON.parse(localStorage.getItem('userinfo'));
								if (myInfo.group == 'administrator' || myInfo.group == 'editor') {
									that.myPurview = 5;
								}

							}


						}
					},
					fail: function(res) {

					}
				})

			},
			ToCopy(text) {
				var that = this;
				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: text,
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: "链接复制成功"
						})
					}
				});
				// #endif
				// #ifdef H5 
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select();
				textarea.setSelectionRange(0, text.length);
				uni.showToast({ //提示
					title: "链接复制成功"
				})
				var result = document.execCommand("copy")
				textarea.remove();

				// #endif
			},
			copyShare() {
				var that = this;
				var linkRule = that.$API.GetforumStar();
				var appname = that.$API.GetAppName()
				var url = linkRule.replace("{id}", that.id);
				var text = that.title + ' - ' + appname + " | 链接：" + url
				that.ToCopy(text);
				that.isShare = false
			},
			toSwiper(id, type) {
				var that = this;

				var typeText = "确定要添加帖子轮播吗？";
				if (type == 0) {
					typeText = "确定要取消帖子轮播吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							that.isswiper = type;
							that.$Net.request({
								url: that.$API.postSwiper(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 0) {
										that.isswiper = 0;
									}
								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
							that.show = false;
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					},

				});
			},
			toTop(id, type) {
				var that = this;

				var typeText = "确定要置顶帖子吗？";
				if (type == 0) {
					typeText = "确定要取消置顶帖子吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});

							that.isTop = type;
							that.$Net.request({
								url: that.$API.postTop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 0) {
										that.isTop = 0;
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
							that.show = false;
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toLock(id, type) {
				var that = this;

				var typeText = "确定要锁定帖子吗？";
				if (type == 1) {
					typeText = "确定要取消锁定帖子吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {

							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postLock(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})


								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toRecommend(id, type) {
				var that = this;

				var typeText = "确定要加精帖子吗？";
				if (type == 0) {
					typeText = "确定要取消帖子加精吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							that.isrecommend = type;
							that.$Net.request({
								url: that.$API.postRecommend(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 0) {
										that.isrecommend = 0;
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
							that.show = false;
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toDelete(id) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"token": token
				}
				uni.showModal({
					title: '确定要删除该帖子吗',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postDelete(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 1) {
										uni.request({
											url: that.$API.SPglyremove(),
											method: 'GET',
											data: {
												id: id,
											},
											dataType: "json",
											success(res) {},
											fail() {
												setTimeout(function() {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}


										})
										that.back();
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
							that.show = false;
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			toEdit(id) {
				var that = this;
				if (that.authorId == that.uid) {
					uni.showModal({
						title: '修改后需要重新审核，确定要修改帖子吗？',
						success: function(res) {
							if (res.confirm) {
								if (that.isMd == 1) {
									uni.navigateTo({
										url: '/pages/edit/addPost?type=edit&section=' + that
											.sectionInfo.name + "&sectionid=" +
											that.sectionInfo.id + "&id=" + id
									});
								} else {
									uni.navigateTo({
										url: '/pages/edit/addPost?type=edit&section=' + that
											.sectionInfo.name + "&sectionid=" +
											that.sectionInfo.id + "&id=" + id
									});
								}
							} else if (res.cancel) {
								return false
								console.log('用户点击取消');
							}
						}
					});
				} else {
					if (that.isMd == 1) {
						uni.navigateTo({
							url: '/pages/edit/addPost?type=edit&section=' + that.sectionInfo.name + "&sectionid=" +
								that.sectionInfo.id + "&id=" + id
						});
					} else {
						uni.navigateTo({
							url: '/pages/edit/addPost?type=edit&section=' + that.sectionInfo.name + "&sectionid=" +
								that.sectionInfo.id + "&id=" + id
						});
					}
				}


			},

			isBuyShop(sid, type) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"sid": sid,
					"token": token
				}
				that.$Net.request({
					url: that.$API.isBuyShop(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res));
						if (res.data.code == 1) {
							that.isBuy = 1;
							if (type == 4) {
								that.toShopValue(sid, type);
							}
						}

					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			getShopList(sid) {
				var that = this;
				var uid = 0;
				if (localStorage.getItem('userinfo')) {

					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					uid = userInfo.uid;

				}
				var data = {
					"id": sid,
				}
				that.$Net.request({
					url: that.$API.shopList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 1,
						"page": 1,
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res));
						if (res.data.code == 1) {
							var list = res.data.data;
							that.shopList = list;
							if (list.length > 0) {
								that.shopID = list[0].id;
								that.isBuyShop(that.shopID, list[0].type);
							}

						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			toBan(uid) {
				uni.navigateTo({
					url: '/pages/manage/banuser?uid=' + uid
				});
			},
			shopBuy(sid, type) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					var timer = setTimeout(function() {
						uni.navigateTo({
							url: '../user/login'
						});
						clearTimeout('timer')
					}, 1000)
					return false
				}
				//因为增加了金币抵扣，所以跳转订单确认
				if (type != 4) {
					uni.navigateTo({
						url: '/pages/shop/orderpay?sid=' + sid
					});
					return false
				}

				var data = {
					"token": token,
					"sid": sid
				}
				if (type == 4) {
				    data.fid = that.id; 
				}
				uni.showModal({
					title: '确定购买此商品吗?',
					content: ' ',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							that.$Net.request({
								url: that.$API.buyShop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 1) {

										if (type != 4) {
											//跳转订单页面
											var timer = setTimeout(function() {
												uni.navigateTo({
													url: '/pages/user/order'
												});
												clearTimeout('timer')
											}, 1000)
										} else {
											that.toShopValue(sid, type);
										}

									} else {
										if (res.data.msg == "购买实体商品前，需要先设置收货地址") {
											var timer = setTimeout(function() {
												uni.redirectTo({
													url: '/pages/user/address'
												});
												clearTimeout('timer')
											}, 1000)
										}
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						}
					}
				});

			},
			toShopValue(id, type) {
				var that = this;
				if (type == 1) {
					uni.showToast({
						title: "实体商品请留意快递信息",
						icon: 'none'
					})
				} else if (type == 4) {
					var that = this;
					var token = "";
					if (localStorage.getItem('token')) {
						token = localStorage.getItem('token');
					}
					var data = {
						"key": id,
						"token": token
					}
					that.$Net.request({
						url: that.$API.shopInfo(),
						data: data,
						header: {
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						method: "get",
						dataType: 'json',
						success: function(res) {

							uni.stopPullDownRefresh();
							if (res.data.value) {
								that.shopIsMd = res.data.isMd;
								that.shopValue = that.quillHtml(res.data.value);
							}


							var timer = setTimeout(function() {
								that.isLoading = 1;
								clearTimeout('timer')
							}, 300)
						},
						fail: function(res) {
							uni.stopPullDownRefresh();
							uni.showToast({
								title: "网络开小差了哦",
								icon: 'none'
							})
							var timer = setTimeout(function() {
								that.isLoading = 1;
								clearTimeout('timer')
							}, 300)
						}
					})
				} else {
					uni.navigateTo({
						url: '/pages/shop/shoptext?sid=' + id
					});
				}
			},
			gorenwu() {
				uni.navigateTo({
					url: '/pages/user/renwu'
				});
			},
			gobuyvip() {
				uni.navigateTo({
					url: '/pages/user/buyvip'
				});
			},
			onMedalLoaded(medals) {
				this.userMedals = medals;
			},
			// 头像框加载错误处理
			onFrameLoadError() {
				console.error('头像框加载失败');
			},
			// 加载用户头像框
			loadUserFrame(uid) {
				if (!uid || !this.fanstey_avatarframe) return;
				
				const that = this;
				that.$Net.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": uid
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame) {
								that.frameUrl = wearingFrame.frame_url;
							}
						}
					}
				});
			},
			getVipInfo() {
				var that = this;
				that.$Net.request({
					url: that.$API.getVipInfo(),
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.vipDiscount = res.data.data.vipDiscount;
							that.vipPrice = res.data.data.vipPrice;
							that.scale = res.data.data.scale;
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
		}
	}
</script>

<style>
	.comNum-right {
		float: right;
		margin: 0 30upx;
		font-size: 11px !important;
		color: #333;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.data-box-title {
		font-size: 14px !important;
	}

	.comPxOn {
		color: #333;
	}

	.comPxOff {
		color: #878787;
	}

	.foot-num {
		position: absolute;
		right: -22upx;
		top: 22upx;
		width: 28upx;
		height: 18upx;
		font-size: 22upx;
		background: #fff;
		line-height: 18upx;
		padding: 2upx 4upx;
		border-radius: 16upx;
	}

	.info-input-box {
		height: 64upx;
		border-radius: 100upx;
		line-height: 64upx;
		margin: 4upx 0;
	}

	.info-footer-btn {
		padding: 0 20upx;
	}

	.info-footer-btn text {
		margin: 0 25upx 0 0;
	}

	.name-vip {
		color: #ff6c3e;
	}

	.user-rz {
		position: relative;
		display: inline-block;
	}

	.user-rz-icon {
		position: absolute;
		right: -4upx;
		bottom: -4upx;
		width: 34upx;
		height: 34upx;
		z-index: 3; /* 确保蓝V标识在最上层 */
	}
	
	.avatar-frame {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 120%;
		height: 120%;
		z-index: 2;
		transform: translate(-50%, -50%) scale(1.15);
	}
	
	.user-avatar-container {
		position: relative;
		z-index: 1; /* 确保头像在最底层 */
	}

	/* 		 #ifdef APP-PLUS || MP */
	.info-jh {
		position: absolute;
		top: 330upx;
		right: 44upx;
		width: 182upx;
		height: 100upx;
		transform: rotate(23deg);
	}

	/* 		 #endif */
	/* 		 #ifdef H5 */
	.info-jh {
		position: absolute;
		top: 260upx;
		right: 44upx;
		width: 182upx;
		height: 100upx;
		transform: rotate(23deg);
	}

	/* 		 #endif */

	.tn-margin-top-xxl {
		margin-top: 60upx;
	}

	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		text-align: center;
	}

	.index-sort-i {
		border-radius: 15px;
	}

	.no-data .cuIcon-community {
		display: block;
		font-size: 32px;
		color: #ddd;
		margin-bottom: 6px;
	}
	.pl-title {
		font-size: 30rpx;
	}
	
	.pl-tool-box {
		display: flex;
		margin: 0 32rpx 16rpx 32rpx;
		
		align-items: center;
		justify-content: space-between;
	}
	.pl-tool-box-2 {
		display: flex;
		justify-content: flex-start;
	}
	.pl-tool {
		font-size: 48rpx;
		margin-right: 30rpx;
	}
	
	.pl-btn {
		font-size: 40rpx;
		border-radius: 50px;
		border: 2px solid #525252;
	}
	.cu-form-group {
	    background-color: #ffffff;
	    padding: 2rpx 20rpx;
	}
	textarea {
		border-radius: 40rpx;
		margin: 20rpx 0;
		background-color: #eeeeee;
		padding: 20rpx;
	}
</style>