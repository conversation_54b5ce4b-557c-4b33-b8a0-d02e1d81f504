<template>
	<view class="container" :class="AppStyle">
		<!-- 自定义导航栏 -->
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					我的视频
				</view>
				<view class="action">
					<text class="cuIcon-upload text-primary" @tap="goUpload"></text>
				</view>
			</view>
		</view>
		<view :style="[{padding:(NavBar) + 'px 0 0'}]"></view>

		<!-- 页面内容 -->
		<view class="content-container">
			<!-- 状态切换 -->
			<view class="status-tabs">
				<view
					class="status-tab"
					:class="{'active': currentStatus === 1}"
					@tap="changeStatus(1)"
				>
					已发布
				</view>
				<view
					class="status-tab"
					:class="{'active': currentStatus === 0}"
					@tap="changeStatus(0)"
				>
					审核中
				</view>
				<view
					class="status-tab"
					:class="{'active': currentStatus === 2}"
					@tap="changeStatus(2)"
				>
					已拒绝
				</view>
			</view>

			<!-- 加载中 -->
			<view v-if="loading" class="loading-container">
				<view class="cu-load loading"></view>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 视频列表 -->
			<view v-else-if="videoList.length > 0" class="video-list">
				<view
					class="video-item"
					v-for="(item, index) in videoList"
					:key="index"
				>
					<view class="video-cover" @tap="goDetail(item.id)">
						<image :src="item.cover_url" mode="aspectFill"></image>
						<view class="video-duration">{{item.duration_text}}</view>
						<view class="video-status" :class="getStatusClass(item.status)">
							{{getStatusText(item.status)}}
						</view>
					</view>
					<view class="video-info">
						<view class="video-title text-cut" @tap="goDetail(item.id)">{{item.title}}</view>
						<view class="video-stats">
							<text class="stats-item">
								<text class="cuIcon-attentionfill"></text>
								<text>{{formatNumber(item.view_count)}}</text>
							</text>
							<text class="stats-item">
								<text class="cuIcon-appreciatefill"></text>
								<text>{{formatNumber(item.like_count)}}</text>
							</text>
							<text class="stats-item">
								<text class="cuIcon-commentfill"></text>
								<text>{{formatNumber(item.comment_count)}}</text>
							</text>
						</view>
						<view class="video-time">{{item.created_at}}</view>
						<view class="video-actions">
							<view class="action-btn" @tap="goDetail(item.id)">
								<text class="cuIcon-attention"></text>
								<text>查看</text>
							</view>
							<view class="action-btn" @tap="deleteVideo(item.id)">
								<text class="cuIcon-delete"></text>
								<text>删除</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 无数据 -->
			<view v-else class="empty-container">
				<image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无视频</text>
			</view>

			<!-- 加载更多 -->
			<view class="cu-load" :class="loadStatus"></view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			StatusBar: this.StatusBar,
			CustomBar: this.CustomBar,
			NavBar: this.StatusBar + this.CustomBar,
			AppStyle: this.$store.state.AppStyle,
			token: '',
			loading: true,
			videoList: [],
			currentStatus: 1, // 默认显示已发布的视频
			page: 1,
			limit: 10,
			hasMore: true,
			loadStatus: 'loading',
			isLogin: false,
			submitStatus: false
		}
	},
	onLoad() {
		// 获取token
		let token = '';
		// #ifdef H5
		token = localStorage.getItem('token') || '';
		// #endif
		
		// #ifdef APP-PLUS || MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
		token = uni.getStorageSync('token') || '';
		// #endif
		
		this.token = token;
		this.isLogin = !!this.token;

		if (!this.isLogin) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});

			setTimeout(() => {
				uni.navigateBack({
					delta: 1
				});
			}, 1500);
			return;
		}

		// 获取视频列表
		this.getVideoList();
	},
	onPullDownRefresh() {
		this.page = 1;
		this.videoList = [];
		this.hasMore = true;
		this.loadStatus = 'loading';
		this.getVideoList();
	},
	onReachBottom() {
		if (this.hasMore) {
			this.page++;
			this.getVideoList();
		}
	},
	methods: {
		// 返回上一页
		back() {
			uni.navigateBack({
				delta: 1
			});
		},

		// 切换状态
		changeStatus(status) {
			if (this.currentStatus === status) return;

			this.currentStatus = status;
			this.page = 1;
			this.videoList = [];
			this.hasMore = true;
			this.loadStatus = 'loading';
			this.getVideoList();
		},

		// 获取视频列表
		getVideoList() {
			if (this.submitStatus) {
				return false;
			}

			if (!this.hasMore && this.page > 1) return;

			const that = this;
			that.loading = true;
			that.submitStatus = true;

			// 调试输出请求参数
			const requestData = {
				action: 'getVideoList',
				plugin: 'xqy_video',
				page: that.page,
				limit: that.limit,
				status: that.currentStatus,
				author_id: 'current', // 获取当前用户的视频
				token: that.token
			};

			uni.request({
				url: that.$API.PluginLoad('xqy_video'),
				data: requestData,
				method: 'POST',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				success: function(res) {
					that.loading = false;
					that.submitStatus = false;
					uni.stopPullDownRefresh();

					if (res.data.code === 200) {
						const videos = res.data.data.videos || [];

						// 检查每个视频的status值
						videos.forEach((video, index) => {
							//console.log(`视频${index+1} status:`, video.status, typeof video.status);
						});

						if (that.page === 1) {
							that.videoList = videos;
						} else {
							that.videoList = [...that.videoList, ...videos];
						}

						that.hasMore = videos.length >= that.limit;
						that.loadStatus = that.hasMore ? 'loading' : 'over';
					} else {
						uni.showToast({
							title: res.data.msg || '获取视频列表失败',
							icon: 'none'
						});
					}
				},
				fail: function(err) {
					that.loading = false;
					that.submitStatus = false;
					uni.stopPullDownRefresh();

					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				}
			});
		},

		// 跳转到视频详情页
		goDetail(id) {
			uni.navigateTo({
				url: `/pages/plugins/xqy_video/detail?id=${id}`
			});
		},

		// 跳转到视频上传页
		goUpload() {
			uni.navigateTo({
				url: '/pages/plugins/xqy_video/upload'
			});
		},

		// 删除视频
		deleteVideo(id) {
			const that = this;

			uni.showModal({
				title: '提示',
				content: '确定要删除这个视频吗？',
				success: function(res) {
					if (res.confirm) {
						// 调试输出
						const requestData = {
							action: 'manageVideo',
							plugin: 'xqy_video',
							op: 'delete',
							video_id: id,
							token: that.token
						};
						//console.log('删除视频请求参数:', requestData);

						uni.request({
							url: that.$API.PluginLoad('xqy_video'),
							data: requestData,
							method: 'POST',
							header: {
								'Content-Type': 'application/x-www-form-urlencoded'
							},
							success: function(res) {
								//console.log('删除视频响应:', res.data);

								if (res.data.code === 200) {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									});

									// 刷新列表
									that.page = 1;
									that.videoList = [];
									that.hasMore = true;
									that.loadStatus = 'loading';
									that.getVideoList();
								} else {
									uni.showToast({
										title: res.data.msg || '删除失败',
										icon: 'none'
									});
								}
							},
							fail: function(err) {
								//console.error('删除视频请求失败:', err);

								uni.showToast({
									title: '网络错误，请稍后重试',
									icon: 'none'
								});
							}
						});
					}
				}
			});
		},

		// 获取状态文本
		getStatusText(status) {
			//console.log('获取状态文本, status:', status, typeof status);

			// 将status转换为数字
			const statusNum = parseInt(status);
			//console.log('转换后的status:', statusNum, typeof statusNum);

			switch (statusNum) {
				case 0: return '审核中';
				case 1: return '已发布';
				case 2: return '已拒绝';
				default: return '未知(' + status + ')';
			}
		},

		// 获取状态样式类
		getStatusClass(status) {
			//将status转换为数字
			const statusNum = parseInt(status);

			switch (statusNum) {
				case 0: return 'status-pending';
				case 1: return 'status-published';
				case 2: return 'status-rejected';
				default: return 'status-unknown';
			}
		},

		// 格式化数字
		formatNumber(num) {
			num = parseInt(num) || 0;
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w';
			} else if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k';
			}
			return num;
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f6f6f6;

	.content-container {
		padding: 0;
	}

	.status-tabs {
		display: flex;
		background-color: #fff;
		border-radius: 0;
		margin-bottom: 0;
		overflow: hidden;

		.status-tab {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			position: relative;

			&.active {
				color: #0081ff;
				font-weight: bold;

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: #0081ff;
				}
			}
		}
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20rpx;

		.loading-text {
			margin-top: 20rpx;
			color: #999;
			font-size: 28rpx;
		}
	}

	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20rpx;

		.empty-image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 30rpx;
		}

		.empty-text {
			color: #999;
			font-size: 28rpx;
		}
	}

	.video-list {
		padding: 20rpx;
		.video-item {
			display: flex;
			background-color: #fff;
			border-radius: 12rpx;
			margin-bottom: 20rpx;
			overflow: hidden;

			.video-cover {
				width: 240rpx;
				height: 180rpx;
				position: relative;

				image {
					width: 100%;
					height: 100%;
				}

				.video-duration {
					position: absolute;
					right: 10rpx;
					bottom: 10rpx;
					background-color: rgba(0, 0, 0, 0.5);
					color: #fff;
					font-size: 20rpx;
					padding: 2rpx 8rpx;
					border-radius: 4rpx;
				}

				.video-status {
					position: absolute;
					left: 0;
					top: 0;
					padding: 4rpx 10rpx;
					font-size: 20rpx;
					color: #fff;

					&.status-pending {
						background-color: #f37b1d;
					}

					&.status-published {
						background-color: #0081ff;
					}

					&.status-rejected {
						background-color: #e54d42;
					}

					&.status-unknown {
						background-color: #8799a3;
					}
				}
			}

			.video-info {
				flex: 1;
				padding: 20rpx;
				display: flex;
				flex-direction: column;

				.video-title {
					font-size: 28rpx;
					font-weight: bold;
					line-height: 1.4;
					margin-bottom: 10rpx;
				}

				.video-stats {
					display: flex;
					margin-bottom: 10rpx;

					.stats-item {
						display: flex;
						align-items: center;
						margin-right: 20rpx;
						color: #999;
						font-size: 24rpx;

						text:first-child {
							margin-right: 4rpx;
						}
					}
				}

				.video-time {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 10rpx;
				}

				.video-actions {
					display: flex;
					margin-top: auto;

					.action-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						height: 60rpx;
						padding: 0 20rpx;
						background-color: #f6f6f6;
						border-radius: 30rpx;
						margin-right: 20rpx;
						font-size: 24rpx;
						color: #666;

						text:first-child {
							margin-right: 4rpx;
						}
					}
				}
			}
		}
	}
}
</style>
