<?php
initializeActions();

// 验证行为验证码
$captcha_type = isset($_POST['captcha_type']) ? $_POST['captcha_type'] : '';
$captcha_data = isset($_POST['captcha_data']) ? $_POST['captcha_data'] : '';

if (empty($captcha_type) || empty($captcha_data)) {
    send_json(400, "验证参数不完整");
    exit;
}

// 获取配置
$sql = "SELECT * FROM Xqy_Plugin_captcha_config WHERE id = 1";
$result = mysqli_query($connect, $sql);

if (!$result || mysqli_num_rows($result) == 0) {
    send_json(404, "验证配置不存在");
    exit;
}

$config = mysqli_fetch_assoc($result);

if (intval($config['enabled']) !== 1) {
    send_json(403, "验证服务未启用");
    exit;
}

$isValid = false;
$message = "";

switch ($captcha_type) {
    case 'geetest':
        $isValid = verifyGeetest($captcha_data, $config);
        $message = $isValid ? "极验验证成功" : "极验验证失败";
        break;
    case 'cloudflare':
        $isValid = verifyCloudflare($captcha_data, $config);
        $message = $isValid ? "Cloudflare验证成功" : "Cloudflare验证失败";
        break;
    case 'recaptcha':
        $isValid = verifyRecaptcha($captcha_data, $config);
        $message = $isValid ? "reCAPTCHA验证成功" : "reCAPTCHA验证失败";
        break;
    default:
        send_json(400, "不支持的验证类型");
        exit;
}

if ($isValid) {
    send_json(200, $message);
} else {
    send_json(400, $message);
}

// 极验验证函数
function verifyGeetest($captcha_data, $config) {
    $data = json_decode($captcha_data, true);
    if (!$data || !isset($data['geetest_challenge']) || !isset($data['geetest_validate']) || !isset($data['geetest_seccode'])) {
        return false;
    }
    
    $challenge = $data['geetest_challenge'];
    $validate = $data['geetest_validate'];
    $seccode = $data['geetest_seccode'];
    
    // 极验二次验证
    $postData = [
        'seccode' => $seccode,
        'challenge' => $challenge,
        'validate' => $validate,
        'json_format' => '1'
    ];
    
    $url = 'http://api.geetest.com/validate.php';
    $response = httpPost($url, $postData);
    $result = json_decode($response, true);
    
    return isset($result['seccode']) && $result['seccode'] === 'true';
}

// Cloudflare验证函数
function verifyCloudflare($captcha_data, $config) {
    $token = $captcha_data;
    
    $postData = [
        'secret' => $config['cloudflare_secret_key'],
        'response' => $token,
        'remoteip' => $_SERVER['REMOTE_ADDR']
    ];
    
    $url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
    $response = httpPost($url, $postData);
    $result = json_decode($response, true);
    
    return isset($result['success']) && $result['success'] === true;
}

// reCAPTCHA验证函数
function verifyRecaptcha($captcha_data, $config) {
    $token = $captcha_data;
    
    $postData = [
        'secret' => $config['recaptcha_secret_key'],
        'response' => $token,
        'remoteip' => $_SERVER['REMOTE_ADDR']
    ];
    
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $response = httpPost($url, $postData);
    $result = json_decode($response, true);
    
    return isset($result['success']) && $result['success'] === true;
}

// HTTP POST请求函数
function httpPost($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return $response;
}
?>
