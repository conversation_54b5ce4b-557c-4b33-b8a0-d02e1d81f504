<template>
  <view class="pretty-number-item" v-if="isPluginEnabled && prettyNumber">
    <view class="pretty-number-container" @click="copyNumber">
      <text class="pretty-number-value" :class="numberType">
        <text class="number-label">靓号</text>
        <span class="number-text">{{ prettyNumber }}</span>
      </text>
      <text class="tn-icon-copy mirror copy-icon"></text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'prettyNumberItem',
  props: {
    uid: {
      type: [Number, String],
      required: true
    },
    showLabel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      prettyNumber: null,
      numberType: 'normal',
      isLoading: false,
      isPluginEnabled: false
    }
  },
  created() {
    try {
      // 检查插件是否启用
      const cachedPlugins = uni.getStorageSync('getPlugins')
      if (cachedPlugins) {
        const pluginList = JSON.parse(cachedPlugins)
        this.isPluginEnabled = pluginList.includes('dor_pretty')
        //console.log('靓号插件状态:', this.isPluginEnabled)
      }
    } catch (error) {
      //console.error('检查插件状态失败:', error)
      this.isPluginEnabled = false
    }

    if (this.isPluginEnabled && this.uid) {
      this.getPrettyNumber()
    }
  },
  watch: {
    uid: {
      handler(newVal) {
        if (newVal && this.isPluginEnabled) {
          this.getPrettyNumber()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取用户靓号
    getPrettyNumber() {
      if (this.isLoading) return;
      if (!this.isPluginEnabled) {
        //console.log('靓号插件未启用')
        return
      }
      if (!this.uid) {
        //console.log('未提供uid')
        return
      }
      
      this.isLoading = true;
      
      uni.request({
        url: this.$API.PluginLoad('dor_pretty'),
        method: 'POST',
        data: {
          plugin: 'dor_pretty',
          action: 'get_user_number',
          uid: this.uid
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          if (res.data && res.data.code === 200 && res.data.data && res.data.data.number) {
            this.prettyNumber = res.data.data.number;
            this.numberType = res.data.data.type || 'normal';
            this.$emit('number-loaded', res.data.data);
          } else if (res.data && res.data.code === 404) {
            // 用户没有靓号，不显示组件
            this.prettyNumber = null;
            this.$emit('number-loaded', null);
          } else {
            // 其他错误情况
            console.error('获取靓号失败:', res.data);
            this.prettyNumber = null;
            this.$emit('number-loaded', null);
          }
        },
        fail: (err) => {
          console.error('获取靓号失败:', err);
          this.prettyNumber = null;
          this.$emit('number-loaded', null);
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    },
    
    // 复制靓号
    copyNumber() {
      if (!this.prettyNumber) return;
      
      uni.setClipboardData({
        data: this.prettyNumber,
        success: () => {
          uni.showToast({
            title: '靓号已复制',
            icon: 'success'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss">
.pretty-number-item {
  display: inline-flex;
  align-items: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  white-space: nowrap;
  
  .pretty-number-container {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    
    .pretty-number-value {
      font-size: 28rpx;
      margin-right: 8rpx;
      border-radius: 100rpx;
      padding: 4rpx 12rpx;
      display: inline-flex;
      align-items: center;
      line-height: 1;
      position: relative;
      white-space: nowrap;
      
      .number-label {
        font-size: 20rpx;
        color: #D4AF37;
        background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(218, 165, 32, 0.1));
        padding: 2rpx 8rpx;
        border-radius: 100rpx;
        margin-right: 6rpx;
        display: inline-block;
        font-weight: normal;
        letter-spacing: 0.5px;
        border: 1px solid rgba(212, 175, 55, 0.2);
        flex-shrink: 0;
        white-space: nowrap;
      }
      
      .number-text {
        font-size: 24rpx;
        font-weight: 600;
        white-space: nowrap;
        display: inline-block;
      }
      
      &.normal {
        color: #8B4513;
        font-weight: 700;
        background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
        border: 1px solid rgba(139, 69, 19, 0.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05),
                    0 1px 2px rgba(0, 0, 0, 0.04);
        backdrop-filter: blur(20px);
        
        &::after {
          content: '';
          position: absolute;
          inset: 0;
          background: linear-gradient(120deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%);
          border-radius: 50px;
          opacity: 0.6;
        }
      }
      
      &.black_gold {
        position: relative;
        background: linear-gradient(135deg, 
          rgba(0, 0, 0, 0.95) 0%,
          rgba(0, 0, 0, 0.98) 100%
        );
        border: 1px solid rgba(255, 215, 0, 0.5);
        border-radius: 100rpx;
        padding: 8rpx 16rpx;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        
        .number-label {
          background: #FFD700;
          color: #000;
          opacity: 0.8;
        }
        
        .number-text {
          background: linear-gradient(90deg,
            #FFD700 0%,
            #FDB931 30%,
            #FFD700 70%,
            #FDB931 100%
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          font-weight: 700;
          position: relative;
          background-size: 200% auto;
          letter-spacing: 1px;
          text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.3),
              transparent
            );
            animation: sweep 2s infinite;
          }
        }
      }
    }
    
    .copy-icon {
      font-size: 24rpx;
      color: #999;
      opacity: 0.8;
      padding: 4rpx;
      min-width: 24rpx;
      transition: opacity 0.3s;
      
      &:hover {
        opacity: 1;
      }
    }
  }
}

@keyframes sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>