/**
 * 跨平台localStorage实现
 */

let localStorage = {
    setItem(key, val) {
        if (typeof val === 'object') {
            val = JSON.stringify(val)
        }
        try {
            uni.setStorageSync(key, val)
        } catch (e) {
            console.error('localStorage setItem 失败:', e)
        }
    },
    getItem(key) {
        try {
            const value = uni.getStorageSync(key)
            return value
        } catch (e) {
            console.error('localStorage getItem 失败:', e)
            return null
        }
    },
    removeItem(key) {
        try {
            uni.removeStorageSync(key)
        } catch (e) {
            console.error('localStorage removeItem 失败:', e)
        }
    },
    clear() {
        try {
            uni.clearStorageSync()
        } catch (e) {
            console.error('localStorage clear 失败:', e)
        }
    }
}

export {
    localStorage
} 