<template>
	<view class="user" :class="$store.state.AppStyle">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back text-black"></text>
				</view>
				<view class="content text-black" :style="[{top:StatusBar + 'px'}]">
					实名认证
				</view>
			</view>
		</view>
		
		<view class="main-content" :style="[{paddingTop: NavBar+5 + 'px'}]">
			<view class="auth-card">
				<view class="auth-header">
					<view class="auth-title">
						<text class="title-text">个人认证</text>
						<view class="status-tag" :class="{
							'verified': identifyConsumer==1,
							'pending': identifyConsumer==-1,
							'unverified': identifyConsumer==0
						}">
							<text v-if="identifyConsumer==1">已认证</text>
							<text v-else-if="identifyConsumer==-1">审核中</text>
							<text v-else @tap="toLink('/pages/identify/consumer')">开始认证</text>
						</view>
					</view>
					<view class="auth-desc">
						进行个人身份的认证，认证后投稿不受限制，并获得认证标识
					</view>
				</view>
			</view>

			<view class="tips-section">
				<view class="tips-title">认证提示</view>
				<view class="tips-content" v-html="smtext"></view>
			</view>
		</view>

		<view class="loading-mask" v-if="isLoading==0">
			<view class="loading-wrapper">
				<image src="../../static/loading.gif" mode="aspectFit"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				
				uid:0,
				
				identifyCompany:0,
				identifyConsumer:0,
				smtext:"",
				userInfo:"",
				token:'',
				isLoading:0,
				
				
			}
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			if(localStorage.getItem('token')){
				
				that.token = localStorage.getItem('token');
			}
			that.getCacheInfo();
			that.identifyStatus();
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
		},
		mounted() {
			this.getset()
		},
		methods: {
			getset() {
			  var that = this;
			      uni.request({
			        url:that.$API.SPset(),
			        method:'GET',
			        data:{
			          id:1
			        },
			        dataType:"json",
			        success(res) {
					    that.smtext = res.data.smtext;
			        },
			        fail(error) {
			          console.log(error);
			        }
			      })
			},
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			getCacheInfo(){
				var that = this;
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.uid=userInfo.uid;
					that.userInfo = userInfo;
				}
			},
			toLink(text){
				var that = this;
				
				if(!localStorage.getItem('token')||localStorage.getItem('token')==""){
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			identifyStatus() {
				var that = this;
				that.$Net.request({
					
					url: that.$API.identifyStatus(),
					data:{
						"uid":that.uid
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						
						if(res.data.code==1){
							
							that.identifyCompany = res.data.data.identifyCompany;
							that.identifyConsumer = res.data.data.identifyConsumer;
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
		}
	}
</script>

<style>
.main-content {
	padding: 0 24rpx;
	background-color: #f8f9fa;
	min-height: 100vh;
}

.auth-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
}

.auth-header {
	position: relative;
}

.auth-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.status-tag {
	padding: 8rpx 24rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
}

.status-tag.verified {
	background: rgba(52, 199, 89, 0.1);
	color: #34c759;
}

.status-tag.pending {
	background: rgba(255, 149, 0, 0.1);
	color: #ff9500;
}

.status-tag.unverified {
	background: rgba(0, 122, 255, 0.1);
	color: #007aff;
}

.auth-desc {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
}

.tips-section {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
}

.tips-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.tips-content {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
}

.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.loading-wrapper {
	width: 120rpx;
	height: 120rpx;
}

.loading-wrapper image {
	width: 100%;
	height: 100%;
}

/* 顶部导航栏样式优化 */
.header {
	background: #ffffff;
	position: fixed;
	width: 100%;
	z-index: 100;
}

.cu-bar {
	background: transparent;
}

.cu-bar .content {
	font-size: 32rpx;
	font-weight: 500;
}

.cuIcon-back {
	font-size: 36rpx;
}
</style>
