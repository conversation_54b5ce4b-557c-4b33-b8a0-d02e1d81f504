<template>
	<!-- 商品 start-->
	<!-- #ifdef APP-PLUS || H5 -->
	<view class="" style="width: 50%;" :class="getIsDark()?'dark':''">
	<!-- #endif -->
	<!-- #ifdef MP -->
	<view class="" style="width: 100%;" :class="getIsDark()?'dark':''">
		<!-- #endif -->
		<view class="tn-blogger-content__wrap" @tap="shopInfo(item)">
		<view class="image-picbook" :style="'background-image:url(' + item.imgurl + ')'">
			<view class="image-book">
			</view>
		</view>
		<view class="tn-blogger-content__label tn-text-justify tn-padding-sm">
			<block v-if="isAdmin">
				<!-- <text class="text-orange" v-if="item.status==0">[待审核]</text>
				<text class="text-green" v-if="item.status==1">[已上架]</text> -->
				<text class="text-red" v-if="item.status==2">[已禁用]</text>
			</block>
			<text class="tn-blogger-content__label__desc text-content-2"
				style="min-height: 35px;">{{ item.title||"无标题商品" }}</text>
			<!-- <text v-if="!isAdmin&&item.integral>0" class="text-green"> 可抵扣</text> -->

		</view>

		<view v-if="!isAdmin"
			class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-left-sm tn-padding-right-sm tn-padding-bottom-sm">
			<!-- 价格部分 - 左侧 -->
			<view class="price-section tn-flex tn-flex-col-center">
				<view class="price-container">
					<text v-if="isvip==1" class="tn-text-bold tn-color-purplered"
						style="font-size: 38rpx;">{{parseInt(item.price * item.vipDiscount)}}</text>
					<text v-else class="tn-text-bold tn-color-purplered"
						style="font-size: 38rpx;">{{ item.price }}</text>
					<text class="tn-blogger-content__count-icon tn-color-purplered"
						style="font-size: 24rpx;">{{currencyName}}</text>
				</view>
			</view>
			
			<!-- 参数信息部分 - 右侧 -->
			<view class="params-section tn-flex tn-flex-col-center">
				<view class="params-container">
					<text class="tn-color-gray tn-text-sm"
						style="font-size: 24rpx;" v-if="showType=='sellNum'">销量{{ item.sellNum }}</text>

					<template v-if="showType=='num' && item.num!=-1">
						<text class="tn-color-gray tn-text-sm"
							style="font-size: 24rpx;">库存{{item.num}}</text>
					</template>

					<template v-if="showType=='all'">
						<text class="tn-color-gray tn-text-sm"
							style="font-size: 24rpx;">销量{{ item.sellNum }}</text>
						<template v-if="item.num!=-1">
							<text class="tn-color-gray tn-text-sm"
								style="font-size: 24rpx;padding-left: 6upx;padding-right: 6upx;">|</text>
							<text class="tn-color-gray tn-text-sm" style="font-size: 24rpx;">
								库存{{item.num}}
							</text>
						</template>
					</template>
				</view>
			</view>
		</view>
			<view  v-else
				class="tn-padding-left-sm tn-padding-right-sm tn-padding-bottom-sm" style="display: flex;justify-content: space-around;">

					<text class="text-orange tn-margin-right-sm" v-if="item.status==0" @tap="auditShop(item.id)">[通过]</text>
					<text class="text-green  tn-margin-right-sm" @tap="editShop(item)">[编辑]</text>
					<text class="text-red  tn-margin-right-sm" @tap="deleteShop(item.id)">[删除]</text>

			</view>
			
			<!-- <view class="justify-content-item tn-text-center">
	            <view v-for="(label_item,label_index) in item.label" :key="label_index" class="tn-blogger-content__label__item tn-float-left tn-margin-right tn-bg-gray--light tn-round tn-text-sm tn-text-bold">
	              <text class="tn-blogger-content__label__item--prefix">#</text> {{ label_item }}
	            </view>
	          </view> -->
		</view>

	</view>
	</view>

</template>

<script>
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	import darkModeMixin from '@/utils/darkModeMixin.js'
	export default {
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
			isAdmin: {
				type: Boolean,
				default: false
			},
			isDark: {
				type: Boolean,
				default: false
			},
			showType: {
				type: String,
				default: 'sellNum' //sellNum,num,all
			}
		},
		mixins: [darkModeMixin],
		name: "forumItem",
		data() {
			return {
				vipDiscount: 0,
				vipPrice: 0,
				scale: 0,
				isvip: 0,
				vip: 0,
				currencyName: "",
				group: "",
			};
		},
		created() {
			var that = this;
			if (localStorage.getItem('userinfo')) {

				var userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.group = userInfo.group;
				that.isvip = userInfo.isvip;
			}
		},
		mounted() {
			var that = this;
			that.getVipInfo();
			that.getleiji()
		},

		methods: {
			getleiji() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.currencyName = res.data.assetsname;
					},
					fail(error) {
						console.log(error);
					}

				})
			},
			shopInfo(data) {
				var that = this;
				
				if (that.isAdmin) {
					return false
				} else {
					uni.navigateTo({
						url: '/pages/shop/shopinfo?sid=' + data.id
					});
					
				}

			},
			getVipInfo() {
				var that = this;
				that.$Net.request({
					url: that.$API.getVipInfo(),
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.vipDiscount = res.data.data.vipDiscount;
							that.vipPrice = res.data.data.vipPrice;
							that.scale = res.data.data.scale;
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getUserInfo(uid) {
				var that = this;
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.getUserInfo(),
					data: {
						"key": uid
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						if (res.data.code == 1) {
							var data = res.data.data;
							that.toUserContents(data);
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toUserContents(data) {
				var that = this;
				var name = data.name;
				var title = data.name + "的信息";
				if (data.screenName) {
					title = data.screenName + " 的信息";
					name = data.screenName
				}
				var id = data.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
			deleteShop(sid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					var timer = setTimeout(function() {
						uni.navigateTo({
							url: '/pages/user/login'
						});
						clearTimeout('timer')
					}, 1000)
					return false
				}
				var data = {
					key: sid,
					token: token,
				}
				uni.showModal({
					title: '确定要删除此商品吗?',
					content: ' ',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							that.$Net.request({

								url: that.$API.deleteShop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.$emit('updateList', 'updateList');
								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
									uni.stopPullDownRefresh()
								}
							})
						}
					}
				});

			},
			auditShop(sid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					var timer = setTimeout(function() {
						uni.navigateTo({
							url: '/pages/user/login'
						});
						clearTimeout('timer')
					}, 1000)
					return false
				}
				var data = {
					key: sid,
					token: token,
				}
				uni.showModal({
					title: '确定审核通过该商品吗?',
					content: ' ',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							that.$Net.request({

								url: that.$API.auditShop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 1) {
										that.$emit('updateList', 'updateList');
									}
								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
									uni.stopPullDownRefresh()
								}
							})
						}
					}
				});
			},
			editShop(data) {
				var that = this;
				var sid = data.id;
				var isMd = data.isMd;
				if (isMd == 1) {
					uni.navigateTo({
						url: '/pages/edit/addshop?type=edit' + '&id=' + sid
					});
				} else {
					//富文本编辑器
					uni.navigateTo({
						url: '/pages/edit/addshop?type=edit' + '&id=' + sid
					});
				}

			},
		}
	}
</script>

<style lang="scss" scoped>
	.template-product {}

	.tn-tabbar-height {
		min-height: 120rpx;
		height: calc(140rpx + env(safe-area-inset-bottom) / 2);
	}

	/* 用户头像 start */
	.logo-image {
		width: 110rpx;
		height: 110rpx;
		position: relative;
	}

	.logo-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 10rpx;
		overflow: hidden;
		// background-color: #FFFFFF;
	}

	/* 胶囊*/
	.tn-custom-nav-bar__back {
		width: 100%;
		height: 100%;
		position: relative;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		box-sizing: border-box;
		background-color: rgba(0, 0, 0, 0.15);
		border-radius: 1000rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.5);
		color: #FFFFFF;
		font-size: 18px;

		.icon {
			display: block;
			flex: 1;
			margin: auto;
			text-align: center;
		}

		&:before {
			content: " ";
			width: 1rpx;
			height: 110%;
			position: absolute;
			top: 22.5%;
			left: 0;
			right: 0;
			margin: auto;
			transform: scale(0.5);
			transform-origin: 0 0;
			pointer-events: none;
			box-sizing: border-box;
			opacity: 0.7;
			background-color: #FFFFFF;
		}
	}

	/* 轮播视觉差 start */
	.card-swiper {
		height: 160rpx !important;
	}

	.card-swiper swiper-item {
		width: 750rpx !important;
		left: 0rpx;
		box-sizing: border-box;
		padding: 0rpx 30rpx 0rpx 30rpx;
		overflow: initial;
	}

	.card-swiper swiper-item .swiper-item {
		width: 100%;
		display: block;
		height: 100%;
		transform: scale(1);
		transition: all 0.2s ease-in 0s;
		border-radius: 10rpx;
		overflow: hidden;
	}

	.card-swiper swiper-item.cur .swiper-item {
		transform: none;
		transition: all 0.2s ease-in 0s;
	}

	.image-banner {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.image-banner image {
		width: 100%;
		height: 100%;
	}

	/* 轮播指示点 start*/
	.indication {
		z-index: 9999;
		width: 100%;
		height: 36rpx;
		position: absolute;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.spot {
		background-color: #FFFFFF;
		opacity: 0.6;
		width: 10rpx;
		height: 10rpx;
		border-radius: 20rpx;
		top: -40rpx;
		margin: 0 8rpx !important;
		position: relative;
	}

	.spot.active {
		opacity: 0.8;
		width: 20rpx;
		background-color: #FFFFFF;
	}

	/* 间隔线 start*/
	.tn-strip-bottom {
		width: 100%;
		border-bottom: 20rpx solid rgba(241, 241, 241, 0.8);
	}

	/* 间隔线 end*/
	/* 标题 start */
	.nav_title {
		-webkit-background-clip: text;
		color: transparent;

		&--wrap {
			position: relative;
			display: flex;
			height: 120rpx;
			font-size: 46rpx;
			align-items: center;
			justify-content: center;
			font-weight: bold;
			background-size: cover;
		}
	}

	/* 标题 end */


	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 10rpx 30rpx;
			margin: 20rpx 20rpx 5rpx 0rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/

	/* 内容图 start */
	.content-backgroup {
		z-index: -1;

		.backgroud-image {
			border-radius: 15rpx;
			width: 100%;
		}
	}

	/* 内容图 end */

	/* 商家商品 start*/
	.tn-blogger-content {
		&__wrap {
			box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
			border-radius: 20rpx;
			background-color: #FFFFFF;
			margin: 15rpx;
		}

		&__info {
			&__btn {
				margin-right: -12rpx;
				opacity: 0.5;
			}
		}

		&__label {
			&__item {
				line-height: 45rpx;
				padding: 0 10rpx;
				margin: 5rpx 18rpx 0 0;

				&--prefix {
					color: #E83A30;
					padding-right: 10rpx;
				}
			}

			&__desc {
				line-height: 35rpx;
			}
		}

		&__main-image {
			border-radius: 16rpx 16rpx 0 0;

			&--1 {
				max-width: 690rpx;
				min-width: 690rpx;
				max-height: 400rpx;
				min-height: 400rpx;
			}

			&--2 {
				max-width: 260rpx;
				max-height: 260rpx;
			}

			&--3 {
				height: 212rpx;
				width: 100%;
			}
		}

		&__count-icon {
			font-size: 24rpx;
			padding-right: 5rpx;
		}
	}

	.image-book {
		padding: 170rpx 0rpx;
		font-size: 16rpx;
		font-weight: 300;
		position: relative;
	}

	.image-picbook {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border-radius: 15rpx 15rpx 0 0;
	}

	.website-shadow {
		border-radius: 15rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
	}

	.text-content-2 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	/* 价格和参数布局样式 */
	.price-section {
		flex: 1;
		align-items: flex-start;
	}

	.params-section {
		align-items: flex-end;
		text-align: right;
	}

	.price-container {
		display: flex;
		align-items: baseline;
		flex-wrap: wrap;
	}

	.params-container {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		justify-content: flex-end;
	}
</style>