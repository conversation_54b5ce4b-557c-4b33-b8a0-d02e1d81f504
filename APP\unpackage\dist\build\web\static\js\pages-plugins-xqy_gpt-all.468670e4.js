(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-plugins-xqy_gpt-all"],{"20dd":function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return s})),e.d(a,"a",(function(){}));var i=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("v-uni-view",{staticClass:"user",class:t.$store.state.AppStyle},[i("v-uni-view",{staticClass:"header",style:[{height:t.CustomBar+"px"}]},[i("v-uni-view",{staticClass:"cu-bar bg-white",style:{height:t.CustomBar+"px","padding-top":t.StatusBar+"px"}},[i("v-uni-view",{staticClass:"action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.back.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"cuIcon-back"})],1),i("v-uni-view",{staticClass:"content text-bold",style:[{top:t.StatusBar+"px"}]},[t._v("AI大模型")]),i("v-uni-view",{staticClass:"action"})],1)],1),i("v-uni-view",{style:[{padding:t.NavBar+"px 10px 0px 10px"}]}),i("v-uni-view",{staticClass:"cu-list menu-avatar userList",staticStyle:{"margin-top":"20upx"}},[i("v-uni-view",{staticClass:"cu-bar bg-white search"},[i("v-uni-view",{staticClass:"search-form round"},[i("v-uni-text",{staticClass:"cuIcon-search"}),i("v-uni-input",{attrs:{type:"text",placeholder:"输入搜索关键字"},on:{input:function(a){arguments[0]=a=t.$handleEvent(a),t.searchTag.apply(void 0,arguments)}},model:{value:t.searchText,callback:function(a){t.searchText=a},expression:"searchText"}}),""!=t.searchText?i("v-uni-view",{staticClass:"search-close",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.searchClose()}}},[i("v-uni-text",{staticClass:"cuIcon-close"})],1):t._e()],1)],1),i("v-uni-view",{staticClass:"search-type grid col-2"},[i("v-uni-view",{staticClass:"search-type-box",class:0==t.type?"active":"",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.setType(0)}}},[i("v-uni-text",[t._v("聊天GPT")])],1),i("v-uni-view",{staticClass:"search-type-box",class:1==t.type?"active":"",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.setType(1)}}},[i("v-uni-text",[t._v("AI应用")])],1)],1),0==t.gptList.length&&t.dataLoad?i("v-uni-view",{staticClass:"no-data"},[i("v-uni-text",{staticClass:"cuIcon-text"}),t._v("暂时没有数据")],1):t._e(),t.dataLoad?t._e():i("v-uni-view",{staticClass:"dataLoad"},[i("v-uni-image",{attrs:{src:e("0b62")}})],1),t._l(t.gptList,(function(a,e){return i("v-uni-view",{key:e,staticClass:"cu-item"},[i("v-uni-view",{staticClass:"cu-avatar round lg",style:a.style}),i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"text-black"},[1==a.isVip?i("v-uni-text",{staticClass:"text-pink margin-right-xs"},[t._v("[VIP]")]):t._e(),t._v(t._s(a.name))],1),i("v-uni-view",{staticClass:"text-gray text-sm flex"},[t._v("单次请求："),i("v-uni-text",{staticClass:"text-orange"},[t._v(t._s(a.price))]),t._v(t._s(t.currencyName))],1)],1),i("v-uni-view",{staticClass:"action goUserIndex"},[i("v-uni-view",{staticClass:"cu-btn bg-blue light",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goChat(a)}}},[i("v-uni-text",{staticClass:"cuIcon-messagefill margin-right-xs"}),t._v("沟通")],1)],1)],1)})),t.gptList.length>=t.limit?i("v-uni-view",{staticClass:"load-more",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.loadMore.apply(void 0,arguments)}}},[i("v-uni-text",[t._v(t._s(t.moreText))])],1):t._e()],2),0==t.isLoading?i("v-uni-view",{staticClass:"loading"},[i("v-uni-view",{staticClass:"loading-main"},[i("v-uni-image",{attrs:{src:e("0b62")}})],1)],1):t._e()],1)},s=[]},"358a":function(t,a,e){"use strict";e.r(a);var i=e("7499"),s=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(n);a["default"]=s.a},"714e":function(t,a,e){"use strict";e.r(a);var i=e("20dd"),s=e("358a");for(var n in s)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return s[t]}))}(n);var o=e("828b"),c=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"408d8c2d",null,!1,i["a"],void 0);a["default"]=c.exports},7499:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("e966"),e("f7a5"),e("aa9c"),e("c223");var i=e("9254"),s={data:function(){return{StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,gptList:[],searchText:"",type:0,page:1,limit:10,moreText:"加载更多",isLoad:0,isLoading:0,currencyName:"",dataLoad:!1}},onPullDownRefresh:function(){this.page=1,this.getGptList(!1),setTimeout((function(){uni.stopPullDownRefresh()}),1e3)},onReachBottom:function(){0==this.isLoad&&this.loadMore()},onShow:function(){this.page=1},onLoad:function(){var t=this;t.getGptList(!1),uni.request({url:t.$API.SPset(),method:"GET",dataType:"json",success:function(a){t.currencyName=a.data.assetsname},fail:function(t){console.log(t)}})},methods:{allCache:function(){},back:function(){uni.navigateBack({delta:1})},searchTag:function(){this.searchText;this.page=1,this.getGptList()},setType:function(t){this.type=t,this.page=1,this.gptList=[],this.getGptList()},searchClose:function(){this.searchText="",this.page=1,this.getGptList()},formatDate:function(t){t=new Date(parseInt(1e3*t));var a=t.getFullYear(),e=("0"+(t.getMonth()+1)).slice(-2),i=("0"+t.getDate()).slice(-2),s=("0"+t.getHours()).slice(-2),n=("0"+t.getMinutes()).slice(-2),o=a+"-"+e+"-"+i+" "+s+":"+n;return o},loadMore:function(){this.moreText="正在加载中...",this.isLoad=1,this.getGptList(!0)},getGptList:function(t){var a=this,e=a.page;t&&e++;var s="";if(i.localStorage.getItem("userinfo")){var n=JSON.parse(i.localStorage.getItem("userinfo"));s=n.token}a.dataLoad=!1,a.$Net.request({url:a.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"models",type:a.type,limit:a.limit,page:e,search_key:a.searchText,token:s},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(e){if(a.isLoad=0,a.dataLoad=!0,1==e.data.code||200==e.data.code){var i=[];if(e.data.data&&Array.isArray(e.data.data)?i=e.data.data:e.data.data&&e.data.data.list&&Array.isArray(e.data.data.list)&&(i=e.data.data.list),i.length>0){var s=[];for(var n in i){var o=i[n];o.avatar&&(o.style="background-image:url("+o.avatar+");"),s.push(o)}t?(a.page++,a.gptList=a.gptList.concat(s)):a.gptList=s}else t?a.moreText="没有更多数据了":a.gptList=[]}else t?a.moreText="加载失败":a.gptList=[],uni.showToast({title:e.data.msg||"获取数据失败",icon:"none"});setTimeout((function(){a.isLoading=1,clearTimeout("timer")}),300)},fail:function(t){a.dataLoad=!0,a.isLoad=0,a.moreText="加载更多",uni.showToast({title:"网络连接失败",icon:"none"});setTimeout((function(){a.isLoading=1,clearTimeout("timer")}),300)}})},toLink:function(t){if(!i.localStorage.getItem("token")||""==i.localStorage.getItem("token"))return uni.showToast({title:"请先登录哦",icon:"none"}),!1;uni.navigateTo({url:t})},goChat:function(t){if(!i.localStorage.getItem("token")||""==i.localStorage.getItem("token"))return uni.showToast({title:"请先登录哦",icon:"none"}),!1;0==t.type?uni.navigateTo({url:"/pages/plugins/xqy_gpt/chat?model_id="+t.id+"&name="+t.name}):uni.navigateTo({url:"/pages/plugins/xqy_gpt/app?model_id="+t.id+"&name="+t.name})},subText:function(t,a){return t?t.length>a?(t=t.substring(0,a),t+"……"):t:"管理员暂未设置介绍"}}};a.default=s}}]);