<?php include 'Head.php'; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="card-title">插件状态</h4>
                <p class="card-title-desc">查看行为验证插件的运行状态</p>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h5 class="card-title">插件信息</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>插件名称：</strong></td>
                                        <td>小祈愿-行为验证</td>
                                    </tr>
                                    <tr>
                                        <td><strong>插件版本：</strong></td>
                                        <td>1.0.0</td>
                                    </tr>
                                    <tr>
                                        <td><strong>作者：</strong></td>
                                        <td>小祈愿</td>
                                    </tr>
                                    <tr>
                                        <td><strong>安装状态：</strong></td>
                                        <td><span id="install_status" class="badge badge-secondary">检查中...</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>启用状态：</strong></td>
                                        <td><span id="enable_status" class="badge badge-secondary">检查中...</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>验证服务：</strong></td>
                                        <td><span id="captcha_status" class="badge badge-secondary">检查中...</span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h5 class="card-title">当前配置</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>验证类型：</strong></td>
                                        <td><span id="captcha_type">-</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>配置状态：</strong></td>
                                        <td><span id="config_status" class="badge badge-secondary">检查中...</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>数据库表：</strong></td>
                                        <td><span id="db_status" class="badge badge-secondary">检查中...</span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border">
                            <div class="card-body">
                                <h5 class="card-title">功能说明</h5>
                                <div class="alert alert-info">
                                    <h6><strong>插件功能：</strong></h6>
                                    <ul class="mb-0">
                                        <li>为用户登录、注册、忘记密码页面提供第三方行为验证</li>
                                        <li>支持极验、Cloudflare Turnstile、Google reCAPTCHA等验证服务</li>
                                        <li>可作为现有图片验证码的可选替代方案</li>
                                        <li>支持动态切换验证方式，保持与原有系统的完全兼容</li>
                                    </ul>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h6><strong>使用说明：</strong></h6>
                                    <ul class="mb-0">
                                        <li>插件启用后，前端页面将自动检测并使用行为验证</li>
                                        <li>插件禁用时，系统将自动回退到原有的图片验证码</li>
                                        <li>请确保在验证配置页面正确配置第三方服务的密钥</li>
                                        <li>建议在测试环境中充分测试后再在生产环境中启用</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="checkStatus()">刷新状态</button>
                        <a href="captchaConfig.php" class="btn btn-secondary">配置管理</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    checkStatus();
});

function checkStatus() {
    // 检查插件状态
    $.ajax({
        url: '../../Actions/getPluginStatus.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.code === 200) {
                var status = response.data;
                
                // 更新安装状态
                if (status.plugin_installed) {
                    $('#install_status').removeClass().addClass('badge badge-success').text('已安装');
                } else {
                    $('#install_status').removeClass().addClass('badge badge-danger').text('未安装');
                }
                
                // 更新启用状态
                if (status.plugin_enabled) {
                    $('#enable_status').removeClass().addClass('badge badge-success').text('已启用');
                } else {
                    $('#enable_status').removeClass().addClass('badge badge-warning').text('未启用');
                }
                
                // 更新验证服务状态
                if (status.captcha_enabled) {
                    $('#captcha_status').removeClass().addClass('badge badge-success').text('服务正常');
                } else {
                    $('#captcha_status').removeClass().addClass('badge badge-warning').text('服务未启用');
                }
                
                // 检查配置状态
                if (status.overall_enabled) {
                    $('#config_status').removeClass().addClass('badge badge-success').text('配置正常');
                    $('#db_status').removeClass().addClass('badge badge-success').text('表结构正常');
                } else {
                    $('#config_status').removeClass().addClass('badge badge-warning').text('需要配置');
                    $('#db_status').removeClass().addClass('badge badge-warning').text('需要检查');
                }
            } else {
                $('#install_status').removeClass().addClass('badge badge-danger').text('检查失败');
                $('#enable_status').removeClass().addClass('badge badge-danger').text('检查失败');
                $('#captcha_status').removeClass().addClass('badge badge-danger').text('检查失败');
            }
        },
        error: function() {
            $('#install_status').removeClass().addClass('badge badge-danger').text('网络错误');
            $('#enable_status').removeClass().addClass('badge badge-danger').text('网络错误');
            $('#captcha_status').removeClass().addClass('badge badge-danger').text('网络错误');
        }
    });
    
    // 检查当前配置
    $.ajax({
        url: '../../Actions/getConfig.php',
        type: 'POST',
        data: { admin: '1' },
        dataType: 'json',
        success: function(response) {
            if (response.code === 200) {
                var config = response.data;
                var typeNames = {
                    'geetest': '极验验证',
                    'cloudflare': 'Cloudflare Turnstile',
                    'recaptcha': 'Google reCAPTCHA'
                };
                $('#captcha_type').text(typeNames[config.captcha_type] || config.captcha_type);
            } else {
                $('#captcha_type').text('配置错误');
            }
        },
        error: function() {
            $('#captcha_type').text('网络错误');
        }
    });
}
</script>

<?php include 'Footer.php'; ?>
