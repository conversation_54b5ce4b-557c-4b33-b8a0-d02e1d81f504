<template>
	<view class="forumIndex" :class="[{'fade-in': isLoaded}, isDark?'dark':'']">
		<view v-if="swiperType==1" :class="{'data-box-2-banner-1': !swiperStyle}" class="animate-item" :style="'animation-delay: 0.1s'">
		<tn-swiper :list="swiperList" :effect3d="swiperStyle" :class="swiperStyle?'':'uni-swiper-slides-1'" @click="swiperclick" :backgroundColor="isDark?'#2c2c2c':'#fff'" :height="swiperHeight"
			:effect3dPreviousSpacing="80"></tn-swiper>
		</view>
		 <swiper class="screen-swiper animate-item" v-if="isSwiper==1&&swiperType==2" :class="dotStyle?'square-dot':'round-dot'" :indicator-dots="true" :circular="true"
		 :autoplay="true" interval="5000" duration="500" :style="'animation-delay: 0.1s'">
			<swiper-item v-for="(item,index) in swiperList" :key="index" @click="swiperclick(index)" >
				<image :src="item.url" mode="aspectFill"></image>
				<view class="forum-swiper">
					<view class="forum-swiper-bg"></view>
					<view class="forum-swiper-title clamp-text-1">{{item.title}}</view>
				</view>
			</swiper-item>
		</swiper>
		<view class="data-box section-box recommend-section animate-item" style="margin-top: 0;" v-if="recommendOf==1" :style="'animation-delay: 0.2s'">
			<view class="section-box-title ">
				<text class="left-title"></text>圈子推荐
			</view>
		  <!-- 方式16 start-->
		  <view class="recommend-container">
		    <view class="recommend-item" v-for="(item, index) in recommendSectionList" :key="index" @click="goSection(item.id)">
		      <view class="recommend-item-content">
		        <view class="recommend-avatar-container">
		          <view class="recommend-avatar" :style="item.pic ? 'background-image:url('+ item.pic +')' : ''" v-if="item.pic">
		          </view>
		          <view class="recommend-avatar-placeholder" v-else>
		            <text class="placeholder-text">{{item.name.charAt(0)}}</text>
		          </view>
		        </view>
		        <view class="recommend-name">{{item.name}}</view>
		        <view class="recommend-count">{{formatNumber(item.followNum)}}人加入</view>
		      </view>
		    </view>
		  </view>
		  <!-- 方式16 end-->
		</view>
		<view class="section">
			<view class="section-box animate-item" v-if="kuaijie == 1" :style="'animation-delay: 0.3s'">
				<view class="section-main">
					<view class="section-box-title">
						<text class="left-title"></text>快捷入口
					</view>
					<view class="section-box-list grid col-2" :style="fatherTitle == 1?'':'margin-top:30upx'">
						<view class="section-item forum-shadow"  @tap="goLinks('/pages/forum/followPost')"
						:class="{
						  'border-radius-10': radiusBoxStyle == 1,
						  'border-radius-32 ': radiusBoxStyle == 2,
						  'border-radius-50': radiusBoxStyle == 3
						}">
							<view class="section-ico">
								<view class="section-ico-no bg-pink"
								:class="{
							  'border-radius-10': radiusStyle == 1,
							  'border-radius-32 ': radiusStyle == 2,
							  'border-radius-50': radiusStyle == 3
							}">
									<text class="cuIcon-likefill"></text>
								</view>
							</view>
							<view class="section-intro">
								<view class="section-item-tile">
									关注人
								</view>
								<view class="section-item-value">
									关注人帖子
								</view>
							</view>
						</view>
						<view class="section-item forum-shadow" @tap="goLinks('/pages/forum/section')"
						:class="{
						  'border-radius-10': radiusBoxStyle == 1,
						  'border-radius-32 ': radiusBoxStyle == 2,
						  'border-radius-50': radiusBoxStyle == 3
						}">
							<view class="section-ico">
								<view class="section-ico-no bg-purple"
								:class="{
								  'border-radius-10': radiusStyle == 1,
								  'border-radius-32 ': radiusStyle == 2,
								  'border-radius-50': radiusStyle == 3
								}">
									<text class="cuIcon-likefill"></text>
								</view>
							</view>
							<view class="section-intro">
								<view class="section-item-tile">
									圈子列表
								</view>
								<view class="section-item-value">
									查看所有圈子
								</view>
							</view>
						</view>
			
					</view>
				</view>
				
			</view>
			<view class="section-box animate-item" v-if="fatherTitle == 0" :style="'animation-delay: 0.4s'">
				<view class="section-main">
					<view class="section-box-title">
						<text class="left-title"></text>全部圈子
					</view>
				</view>
			</view>
			<view class="section-box animate-item" v-for="(item,index) in sectionList" :key="index" :style="'animation-delay: ' + (0.4 + index * 0.1) + 's'">
				<view class="section-main">
					<view class="section-box-title" v-if="fatherTitle == 1">
						<text class="left-title"></text>{{item.name}}
					</view>
					<view class="section-box-list grid col-2">
						<view class="section-item forum-shadow" v-for="(data,j) in item.subList" :key="j" @tap="goSection(data.id)"
						 :class="{
						   'border-radius-10': radiusBoxStyle == 1,
						   'border-radius-32 ': radiusBoxStyle == 2,
						   'border-radius-50': radiusBoxStyle == 3
						 }" >
							<view class="section-ico"
							:class="{
							  'img-border-radius-10': radiusStyle == 1,
							  'img-border-radius-32 ': radiusStyle == 2,
							  'img-border-radius-50': radiusStyle == 3
							}">
								<image :src="data.pic" mode="aspectFill" v-if="data.pic&&data.pic!=''"></image>
								<view class="section-placeholder" v-else
									:class="{
									  'img-border-radius-10': radiusStyle == 1,
									  'img-border-radius-32 ': radiusStyle == 2,
									  'img-border-radius-50': radiusStyle == 3
									}">
									<text class="placeholder-icon">{{data.name.charAt(0)}}</text>
								</view>
							</view>
							<view class="section-intro">
								<view class="section-item-tile clamp-text-1">
									{{data.name}}
								</view>
								<view class="section-item-value clamp-text-1">
									{{formatNumber(data.postNum)}} 帖子<span style="font-size: 20upx;margin: 0upx 10upx;">|</span> {{formatNumber(data.followNum)}} 加入
								</view>
								 
							</view>
						</view>

					</view>
				</view>
				
			</view>

		</view>
	</view>
</template>

<script>
	import darkModeMixin from '@/utils/darkModeMixin.js'
	export default {
		props: {
		    sectionList: {
			  type: Array,
			  default: () => []
			},
		    forumSwiper: {
			  type: Array,
			  default: () => []
			},
			swiperList: {
			  type: Array,
			  default: () => []
			},
			recommendSectionList: {
			  type: Array,
			  default: () => []
			},
			recommendOf: {
			  type: Number,
			  default: 1
			},
			kuaijie: {
			  type: Number,
			  default: 0
			},
			swiperType: {
			  type: Number,
			  default: 1
			},
			fatherTitle: {
			  type: Number,
			  default: 1
			},
			isSwiper: {
			  type: Number,
			  default: 1
			},
			radiusStyle:{
		      type: Number,
			  default: 1
			},
			radiusBoxStyle:{
			  type: Number,
			  default: 1
			},
			swiperStyle:{
				type: Boolean,
				default: false
			},
			isDark: {
				type: Boolean,
				default: false
			}
		},
		mixins: [darkModeMixin],
		name: "forumIndex",
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				cardCur: 0,
				dotStyle: false,
				swiperHeight: this.$API.SPforumSwiperHeight(),
				towerStart: 0,
				direction: '',
				isLoaded: false
			};
		},
		mounted() {
			// 延迟触发动画，确保DOM渲染完成
			setTimeout(() => {
				this.isLoaded = true;
			}, 100);
		},
		methods: {
			goLogin(){
				uni.navigateTo({
				    url: '/pages/user/login'
				});
			},
			goSection(id){
				var that = this;
				uni.navigateTo({
				    url: '/pages/forum/home?id='+id
				});
			},
			goLinks(link){
				var that = this;
				uni.navigateTo({
				    url: link
				});
			},
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},
			swiperclick(index) {
				//console.log('Clicked on index:', index);
				const data = this.swiperList[index];
				if (data.type=='image') {
					this.goInfo(data)
				}else{
					this.goAds2(data.zt)
				}
				
			},
			goAds2(url) {
				var that = this;
				// #ifdef APP-PLUS
				plus.runtime.openWeb(url);
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			goInfo(data){
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/info?id='+data.id
				});
			},
		}
	}
</script>

<style>
	.text-content-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}
  .image-circle{
    width: 108rpx;
    height: 108rpx;
    font-size: 40rpx;
    font-weight: 300;
    position: relative;
	box-shadow: 0rpx 4rpx 20rpx #e7e7e7;
	border-radius: 40rpx;
  }
  .image-pic{
    background-size: cover;
    background-repeat:no-repeat;
    background-position:top;
    border-radius: 40rpx;
  }
	.left-title{
		border-left: 6px solid #000;
		margin-right: 15upx;
		border-radius: 20upx;
	}
	.grid.col-2>view{
		width: 47%;
	}
	.forum-shadow {
	 box-shadow: 0upx 10upx 20upx rgb(207 207 207 / 40%);
	}
	
	.img-border-radius-10 image{
		border-radius: 20upx;
	}
	.img-border-radius-32 image{
		border-radius: 32upx;
	}
	.img-border-radius-50 image{
		border-radius: 50%;
	}
	.border-radius-10{
		border-radius: 20upx;
	}
	.border-radius-32{
		border-radius: 32upx;
	}
	.border-radius-50{
		border-radius: 100upx;
	}
	.section-item{
		padding: 10upx;
		transition: all 0.2s;
		margin: 12upx 8upx;
	}
	.section-item-value{
		font-size: 22upx;
	}
	.section-item-tile{
		margin-top: 12upx;
	}
	.section-box-title{
		color: #333;
		font-size: 30upx !important;
		font-weight: bold;
		padding:4upx 20upx 0upx 20upx;
		margin-bottom: 6upx;
		margin-top: 15upx;
	}
	.clamp-text-1 {
	  -webkit-line-clamp: 1;
	  display: -webkit-box;
	  -webkit-box-orient: vertical;
	  text-overflow: ellipsis;
	  overflow: hidden;
	}
	.data-box-2-banner-1 {
		padding: 0;
		margin: 0 20upx;
	}
	.forumIndex {
    background-color: #ffffff;
    color: #333333;
	opacity: 0;
	transition: opacity 0.6s ease-in-out;
}

.forumIndex.fade-in {
	opacity: 1;
}

/* 渐入动画 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30upx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-item {
	opacity: 0;
	animation: fadeInUp 0.6s ease-out forwards;
}

/* 圈子推荐样式优化 */
.recommend-container {
	display: flex;
	flex-wrap: wrap;
	padding: 0 20upx;
}

.recommend-item {
	width: 25%;
	padding: 0 10upx;
	margin-bottom: 30upx;
	box-sizing: border-box;
}

.recommend-item-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.recommend-avatar-container {
	width: 108upx;
	height: 108upx;
	margin-bottom: 15upx;
	position: relative;
}

.recommend-avatar {
	width: 100%;
	height: 100%;
	border-radius: 40upx;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	box-shadow: 0 4upx 20upx rgba(231, 231, 231, 0.6);
}

.recommend-avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 40upx;
	background: linear-gradient(135deg, #3cc9a4, #2ba085);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4upx 20upx rgba(60, 201, 164, 0.3);
}

.placeholder-text {
	color: #ffffff;
	font-size: 40upx;
	font-weight: bold;
}

.recommend-name {
	font-size: 27upx;
	color: #333;
	margin-bottom: 8upx;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.recommend-count {
	font-size: 22upx;
	color: #999;
}

/* 板块图标占位符样式 */
.section-placeholder {
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #3cc9a4, #2ba085);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.section-placeholder.img-border-radius-10 {
	border-radius: 20upx;
}

.section-placeholder.img-border-radius-32 {
	border-radius: 32upx;
}

.section-placeholder.img-border-radius-50 {
	border-radius: 50%;
}

.placeholder-icon {
	color: #ffffff;
	font-size: 36upx;
	font-weight: bold;
}
</style>