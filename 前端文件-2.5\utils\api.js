/*
StarPro 版本：2.5
StarPro 作者：森云 QQ：2504531378
StarPro 交流群：752454468
StarPro 文档：https://www.yuque.com/senyun-ev0j3/StarPro
禁止使用该源码搭建运行违规违法内容。一经发现永久永久封禁授权并将违规信息提交至公安机关！
同时欢迎各位向我举报用于搭建运营违规的站点。
未经允许禁止破解、传播、售卖源码...否则保留追究法律责任的权利！
盗版有许多未知风险，如果喜欢这套程序请支持正版！


*/
//核心配置
	var API_URL = '#'; //StarApi域名，最后要带“/” 比如：https://baidu.com/
	var STAR_URL = '#'; //Star后台域名，最后要带“/” 比如：https://baidu.com/
	var WEB_URL = '#'; //h5或网页端域名，可不填，用于生成分享链接，最后要带“/” 比如：https://baidu.com/
	var appKey = "#"; //APPkey 在StarApi管理中心的应用管理生成获取
//基本配置
	var userID = "UID"; //用户标识 例如：UID、社区号
	var appEmail = "#"; //站长邮箱
	var appName = "#"; //社区名称
	var appLogo = "../../static/icon.png"; //社区LOGO，支持外链与本地图片
	var company = "#"; //运营主体 例如：XX公司  用于隐私政策显示
	
//转跳配置
	//卡密购买链接 ↓ ↓ ↓ 
	var buyCodeUrl = ""; //获取卡密转跳的卡网链接，不填留空
	//邀请码购买链接 ↓ ↓ ↓ 
	var buyInviteCodeUrl = "";

//维护模式配置（用户无法连接你的服务器时候会弹出维护弹窗，通过弹窗讲用户引流到你的官网、QQ群获取最新版本）
	var failText = "当你看到此通知，可能因为<b>版本过旧或网站被运营商屏蔽，重试更换网络、关闭VPN、重新进入APP</b>，也可前往官网或添加官方群来获取最新版本！官方用户交流QQ群：XXXXXXX";
	//维护弹窗的说明（支持html，除a标签）
	var failWeb = "#";
	//你的官网（例如：https://www.baidu.com/）
	var failUrl = "#";
	//你的官方QQ群（加群链接）

//其他配置
	//全局置顶是否合并在首页帖子最上方显示
	var isShowTop = true;//true为是，false为否（首页为帖子模式下生效）
	
	//轮播图高度设置  1px约等于2upx
	var homeSwiperHeight = 300; //首页，建议200-400之间 单位upx
	var forumSwiperHeight = 300; //圈子页（仅在唯美风格下生效），建议200-400之间 单位upx
	var findSwiperHeight = 300; //发现页，建议200-400之间 单位upx

	//VIP、等级图片本地化
	var isLocalPic = 1 // 0关 1开 说明：打开后修改等级会员图标需要修改APP源码中的图片，重新打包生效。关闭后图标以Star后台内img文件夹的图片显示，修改无需重新打包

	//全局显示IP归属地
	var localof = 0; //0关 1开 说明：打开后，评论帖子发布都会显示用户的IP归属地。

	//编辑器上传文件超时时间 
	var uploadTime = 300; //单位：秒 建议60秒左右，如果运营的是视频站或者服务器配置较低，可以调久一些

	//禁止VPN环境使用APP，为1时开启（可以在安卓和苹果APP中防止抓包）
	var banVPN = 1; //由于uniapp官方的问题，只能拦截部分条件

	//上架配置
	var isHuaWei = 0 //上架华为  0关 1开

//下列勿动 下列勿动 下列勿动 下列勿动！！！
//下列勿动 下列勿动 下列勿动 下列勿动！！！
//下列勿动 下列勿动 下列勿动 下列勿动！！！
//下列勿动 下列勿动 下列勿动 下列勿动！！！

var linkStar = WEB_URL + "#/pages/contents/info?cid={cid}&title=title" //文章
var forumStar = WEB_URL + "#/pages/forum/info?id={id}&title=post" //帖子
var appStar = WEB_URL + "#/pages/plugins/sy_appbox/info?id={id}&title=app" //应用
var shopStar = WEB_URL + "#/pages/shop/shopinfo?sid={sid}&title=shop" //商品
var videoStar = WEB_URL + "#/pages/plugins/xqy_video/detail?id={id}&title=video" //视频
var rankList = [];
var leverList = [];
var rankStyle = [];

if (localStorage.getItem('AppInfo')) {
	try {
		var AppInfo = JSON.parse(localStorage.getItem('AppInfo'));
	} catch (e) {
		console.log(e);
	}

}



//自定义字段配置（和可视化配置中心保持一致，英文逗号分割），默认的字段名称是小灯泡模板的abcimg字段，假如你的模板是用其它的字段进行判断，可以自己全局搜索abcimg进行修改，当然也可以什么都不做，这并不会导致使用出现问题。
var fields = "abcimg";


import {
	localStorage
} from '@/js_sdk/mp-storage/mp-storage/index.js'
//获取应用信息
var videoPoster = '';
if (localStorage.getItem('appVideoPoster')) {
	try {
		videoPoster = localStorage.getItem('appVideoPoster');
	} catch (e) {
		console.log(e);
	}
}
var cachedPlugins = localStorage.getItem('getPlugins');
if (cachedPlugins) {
    var pluginList = JSON.parse(cachedPlugins);
}
var swiperStyle = 1;
if (localStorage.getItem('appSwiperStyle')) {
	swiperStyle = localStorage.getItem('appSwiperStyle');
}
var haveAppInfo = 0;
if (localStorage.getItem('AppInfo')) {
	haveAppInfo = 1;
}
var ficlke = 0;
if (videoPoster == '') {
	uni.request({
		url: STAR_URL + 'Plugins/sy_starpro/api.php?act=gonggao',
		header: {
			'Content-Type': 'application/x-www-form-urlencoded'
		},
		method: "get",
		dataType: "json",
		success(res) {
			videoPoster = res.data.videoimg;
			localStorage.setItem('appVideoPoster', videoPoster);
		},
		fail: function(res) {
			uni.showToast({
				title: "获取应用的配置信息失败！",
				icon: 'none'
			})
		}
	})
}
uni.request({
	url: STAR_URL + 'Plugins/sy_starpro/api.php?act=getPlugins',
	header: {
	  'Content-Type': 'application/x-www-form-urlencoded'
	},
	method: 'GET',
	dataType: 'json',
	success: (res) => {
		if (res.data.code === 200) {
			pluginList = res.data.data;
			localStorage.setItem('getPlugins', JSON.stringify(pluginList));
			console.log(pluginList);
		}
	},
	fail: (res) => {
		uni.showToast({
			title: '获取应用配置的信息失败.',
			icon: 'none'
		});
	}
});
uni.request({
	url: STAR_URL + 'Plugins/sy_starpro/api.php?act=opset',
	header: {
		'Content-Type': 'application/x-www-form-urlencoded'
	},
	method: 'GET',
	dataType: "json",
	success(res) {
			swiperStyle = res.data.swiperinfo;
			localStorage.setItem('appSwiperStyle', swiperStyle);
	},
	fail: function(res) {
		localStorage.setItem('appSwiperStyle', ficlke);
		uni.showToast({
			title: "获取应用配置的信息失败！",
			icon: 'none'
		})
		
	}
})
if(haveAppInfo!=1){
	uni.request({
		url: API_URL + "systemStarPro/app",
		data: {
			"key": appKey
		},
		header: {
			'Content-Type': 'application/x-www-form-urlencoded'
		},
		method: "get",
		dataType: 'json',
		success: function(res) {
			if (res.data.code == 1) {
				localStorage.setItem('AppInfo', JSON.stringify(res.data.data));
			}
		},
		fail: function(res) {
			uni.showToast({
				title: "获取应用配置信息失败！",
				icon: 'none'
			})
		}
	})
}
module.exports = {
	getVideoPoster() {
		return videoPoster;
	},
	getFailText() {
		return failText;
	},
	getFailWeb() {
		return failWeb;
	},
	getFailUrl() {
		return failUrl;
	},
	localOf() {
		return localof;
	},
	isHuaWei() {
		return isHuaWei;
	},
	getBanVPN() {
		return banVPN;
	},
	SPwxmpurl() {
		return STAR_URL;
	},
	GetCompany() {
		return company;
	},
	getAppKey() {
		return appKey;
	},
	GetuserID() {
		return userID;
	},
	GetStyleIndex() {
		return "index";
	},
	GetRankList() {
		return rankList;
	},
	GetLeverList() {
		return leverList;
	},
	GetRankStyle() {
		return rankStyle;
	},
	SPhomeSwiperHeight() {
		return homeSwiperHeight;
	},
	SPforumSwiperHeight() {
		return forumSwiperHeight;
	},
	SPfindSwiperHeight() {
		return findSwiperHeight;
	},
	GetAppName: function() {

		if (localStorage.getItem('AppInfo')) {
			var AppInfo = JSON.parse(localStorage.getItem('AppInfo'));
			appName = AppInfo.name;
		}
		return appName;
	},
	GetBuyCodeUrl: function() {
		return buyCodeUrl;
	},
	GetBuyInviteCodeUrl: function() {
		return buyInviteCodeUrl;
	},
	GetAppEmail: function() {
		return appEmail;
	},
	GetAdpid: function() {
		var adpid = "";
		if (localStorage.getItem('AppInfo')) {
			var AppInfo = JSON.parse(localStorage.getItem('AppInfo'));
			adpid = AppInfo.adpid;
		}
		return adpid;
	},
	GetLogo: function() {
		var logo = "";
		if (localStorage.getItem('AppInfo')) {
			var AppInfo = JSON.parse(localStorage.getItem('AppInfo'));
			logo = AppInfo.logo;
		}
		return logo;
	},
	GetAdsVideoType: function() {
		var adsVideoType = 1;
		if (localStorage.getItem('AppInfo')) {
			var AppInfo = JSON.parse(localStorage.getItem('AppInfo'));
			adsVideoType = AppInfo.adsVideoType;
		}
		return adsVideoType;
	},
	GetLinkRule: function() {
		return linkStar;
	},
	GetAppLogo: function() {
		return appLogo;
	},
	GetforumStar: function() {
		return forumStar;
	},
	GetappStar: function() {
		return appStar;
	},
	GetshopStar: function() {
		return shopStar;
	},
	GetvideoStar: function() {
		return videoStar;
	},
	GetisShowTop: function() {
		return isShowTop;
	},
	GetRaiders: function() {
		return raiders;
	},
	GetAboutme: function() {
		return aboutme;
	},
	GetFields: function() {
		return fields;
	},
	GetWebUrl: function() {
		return WEB_URL;
	},
	getApiUrl: function() {
		return API_URL;
	},
	getuploadTime: function() {
		return uploadTime;
	},
	getAppinfo: function() {
		return API_URL + "systemStarPro/app";
	},
	userLogin: function() {
		return API_URL + 'SProUsers/userLogin';
	},
	phoneLogin: function() {
		return API_URL + 'SProUsers/phoneLogin';
	},
	RegSendCode: function() {
		return API_URL + 'SProUsers/RegSendCode';
	},
	userPhoneFoget(){
		return API_URL + 'SProUsers/userPhoneFoget';
	},
	userRegisterByPhone(){
		return API_URL + 'SProUsers/userRegisterByPhone';
	},
	sendSMS: function() {
		return API_URL + 'SProUsers/sendSMS';
	},
	SendCode: function() {
		return API_URL + 'SProUsers/SendCode';
	},
	userApi: function() {
		return API_URL + 'SProUsers/apiLogin';
	},
	userRegister: function() {
		return API_URL + 'SProUsers/userRegister';
	},
	userFoget: function() {
		return API_URL + 'SProUsers/userFoget';
	},
	getUserInfo: function() {
		return API_URL + 'SProUsers/userInfo';
	},
	getUserList: function() {
		return API_URL + 'SProUsers/userList';
	},
	userEdit: function() {
		return API_URL + 'SProUsers/userEdit';
	},
	getUserData: function() {
		return API_URL + 'SProUsers/userData';
	},
	userDelete: function() {
		return API_URL + 'SProUsers/userDelete';
	},
	userRecharge: function() {
		return API_URL + 'SProUsers/userRecharge';
	},
	selfDeleteOk: function() {
		return API_URL + 'SProUsers/selfDeleteOk';
	},
	selfDeleteList: function() {
		return API_URL + 'SProUsers/selfDeleteList';
	},
	userWithdraw: function() {
		return API_URL + 'SProUsers/userWithdraw';
	},
	withdrawList: function() {
		return API_URL + 'SProUsers/withdrawList';
	},
	withdrawStatus: function() {
		return API_URL + 'SProUsers/withdrawStatus';
	},
	manageUserEdit: function() {
		return API_URL + 'SProUsers/manageUserEdit';
	},
	apiBind: function() {
		return API_URL + 'SProUsers/apiBind';
	},
	apiBindDelete: function() {
		return API_URL + 'SProUsers/apiBindDelete';
	},
	userBindStatus: function() {
		return API_URL + 'SProUsers/userBindStatus';
	},
	setScan: function() {
		return API_URL + 'SProUsers/setScan';
	},
	userStatus: function() {
		return API_URL + 'SProUsers/userStatus';
	},
	regConfig: function() {
		return API_URL + 'SProUsers/regConfig';
	},
	signOut: function() {
		return API_URL + 'SProUsers/signOut';
	},
	getScan:function(){
		  return API_URL +  'SProUsers/getScan';
		},
	getScanStatus:function(){
	  return API_URL +  'SProUsers/getScanStatus';
	},
	//邀请码注册相关
	madeInvitation: function() {
		return API_URL + 'SProUsers/madeInvitation';
	},
	invitationList: function() {
		return API_URL + 'SProUsers/invitationList';
	},
	invitationExcel: function() {
		return API_URL + 'SProUsers/invitationExcel';
	},
	setClientId: function() {
		return API_URL + 'SProUsers/setClientId';
	},
	//消息相关
	getInbox: function() {
		return API_URL + 'SProUsers/inbox';
	},
	unreadNum: function() {
		return API_URL + 'SProUsers/unreadNum';
	},
	setRead: function() {
		return API_URL + 'SProUsers/setRead';
	},
	sendUser: function() {
		return API_URL + 'SProUsers/sendUser';
	},
	//关注
	follow: function() {
		return API_URL + 'SProUsers/follow';
	},
	isFollow: function() {
		return API_URL + 'SProUsers/isFollow';
	},
	followList: function() {
		return API_URL + 'SProUsers/followList';
	},
	fanList: function() {
		return API_URL + 'SProUsers/fanList';
	},
	selfDelete: function() {
		return API_URL + 'SProUsers/selfDelete';
	},
	//封禁
	banUser: function() {
		return API_URL + 'SProUsers/banUser';
	},
	unblockUser: function() {
		return API_URL + 'SProUsers/unblockUser';
	},
	violationList: function() {
		return API_URL + 'SProUsers/violationList';
	},
	userClean: function() {
		return API_URL + 'SProUsers/userClean';
	},
	restrict: function() {
		return API_URL + 'SProUsers/restrict';
	},
	getKaptcha: function() {
		return API_URL + 'SProUsers/getKaptcha';
	},
	giftVIP: function() {
		return API_URL + 'SProUsers/giftVIP';
	},
	getInvitationCode: function() {
		return API_URL + 'SProUsers/getInvitationCode';
	},

	getMarkList: function() {
		return API_URL + 'SProUserlog/markList';
	},
	getIsMark: function() {
		return API_URL + 'SProUserlog/isMark';
	},

	addLog: function() {
		return API_URL + 'SProUserlog/addLog';
	},
	removeLog: function() {
		return API_URL + 'SProUserlog/removeLog';
	},
	dataClean: function() {
		return API_URL + 'SProUserlog/dataClean';
	},
	adsGift: function() {
		return API_URL + 'SProUserlog/adsGift';
	},
	adsGiftNotify: function() {
		return API_URL + 'SProUserlog/adsGiftNotify';
	},

	getCommentsList: function() {
		return API_URL + 'SProComments/commentsList';
	},
	setComments: function() {
		return API_URL + 'SProComments/commentsAdd';
	},
	commentsDelete: function() {
		return API_URL + 'SProComments/commentsDelete';
	},
	commentsAudit: function() {
		return API_URL + 'SProComments/commentsAudit';
	},


	//根据标签或者分类获取文章
	getMetaContents: function() {
		return API_URL + 'SProMetas/selectContents';
	},
	getMetasList: function() {
		return API_URL + 'SProMetas/metasList';
	},
	geMetaInfo: function() {
		return API_URL + 'SProMetas/metaInfo';
	},
	editMeta: function() {
		return API_URL + 'SProMetas/editMeta';
	},
	deleteMeta: function() {
		return API_URL + 'SProMetas/deleteMeta';
	},
	addMeta: function() {
		return API_URL + 'SProMetas/addMeta';
	},
	metaRecommend: function() {
		return API_URL + 'SProMetas/toRecommend';
	},

	getContentsList: function() {
		return API_URL + 'SProContents/contentsList';
	},
	getContentsInfo: function() {
		return API_URL + 'SProContents/contentsInfo';
	},
	contentsAdd: function() {
		return API_URL + 'SProContents/contentsAdd';
	},

	contentsUpdate: function() {
		return API_URL + 'SProContents/contentsUpdate';
	},
	contentsImage: function() {
		return API_URL + 'SProContents/ImagePexels';
	},
	allData: function() {
		return API_URL + 'SProContents/allData';
	},
	contentsDelete: function() {
		return API_URL + 'SProContents/contentsDelete';
	},
	contentsAudit: function() {
		return API_URL + 'SProContents/contentsAudit';
	},
	getForeverblog: function() {
		return API_URL + 'SProContents/foreverblog';
	},
	//文章是否评论过
	isCommnet: function() {
		return API_URL + 'SProContents/isCommnet';
	},
	//文章推荐
	toRecommend: function() {
		return API_URL + 'SProContents/toRecommend';
	},
	//文章置顶
	toTop: function() {
		return API_URL + 'SProContents/addTop';
	},
	//文章轮播
	toSwiper: function() {
		return API_URL + 'SProContents/addSwiper';
	},
	//设置自定义字段
	setFields: function() {
		return API_URL + 'SProContents/setFields';
	},
	contentConfig: function() {
		return API_URL + 'SProContents/contentConfig';
	},
	rewardList: function() {
		return API_URL + 'SProContents/rewardList';
	},
	upload: function() {
		return API_URL + 'upload/full';
	},
	shopList: function() {
		return API_URL + 'SProShop/shopList';
	},
	shopInfo: function() {
		return API_URL + 'SProShop/shopInfo';
	},
	addShop: function() {
		return API_URL + 'SProShop/addShop';
	},
	editShop: function() {
		return API_URL + 'SProShop/editShop';
	},
	deleteShop: function() {
		return API_URL + 'SProShop/deleteShop';
	},
	deleteShopType: function() {
		return API_URL + 'SProShop/deleteShopType';
	},
	shopTypeInfo: function() {
		return API_URL + 'SProShop/shopTypeInfo';
	},
	editShopType: function() {
		return API_URL + 'SProShop/editShopType';
	},
	addShopType: function() {
		return API_URL + 'SProShop/addShopType';
	},
	shopTypeList: function() {
		return API_URL + 'SProShop/shopTypeList';
	},
	buyShop: function() {
		return API_URL + 'SProShop/buyShop';
	},
	isBuyShop: function() {
		return API_URL + 'SProShop/isBuyShop';
	},
	auditShop: function() {
		return API_URL + 'SProShop/auditShop';
	},
	getVipInfo: function() {
		return API_URL + 'SProShop/vipInfo';
	},
	buyVIP: function() {
		return API_URL + 'SProShop/buyVIP';
	},
	buyVIPpackage: function() {
		return API_URL + 'SProShop/buyVIPpackage';
	},
	orderList: function() {
		return API_URL + 'SProUserlog/orderList';
	},
	orderSellList: function() {
		return API_URL + 'SProUserlog/orderSellList';
	},

	//文章挂载商品
	mountShop: function() {
		return API_URL + 'SProShop/mountShop';
	},
	//支付宝当面付
	scancodePayStar: function() {
		return API_URL + 'pay/scancodePayStar';
	},
	//微信支付（官方）
	WxPayStar: function() {
		return API_URL + 'pay/WxPayStar';
	},
	//卡密充值
	tokenPayStar: function() {
		return API_URL + 'pay/tokenPayStar';
	},
	//易支付
	EPayStar: function() {
		return API_URL + 'pay/EPayStar';
	},
	//充值二维码生成
	qrCodeStar: function() {
		return API_URL + 'pay/qrCodeStar';
	},
	payLogList: function() {
		return API_URL + 'pay/payorderList';
	},
	//卡密充值相关
	tokenPayList: function() {
		return API_URL + 'pay/tokenPayList';
	},
	tokenPayExcel: function() {
		return API_URL + 'pay/tokenPayExcel';
	},

	//生成卡密
	madetoken: function() {
		return API_URL + 'pay/madetoken';
	},
	//财务记录
	financeList: function() {
		return API_URL + 'pay/financeList';
	},
	financeTotal: function() {
		return API_URL + 'pay/financeTotal';
	},
	//付费广告
	adsConfig: function() {
		return API_URL + 'SProAds/adsConfig';
	},
	adsInfo: function() {
		return API_URL + 'SProAds/adsInfo';
	},

	addAds: function() {
		return API_URL + 'SProAds/addAds';
	},
	adsList: function() {
		return API_URL + 'SProAds/adsList';
	},
	editAds: function() {
		return API_URL + 'SProAds/editAds';
	},
	deleteAds: function() {
		return API_URL + 'SProAds/deleteAds';
	},
	auditAds: function() {
		return API_URL + 'SProAds/auditAds';
	},
	renewalAds: function() {
		return API_URL + 'SProAds/renewalAds';
	},
	//聊天
	getPrivateChat: function() {
		return API_URL + 'SProChat/getPrivateChat';
	},
	sendMsg: function() {
		return API_URL + 'SProChat/sendMsg';
	},
	myChat: function() {
		return API_URL + 'SProChat/myChat';
	},
	msgList: function() {
		return API_URL + 'SProChat/msgList';
	},
	msgSetRead: function() {
		return API_URL + 'SProChat/msgSetRead';
	},
	deleteChat: function() {
		return API_URL + 'SProChat/deleteChat';
	},
	deleteMsg: function() {
		return API_URL + 'SProChat/deleteMsg';
	},
	createGroup: function() {
		return API_URL + 'SProChat/createGroup';
	},
	editGroup: function() {
		return API_URL + 'SProChat/editGroup';
	},
	allChat: function() {
		return API_URL + 'SProChat/allChat';
	},
	banChat: function() {
		return API_URL + 'SProChat/banChat';
	},
	groupInfo: function() {
		return API_URL + 'SProChat/groupInfo';
	},
	//动态开始

	addSpace: function() {
		return API_URL + 'SProSpace/addSpace';
	},
	editSpace: function() {
		return API_URL + 'SProSpace/editSpace';
	},
	spaceInfo: function() {
		return API_URL + 'SProSpace/spaceInfo';
	},
	spaceList: function() {
		return API_URL + 'SProSpace/spaceList';
	},
	myFollowSpace: function() {
		return API_URL + 'SProSpace/myFollowSpace';
	},
	spaceDelete: function() {
		return API_URL + 'SProSpace/spaceDelete';
	},
	spaceLikes: function() {
		return API_URL + 'SProSpace/spaceLikes';
	},
	spaceReview: function() {
		return API_URL + 'SProSpace/spaceReview';
	},
	spaceLock: function() {
		return API_URL + 'SProSpace/spaceLock';
	},
	followSpace: function() {
		return API_URL + 'SProSpace/followSpace';
	},

	//圈子开始
	addSection: function() {
		return API_URL + 'SProForum/addSection';
	},
	editSection: function() {
		return API_URL + 'SProForum/editSection';
	},
	sectionList: function() {
		return API_URL + 'SProForum/sectionList';
	},
	deleteSection: function() {
		return API_URL + 'SProForum/deleteSection';
	},
	sectionInfo: function() {
		return API_URL + 'SProForum/sectionInfo';
	},
	sectionFollow: function() {
		return API_URL + 'SProForum/sectionFollow';
	},
	sectionClock: function() {
		return API_URL + 'SProForum/sectionClock';
	},
	postCommentReview: function() {
		return API_URL + 'SProForum/postCommentReview';
	},
	setModerator: function() {
		return API_URL + 'SProForum/setModerator';
	},
	deleteModerator: function() {
		return API_URL + 'SProForum/deleteModerator';
	},
	postForum: function() {
		return API_URL + 'SProForum/post';
	},
	editForum: function() {
		return API_URL + 'SProForum/edit';
	},
	postInfoForum: function() {
		return API_URL + 'SProForum/postInfo';
	},
	postList: function() {
		return API_URL + 'SProForum/postList';
	},
	postLikes: function() {
		return API_URL + 'SProForum/postLikes';
	},
	postLock: function() {
		return API_URL + 'SProForum/postLock';
	},
	postReview: function() {
		return API_URL + 'SProForum/postReview';
	},
	postTop: function() {
		return API_URL + 'SProForum/postTop';
	},
	postRecommend: function() {
		return API_URL + 'SProForum/postRecommend';
	},
	postSwiper: function() {
		return API_URL + 'SProForum/postSwiper';
	},
	postDelete: function() {
		return API_URL + 'SProForum/postDelete';
	},
	sectionClockList: function() {
		return API_URL + 'SProForum/sectionClockList';
	},
	postComments: function() {
		return API_URL + 'SProForum/postComments';
	},
	postCommentLike: function() {
		return API_URL + 'SProForum/postCommentLike';
	},
	postCommentDelete: function() {
		return API_URL + 'SProForum/postCommentDelete';
	},
	postCommentList: function() {
		return API_URL + 'SProForum/postCommentList';
	},
	userPurview: function() {
		return API_URL + 'SProForum/userPurview';
	},
	postMark: function() {
		return API_URL + 'SProForum/postMark';
	},
	postMarkList: function() {
		return API_URL + 'SProForum/postMarkList';
	},
	postReward: function() {
		return API_URL + 'SProForum/postReward';
	},
	postRewardList: function() {
		return API_URL + 'SProForum/postRewardList';
	},
	sectionRecommend: function() {
		return API_URL + 'SProForum/sectionRecommend';
	},
	draftList: function() {
		return API_URL + 'SProForum/draftList';
	},

	draftDelete: function() {
		return API_URL + 'SProForum/draftDelete';
	},
	postTransfer: function() {
		return API_URL + 'SProForum/postTransfer';
	},
	followPosts: function() {
		return API_URL + 'SProForum/followPosts';
	},
	vipTypeListStar: function() {
		return API_URL + 'systemStarPro/vipTypeList';
	},
	identifyConsumer: function() {
		return API_URL + 'identify/identifyConsumer';
	},
	identifyCompany: function() {
		return API_URL + 'identify/identifyCompany';
	},
	identifyHand: function() {
		return API_URL + 'identify/identifyHand';
	},
	identifyStatus: function() {
		return API_URL + 'identify/identifyStatus';
	},
	identifyInfo: function() {
		return API_URL + 'identify/identifyInfo';
	},
	addConsumer: function() {
		return API_URL + 'identify/addConsumer';
	},
	systemIdentifyConsumer: function() {
		return API_URL + 'identify/systemIdentifyConsumer';
	},
	systemIdentifyCompany: function() {
		return API_URL + 'identify/systemIdentifyCompany';
	},
	removeConsumer: function() {
		return API_URL + 'identify/removeConsumer';
	},
	addCompany: function() {
		return API_URL + 'identify/addCompany';
	},
	removeCompany: function() {
		return API_URL + 'identify/removeCompany';
	},
	companyList: function() {
		return API_URL + 'identify/companyList';
	},
	consumerList: function() {
		return API_URL + 'identify/consumerList';
	},
	//SPro
	SPuser: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=usercount'
	},
	SPvip: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/vip.gif'
		}else{
			return '../../static/localpic/vip.gif'
		}
	},
	SpPluginVip: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/vip.gif'
		}else{
			return '../../../static/localpic/vip.gif'
		}
	},
	SpPluginLv: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/lv'
		}else{
			return '../../../static/localpic/lv'
		}
	},
	SpPluginRz: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/rz.png'
		}else{
			return '../../../static/localpic/rz.png'
		}
	},
	SPman: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/man.png'
		}else{
			return '../../static/localpic/man.png'
		}
	},
	SPwoman: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/woman.png'
		}else{
			return '../../static/localpic/woman.png'
		}
	},
	SPLv: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/lv'
		}else{
			return '../../static/localpic/lv'
		}
	},
	SPRz: function() {
		if(isLocalPic==0){
			return STAR_URL + 'img/rz.png'
		}else{
			return '../../static/localpic/rz.png'
		}
	},
	SPmusicpic: function() {
		return STAR_URL + 'img/music.png'
	},
	SPimgs: function() {
		return STAR_URL + 'img/'
	},
	SPgonggao: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=gonggao'
	},
	SPset: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=opset'
	},
	SPguanzhu: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=guanzhu'
	},
	SPquguan: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=quguan'
	},
	SPdongtairemove: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=dongtairemove'
	},
	SPpinglun: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=pinglun'
	},
	SPfenlei: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=fenlei'
	},
	SPdashang: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=dashang'
	},
	SPhy: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=vip'
	},
	SPjingyan: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=jingyan'
	},
	SPjifen: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=jifen'
	},
	SPadimg: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=adimg2'
	},
	GetUpdateUrl: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?update=1';
	},
	SPgetdongtai: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getdongtai'
	},
	SPleiji: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=leiji'
	},
	SPqiandaojl: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=qiandaojl'
	},
	SPqiandao: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=qiandao'
	},
	SPqqlogin: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=qqlogin'
	},
	SPqquser: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=qquser'
	},
	SPqqqun: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=qqqun'
	},
	SPshenhe: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=shenhe'
	},
	SPkefu: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=kefu'
	},
	SPgetjifen: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getjifen'
	},
	SPgetrenwu: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getrenwu'
	},
	SPQQbang: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=QQbang'
	},
	SPWXbang: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=WXbang'
	},
	SPWBbang: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=WBbang'
	},
	SPremove: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=remove'
	},
	SPchongzhiset: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=chongzhiset'
	},
	SPviphide: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=viphide'
	},
	SPqzxz: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=qzxz'
	},
	SPlikeall: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=likeall'
	},
	SPglyremove: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=glyremove'
	},
	SPiconimg: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=geticonimg'
	},
	SPgetcompany: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getcompany'
	},
	SPgetuncompany: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getuncompany'
	},
	SPgetconsumer: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getconsumer'
	},
	SPgetunconsumer: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getunconsumer'
	},
	SPgetwzpost: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=getwzpost'
	},
	SPwzpostremove: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=wzpostremove'
	},
	SPwzpostmyremove: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=wzpostmyremove'
	},
	SPstardmAdlogin: function() {
		return STAR_URL + 'Plugins/sy_starpro/api.php?act=adminLogin';
	},
	SPstarMusicUpload: function() {
		return STAR_URL + 'mp3.php';
	},
	SPgetAppInfoUrl: function() {
		return STAR_URL + 'Plugins/sy_appbox/Pages/getAppInfo.php';
	},
	//插件1.0
	LoadPluginLoad: function() {
		return STAR_URL + 'Plugins/Load_Plugin.php?';
	},
	LoadFreePluginLoad: function() {
		return STAR_URL + 'Plugins/Load_FreePlugin.php?';
	},
	//插件2.0
	PluginLoad(pluginName) {
		const timestamp = Date.now();
		return `${STAR_URL}Plugins/${pluginName}/index.php?timestamp=${timestamp}&plugin=${pluginName}&`;
    },
	gptSendMsg:function(){
		return API_URL + 'SProgpt/sendMsg';
	},
	gptSendText:function(){
		return API_URL + 'SProgpt/sendText';
	},
	gptLastMsg:function(){
		return API_URL + 'SProgpt/lastMsg';
	},
	gptMsgList:function(){
		return API_URL + 'SProgpt/msgList';
	},
	gptAdd:function(){
		return API_URL + 'SProgpt/gptAdd';
	},
	gptEdit:function(){
		return API_URL + 'SProgpt/gptEdit';
	},
	gptChatDelete:function(){
		return API_URL + 'SProgpt/gptChatDelete';
	},
	gptSystemMsgList:function(){
		return API_URL + 'SProgpt/systemMsgList';
	},
	gptList:function(){
		return API_URL + 'SProgpt/gptList';
	},
	gptInfo:function(){
		return API_URL + 'SProgpt/gptInfo';
	},
	gptDelete:function(){
		return API_URL + 'SProgpt/gptDelete';
	},
	
	IsNull(obj) {
		return (obj != null && obj != undefined);
	},
	randomHexColor() { //随机生成十六进制颜色
		var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
		while (hex.length < 6) { //while循环判断hex位数，少于6位前面加0凑够6位
			hex = '0' + hex;
		}
		return '#' + hex; //返回‘#'开头16进制颜色
	},
	//获取日期
	formatDate(datetime) {
		var datetime = new Date(parseInt(datetime * 1000));
		// 获取年月日时分秒值  slice(-2)过滤掉大于10日期前面的0
		var year = datetime.getFullYear(),
			month = ("0" + (datetime.getMonth() + 1)).slice(-2),
			date = ("0" + datetime.getDate()).slice(-2),
			hour = ("0" + datetime.getHours()).slice(-2),
			minute = ("0" + datetime.getMinutes()).slice(-2);
		//second = ("0" + date.getSeconds()).slice(-2);
		// 拼接
		var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
		// 返回
		return result;
	},
	getLever(num) {
		var lv = 0;
		if (num < 10) {
			lv = 1;
		} else if (num >= 10 && num < 50) {
			lv = 2;
		} else if (num >= 50 && num < 200) {
			lv = 3;
		} else if (num >= 200 && num < 500) {
			lv = 4;
		} else if (num >= 500 && num < 1000) {
			lv = 5;
		} else if (num >= 1000 && num < 2000) {
			lv = 6;
		} else if (num >= 2000 && num < 5000) {
			lv = 7;
		} else if (num >= 5000 && num < 10000) {
			lv = 8;
		} else if (num >= 10000) {
			lv = 9;
		}
		return lv;
	},
	//移除数据中的空对象
	removeObjectEmptyKey(json) {
		var value;
		for (var key in json) {
			if (json.hasOwnProperty(key)) {
				value = json[key];
				if (value === undefined || value === '' || value === null) {
					delete json[key]
				}
			}
		}
		return json;
	}
}