(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-plugins-xqy_gpt-manage-gptchat"],{"10be":function(t,e,a){"use strict";a.d(e,"b",(function(){return s})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={mpHtml:a("efec").default},s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"user",class:t.$store.state.AppStyle},[i("v-uni-view",{staticClass:"header",style:[{height:t.CustomBar+"px"}]},[i("v-uni-view",{staticClass:"cu-bar bg-white",style:{height:t.CustomBar+"px","padding-top":t.<PERSON><PERSON><PERSON>+"px"}},[i("v-uni-view",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"cuIcon-back"})],1),i("v-uni-view",{staticClass:"content text-bold",style:[{top:t.StatusBar+"px"}]},[0==t.uid?[t._v("GPT聊天消息管理")]:[t._v("单独查询UID："+t._s(t.uid))]],2),i("v-uni-view",{staticClass:"action"},[0!=t.uid?[i("v-uni-text",{staticClass:"text-blue",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setUid(0)}}},[t._v("重置查询")])]:t._e()],2)],1)],1),i("v-uni-view",{style:[{padding:t.NavBar+"px 10px 0px 10px"}]}),i("v-uni-view",{staticClass:"cu-card dynamic no-card",staticStyle:{"margin-top":"20upx"}},[i("v-uni-view",{staticClass:"cu-bar bg-white search"},[i("v-uni-view",{staticClass:"search-form round"},[i("v-uni-text",{staticClass:"cuIcon-search"}),i("v-uni-input",{attrs:{type:"text",placeholder:"搜索消息内容或用户名"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.searchTag.apply(void 0,arguments)}},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}})],1)],1),0==t.uid?i("v-uni-view",{staticClass:"select-type"},[i("v-uni-text",{staticClass:"select-type-value",class:-1==t.isAI?"cur":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setType(-1)}}},[t._v("全部")]),i("v-uni-text",[t._v("|")]),i("v-uni-text",{staticClass:"select-type-value",class:0==t.isAI?"cur":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setType(0)}}},[t._v("仅用户")]),i("v-uni-text",[t._v("|")]),i("v-uni-text",{staticClass:"select-type-value",class:1==t.isAI?"cur":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setType(1)}}},[t._v("仅AI")])],1):t._e(),t._l(t.msgList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"cu-item chat-history",class:0==e.isAI?"self":""},[i("v-uni-view",{staticClass:"chat-history-user"},[1==e.isAI?[i("v-uni-view",{staticClass:"cu-avatar radius",style:"background-image:url("+e.gptJson.avatar+");"})]:[i("v-uni-view",{staticClass:"cu-avatar radius",style:"background-image:url("+e.userJson.avatar+");"})]],2),i("v-uni-view",{staticClass:"chat-history-main"},[i("v-uni-view",{staticClass:"chat-history-user"},[1==e.isAI?[t._v(t._s(e.gptJson.name))]:[t._v(t._s(e.userJson.name))]],2),i("v-uni-view",{staticClass:"content shadow break-all",on:{longpress:function(a){arguments[0]=a=t.$handleEvent(a),t.ToCopy(e.text)}}},[i("mp-html",{attrs:{content:e.text,selectable:!0,"show-img-menu":!0,"scroll-table":!0,markdown:!0}})],1),i("v-uni-view",{staticClass:"date"},[t._v(t._s(t.formatDate(e.created)))]),0==e.isAI?[i("v-uni-view",{staticClass:"chat-history-btn"},[i("v-uni-text",{staticClass:"text-blue",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.setUid(e.userJson.uid)}}},[t._v("查看消息")]),i("v-uni-text",{staticClass:"text-red margin-left",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toBan(e.userJson.uid)}}},[t._v("封禁用户")])],1)]:t._e()],2)],1)})),t.msgList.length>=t.limit?i("v-uni-view",{staticClass:"load-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[i("v-uni-text",[t._v(t._s(t.moreText))])],1):t._e()],2),0==t.isLoading?i("v-uni-view",{staticClass:"loading"},[i("v-uni-view",{staticClass:"loading-main"},[i("v-uni-image",{attrs:{src:a("0b62")}})],1)],1):t._e()],1)},n=[]},"17d9":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("5c47"),a("dfcf"),a("aa9c"),a("c223"),a("e966"),a("f7a5"),a("a1c1");var i=a("9254"),s={data:function(){return{StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,uid:0,isAI:-1,id:0,msgList:[],moreText:"加载更多",page:1,limit:10,searchText:"",isLoading:0,avatar:"",name:"",price:"",intro:""}},onPullDownRefresh:function(){this.page=1,this.getGptMsgList(!1);setTimeout((function(){uni.stopPullDownRefresh()}),1e3)},onReachBottom:function(){this.loadMore()},onShow:function(){this.page=1},onLoad:function(t){this.getGptMsgList(!1)},methods:{back:function(){uni.navigateBack({delta:1})},replaceAll:function(t,e,a){return t.split(e).join(a)},loadMore:function(){this.moreText="正在加载中...",0==this.isLoad&&this.getGptMsgList(!0)},toInfo:function(t,e){uni.navigateTo({url:"/pages/contents/info?cid="+t+"&title="+e})},setType:function(t){this.isAI=t,this.page=1,this.getGptMsgList(!1)},setUid:function(t){this.isAI=-1,this.uid=t,this.page=1,this.getGptMsgList()},searchTag:function(){this.searchText;this.page=1,this.getGptMsgList()},getGptMsgList:function(t){var e=this,a="";if(i.localStorage.getItem("userinfo")){var s=JSON.parse(i.localStorage.getItem("userinfo"));a=s.token}var n=e.page;t&&n++;e.$Net.request({url:e.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"history",limit:e.limit,page:n,uid:e.uid>0?e.uid:null,search_key:e.searchText,isAI:e.isAI,scene:"admin",token:a},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(a){if(e.isLoad=0,1==a.data.code||200==a.data.code){var i=[];if(a.data.data&&Array.isArray(a.data.data)?i=a.data.data:a.data.data&&a.data.data.list&&Array.isArray(a.data.data.list)&&(i=a.data.data.list),i.length>0){var s=[];for(var n in i){var c=i[n];c.userJson||(c.userJson={name:"用户",avatar:"/static/user/avatar.png"}),1!=c.isAI||c.gptJson||(c.gptJson={name:"AI助手",avatar:"/static/admin/images/ai.png"}),s.push(c)}t?(e.page++,e.msgList=e.msgList.concat(s)):e.msgList=s,e.moreText="加载更多"}else e.moreText="没有更多数据了",t||(e.msgList=i)}else uni.showToast({title:a.data.msg||"获取消息列表失败",icon:"none"});setTimeout((function(){e.isLoading=1,clearTimeout("timer")}),300)},fail:function(t){e.isLoad=0,e.moreText="加载更多",uni.showToast({title:"网络请求失败",icon:"none"});setTimeout((function(){e.isLoading=1,clearTimeout("timer")}),300)}})},toStatus:function(t){this.status=t,this.page=1,this.moreText="加载更多",this.isLoad=0,this.getCommentsList(!1)},formatDate:function(t){t=new Date(parseInt(1e3*t));var e=t.getFullYear(),a=("0"+(t.getMonth()+1)).slice(-2),i=("0"+t.getDate()).slice(-2),s=("0"+t.getHours()).slice(-2),n=("0"+t.getMinutes()).slice(-2),c=e+"-"+a+"-"+i+" "+s+":"+n;return c},replaceSpecialChar:function(t){return!!t&&(t=t.replace(/&quot;/g,'"'),t=t.replace(/&amp;/g,"&"),t=t.replace(/&lt;/g,"<"),t=t.replace(/&gt;/g,">"),t=t.replace(/&nbsp;/g," "),t)},toBan:function(t){uni.navigateTo({url:"/pages/manage/banuser?uid="+t})}}};e.default=s},ccdc:function(t,e,a){"use strict";a.r(e);var i=a("17d9"),s=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=s.a},ee9e:function(t,e,a){"use strict";a.r(e);var i=a("10be"),s=a("ccdc");for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(n);var c=a("828b"),o=Object(c["a"])(s["default"],i["b"],i["c"],!1,null,"8f09c552",null,!1,i["a"],void 0);e["default"]=o.exports}}]);