(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-plugins-xqy_gpt-chat"],{"0cef":function(t,a,e){"use strict";var n=e("3780"),i=e.n(n);i.a},3780:function(t,a,e){var n=e("d295");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("967d").default;i("44d87a9a",n,!0,{sourceMap:!1,shadowMode:!1})},"3b72":function(t,a,e){"use strict";e("6a54");var n=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("e966"),e("f7a5"),e("aa9c"),e("8f71"),e("bf0f"),e("0c26"),e("d4b5"),e("5c47"),e("a1c1"),e("45da"),e("5ef2"),e("dfcf");var i=n(e("fcf3")),o=n(e("39d8")),s=e("9254"),c={data:function(){var t;return t={StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,InputBottom:0,id:0,name:"未知大模型",moreText:"获取更多",isWaiting:0,toid:0,avatar:"",userInfo:null,token:"",avatarstyle:"",msg:"",page:1,msgList:[],uid:"",msgLoading:null,lastTime:0,group:"",modalName:"",lastid:0,isLoading:!0,price:"",intro:"",currencyName:"",scrollTop:0,model_id:0},(0,o.default)(t,"name","AI助手"),(0,o.default)(t,"avatar",""),(0,o.default)(t,"isDisabled",!1),(0,o.default)(t,"gpt",{id:0,name:"AI助手",model_name:"",model_id:"",api_key:"",source:"喵小算",welcome:"",tips:"",system:"",status:1}),(0,o.default)(t,"chatBg",""),(0,o.default)(t,"msgContent",""),(0,o.default)(t,"statusBarHeight",0),(0,o.default)(t,"windowHeight",0),(0,o.default)(t,"showNoLoginTip",!1),(0,o.default)(t,"sending",!1),t},onPageScroll:function(t){this.scrollTop=t.scrollTop},onShow:function(){s.localStorage.getItem("userinfo")&&(this.userInfo=JSON.parse(s.localStorage.getItem("userinfo")),this.uid=this.userInfo.uid,this.group=this.userInfo.group)},onReachBottom:function(){this.page=1},onBackPress:function(){},onUnload:function(){},onLoad:function(t){var a=this;uni.showLoading({title:"加载中..."}),uni.request({url:a.$API.SPset(),method:"GET",dataType:"json",success:function(t){a.currencyName=t.data.assetsname},fail:function(t){console.error("获取系统设置失败:",t)}}),t.id?a.id=t.id:t.model_id&&(a.id=t.model_id),t.name&&(a.name=t.name),a.id?(a.getGptInfo(),a.getMsgList(),a.isLoading=1):(console.error("未提供有效的模型ID"),uni.hideLoading(),uni.showToast({title:"未提供有效的模型ID",icon:"none"})),setTimeout((function(){uni.hideLoading(),uni.pageScrollTo({duration:0,scrollTop:99999999})}),300)},methods:{back:function(){clearInterval(this.msgLoading),this.msgLoading=null,uni.navigateBack({delta:1})},formatDate:function(t){t=new Date(parseInt(1e3*t));var a=t.getFullYear(),e=("0"+(t.getMonth()+1)).slice(-2),n=("0"+t.getDate()).slice(-2),i=("0"+t.getHours()).slice(-2),o=("0"+t.getMinutes()).slice(-2),s=a+"-"+e+"-"+n+" "+i+":"+o;return s},InputFocus:function(t){this.isOwO=!1,this.InputBottom=t.detail.height},InputBlur:function(t){this.InputBottom=0},previewImage:function(t){var a=[];a.push(t),uni.previewImage({urls:a,current:a[0]})},getMsgList:function(){var t=this,a="";if(s.localStorage.getItem("userinfo")){var e=JSON.parse(s.localStorage.getItem("userinfo"));a=e.token}t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"history",model_id:t.id,limit:100,scene:"chat",isAI:-1,token:a},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(a){if(uni.stopPullDownRefresh(),t.isLoad=0,1==a.data.code||200==a.data.code){var e=[];a.data.data&&Array.isArray(a.data.data)?e=a.data.data:a.data.data&&a.data.data.list&&Array.isArray(a.data.data.list)&&(e=a.data.data.list),e.length>0&&(t.msgList=e,t.msgList=t.msgList.filter((function(a){return a.isAI=a.isAI||(1==a.type?1:0),!!(1!=a.isAI||a.text&&""!==a.text.trim())&&(1==a.isAI?a.gptJson||(a.gptJson={avatar:t.avatar||"/static/admin/images/ai.png",name:t.name||"AI助手"}):(a.userJson=a.userJson||{},!a.userJson.avatar&&t.userInfo&&(a.userJson.avatar=t.userInfo.avatar||"/static/user/avatar.png"),!a.userJson.nickname&&t.userInfo&&(a.userJson.nickname=t.userInfo.nickname||t.userInfo.name||"用户")),!0)})))}else console.error("获取历史消息失败:",a.data.msg),uni.showToast({title:a.data.msg||"获取历史消息失败",icon:"none"});t.isLoading=1,setTimeout((function(){uni.pageScrollTo({duration:0,scrollTop:99999999})}),200)},fail:function(a){uni.stopPullDownRefresh(),t.isLoad=0,t.isLoading=1,console.error("获取消息列表失败:",a),uni.showToast({title:"网络连接失败，请稍后重试",icon:"none"})}})},getGptInfo:function(){var t=this;t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"models",id:t.id},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(a){if(1==a.data.code||200==a.data.code){var e=null;"object"===(0,i.default)(a.data.data)&&null!==a.data.data?e=a.data.data.info?a.data.data.info:a.data.data:Array.isArray(a.data.data)&&a.data.data.length>0&&(e=a.data.data[0]),e?(t.avatarstyle="background-image:url("+e.avatar+");",t.avatar=e.avatar,t.name=e.name,t.price=e.price,t.intro=e.intro):(console.error("无法解析模型信息"),uni.showToast({title:"无法获取模型信息",icon:"none"}))}else console.error("获取模型信息失败:",a.data.msg),uni.showToast({title:a.data.msg||"获取模型信息失败",icon:"none"})},fail:function(t){console.error("获取GPT信息失败:",t),uni.showToast({title:"网络连接失败，请稍后再试",icon:"none"})}})},sendMsg:function(){var t=this;if(""==t.msg)return!1;if(t.msg.length>1500)return uni.showToast({title:"最大字符数为1500",icon:"none"}),!1;if(!s.localStorage.getItem("userinfo")||""==s.localStorage.getItem("userinfo"))return uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/user/login"}),!1;t.userInfo=JSON.parse(s.localStorage.getItem("userinfo")),t.uid=t.userInfo.uid;var a=t.userInfo.token,e=Date.parse(new Date),n={created:e/1e3,text:t.msg,isAI:0,type:0,uid:t.uid,userJson:{avatar:t.userInfo.avatar,nickname:t.userInfo.nickname||t.userInfo.name}};t.msgList.push(n),setTimeout((function(){uni.pageScrollTo({duration:0,scrollTop:99999999})}),100);var o=t.msg;t.msg="",t.isWaiting=1,t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"chat",model_id:t.id,message:o,token:a},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(a){if(t.isWaiting=0,1==a.data.code||200==a.data.code){var e="";e="string"===typeof a.data.data?a.data.data:"object"===(0,i.default)(a.data.data)&&null!==a.data.data?a.data.data.response?a.data.data.response:JSON.stringify(a.data.data):"很抱歉，服务器返回了意外的响应格式，请稍后再试。",e&&""!==e||(e="很抱歉，服务器返回了空内容，请稍后再试。");var n={created:Date.parse(new Date)/1e3,text:e,isAI:1,uid:"ai",userJson:{avatar:t.avatar,nickname:t.name}};t.msgList.push(n),setTimeout((function(){uni.pageScrollTo({duration:0,scrollTop:99999999})}),200)}else{402==a.data.code?uni.showModal({title:"余额不足",content:"您的余额不足，无法使用该模型，请充值后再试",showCancel:!0,confirmText:"去充值",success:function(t){t.confirm&&uni.navigateTo({url:"/pages/user/pay"})}}):uni.showToast({title:a.data.msg||"请求失败",icon:"none"});var o={created:Date.parse(new Date)/1e3,text:"很抱歉，我无法回答您的问题。"+(a.data.msg||"发生了一个错误"),isAI:1,uid:"ai",userJson:{avatar:t.avatar,nickname:t.name}};t.msgList.push(o),setTimeout((function(){uni.pageScrollTo({duration:0,scrollTop:99999999})}),200)}},fail:function(a){t.isWaiting=0,console.error("发送消息失败:",a),uni.showToast({title:"网络开小差了哦",icon:"none"});var e={created:Date.parse(new Date)/1e3,text:"很抱歉，网络连接失败，请稍后再试。",isAI:1,uid:"ai",userJson:{avatar:t.avatar,nickname:t.name}};t.msgList.push(e),setTimeout((function(){uni.pageScrollTo({duration:0,scrollTop:99999999})}),200)}})},goHistory:function(){uni.navigateTo({url:"/pages/plugins/xqy_gpt/history?id="+this.id+"&name="+this.name})},ToCopy:function(t){var a=document.createElement("textarea");a.value=t,a.readOnly="readOnly",document.body.appendChild(a),a.select(),a.setSelectionRange(0,t.length),uni.showToast({title:"复制成功"});document.execCommand("copy");a.remove()},upload:function(){var t=this,a="";if(!s.localStorage.getItem("userinfo"))return uni.showToast({title:"请先登录",icon:"none"}),clearInterval(t.msgLoading),t.msgLoading=null,uni.navigateTo({url:"/pages/user/login"}),!1;var e=JSON.parse(s.localStorage.getItem("userinfo"));a=e.token,uni.chooseImage({count:6,sourceType:["album","camera"],success:function(e){uni.showLoading({title:"上传中"});for(var n=e.tempFilePaths,i=0;i<n.length;i++)uni.uploadFile({url:t.$API.upload(),filePath:n[i],name:"file",formData:{token:a},success:function(a){1==n.length&&setTimeout((function(){uni.hideLoading()}),1e3);var e=JSON.parse(a.data);1==e.code&&t.sendURL(1,e.data.url)},fail:function(){}})}})},sendURL:function(t,a){var e=this,n="";if(""==e.url)return!1;if(!s.localStorage.getItem("userinfo"))return uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/user/login"}),!1;var i=JSON.parse(s.localStorage.getItem("userinfo"));n=i.token;var o={chatid:e.chatid,token:n,url:a,type:t};e.$Net.request({url:e.$API.sendMsg(),data:o,header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(t){1==t.data.code?(e.getMsgList(),e.msg=""):uni.showToast({title:t.data.msg,icon:"none"})},fail:function(t){uni.showToast({title:"网络开小差了哦",icon:"none"})}})},markHtml:function(t){t=this.replaceAll(t,"<","&lt;"),t=this.replaceAll(t,">","&gt;");var a=this.owoTextList;for(var e in a)-1!=this.replaceSpecialChar(t).indexOf(a[e].data)&&(t=this.replaceAll(this.replaceSpecialChar(t),a[e].data,"<img src='/"+a[e].icon+"' class='tImg' />"));return t},goChatInfo:function(){},replaceSpecialChar:function(t){return!!t&&(t=t.replace(/&quot;/g,'"'),t=t.replace(/&amp;/g,"&"),t=t.replace(/&lt;/g,"<"),t=t.replace(/&gt;/g,">"),t=t.replace(/&nbsp;/g," "),t)},replaceAll:function(t,a,e){return t.split(a).join(e)},showModal:function(t){this.modalName=t.currentTarget.dataset.target},hideModal:function(){this.modalName=null},gptChatDelete:function(){var t=this;if(this.modalName=null,s.localStorage.getItem("userinfo")&&""!=s.localStorage.getItem("userinfo")){var a=JSON.parse(s.localStorage.getItem("userinfo")),e=a.token;uni.showModal({title:"重置聊天",content:"此操作将删除所有消息，并将聊天重置，是否继续？",success:function(a){a.confirm&&(uni.showLoading({title:"处理中..."}),t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"resetChat",model_id:t.id,token:e},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(a){setTimeout((function(){uni.hideLoading()}),1e3),1==a.data.code||200==a.data.code?(uni.showToast({title:a.data.msg||"重置成功",icon:"success"}),t.back()):uni.showToast({title:a.data.msg||"重置失败",icon:"none"})},fail:function(t){setTimeout((function(){uni.hideLoading()}),1e3),console.error("重置聊天失败:",t),uni.showToast({title:"网络连接失败，请稍后再试",icon:"none"})}}))}})}else uni.showToast({title:"请先登录",icon:"none"})},onImageError:function(t){console.error("背景图片加载失败:",t)},onImageLoad:function(t){}}};a.default=c},"4a67":function(t,a,e){"use strict";e.r(a);var n=e("f5e6"),i=e("f15c");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(o);e("0cef");var s=e("828b"),c=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"3cfc7aae",null,!1,n["a"],void 0);a["default"]=c.exports},d295:function(t,a,e){var n=e("c86c");a=n(!1),a.push([t.i,'uni-page-body[data-v-3cfc7aae]{padding-bottom:%?100?%;background-color:initial}body.?%PAGE?%[data-v-3cfc7aae]{background-color:initial}.page-container[data-v-3cfc7aae]{position:relative;width:100%;min-height:100vh;z-index:1}.cu-bar.foot[data-v-3cfc7aae]{z-index:998}.gpt-header[data-v-3cfc7aae]{background-color:hsla(0,0%,100%,.8);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:996}.cu-chat-gpt[data-v-3cfc7aae]{background-color:initial;padding-bottom:%?120?%}.gpt-bg[data-v-3cfc7aae]{position:fixed;top:0;left:0;right:0;bottom:0;z-index:-2;background:linear-gradient(135deg,#f5f7fa,#c3cfe2)}.bg-gradient[data-v-3cfc7aae]{position:absolute;top:0;left:0;width:100%;height:100%;background-color:#e8f4ff; /* 设置背景颜色 */background-image:linear-gradient(180deg,#e8f4ff,#f5f8ff); /* 添加渐变背景 */z-index:-1}.gpt-nodata-info[data-v-3cfc7aae]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?40?% 0;width:100%;text-align:center}.cu-chat .cu-item[data-v-3cfc7aae]{padding:%?30?% %?30?% %?70?%;display:flex;position:relative}\n/* 默认样式 */.cu-chat .cu-item > .main[data-v-3cfc7aae]{max-width:calc(100% - %?260?%);margin:0 %?40?%}\n/* APP端专用样式 */\n.cu-chat .cu-item.self[data-v-3cfc7aae]{justify-content:flex-end;text-align:right}\n/* 默认头像样式 */.cu-chat .cu-avatar[data-v-3cfc7aae]{width:%?80?%;height:%?80?%;flex-shrink:0 /* 防止头像被挤压 */}\n/* APP端头像样式优化 */\n.cu-chat .cu-item > .main .content[data-v-3cfc7aae]{padding:%?20?%;border-radius:%?20?%;background-color:hsla(0,0%,97.3%,.9);max-width:100%;box-shadow:0 %?10?% %?20?% rgba(0,0,0,.1);white-space:pre-wrap}\n/* APP端消息内容优化 */\n.cu-chat .cu-item.self > .main .content[data-v-3cfc7aae]{background-color:rgba(203,230,254,.9);color:#333}.cu-chat .cu-item .date[data-v-3cfc7aae]{position:absolute;font-size:%?24?%;color:#8799a3;width:100%;bottom:%?20?%;left:0;text-align:center}.gpt-content[data-v-3cfc7aae]{max-width:80vw;word-break:break-all}\n/* APP端内容宽度优化 */\n.cu-chat .cu-info[data-v-3cfc7aae]{display:inline-block;padding:%?10?% %?20?%;border-radius:%?10?%;background-color:rgba(0,0,0,.1);font-size:%?24?%;color:#666;max-width:%?400?%;margin:%?20?% auto;text-align:center}.gpt-nodata-info-ico[data-v-3cfc7aae]{width:%?150?%;height:%?150?%;margin-bottom:%?20?%;display:flex;justify-content:center;align-items:center}.ai-icon[data-v-3cfc7aae]{width:%?100?%;height:%?100?%;background-color:#0081ff;border-radius:50%;position:relative;display:flex;justify-content:center;align-items:center;box-shadow:0 %?8?% %?20?% rgba(0,129,255,.3);margin:0 auto}.ai-icon[data-v-3cfc7aae]:before{content:"AI";color:#fff;font-size:%?48?%;font-weight:700}.gpt-nodata-info-tips[data-v-3cfc7aae]{font-size:%?28?%;color:#666;margin-bottom:%?30?%}.gpt-nodata-info-box[data-v-3cfc7aae]{width:90%;max-width:%?600?%;margin:0 auto %?20?%}.gpt-nodata-info-main[data-v-3cfc7aae]{background-color:#fff;padding:%?20?%;border-radius:%?10?%;font-size:%?28?%;color:#333}\n/* APP端特定样式 */\n\n\n\n\n\n',""]),t.exports=a},f15c:function(t,a,e){"use strict";e.r(a);var n=e("3b72"),i=e.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(o);a["default"]=i.a},f5e6:function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){return n}));var n={mpHtml:e("efec").default},i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"page-container"},[e("v-uni-view",{staticClass:"gpt-bg"},[e("v-uni-view",{staticClass:"bg-gradient"})],1),e("v-uni-view",{staticClass:"header gpt-header",class:t.scrollTop>40?"goScroll":"",style:[{height:t.CustomBar+"px"}]},[e("v-uni-view",{staticClass:"cu-bar",style:{height:t.CustomBar+"px","padding-top":t.StatusBar+"px"}},[e("v-uni-view",{staticClass:"action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.back.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"cuIcon-back"})],1),e("v-uni-view",{staticClass:"content",style:[{top:t.StatusBar+"px"}]},[e("v-uni-text",{staticClass:"text-bold"},[t._v(t._s(t.name))])],1),e("v-uni-view",{staticClass:"action"},[""!=t.avatarstyle?e("v-uni-view",{staticClass:"cu-avatar round",style:t.avatarstyle,attrs:{"data-target":"chatInfo"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.showModal.apply(void 0,arguments)}}}):e("v-uni-view",{staticClass:"cu-avatar round"},[e("v-uni-text",{staticClass:"home-noLogin"})],1)],1)],1)],1),e("v-uni-view",{staticClass:"cu-chat cu-chat-gpt"},[e("v-uni-view",{staticClass:"cu-item",style:[{padding:t.NavBar+"px 0px 0px 0px"}]}),0==t.msgList.length?e("v-uni-view",{staticClass:"gpt-nodata-info"},[e("v-uni-view",{staticClass:"gpt-nodata-info-ico"},[e("v-uni-view",{staticClass:"ai-icon"})],1),e("v-uni-view",{staticClass:"gpt-nodata-info-tips"},[t._v("欢迎使用AI聊天功能，您可以按如下方式询问。")]),e("v-uni-view",{staticClass:"gpt-nodata-info-box"},[e("v-uni-view",{staticClass:"gpt-nodata-info-main",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.msg="我要怎么克服拖延症？"}}},[t._v("我要怎么克服拖延症？")])],1),e("v-uni-view",{staticClass:"gpt-nodata-info-box"},[e("v-uni-view",{staticClass:"gpt-nodata-info-main",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.msg="请帮我写一个关于秋天的故事。"}}},[t._v("请帮我写一个关于秋天的故事。")])],1),e("v-uni-view",{staticClass:"gpt-nodata-info-box"},[e("v-uni-view",{staticClass:"gpt-nodata-info-main",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.msg="怎么用javascript实现随机数生成？"}}},[t._v("怎么用javascript实现随机数生成？")])],1)],1):t._e(),t.msgList.length>0?e("v-uni-view",{staticClass:"cu-info gpt-cu-info"},[t._v("聊天窗口消息仅显示最新的100条，可在历史记录中查询之前的消息")]):t._e(),t._l(t.msgList,(function(a,n){return[e("v-uni-view",{key:n+"_0",staticClass:"cu-item ",class:0==a.type||0==a.isAI?"self":""},[1==a.type||1==a.isAI?e("v-uni-view",{staticClass:"cu-avatar radius",style:"background-image:url("+(a.gptJson?a.gptJson.avatar:a.userJson.avatar)+");"}):t._e(),e("v-uni-view",{staticClass:"main"},[e("v-uni-view",{staticClass:"content shadow break-all gpt-content",class:0==a.type||0==a.isAI?"bg-blue light":""},[e("mp-html",{attrs:{content:a.text,selectable:!0,"show-img-menu":!0,"scroll-table":!0,markdown:!0}})],1)],1),0==a.type||0==a.isAI?e("v-uni-view",{staticClass:"cu-avatar radius",style:"background-image:url("+a.userJson.avatar+");"}):t._e(),e("v-uni-view",{staticClass:"date"},[t._v(t._s(t.formatDate(a.created)))])],1)]})),1==t.isWaiting?e("v-uni-view",{staticClass:"cu-item"},[e("v-uni-view",{staticClass:"main"},[e("v-uni-view",{staticClass:"content shadow break-all"},[t._v("AI正在思考中...")])],1)],1):t._e()],2),e("v-uni-view",{staticClass:"cu-bar foot input",style:[{bottom:t.InputBottom+"px"}]},[0==t.isWaiting?[e("v-uni-textarea",{staticClass:"msg-input",attrs:{"adjust-position":!1,focus:!1,maxlength:"-1","auto-height":!0,placeholder:"当前AI单次请求消耗"+t.price+t.currencyName},on:{focus:function(a){arguments[0]=a=t.$handleEvent(a),t.InputFocus.apply(void 0,arguments)},blur:function(a){arguments[0]=a=t.$handleEvent(a),t.InputBlur.apply(void 0,arguments)}},model:{value:t.msg,callback:function(a){t.msg=a},expression:"msg"}}),e("v-uni-button",{staticClass:"cu-btn bg-blue light",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.sendMsg()}}},[t._v("发送")])]:t._e(),1==t.isWaiting?[e("v-uni-input",{staticClass:"solid-bottom",attrs:{"adjust-position":!1,focus:!1,maxlength:"300","cursor-spacing":"10",placeholder:"AI正在思考..."}}),e("v-uni-button",{staticClass:"cu-btn bg-blue light"},[t._v("等待")])]:t._e()],2),e("v-uni-view",{staticClass:"cu-modal",class:"chatInfo"==t.modalName?"show":""},[e("v-uni-view",{staticClass:"cu-dialog"},[e("v-uni-view",{staticClass:"cu-bar bg-white justify-end"},[e("v-uni-view",{staticClass:"content"},[e("v-uni-text",{staticClass:"text-bold"},[t._v(t._s(t.name))])],1),e("v-uni-view",{staticClass:"action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.hideModal.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"cuIcon-close text-red"})],1)],1),e("v-uni-view",{staticClass:"user-edit-header"},[e("v-uni-image",{attrs:{src:t.avatar}}),e("v-uni-view",{staticClass:"gpt-tips-intro"},[t._v(t._s(t.intro))])],1),e("v-uni-view",{staticClass:"cu-bar bg-white justify-center"},[e("v-uni-view",{staticClass:"action"},[e("v-uni-button",{staticClass:"cu-btn bg-blue light",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goHistory()}}},[t._v("历史消息")]),e("v-uni-button",{staticClass:"cu-btn bg-red margin-left",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.gptChatDelete()}}},[t._v("重置聊天")])],1)],1)],1)],1),0==t.isLoading?e("v-uni-view",{staticClass:"loading"},[e("v-uni-view",{staticClass:"loading-main"},[e("v-uni-image",{attrs:{src:"style/loading.gif"}})],1)],1):t._e()],1)},o=[]}}]);