<template>
	<view class="header" :style="[{height:CustomBar + 'px'}]" :class="AppStyle">
		<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
			<view class="action" @tap="back">
				<text class="cuIcon-back"></text>
			</view>
			<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
				隐私政策
			</view>
		</view>
		<scroll-view scroll-y class="agreement">
			<br>
					<p>欢迎使用{{appname}}！{{gongsi}}（以下简称“我们”）非常重视您的个人隐私。</p>
					<p>本隐私政策旨在帮助您了解我们收集、使用、分享和保护您的个人信息的情况。</p>
					<p>我们希望通过本《隐私政策》向您说明，在使用我们的服务时，我们如何收集、使用、储存和分享这些信息，以及我们为您提供的访问、更新、控制和保护这些信息的方式。</p>
					<p>本《隐私政策》与您所使用的服务息息相关，希望您仔细阅读，在需要时，按照本《隐私政策》的指引，作出您认为适当的选择。</p>
					<p>本《隐私政策》中涉及的相关技术词汇，我们尽量以简明扼要的表述，并提供进一步说明的链接，以便您的理解。</p>
					<p>您使用或继续使用我们的服务，即意味着同意我们按照本《隐私政策》收集、使用、储存和分享您的相关信息。</p>
					<p>如对本《隐私政策》或相关事宜有任何问题，请通过{{email}}与我们联系。一般情况下，我们将在【三十天】内回复。</p>
					<p>最近更新日期： 2024 年 3 月 25 日</p>
			
					<h2>我们收集的信息</h2>
					<p>我们将根据您使用{{appname}}的不同功能和服务，收集您的个人信息，我们提供服务时，可能会收集、储存和使用下列与您有关的信息。如果您不提供相关信息，可能无法享受我们提供的某些服务，或者无法达到相关服务拟达到的效果。包括但不限于：
					</p>
					<ul>
						<li>您提供的账户信息（如昵称、头像等）；</li>
						<li>您在使用{{appname}}服务时上传的信息（如图片、文字等）；</li>
						<li>您在使用{{appname}}服务时产生的日志信息（如IP地址、设备信息等）。</li>
						<li>地理位置信息：使用Geolocation定位SDK收集。</li>
						<li>登录鉴权信息：使用OAuth登录鉴权SDK收集。</li>
						<li>分享信息：使用share分享SDK收集。</li>
						<li>浏览器信息：使用安卓X5腾讯tbs SDK收集。</li>
						<li>广告联盟信息：使用快手广告联盟SDK收集。</li>
						<li>蜂窝数据网络、WLAN网络、设备信息、蓝牙、位置信息</li>
						<li>设备信息：包括但不限于Android ID、OAID、设备型号、操作系统版本、唯一设备标识符等。</li>
						<li>应用信息：包括应用安装列表、应用版本等。</li>
						<li>传感器信息：如加速度计、陀螺仪等传感器数据。</li>
						<li>网络信息：包括IP地址、MAC地址等。</li>
						<li>摄像头和麦克风信息：用于拍照、录像、语音识别等。</li>
						<li>我们的产品基于DCloud uni-app(5+ App/Wap2App)开发，应用运行期间需要收集您的设备唯一识别码（IMEI/android ID/DEVICE_ID/IDFA、SIM 卡 IMSI
							信息、OAID）以提供统计分析服务，并通过应用启动数据及异常错误日志分析改进性能和用户体验，为用户提供更好的服务。详情内容请访问<a
								href="https://ask.dcloud.net.cn/protocol.html">《DCloud用户服务条款》</a></li>
						<li>您提供的信息:
							您在使用我们的服务时，向我们提供的相关个人信息，以及您使用我们的服务时所储存的信息。信息包括：
							我们在处理和执行您的咨询请求、技术管理以及相应网站或应用程序中提供的其它服务过程中，您可能需要向我们提供如下信息：姓名、电话号码/手机号码、微信号、电子邮箱地址、地址、这些信息并非我们提供业务基本功能所必须的，但这些信息对改善服务质量、研发新产品或服务等有非常重要的意义。如您不愿提供，您将无法正常使用相关扩展功能及/或无法达到该服务的最佳效果，但不会影响使用该服务的基本功能
						</li>
						<li> 日志信息，指您使用我们的服务时，系统可能通过cookies、web</li> beacon或其他方式自动采集的技术信息或软件生成的数据日志；
						<li>在使用我们服务时搜索或浏览的信息，例如您使用的网页搜索词语、访问的社交媒体页面url地址，以及您在使用我们服务时浏览或要求提供的其他信息和内容详情；</li>
						<li>有关您曾使用的移动应用（APP）和其他软件的信息，以及您曾经使用该等移动应用和软件的信息；</li>
						<li>位置信息，指您开启设备定位功能并使用我们基于位置提供的相关服务时，收集的有关您位置的信息，包括：</li>
						<li>您通过具有定位功能的移动设备使用我们的服务时，通过GPS或WiFi等方式收集的您的地理位置信息</li>
						<li>您或其他用户提供的包含您所处地理位置的实时信息，例如您提供的账户信息中包含的您所在地区信息，您或其他人上传的显示您当前或曾经所处地理位置的共享信息;
						</li>
						<li>您可以通过关闭定位功能，停止对您的地理位置信息的收集。</li>
						<li>我们可能从第三方获取您授权共享的信息，我们会将依据与第三方的约定、对个人信息来源的合法性进行确认，并在您同意本《隐私政策》且在您同意我们从第三方获取您授权共享的信息后，在符合相关法律和法规规定的前提下，使用您的这些个人信息。
						</li>
					</ul>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:15%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接/官网</th>
						</tr>
						<tr>
							<td>阿里weexSDK</td>
							<td>com.taobao</td>
							<td>uni-app基础模块默认集成，用于渲染uniapp的nvue页面引擎</td>
							<td style="font-size:12px"> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.READ_EXTERNAL_STORAG </td>
							<td>存储的个人文件</td>
							<td><a href="https://weexapp.com/zh/" target="_blank">https://weexapp.com/zh/</a></td>
						</tr>
						<tr>
							<td>Fresco图库</td>
							<td>com.facebook.fresco</td>
							<td>用于nvue页面加载图片使用</td>
							<td style="font-size:12px"> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.READ_EXTERNAL_STORAG </td>
							<td>存储的个人文件</td>
							<td><a href="https://www.fresco-cn.org/" target="_blank">https://www.fresco-cn.org/</a></td>
						</tr>
						<tr>
							<td>glide图库</td>
							<td>com.bumptech.glide</td>
							<td>用于图片预览使用</td>
							<td style="font-size:12px"> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.READ_EXTERNAL_STORAG </td>
							<td>存储的个人文件</td>
							<td><a href="http://bumptech.github.io/glide/" target="_blank">http://bumptech.github.io/glide/</a></td>
						</tr>
						<tr>
							<td>gif-drawable</td>
							<td>pl.droidsonroids.gif</td>
							<td>加载gif图</td>
							<td style="font-size:12px"> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.READ_EXTERNAL_STORAG </td>
							<td>存储文件</td>
							<td><a href="https://github.com/alibaba/fastjson"
									target="_blank">https://github.com/koral--/android-gif-drawable</a></td>
						</tr>
						<tr>
							<td>fastjson</td>
							<td>com.alibaba.fastjson</td>
							<td>JSON解析</td>
							<td style="font-size:12px"> 无 </td>
							<td>无</td>
							<td><a href="https://github.com/alibaba/fastjson"
									target="_blank">https://github.com/alibaba/fastjson</a></td>
						</tr>
						<tr>
							<td>移动安全联盟 OAID</td>
							<td>com.bun.miitmdid、com.zui.opendeviceidlibrary、com.netease.nis、com.samsung.android、com.huawei.hms</td>
							<td>获取oaid</td>
							<td style="font-size:12px"> 无 </td>
							<td>设备信息</td>
							<td><a href="http://www.msa-alliance.cn/col.jsp?id=122"
									target="_blank">http://www.msa-alliance.cn/col.jsp?id=122</a></td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:15%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接/官网</th>
						</tr>
						<tr>
							<td>uni-app(5+、web2app)</td>
							<td>io.dcloud</td>
							<td>基础模块</td>
							<td style="font-size:12px"> android.permission.INTERNET<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.WRITE_EXTERNAL_STORAGE<br> android.permission.READ_EXTERNAL_STORAG<br>
								android.permission.READ_PHONE_STATE </td>
							<td>存储的个人文件，设备信息（IMEI、ANDROID_ID、DEVICE_ID、IMSI），网络信息</td>
							<td><a href="https://ask.dcloud.net.cn/protocol.html"
									target="_blank">https://ask.dcloud.net.cn/protocol.html</a></td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>个推·消息推送</td>
							<td>com.igexin</td>
							<td>消息推送</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE<br> android.permission.READ_PHONE_STATE<br>
								android.permission.WRITE_EXTERNAL_STORAGE<br> android.permission.VIBRATE<br>
								android.permission.GET_TASKS<br> android.permission.QUERY_ALL_PACKAGES </td>
							<td>存储的个人文件、设备信息（IMEI、MAC、ANDROID_ID、DEVICE_ID、IMSI）、应用已安装列表、网络信息</td>
							<td><a href="http://docs.getui.com/privacy" target="_blank">http://docs.getui.com/privacy</a></td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>华为 HMS push</td>
							<td>com.huawei.hms</td>
							<td>华为手机厂商推送</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE<br> android.permission.REQUEST_INSTALL_PACKAGES<br>
								android.permission.FOREGROUND_SERVICE<br> android.permission.READ_PHONE_STATE </td>
							<td>设备信息（IMEI、ANDROID_ID、DEVICE_ID、IMSI）、应用已安装列表、网络信息</td>
							<td><a href="https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/sdk-data-security-0000001050042177"
									target="_blank">推送隐私声明</a></td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>魅族 Flyme push</td>
							<td>com.meizu.cloud</td>
							<td>魅族手机厂商推送</td>
							<td style="font-size:12px"> android.permission.INTERNET<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.READ_EXTERNAL_STORAGE<br> android.permission.WRITE_EXTERNAL_STORAGE </td>
							<td>网络信息、存储的个人文件</td>
							<td><a href="http://static.meizu.com/resources/term/privacy8.html" target="_blank">推送隐私声明</a></td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>OPPO push</td>
							<td>com.heytap</td>
							<td>OPPO手机厂商推送</td>
							<td style="font-size:12px"> android.permission.INTERNET<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE </td>
							<td>网络信息</td>
							<td><a href="https://open.oppomobile.com/new/developmentDoc/info?id=10288" target="_blank">推送隐私声明</a>
							</td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>vivo push</td>
							<td>com.vivo.push</td>
							<td>vivo手机厂商推送</td>
							<td style="font-size:12px"> android.permission.INTERNET </td>
							<td>网络信息</td>
							<td><a href="https://dev.vivo.com.cn/documentCenter/doc/652#w1-12075822" target="_blank">推送隐私声明</a></td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>MiPush</td>
							<td>com.xiaomi.push</td>
							<td>小米手机厂商推送</td>
							<td style="font-size:12px"> android.permission.INTERNET<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.VIBRATE </td>
							<td>网络信息</td>
							<td><a href="https://dev.mi.com/distribute/doc/details?pId=1534" target="_blank">推送隐私声明</a></td>
						</tr>
					</table>
					<a id="oauth" /> <a id="payment" /> <a id="share" /> 登录、分享、支付存在引入相同SDK，这里统一进行说明： <table
						style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>微信开放平台</td>
							<td>com.tencent.mm</td>
							<td>登录、分享、支付</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE </td>
							<td>存储的个人文件、网络信息</td>
							<td><a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&t=weixin_agreement&s=privacy"
									target="_blank">微信隐私协议</a></td>
						</tr>
						<tr>
							<td>新浪开放平台</td>
							<td>com.sina.weibo</td>
							<td>登录、分享</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE </td>
							<td>存储的个人文件、网络信息</td>
							<td><a href="https://weibo.com/signup/v5/privacy?spm=a1zaa.8161610.0.0.4f8776217Wu8R1"
									target="_blank">新浪隐私协议</a></td>
						</tr>
						<tr>
							<td>QQ开放平台</td>
							<td>com.tencent.open</td>
							<td>登录、分享(用于用户登录)</td>
							<td style="font-size:12px"> android.permission.MODIFY_AUDIO_SETTINGS<br>
								android.permission.ACCESS_NETWORK_STATE<br> android.permission.ACCESS_WIFI_STATE </td>
							<td>存储的个人文件、读取手机状态和身份、网络信息</td>
							<td><a href="https://wiki.connect.qq.com/qq%e4%ba%92%e8%81%94sdk%e9%9a%90%e7%a7%81%e4%bf%9d%e6%8a%a4%e5%a3%b0%e6%98%8e"
									target="_blank">qq隐私协议</a></td>
						</tr>
						<tr>
							<td>支付宝开放平台</td>
							<td>com.alipay</td>
							<td>登录、分享</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE </td>
							<td>网络信息 设备id IP地址 OAID IMSI</td>
							<td><a href="https://render.alipay.com/p/c/k2cx0tg8" target="_blank">支付宝隐私协议</a></td>
						</tr>
					</table>
					<a id="map-amp-geolocation" /> Map & Geolocation模块集成的三方SDK说明 <table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>高德开放平台</td>
							<td>com.amap.api, com.loc, com.autonavi</td>
							<td>实现定位/展现地图</td>
							<td style="font-size:12px"> android.permission.ACCESS_COARSE_LOCATION<br>
								android.permission.ACCESS_FINE_LOCATION<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE<br> android.permission.CHANGE_WIFI_STATE<br>
								android.permission.READ_PHONE_STATE<br> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.ACCESS_LOCATION_EXTRA_COMMANDS </td>
							<td>存储的个人文件、位置信息、读取手机状态和身份、网络信息、传感器信息、Android ID</td>
							<td><a href="https://lbs.amap.com/pages/privacy/"
									target="_blank">https://lbs.amap.com/pages/privacy/</a></td>
						</tr>
						<tr>
							<td>百度开放平台</td>
							<td>com.baidu</td>
							<td>实现定位/展现地图</td>
							<td style="font-size:12px"> android.permission.ACCESS_COARSE_LOCATION<br>
								android.permission.ACCESS_FINE_LOCATION<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE<br> android.permission.CHANGE_WIFI_STATE<br>
								android.permission.READ_PHONE_STATE<br> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.ACCESS_LOCATION_EXTRA_COMMANDS<br> android.permission.READ_LOGS<br>
								android.permission.WRITE_SETTINGS<br> android.permission.MOUNT_UNMOUNT_FILESYSTEM </td>
							<td>存储的个人文件、位置信息、读取手机状态和身份、网络信息</td>
							<td><a href="https://map.baidu.com/zt/client/privacy/index.html"
									target="_blank">https://map.baidu.com/zt/client/privacy/index.html</a></td>
						</tr>
					</table>
					<a id="uniAd" />
			
			
					<table style="word-break:break-all">
						<tr>
							<th style="width:8%">SDK名称</th>
							<th style="width:10%">SDK包名/网址</th>
							<th style="width:8%">SDK用途</th>
							<th style="width:20%">可能获取的个人信息类型</th>
							<th style="width:25%">调用的设备权限</th>
							<th style="width:20%">信息用途</th>
							<th style="width:10%">SDK隐私政策链接/目的</th>
						</tr>
						<tr>
							<td>uni-AD</td>
							<td></td>
							<td>基础广告 </td>
							<td style="font-size:12px">设备品牌、型号、操作系统版本、OAID、分辨率、IMEI、android ID、SIM 卡 IMSI
								信息、应用名称、应用包名、应用版本号、网络信息、应用安装列表、位置信息 </td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE <br>
								android.permission.READ_PHONE_STATE <br> android.permission.ACCESS_COARSE_LOCATION </td>
							<td>广告投放合作，广告归因、反作弊、安全 </td>
							<td><a href="https://doc.dcloud.net.cn/markdown-share-docs/1d821cdd3cdf2681045ec4be94bc8404/"
									target="_blank">隐私协议</a></td>
						</tr>
						<tr>
							<td>推啊 </td>
							<td> engine.tuifish.com </td>
							<td>基础广告 </td>
							<td style="font-size:12px">设备品牌、型号、操作系统版本、OAID、分辨率、IMEI、android ID、SIM 卡 IMSI
								信息、应用名称、应用包名、应用版本号、网络信息、应用安装列表、位置信息 </td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE <br>
								android.permission.READ_PHONE_STATE <br> android.permission.ACCESS_COARSE_LOCATION </td>
							<td style="font-size:12px">识别广告、活动作弊行为；改善 SDK 崩溃率。 </td>
							<td><a href="https://yun.tuia.cn/tuia/sdk/agreement/index.html" target="_blank">推啊隐私协议</a></td>
						</tr>
						<tr>
							<td>快手 </td>
							<td>com.kwad.sdk </td>
							<td>增强广告 </td>
							<td>基础信息：设备品牌、设备型号、软件系统版本、存储信息、运营商信息、设备时区、设备语言、网络信息等基础信息</br>
								设备标识：IMEIs、MEID、OAID、Androidld、IMSIs、ICCID</br> 位置信息：IP地址、MAC地址、GPS位置信息、基站信息、WIFI信息</br>
								应用信息：应用安装列表</br> 其他信息：传感器信息、sim卡激活信息</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE <br> android.permission.INTERNET
								<br> android.permission.READ_PHONE_STATE <br> android.permission.ACCESS_WIFI_STATE <br>
								android.permission.REQUEST_INSTALL_PACKAGES <br> android.permission.VIBRATE </td>
							<td>广告投放、广告归因、反作弊、安全 </td>
							<td><a href="https://www.kuaishou.com/about/policy" target="_blank">快手内容联盟隐私协议</a>和<a
									href="https://u.kuaishou.com/home/<USER>/1220" target="_blank">SDK使用规范</a></td>
						</tr>
						<tr>
							<td>快手内容联盟 </td>
							<td>com.kwad.sdk </td>
							<td>增强广告 </td>
							<td>基础信息：设备品牌、设备型号、软件系统版本、存储信息、运营商信息、设备时区、设备语言、网络信息等基础信息</br>
								设备标识：IMEIs、MEID、OAID、Androidld、IMSIs、ICCID</br> 位置信息：IP地址、MAC地址、GPS位置信息、基站信息、WIFI信息</br>
								应用信息：应用安装列表</br> 其他信息：传感器信息、sim卡激活信息 </td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE <br> android.permission.INTERNET
								<br> android.permission.READ_PHONE_STATE <br> android.permission.ACCESS_WIFI_STATE <br>
								android.permission.REQUEST_INSTALL_PACKAGES <br> android.permission.VIBRATE <br>
								android.permission.SET_WALLPAPER<br> android.permission.READ_EXTERNAL_STORAGE <br>
								android.permission.WRITE_EXTERNAL_STORAGE <br> android.permission.ACCESS_COARSE_LOCATION <br>
								android.permission.BLUETOOTH </td>
							<td>广告投放、广告归因、反作弊、安全 </td>
							<td><a href="https://www.kuaishou.com/about/policy" target="_blank">快手内容联盟隐私协议</a>和<a
									href="https://u.kuaishou.com/home/<USER>/1220" target="_blank">SDK使用规范</a></td>
						</tr>
						<tr>
							<td>优量汇 </td>
							<td>com.qq.e </td>
							<td>增强广告 </td>
							<td>基站、附近的WIFI、连接的WIFI、位置信息、设备制造商、设备型号、操作系统版本、屏幕分辨率、屏幕方向、屏幕DPI、IP地址、加速度传感器、磁场传感器、OAID、IMEI/MEID（Device
								ID）、Android_ID、包名、版本号、进程名称、运行状态、可疑行为、应用安装信息 </td>
							<td style="font-size:12px"> android.permission.INTERNET <br> android.permission.ACCESS_NETWORK_STATE
								<br> android.permission.ACCESS_WIFI_STATE <br> android.permission.REQUEST_INSTALL_PACKAGES <br>
								android.permission.CHANGE_NETWORK_STATE<br> android.permission.QUERY_ALL_PACKAGES <br>
								android.permission.REORDER_TASKS<br> android.permission.VIBRATE <br>
								android.permission.ACCESS_COARSE_LOCATION </td>
							<td style="font-size:12px">广告投放与监测归因、广告主统计投放结果、减少App崩溃、确保服务器正常运行、提升可扩展性和性能</td>
							<td><a href="https://e.qq.com/dev/help_detail.html?cid=2005&pid=5983" target="_blank">优量汇隐私协议</a></td>
						</tr>
						<tr>
							<td>穿山甲 </td>
							<td>com.bytedance.sdk.openadsdk</td>
							<td>增强广告 </td>
							<td>设备品牌、型号、软件系统版本、分辨率、网络信号强度、IP地址、设备语言、传感器信息等基础信息、无线网SSID名称、MAC地址、AndroidID、应用名、应用包名、版本号、应用前后台状态、应用列表信息、运营商信息、设备时区
							</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE <br>
								android.permission.READ_PHONE_STATE <br> android.permission.WRITE_EXTERNAL_STORAGE </td>
							<td>广告投放合作、广告归因、反作弊 </td>
							<td><a href="https://www.pangle.cn/privacy/partner" target="_blank">穿山甲隐私协议</a></td>
						</tr>
						<tr>
							<td>Sigmob </td>
							<td>com.sigmob.windad </td>
							<td>增强广告 </td>
							<td>设备信息：设备品牌、型号、操作系统版本、OAID、分辨率等基础设备信息 应用信息：应用名称、应用包名、应用版本号等 其他：运营商信息、时区</td>
							<td style="font-size:12px"> android.permission.ACCESS_NETWORK_STATE<br> android.permission.INTERNET <br>
								android.permission.ACCESS_WIFI_STATE <br> android.permission.CHANGE_WIFI_STATE <br>
								android.permission.READ_PHONE_STATE <br> android.permission.REQUEST_INSTALL_PACKAGES <br>
								android.permission.QUERY_ALL_PACKAGES </td>
							<td>广告投放、广告主归因、反作弊 </td>
							<td><a
									href="https://doc.sigmob.com/#/Sigmob%E4%BD%BF%E7%94%A8%E6%8C%87%E5%8D%97/%E9%9A%90%E7%A7%81%E6%9D%A1%E6%AC%BE/%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96/">Sigmob隐私协议</a>
							</td>
						</tr>
						<tr>
							<td>百度百青藤</td>
							<td>com.baidu.mobads.proxy </td>
							<td>增强广告 </td>
							<td>设备信息：设备品牌、型号、软件系统版本、分辨率、网络信号强度、传感器信息，磁盘总空间、系统总内存空间、手机重启信息、手机系统更新时间等基础信息、OAID、AndroidID、屏幕宽高，屏幕像素密度，系统版本号，设备厂商，设备型号，手机运营商，手机网络状态，设备剩余存储空间，手机重启时间和更新时间
								开发者应用信息：应用包名、应用前后台状态 设备信息：IMEI、IMSI、MEID 位置信息</td>
							<td style="font-size:12px"> android.permission.INTERNET<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE<br> android.permission.READ_PHONE_STATE<br>
								android.permission.ACCESS_COARSE_LOCATION<br> android.permissio.WRITE_EXTERNAL_STORAGE<br> </td>
							<td>为最终用户提供安全保障、改善我们的产品和服务，开展内部审计、数据分析和研究 </td>
							<td><a href="https://union.baidu.com/bqt/#/legal/policies">百度百青藤隐私协议</a></td>
						</tr>
						<tr>
							<td>HUAWEI Ads</td>
							<td>com.huawei.hms.ads.lite</td>
							<td>增强广告 </td>
							<td>设备信息、网络信息、位置信息、应用信息、应用使用信息、上下文信息</td>
							<td style="font-size:12px"> android.permission.INTERNET<br> android.permission.ACCESS_NETWORK_STATE<br>
								android.permission.ACCESS_WIFI_STATE<br> </td>
							<td>单次请求的广告定向投放、程序化广告投放、广告监测归因与反作弊。</td>
							<td><a
									href="https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/whale-hong-kinetic-energy-sdk-privacy-statement-0000001658283582">HUAWEI
									Ads 隐私声明</a><br><br>和<br><br><a
									href="https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/publisher-service-personal-data-0000001050066921">HUAWEI
									Ads SDK隐私安全说明</a></td>
						</tr>
					</table>
					<a id="e885bee8aeafx5e58685e6a0b8" />
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>腾讯浏览服务SDK</td>
							<td>com.tencent.tbs、com.tencent.smtt</td>
							<td>x5内核渲染webview</td>
							<td style="font-size:12px"> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.ACCESS_NETWORK_STATE<br> android.permission.ACCESS_WIFI_STATE<br>
								android.permission.READ_PHONE_STATE </td>
							<td>存储的个人文件、读取手机状态和身份、网络信息</td>
							<td><a href="https://x5.tencent.com/docs/privacy.html"
									target="_blank">https://x5.tencent.com/docs/privacy.html</a></td>
						</tr>
					</table>
					<table style="word-break:break-all">
						<tr>
							<th style="width:10%">SDK名称</th>
							<th style="width:10%">包名信息</th>
							<th style="width:15%">使用目的</th>
							<th style="width:30%">使用的权限</th>
							<th style="width:20%">涉及个人信息</th>
							<th style="width:10%">隐私权政策链接</th>
						</tr>
						<tr>
							<td>个验一键登录</td>
							<td>com.g.elogin、com.g.gysdk、cn.com.chinatelecom</td>
							<td>登录</td>
							<td style="font-size:12px"> android.permission.READ_PHONE_STATE<br>
								android.permission.READ_EXTERNAL_STORAGE<br> android.permission.WRITE_EXTERNAL_STORAGE<br>
								android.permission.ACCESS_NETWORK_STATE<br> android.permission.ACCESS_WIFI_STATE<br>
								android.permission.CHANGE_NETWORK_STATE </td>
							<td>设备信息（IMEI、MAC、ANDROID_ID、DEVICE_ID、IMSI）存储的个人文件、读取手机状态和身份、网络信息、应用已安装列表</td>
							<td> <a href="https://docs.getui.com/privacy/" target="_blank">个验</a> <br><a
									href="https://wap.cmpassport.com/resources/html/contract.html"
									target="_blank">中国移动认证服务条款</a><br> <a
									href="https://opencloud.wostore.cn/authz/resource/html/disclaimer.html?fromsdk=true"
									target="_blank">联通统一认证服务条款</a> </td>
						</tr>
					</table>
			
					<h2>与授权合作伙伴共享</h2>
					<p> 消息推送服务供应商：由每日互动股份有限公司提供推送技术服务，我们可能会将您的设备平台、设备厂商、设备品牌、设备识别码等设备信息，应用列表信息、网络信息以及位置相关信息提供给每日互动股份有限公司，用于为您提供消息推送技术服务。我们在向您推送消息时，我们可能会授权每日互动股份有限公司进行链路调节，相互促活被关闭的SDK推送进程，保障您可以及时接收到我们向您推送的消息。详细内容请访问<a
							href="http://docs.getui.com/privacy">《个推用户隐私政策》</a></p>
			
					<h2>信息使用</h2>
					<p>我们会将收集的个人信息用于以下目的：</p>
					<ul>
						<li>提供、维护、改进{{appname}}的服务；</li>
						<li>保护{{appname}}的安全和完整性；</li>
						<li>与您进行联系和沟通。</li>
						<li>满足法律、法规的规定。</li>
						<li>在我们提供服务时，用于身份验证、客户服务、安全防范、诈骗监测、存档和备份用途，确保我们向您提供的产品和服务的安全性；</li>
						<li>帮助我们设计新服务，改善我们现有服务；</li>
						<li>使我们更加了解您如何接入和使用我们的服务，从而针对性地回应您的需求，例如语言设定、位置设定的帮助服务和指示，或对您和其他用户作出其他方面的回应；</li>
						<li> 软件认证或管理软件升级；</li>
						<li>让您参与有关我们产品和服务的调查。</li>
					</ul>
					<h2>信息披露</h2>
					<p>我们会在以下情况下披露您的个人信息：</p>
			
					<li>在法律要求或司法机关要求的情况下。</li>
					<li>在维护{{appname}}软件的安全和完整性方面。</li>
					<li>在遵守适用的法律法规的事项。</li>
			
					<h2>信息分享</h2>
					<p>我们将尽一切可能采取适当的技术手段，保证您可以访问、更新和更正自己的注册信息或使用我们的服务时提供的其他个人信息。在访问、更新、更正和删除前述信息时，我们可能会要求您进行身份验证，以保障账户安全。除非经您同意或法律允许，我们不会向第三方分享您的个人信息。
					</p>
			
					<h2>信息保护</h2>
					<p>除以下情形外，未经您同意，我们不会与任何第三方分享您的个人信息：
						我们可能将您的个人信息与第三方服务供应商、承包商及代理（例如代表我们发出电子邮件或推送通知的通讯服务提供商、为我们提供位置数据的地图服务供应商），在遵守本《隐私政策》约定的前提下，我们可能会向我们的关联方分享（他们可能并非位于您所在的司法管辖区，包括没有与您所在司法管辖区的法律实质上相似或达到该等法律目的的个人信息保护法规的地区。），在以下情形用作对应用途：
					</p>
					<ul>
			
						<li>为了向您提供完善的产品和服务，我们的某些服务或技术将由第三方服务供应商、承包商及代理提供。我们仅会出于合法、正当、必要、特定、明确的目的共享您的个人信息;</li>
						<li>为实现“我们可能如何使用信息”部分所述目的，在本《隐私政策》声明的使用目的范围内并受限于本《隐私政策》的约定，您的个人信息可能会与我们在全球范围内的关联公司共享，但我们只会共享必要的信息。我们的关联公司如要改变个人信息的处理目的，将再次征求您的授权同意；
						</li>
						<li>如我们与任何上述第三方分享您的个人信息，我们将努力确保该等第三方在使用您的个人信息时遵守本《隐私政策》及我们要求其遵守的其他适当的保密和安全措施。</li>
						<li>随着我们业务的持续发展，我们有可能进行合并、收购或类似的交易，如涉及到您的个人信息的转移。我们会要求继承上述义务的公司、组织继续受此个人信息保护政策的约束，否则我们将要求该公司、组织重新向您征求授权同意。
						</li>
						<li>我们还可能为以下需要而保留、保存或披露您的个人信息：</li>
						<li>遵守适用的法律法规；</li>
						<li>遵守法院命令或其他法律程序的规定；</li>
						<li>遵守相关政府机关的要求；</li>
						<li>为遵守适用的法律法规、维护社会公共利益，或保护我们的客户、我们、其他用户的人身和财产安全或合法权益所合理必需的用途。</li>
			
			
					</ul>
					<h2>您的权利</h2>
					<p>按照中国相关的法律、法规、标准，以及其他国家、地区的通行做法，当您发现我们处理的关于您的个人信息有错误时，您有权要求我们做出更正。如果我们违反法律、行政法规的规定或者本隐私权协议的约定收集、使用您个人信息的，您有权要求我们删除您的个人信息。
						您同意按本《隐私政策》所述的目的和方式来处理您的敏感个人信息。</p>
			
			
					<h2>隐私政策的更新</h2>
					<p>我们可能会不时更新本隐私政策，更新后的隐私政策将在{{appname}}上公布。您可以在隐私政策更新后继续使用{{appname}}服务，也可以选择停止使用。</p>
			
					<h2>联系我们</h2>
					<p>如果您对本隐私政策有任何疑问或意见，可以通过以下方式与我们联系：</p>
					<p>公司名称：{{gongsi}}</p>
					<p>联系邮箱：{{email}}</p>
			<view style="height:100px;"></view>
		</scroll-view> 
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				
				uid:0,
				name:'',
				screenName:'',
				password:'',
				repassword:'',
				mail:'',
				url:'',
				
				appname:'',
				email:'',
				gongsi:'',
				token:''
				
			}
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			that.appname = that.$API.GetAppName();
			that.email = that.$API.GetAppEmail();
			that.gongsi = that.$API.GetCompany();
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
		},
		methods: {
			agree(){
				var that = this;
				localStorage.setItem('isAgree', 'true');
				that.back()
			},
			disagree(){
				var that = this;
				localStorage.setItem('isAgree', 'false');
				that.back()
				uni.showToast({
					title: "同意后再来注册吧",
					icon: 'none',
					duration: 3000,
					position: 'center',
				});
			},
			back(){
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>


<style>
	.bottom-buttons {  
	  position: fixed;  
	  bottom: 0;  
	  width: 100%;  
	  display: flex;  
	  justify-content: space-between;  
	  padding: 10px;  
	  background-color: #fff;
	  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1); 
	}  
	  
	.bottom-buttons .button1 {  
	  flex: 1;  
	  text-align: center;  
	  padding: 8px;  
	  margin: 0 20px;
	  border-radius: 30px;
      background-color: #00BCD4;
	  color: #fff;  
	  font-size: 16px;  
	}  
	.bottom-buttons .button2 {
	  flex: 1;  
	  text-align: center;  
	  padding: 8px;  
	  margin: 0 20px;
	  border-radius: 30px;
	  background-color: #ffffff;
	  border: 2px solid #00BCD4;
	  color: #00BCD4;  
	  font-size: 16px;  
	}  
	  
	.bottom-buttons .button:not(:last-child) {  
	  margin-right: 20px;
	}
</style>
