(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-inbox-group"],{"466a":function(t,a,e){"use strict";e.r(a);var i=e("a99c"),s=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(n);a["default"]=s.a},a99c:function(t,a,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("d4b5"),e("e966"),e("f7a5"),e("5c47"),e("a1c1"),e("aa9c"),e("c223");var s=e("3dda"),n=i(e("3b46")),o=(i(e("47e0")),{data:function(){return{StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,sectionList:[],page:1,swiperList:[],submitStatus5:!1,submitStatus3:!1,submitStatus2:!1,submitStatus1:!1,squareid:0,isLogin:1,metaList:[],adimage_sl:0,tagList:[],recommendList:[],shopList:[],rzImg:this.$API.SPRz(),Rz:!1,isvip:0,userList:[],tabCurTab:1,spaceDataType:2,uid:0,homeStyle:0,swiperHeight:this.$API.SPfindSwiperHeight(),swiperBgcolor:"",actStyle:0,Hyperlinks:0,Gallery:0,Findtop:0,Code:0,isLoading:0,kuaijie:2,radiusBoxStyle:0,radiusStyle:0,fatherTitle:2,swiperStyle:!1,swiperType:0,recommendOf:0,ads:"",wzof:0,tzof:0,shopof:0,Bannerswitch:0,searchText:"",iconimg:[],avatarurl:"../../static/user/avatar.png",dataLoad:!1,spaceLoad:!1,recommendSectionList:[],swiperList2:[],chatList:[],oldChatList:[],identifyCompany:0,curIMG:"",bannerAds:[],bannerAdsInfo:null,noticeSumof:!1,noticeSum:0,spaceList:[],token:"",isGetChat:null,userInfo:null,sy_gpt:!1,noLogin:!1}},onPullDownRefresh:function(){clearInterval(this.msgLoading),this.msgLoading=null,this.page=1,this.getMyChat(!1),setTimeout((function(){uni.stopPullDownRefresh()}),1e3)},onBackPress:function(){clearInterval(this.msgLoading),this.msgLoading=null},onUnload:function(){clearInterval(this.msgLoading),this.msgLoading=null},onReachBottom:function(){},onShow:function(){var t=this;if(t.page=1,s.localStorage.getItem("userinfo")){var a=JSON.parse(s.localStorage.getItem("userinfo"));t.uid=a.uid}s.localStorage.getItem("token")&&(t.token=s.localStorage.getItem("token"),""!=t.token&&(t.getMyChat(!1),t.isGetChat=setInterval((function(){t.getMyChat(!1)}),4e3))),t.allCache(),t.unreadNum()},onLoad:function(){},methods:{back:function(){clearInterval(this.msgLoading),this.msgLoading=null,uni.navigateBack({delta:1})},loadMore:function(){0==this.isLoad&&(this.moreText="正在加载中...",this.getMyChat(!0))},searchClose:function(){this.searchText="",this.page=1,this.getUserList(!1)},goChat:function(t){var a=t.id;clearInterval(this.chatLoading),this.chatLoading=null;var e=this.chatList;for(var i in e)e[i].id==a&&(e[i].isNew=0,e[i].unRead=0);if(this.chatList=e,this.oldChatList=this.chatList,s.localStorage.setItem("AllchatList",JSON.stringify(this.chatList)),0==t.type){var n=t.userJson.name,o=t.userJson.uid;uni.navigateTo({url:"/pages/chat/chat?uid="+o+"&name="+n+"&chatid="+a+"&type=0"})}if(1==t.type){n=t.name;uni.navigateTo({url:"/pages/chat/chat?&name="+n+"&chatid="+a+"&type=1"})}},setRead:function(){var t=this;t.$Net.request({url:t.$API.setRead(),data:{token:t.token},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"get",dataType:"json",success:function(a){if(1==a.data.code){uni.showToast({title:"操作成功！",icon:"none"}),s.localStorage.setItem("noticeSum",0);var e=t.chatList;for(var i in e)e[i].myUnRead=0,e[i].otherUnRead=0;t.chatList=e}},fail:function(t){uni.showToast({title:"网络开小差了哦",icon:"none"})}})},chatFormatDate:function(t){t=new Date(parseInt(1e3*t));var a=t.getFullYear(),e=("0"+(t.getMonth()+1)).slice(-2),i=("0"+t.getDate()).slice(-2),s=("0"+t.getHours()).slice(-2),n=("0"+t.getMinutes()).slice(-2),o=s+":"+n,c=new Date,u=c.getFullYear(),l=(("0"+(c.getMonth()+1)).slice(-2),("0"+c.getDate()).slice(-2));return o=a==u&&a==u&&i==l?s+":"+n:e+"-"+i,o},replaceSpecialChar:function(t){return!!t&&(t=t.replace(/&quot;/g,'"'),t=t.replace(/&amp;/g,"&"),t=t.replace(/&lt;/g,"<"),t=t.replace(/&gt;/g,">"),t=t.replace(/&nbsp;/g," "),t)},allCache:function(){var t=this;setTimeout((function(){t.isLoading=1,clearTimeout("timer")}),300);s.localStorage.getItem("AllchatList")&&(t.chatList=JSON.parse(s.localStorage.getItem("AllchatList")))},getMyChat:function(t){var a=this;a.page;if(t&&0,""==a.token)return uni.showToast({title:"请先登录",icon:"none",duration:1e3,position:"bottom"}),!1;a.$Net.request({url:a.$API.allChat(),data:{token:a.token,limit:30,page:1,order:"lastTime"},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"get",dataType:"json",success:function(e){if(a.isLoad=0,1==e.data.code){var i=e.data.data;if(i.length>0){var n=[];for(var o in i){var c=i[o];c.isNew=0,c.unRead=0,n.push(c)}if(t)a.page++,a.chatList=a.chatList.concat(n);else{var u=[];if(null!=a.oldChatList&&(u=a.oldChatList),u.length>0){if(!a.arraysEqual(u,n)){for(var l in console.log("开始对比"),n)for(var r in u)if(u[r].id==n[l].id&&u[r].lastTime<n[l].lastTime){console.log("赋值完成"),n[l].isNew=1;var d=n[l].msgNum-u[r].msgNum;d<=0&&(d=0),n[l].unRead=d}a.oldChatList=n,a.chatList=n,s.localStorage.setItem("AllchatList",JSON.stringify(n))}}else a.oldChatList=n,a.chatList=n,s.localStorage.setItem("AllchatList",JSON.stringify(n))}}}},fail:function(t){a.isLoad=0}})},arraysEqual:function(t,a){if(t===a)return!0;if(null==t||null==a)return!1;if(t.length!=a.length)return!1;for(var e in t)for(var i in a)if(a[i].id==t[e].id&&a[i].lastTime!=t[e].lastTime)return!1},goRegister:function(){uni.navigateTo({url:"/pages/user/register"})},goLogin:function(){uni.navigateTo({url:"/pages/user/login"})},goPage:function(t){uni.navigateTo({url:t})},unreadNum:function(){var t=this,a="";if(s.localStorage.getItem("userinfo")){var e=JSON.parse(s.localStorage.getItem("userinfo"));a=e.token}t.$Net.request({url:t.$API.unreadNum(),data:{token:a},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"get",dataType:"json",success:function(a){1==a.data.code&&(t.unreadData=a.data.data)},fail:function(t){uni.showToast({title:"网络开小差了哦",icon:"none"})}})}},components:{waves:n.default}});a.default=o},b5b7:function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return s})),e.d(a,"a",(function(){}));var i=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("v-uni-view",{class:t.$store.state.AppStyle,staticStyle:{"background-color":"#f6f6f6"}},[i("v-uni-view",{staticClass:"header",style:[{height:t.CustomBar+"px"}]},[i("v-uni-view",{staticClass:"cu-bar bg-white",style:{height:t.CustomBar+"px","padding-top":t.StatusBar+"px"}},[i("v-uni-view",{staticClass:"action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.back.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"cuIcon-back"})],1),i("v-uni-view",{staticClass:"content text-bold",style:[{top:t.StatusBar+"px"}]},[t._v("群聊助手")]),i("v-uni-view",{staticClass:"action"})],1)],1),i("v-uni-view",{style:[{padding:t.NavBar+"px 10px 0px 10px"}]}),i("v-uni-view",{staticClass:"data-box"},[""==t.token?i("v-uni-view",{staticClass:"no-data"},[i("v-uni-view",{staticClass:"full-noLogin"},[i("v-uni-view",{staticClass:"full-noLogin-main"},[i("v-uni-text",{staticClass:"cuIcon-text"}),i("v-uni-view",{staticClass:"full-noLogin-text"},[t._v("请先登录账号")]),i("v-uni-view",{staticClass:"full-noLogin-btn"},[i("v-uni-view",{staticClass:"text-center margin-top-sm"},[i("v-uni-text",{staticClass:"cu-btn bg-blue radius-50",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goLogin()}}},[t._v("登录")]),i("v-uni-text",{staticClass:"cu-btn margin-left-sm radius-50 zhuce",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goRegister()}}},[t._v("注册")])],1)],1)],1)],1)],1):t._e(),""!=t.token?i("v-uni-view",{staticClass:"cu-list menu-avatar"},[i("v-uni-view",{staticClass:"cu-bar bg-white search"},[i("v-uni-view",{staticClass:"search-form round"},[i("v-uni-text",{staticClass:"cuIcon-search"}),i("v-uni-input",{attrs:{type:"text",placeholder:"搜索群聊"},model:{value:t.searchText,callback:function(a){t.searchText=a},expression:"searchText"}}),""!=t.searchText?i("v-uni-view",{staticClass:"search-close",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.searchClose()}}},[i("v-uni-text",{staticClass:"cuIcon-close"})],1):t._e()],1)],1),0==t.chatList.length?i("v-uni-view",{staticClass:"no-data"},[i("v-uni-text",{staticClass:"cuIcon-text"}),t._v("暂时没有数据")],1):t._e(),t._l(t.chatList,(function(a,e){return[-1!=a.name.indexOf(t.searchText)?i("v-uni-view",{key:e+"_0",staticClass:"cu-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goChat(a)}}},[1==a.type?[i("v-uni-view",{staticClass:"cu-avatar round lg",style:"background-image:url("+a.pic+");"})]:[i("v-uni-view",{staticClass:"cu-avatar round lg",style:"background-image:url("+a.userJson.avatar+");"})],i("v-uni-view",{staticClass:"content"},[i("v-uni-view",[i("v-uni-view",{staticClass:"text-cut"},[t._v(t._s(a.name))])],1),i("v-uni-view",{staticClass:"text-gray text-sm flex"},[i("v-uni-view",{staticClass:"text-cut"},[null!=a.lastMsg?[4!=a.lastMsg.type?[a.lastMsg.uid==t.uid?[t._v("我:")]:t._e(),a.lastMsg.uid!=t.uid?[t._v(t._s(a.name)+":")]:t._e(),0==a.lastMsg.type?[t._v(t._s(a.lastMsg.text))]:t._e(),1==a.lastMsg.type?[t._v("[图片]")]:t._e()]:["ban"==a.lastMsg.text?[i("v-uni-text",{staticClass:"text-red"},[t._v("[已开启全体禁言]")])]:[i("v-uni-text",{staticClass:"text-blue"},[t._v("[已解除全体禁言]")])]]]:[t._v("暂无消息")]],2)],1)],1),i("v-uni-view",{staticClass:"action"},[i("v-uni-view",{staticClass:"text-grey text-xs"},[t._v(t._s(t.chatFormatDate(a.lastTime)))]),null!=a.lastMsg?[a.lastMsg.uid==t.uid?[i("v-uni-view",{staticClass:"cu-tag sm",staticStyle:{background:"none"}},[t._v("")])]:[0==a.isNew?i("v-uni-view",{staticClass:"cu-tag sm",staticStyle:{background:"none"}},[t._v("")]):i("v-uni-view",{staticClass:"cu-tag round bg-red sm"},[t._v(t._s(a.unRead))])]]:[i("v-uni-view",{staticClass:"cu-tag sm",staticStyle:{background:"none"}},[t._v("")])]],2)],2):t._e()]}))],2):t._e()],1),0==t.isLoading?i("v-uni-view",{staticClass:"loading"},[i("v-uni-view",{staticClass:"loading-main"},[i("v-uni-image",{attrs:{src:e("6466")}})],1)],1):t._e()],1)},s=[]},d8d0:function(t,a,e){"use strict";e.r(a);var i=e("b5b7"),s=e("466a");for(var n in s)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return s[t]}))}(n);var o=e("828b"),c=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"75c8e67a",null,!1,i["a"],void 0);a["default"]=c.exports}}]);