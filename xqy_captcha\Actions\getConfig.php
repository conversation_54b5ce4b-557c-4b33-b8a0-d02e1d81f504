<?php
initializeActions();

// 获取验证配置
$sql = "SELECT * FROM Xqy_Plugin_captcha_config WHERE id = 1";
$result = mysqli_query($connect, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $config = mysqli_fetch_assoc($result);
    
    // 不返回敏感信息给前端
    $safeConfig = [
        'id' => $config['id'],
        'captcha_type' => $config['captcha_type'],
        'enabled' => $config['enabled']
    ];
    
    // 如果是管理员请求，返回完整配置
    if (isset($_POST['admin']) && $_POST['admin'] == '1') {
        $safeConfig = $config;
    }
    
    send_json(200, "获取配置成功", $safeConfig);
} else {
    send_json(404, "配置不存在");
}
?>
