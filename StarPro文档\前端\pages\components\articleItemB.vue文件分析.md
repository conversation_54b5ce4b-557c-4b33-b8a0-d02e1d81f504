# articleItemB.vue 文件分析

## 概述

`articleItemB.vue` 是文章列表项的第三种样式变体，与 `articleItemA.vue` 类似，也支持广告模式和多种文章布局（默认/小图、三图、大图）。其显著特点是在底部增加了一行**操作按钮**，如编辑、删除、推荐等，因此推测它主要用于需要对文章进行管理的场景，例如"我的文章"、"我的收藏"或后台管理列表。

## 文件信息
- **文件路径**：`APP前端部分/pages/components/articleItemB.vue.md`
- **主要功能**：渲染文章或广告列表项，提供管理操作入口（编辑、删除等）。

## 主要组成部分分析

### 1. Props
   - **`item`**: 
     - 类型: `Object`
     - 默认值: `{}`
     - 说明: 核心数据对象，结构与 `articleItemA.vue` 的 `item` 基本一致，可能额外包含管理所需字段（如 `status`）。
   - **`isTop`**: 
     - 类型: `Boolean`
     - 默认值: `false`
     - 说明: 控制是否显示置顶标识。
   - **`isDraft`**: 
     - 类型: `Boolean`
     - 默认值: `false`
     - 说明: 是否为草稿。
   - **`isManager`**: 
     - 类型: `Boolean`
     - 默认值: `false`
     - 说明: 是否为管理模式，用于控制部分按钮（如设为推荐）的显示。
   - **`isReview`**: 
     - 类型: `Boolean`
     - 默认值: `false`
     - 说明: 是否为审核模式，用于控制审核相关按钮（通过、拒绝）的显示。

### 2. 模板 (`<template>`)
   - **顶层逻辑**: 同样使用 `v-if` 区分广告 (`item.isAds`) 和文章，以及文章的不同布局 (`item.abcimg`)。
   - **广告模式**: 
     - 布局与 `articleItemA.vue` 的广告模式类似，使用图文左右结构。
     - 广告标识文字为"广告"。
     - 点击事件为 `goAds(item)`。
   - **文章模式 (多种布局)**: 
     - 布局逻辑（默认/小图 `able`、三图 `mable`、大图 `bable`）与 `articleItemA.vue` 类似，使用 Tuniao UI 组件和样式。
     - **标题和内容**: 显示方式与 `articleItemA` 类似。
     - **底部信息栏**: 显示置顶标识（如果 `isTop`）、分类、浏览/评论/点赞数，与 `articleItemA` 类似。
     - **新增操作按钮栏 (`action-btn`)**: 
       - 这是与 `articleItemA` 最主要的区别。
       - **草稿模式 (`isDraft == true`)**: 显示"编辑"和"删除"按钮，触发 `goEdit(item)` 和 `toDelete(item, index)`。
       - **非草稿模式**: 
         - **管理模式 (`isManager == true`)**: 
           - 显示"编辑"和"删除"按钮。
           - 如果文章未推荐 (`item.isRecommend == 0`)，显示"设为推荐"按钮 (`toRecomm(item, index)`)。
           - 如果文章已推荐，显示"取消推荐"按钮 (`toRecomm(item, index)`)。
         - **审核模式 (`isReview == true`)**: 
           - 显示"通过"和"拒绝"按钮，触发 `reviewPost(item, 1, index)` 和 `reviewPost(item, 2, index)`。
         - **普通模式 (非草稿、非管理、非审核)**: 
           - 显示"编辑"和"删除"按钮。

### 3. 脚本 (`<script>`)
   - **依赖**: `localStorage`。
   - **`props`**: 定义了 `item`, `isTop`, `isDraft`, `isManager`, `isReview`。
   - **`name`**: 组件名 `articleItemB`。
   - **`data`**: 
     - `vip_img`, `vip_img_big`, `no_img`: 图片资源路径 (VIP占位图、无图占位图)。
     - `token`: 用户登录凭证。
   - **`created()`**: 组件创建时获取 `token`。
   - **`methods`**: 
     - `subText`, `replaceSpecialChar`, `formatDate`, `formatNumber`: 与前两个组件类似的数据格式化和文本处理方法。
     - `toInfo(data)`: 跳转文章详情页。
     - `goAds(data)`: 处理广告点击跳转。
     - `goEdit(data)`: 跳转到文章编辑页 (`/pages/edit/articlePost`)，携带 `cid`。
     - `toDelete(data, index)`: 
       - 弹出确认框。
       - 调用 `$API.deleteContent()` 接口删除文章。
       - 成功后通过 `$emit(\'deletef\', index)` 通知父组件删除该项。
     - `toRecomm(data, index)`: 
       - 弹出确认框。
       - 调用 `$API.recommend()` 接口设置/取消推荐。
       - 成功后通过 `$emit(\'recomm\', index, res.data.msg)` 通知父组件更新状态。
     - `reviewPost(data, status, index)`: 
       - 弹出确认框。
       - 调用 `$API.postReview()` 接口进行审核操作。
       - 成功后通过 `$emit(\'review\', index, status)` 通知父组件移除该项。

### 4. Emitted Events
   - **`deletef(index)`**: 当文章删除成功时触发，传递被删除项的索引。
   - **`recomm(index, msg)`**: 当设置/取消推荐成功时触发，传递项的索引和操作结果信息。
   - **`review(index, status)`**: 当审核操作成功时触发，传递项的索引和审核状态。

## 总结与注意事项

-   `articleItemB.vue` 是一个功能增强型的文章列表项组件，特别适用于需要进行管理操作的列表。
-   通过 `isDraft`, `isManager`, `isReview` 等 props 控制显示不同的操作按钮。
-   删除、推荐、审核等操作会调用相应的后端 API，并通过 `$emit` 将结果通知父组件进行列表更新。
-   布局和样式与 `articleItemA` 类似，可能同样依赖 Tuniao UI。
-   VIP文章内容同样会显示占位图。

## 后续分析建议

-   **API 依赖**: 查看 `$API.deleteContent()`, `$API.recommend()`, `$API.postReview()` 等接口的后端实现。
-   **父组件交互**: 分析使用 `articleItemB` 的父组件如何接收和处理 `deletef`, `recomm`, `review` 事件，并更新列表状态。
-   **权限控制**: 确认调用删除、推荐、审核接口时后端是否有相应的权限校验。
-   **使用场景**: 明确 `isDraft`, `isManager`, `isReview` 这几个 prop 分别在哪些页面被设为 true。 