<template>
	<view class="padding-top-sm" :class="isDark?'dark':''">
		<!--帖子推流广告区域-->
		<view class="cu-card article no-card home-shadow" :class="isTop?'topContents':''" v-if="item.isAds" @tap="goAds(item)">
			<view class="cu-item shadow">
				<view class="title">
					<view class="text-cut">{{replaceSpecialChar(item.name)}}</view>
				</view>
				<view class="content article-content" style="position: relative;">
					<image :src="item.img" mode="aspectFill"></image>
					<view class="desc">
						<view class="text-content">{{item.intro}}</view>
						<view class="ads-more" @tap="goAds(item)">了解更多<text class="cuIcon-right"></text></view>
					</view>
					<text class="ads-ico">广告</text>
				</view>
			</view>
		</view>
		<!--帖子推流广告区域结束-->
		<view class="cu-card article no-card home-shadow" v-else @tap="toInfo(item)" >
			<view class="cu-item" style="border-radius: 20upx;">
				<view class="title clamp-text-1">
					<view class="content-author flex align-center" style="margin-top: 0upx;line-height: 20upx;">
						<view class="user-rz">
							<image :src="item.authorInfo.avatar" mode="aspectFill"></image> 
							<image class="user-rz-icon" :src="rzImg" mode="aspectFill" v-if="item.authorInfo.lvrz==1"></image>
						</view>
							<text class="content-author-name text-bold" :class="item.authorInfo.isvip>0?'name-vip':''">{{item.authorInfo.name}}</text>
							<image v-if="item.authorInfo.isvip>0" :src="vipImg" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;" mode="widthFix"></image>							
							<image :src="lvImg+getLv(item.authorInfo.experience)+'.png'" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 44upx;height: 22upx;" mode="widthFix"></image>
						
					</view>
					<view class="text-cut">
						<!-- 
						<view class="data-time" style="color: #888;font-size: 9px;font-weight: normal !important;">
							{{formatDate(item.created)}}</view> -->
						<text style="color: #444;">{{replaceSpecialChar(item.title)}}</text>
					</view>
				</view>
				<!-- 大图 -->
				<view v-if="item.images.length == 0">
					<view class="content article-content clamp-text-2">
						<view class="text-content" v-if="item.text.length > 0">{{subText(item.text)}}</view>
					</view>
				</view>
				<view v-if="item.images.length > 0">
					<view class="content article-content clamp-text-2">
						<view class="text-content" v-if="item.text.length > 0">{{subText(item.text)}}</view>

						<view class="grid flex-sub col-3 grid-square grid-square-wz" :class="item.text.length>10?'tn-margin-top-sm':''">
							<block v-if="item.category && item.category.length > 0 && item.category[0].slug !== 'vip'">
								
								<!-- #ifdef APP-PLUS || H5-->
								<tn-lazy-load v-if="item.images.length > 0" height="250" :threshold="6000" :image="item.images[0]" imgMode="aspectFill">
								</tn-lazy-load>
								<tn-lazy-load v-if="item.images.length > 1" height="250" :threshold="6000" :image="item.images[1]" imgMode="aspectFill">
								</tn-lazy-load>
								<view class="bg-img" v-if="item.images.length > 2">
								<text v-if="item.images.length > 3" class="extra-count">
										<view class="cuIcon-add center-add"> {{ item.images.length-3 }}</view>
									</text>
								<tn-lazy-load v-if="item.images.length > 1" height="250" :threshold="6000" :image="item.images[2]" imgMode="aspectFill">
								</tn-lazy-load>
								</view>
								<!-- #endif-->
								<!-- #ifdef  MP-->
								<view class="bg-img">
									<image v-if="item.images.length > 0" :src="item.images[0]" mode="aspectFill">
									</image>
								</view>
								<view class="bg-img" v-if="item.images.length > 1">
									<image v-if="item.images.length > 1" :src="item.images[1]" mode="aspectFill">
									</image>
								</view>
								<view class="bg-img" v-if="item.images.length > 2">
									<image v-if="item.images.length > 2" :src="item.images[2]" mode="aspectFill">
									</image>
									
								</view>
								<!-- #endif-->
								
							</block>
							 <block v-else>
								<view class="bg-img">
									<image v-if="item.images.length > 0" src="../../static/page/vip_img.png"
										mode="aspectFill"></image>
								</view>
								<view class="bg-img" v-if="item.images.length > 1">
									<image v-if="item.images.length > 1" src="../../static/page/vip_img.png"
										mode="aspectFill"></image>
								</view>
								<view class="bg-img" v-if="item.images.length > 2">
									<image v-if="item.images.length > 2" src="../../static/page/vip_img.png"
										mode="aspectFill"></image>
									<text v-if="item.images.length > 3" class="extra-count">
										<view class="cuIcon-add center-add"> {{ item.images.length-3 }}</view>
									</text>
								</view>
							</block> 
						</view>

					</view>
				</view>

				<view class="article-content-btn article-list-btn flex justify-between" style="margin-top:30upx;">
					
					<view class="content-author flex align-center" style="margin-top: 0upx;line-height: 20upx;">
						<view class="justify-content-item tn-tag-content__item tn-margin-right-sm tn-text" v-if="isTop">
						  <text class="tn-tag-content__item--prefix tn-text-bold" style="padding-right: 0upx;">
							  <text class="tn-icon-totop-simple tn-tag-content__item--prefix" style="color: #00BCD4;"></text>置顶</text>
						</view>
						<view class="justify-content-item tn-tag-content__item tn-text-sm tn-text-bold" style="border-radius: 50upx;padding: 6upx 30upx;">
							{{item.category && item.category.length > 0 ? item.category[0].name : '暂无分类'}}
						</view>
						
					</view>
					<view class="flex align-center" style="color:#666">

						<view class="margin-left-sm">
							<span class="tn-icon-eye tn-color-gray" style="font-size: 36upx;"></span>
						</view>
						<text> {{formatNumber(item.views)}} </text>

						<view class="margin-left-sm">
							<span class="tn-icon-comment tn-color-gray" style="font-size: 36upx;"></span>
						</view>
						<text> {{item.commentsNum}} </text>

						<view class="margin-left-sm">
							<span class="tn-icon-like-lack tn-color-gray" style="font-size: 36upx;"></span>
						</view>
						<text> {{item.likes}} </text>

					</view>

				</view>

			</view>
		</view>
	</view>

</template>

<script>
	import darkModeMixin from '@/utils/darkModeMixin.js'
	// #ifdef APP-PLUS
	import owo from '../../static/app-plus/owo/OwO.js'
	// #endif
	// #ifdef H5
	import owo from '../../static/h5/owo/OwO.js'
	// #endif
	// #ifdef MP
	var owo = [];
	// #endif
	export default {
		mixins: [darkModeMixin],
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
			isTop: {
				type: Boolean,
				default: false
			},
			isDraft: {
				type: Boolean,
				default: false
			}
		},
		name: "articleItemA",
		data() {
			return {
				rzImg: this.$API.SPRz(),
				vipImg: this.$API.SPvip(),
				lvImg: this.$API.SPLv(),
				needRefresh: false
			};
		},
		created() {
			
		},
		methods: {
			
			getLv(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var lv  = that.$API.getLever(i);
				return lv;
			},
			subText(text) {
				text = text.replace(/vip(.*?)\/vip/g, "(该内容仅会员可见)");
				text = text.replace(/audio(.*?)\/audio/g, "(该帖子包含音乐)");
				text = text.replace(/video(.*?)\/video/g, "(该帖子包含视频)");
				if (text.length > 45) {
					return text.substring(0, 45) + "......"
				} else {
					return text;
				}
			},
			replaceAll(string, search, replace) {
				return string.split(search).join(replace);
			},

			replaceSpecialChar(text) {
				text = text.replace(/&quot;/g, '"');
				text = text.replace(/&amp;/g, '&');
				text = text.replace(/&lt;/g, '<');
				text = text.replace(/&gt;/g, '>');
				text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var now = new Date();
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
			
				var timeDiff = now - datetime;
				var seconds = Math.floor(timeDiff / 1000);
				var minutes = Math.floor(timeDiff / (1000 * 60));
				var hours = Math.floor(timeDiff / (1000 * 60 * 60));
				var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
				var months = Math.floor(days / 30);
			
				var result = "";
			
				if (seconds < 60) {
					result = seconds + "秒前";
				} else if (minutes < 60) {
					result = minutes + "分钟前";
				} else if (hours < 24) {
					result = hours + "小时前";
				} else if (days < 7) {
					result = days + "天前";
				} else if (year === now.getFullYear()) {
					result = month + "月" + date + "日";
				} else {
					result = year + "年" + month + "月" + date + "日";
				}
			
				return result;
			},
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},
			toInfo(data) {
				var that = this;
				if(!that.isDraft){
					uni.navigateTo({
					    url: '/pages/contents/info?cid='+data.cid+"&title="+data.title
					});
				}
				
			},
			toVideoInfo(data) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/videoInfo?cid=' + data.cid + "&title=" + data.title
				});
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
		}
	}
</script>

<style lang="scss" scoped>
.user-rz{
	position: relative;
	display: inline-block;
}
.user-rz-icon{
	position: absolute;
	right: -6upx;
	bottom: -4upx;
	width: 24upx;
	height: 24upx;
}
.user-rz-icon-pbl {
    position: absolute;
    right: -6upx;
    bottom: 2upx;
	width: 24upx;
	height: 24upx;
}
.user-rz-icon-htop {
    position: absolute;
    right: -4upx;
    bottom: 4upx;
    width: 30upx;
    height: 30upx;
    z-index: 10;
}
.name-vip{
	color:#ff6c3e;
}
	/* 标签内容 start*/
	.tn-tag-content {
 	    &__item {
 	      display: inline-block;
 	      line-height: 35rpx;
 	      color: #1D2541;
 	      background-color: #F3F2F7;
 	      border-radius: 10rpx;
 	      font-size: 22rpx;
 	      padding: 5rpx 15rpx;
 	  
 	      &--prefix {
 	        padding-right: 10rpx;
 	      }
 	    }
 	  }
	.text-content {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	/* 阴影 start*/
	.home-shadow {
		border-radius: 15rpx;
		border-bottom: 1px solid #ebebeb;
	}
	.content-author{
		height: 54upx;
	}
	.extra-count {
		position: absolute;
		background-color: #0000005c;
		color: white;
		z-index: 100;
		border-radius: 20upx;
		font-size: 20px;
		font-weight: bold;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.grid.grid-square>uni-view {
		border-radius: 20upx;
	}
	.data-box .cu-item{
		padding-top: 0px;
	}
</style>