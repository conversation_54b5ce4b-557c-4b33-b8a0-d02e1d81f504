<template>
	<view class="editor-container">
		<editor :adjust-position="false" id="editor" class="cu-editor"
			:style="{ height: inputFocus?(scrollViewHeight + NavBar*2)-50 + 'px':'800rpx' }" :placeholder="placeholder"
			:read-only="readOnly" :show-img-size="showImgSize" :show-img-toolbar="showImgToolbar"
			:show-img-resize="showImgResize" @statuschange="onStatusChange" @ready="onEditorReady"
			@input="onEditorInput" @focus="onEditorFocus" @blur="onEditorBlur"></editor>
		<view class="fixed-bottom" :hidden="!toolbarShow"
			:style="{ bottom:  (iphoneXBottomH > 0) ? (keyboardHeight > 0 ? keyboardHeight : iphoneXBottomH) : ((keyboardHeight > 0) ? keyboardHeight : 0) + 'px' }"
			@touchstart.stop="">
			<view class="toolbar selector" :style="{height: toolBarHeight + 'rpx'}">
				<!-- #ifdef APP-PLUS || H5 -->
					<view class="toolbar-item-header" @click="changeKeyBoard"><i class="iconfont icon-keyboard"></i>
					</view>
					<view v-for="(icon, index) in ['icon-add', 'icon-textformat', 'icon-align-left']" class="toolbar-item"
						@click="changeSwiper(index)">
						<i class="iconfont" :class="[icon, { active: toolBarContentShow && swiperCurrent == index }]"></i>
					</view>
					
					<view class="toolbar-item" @click="formatformat('check')"><i
							class="iconfont icon-list-check"></i></view>
					<view class="toolbar-item" @click="clickUndo()"><i class="iconfont icon-undo"></i>
					</view>
					<view class="toolbar-item" @click="clickRedo()"><i class="iconfont icon-redo"></i>
					</view>
					<view class="toolbar-item-footer" @click="hideToolbar"><i class="iconfont icon-check"></i>
					</view>
				<!--#endif -->
				<!--#ifdef MP-WEIXIN -->
					<view class="toolbar-item-header" @touchend.stop="changeKeyBoard"><i class="iconfont icon-keyboard"></i>
					</view>
					<view v-for="(icon, index) in ['icon-add', 'icon-textformat', 'icon-align-left']" class="toolbar-item"
						@touchend.stop="changeSwiper(index)">
						<i class="iconfont" :class="[icon, { active: toolBarContentShow && swiperCurrent == index }]"></i>
					</view>
					
					<view class="toolbar-item" @touchend.stop="formatformat('check')"><i
							class="iconfont icon-list-check"></i></view>
					<view class="toolbar-item" @touchend.stop="clickUndo()"><i class="iconfont icon-undo"></i>
					</view>
					<view class="toolbar-item" @touchend.stop="clickRedo()"><i class="iconfont icon-redo"></i>
					</view>
					<view class="toolbar-item-footer" @touchend.stop="hideToolbar"><i class="iconfont icon-check"></i>
					</view>
				<!--#endif -->
				
			</view>
			<swiper :hidden="!toolBarContentShow" class="toolbar-content swiper-box"
				:style="{height: toolBarContentHeight + 'rpx'}" @change="handleSwiperChange" :current="swiperCurrent"
				duration="300">
				
				<!-- @touchstart.stop="" 禁用手动滑动 -->
				<!-- #ifdef APP-PLUS || H5 -->
				<swiper-item v-for="(page, i) in formatArray" :key="i" class="swiper-item">
				<!--#endif -->
				<!--#ifdef MP-WEIXIN -->
				<swiper-item v-for="(page, i) in formatArray" :key="i" class="swiper-item" @touchstart.stop="">
				<!--#endif -->
				
					<template v-if="page.type === 'feature'">
						<!-- #ifdef APP-PLUS || H5 -->
						<view class="feature-items flex">
							<view class="feature" @click="getPhotoBtShow()">
								<view class="icon"><text class="iconfont icon-image"></text></view>
							</view>
							<view class="feature" @click="getVideoBtShow()">
								<view class="icon"><text class="tn-icon-video-square-fill"
										style="font-size:50upx;"></text></view>
							</view>
							<view class="feature" @click="getUrlAudioShow()">
								<view class="icon"><text class="tn-icon-music-fill" style="font-size:47upx;"></text>
								</view>
							</view>
							<view class="feature" @click="getUrlFileShow()">
								<view class="icon"><text class="tn-icon-folder-upload-fill"
										style="font-size:47upx;"></text></view>
							</view>
						</view>
						<view class="feature-items flex">
							<view class="feature" style="margin-top: 0px;" @click="getUrlBtShow()">
								<view class="icon"><text class="tn-icon-link" style="font-size:44upx;"></text></view>
							</view>
							<block v-if="!isPay">
								<view class="feature" style="margin-top: 0px;" @click="getVipBtShow()">
									<view class="icon"><text class="tn-icon-vip-diamond"
											style="font-size:47upx;"></text></view>
								</view>
								<view class="feature" style="margin-top: 0px;" @click="getReplyBtShow()">
									<view class="icon"><text class="tn-icon-eye-close" style="font-size:47upx;"></text>
									</view>
								</view>
							</block>
							<view class="feature" style="margin-top: 0px;" @click="clickInsertDivider()">
								<view class="icon"><text class="iconfont icon-line"></text></view>
							</view>
						</view>
						<view class="feature-items flex">
							<view class="feature" style="margin-top: 0px;" v-if="isWz">
								<view class="icon" @click="setShop()"><text class="tn-icon-shopbag-fill"
										style="font-size:47upx;"></text></view>
							</view>
						</view>
						<!--#endif -->
						<!--#ifdef MP-WEIXIN -->
						<view class="feature-items flex">
							<view class="feature" @touchend.stop="getPhotoBtShow()">
								<view class="icon"><text class="iconfont icon-image"></text></view>
							</view>
							<view class="feature" @touchend.stop="getVideoBtShow()">
								<view class="icon"><text class="tn-icon-video-square-fill"
										style="font-size:50upx;"></text></view>
							</view>
							<view class="feature" @touchend.stop="getUrlAudioShow()">
								<view class="icon"><text class="tn-icon-music-fill" style="font-size:47upx;"></text>
								</view>
							</view>
							<view class="feature" @touchend.stop="getUrlFileShow()">
								<view class="icon"><text class="tn-icon-folder-upload-fill"
										style="font-size:47upx;"></text></view>
							</view>
						</view>
						<view class="feature-items flex">
							<view class="feature" style="margin-top: 0px;" @touchend.stop="getUrlBtShow()">
								<view class="icon"><text class="tn-icon-link" style="font-size:44upx;"></text></view>
							</view>
							<block v-if="!isPay">
								<view class="feature" style="margin-top: 0px;" @touchend.stop="getVipBtShow()">
									<view class="icon"><text class="tn-icon-vip-diamond"
											style="font-size:47upx;"></text></view>
								</view>
								<view class="feature" style="margin-top: 0px;" @touchend.stop="getReplyBtShow()">
									<view class="icon"><text class="tn-icon-eye-close" style="font-size:47upx;"></text>
									</view>
								</view>
							</block>
							<view class="feature" style="margin-top: 0px;" @touchend.stop="clickInsertDivider()">
								<view class="icon"><text class="iconfont icon-line"></text></view>
							</view>
						</view>
						<view class="feature-items flex">
							<view class="feature" style="margin-top: 0px;" v-if="isWz">
								<view class="icon" @touchend.stop="setShop()"><text class="tn-icon-shopbag-fill"
										style="font-size:47upx;"></text></view>
							</view>
						</view>
						<!--#endif -->
						
						
					</template>
					<template v-else-if="page.type === 'tool'">
						<view v-for="(pitem, pindex) in page.array" :key="pindex" class="tool-items flex">
							<!-- #ifdef APP-PLUS || H5 -->
							<view v-for="(item, index) in pitem.items" :key="index" class="tool-item"
								@click="formatformat('format', item, pitem)"
								:class="{ 'ql-active': isActive(item, pitem), noBgColor: pitem.name == 'color' }">
								<view v-if="pitem.name == 'color'" class="color-circle"
									:style="{ 'background-color': item.value }"></view>
								<i v-else-if="pitem.label == 'icon'" class="iconfont" :class="'icon-' + item.icon"></i>
								<text v-else class="txt"
									:style="[{fontSize : pitem.name == 'fontSize' ? item.value : ''}, item.style]">{{ item.title || item.value }}</text>
							</view>
							<!--#endif -->
							<!--#ifdef MP-WEIXIN -->
							<view v-for="(item, index) in pitem.items" :key="index" class="tool-item"
								@touchend.stop="formatformat('format', item, pitem)"
								:class="{ 'ql-active': isActive(item, pitem), noBgColor: pitem.name == 'color' }">
								<view v-if="pitem.name == 'color'" class="color-circle"
									:style="{ 'background-color': item.value }"></view>
								<i v-else-if="pitem.label == 'icon'" class="iconfont" :class="'icon-' + item.icon"></i>
								<text v-else class="txt"
									:style="[{fontSize : pitem.name == 'fontSize' ? item.value : ''}, item.style]">{{ item.title || item.value }}</text>
							</view>
							<!--#endif -->
							
						</view>
					</template>
				</swiper-item>
			</swiper>
		</view>
		<!--图片-->
		
		<tn-modal v-model="photoBtShow" showCloseBtn :custom="true" :background-color="isDark?'#1c1c1c':'#ffffff'">
			<view class="custom-modal-content">
				<view class="tn-flex justify-content-center" style="flex-direction: column;">
					<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-blue"
						style="margin:30upx 0 15upx 0" @click="chooseImage()">相册上传</tn-button>
					<!-- #ifdef APP-PLUS || H5 -->
					<view class="tn-flex" style="margin:15upx 0 30upx 0;justify-content:space-between">
						<tn-button padding="40rpx 30rpx" width="45%" backgroundColor="tn-main-gradient-blue--light"
							@click="getUrlImageShow()">外链插入</tn-button>
						<tn-button padding="40rpx 30rpx" width="45%" backgroundColor="tn-main-gradient-indigo--light"
							@click="getBatchUrlImageShow()">批量外链</tn-button>
					</view>
					<!--#endif -->
					<!--#ifdef MP-WEIXIN -->
					<view style="margin:15upx 0 30upx 0;">
						<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-blue--light"
							@click="getUrlImageShow()">外链插入</tn-button>
					</view>
					<view style="margin:15upx 0 30upx 0;">
					<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
						@click="getBatchUrlImageShow()">批量外链</tn-button>
					</view>
					<!--#endif -->
					
				</view>
			</view>
		</tn-modal>
		<tn-modal v-model="UrlImageShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="cu-form-group margin-top">
						<view class="title">链接</view>
						<input v-model="imgurl" placeholder="请输入图片链接" maxlength="1000" name="url"></input>
					</view>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
					style="margin:10upx 0 30upx 0" @click="setUrlImage()">插入</tn-button>
			</view>
		</tn-modal>
		<tn-modal v-model="batchUrlImageShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="margin-top">
						<view class="title margin-bottom" style="font-size: 30upx;">批量链接</view>
						<textarea maxlength="1000" style="font-size: 30upx;" v-model="batchImgUrls"
							placeholder="请输入每行一个图片链接" name="text"></textarea>
					</view>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-blue--light"
					style="margin:10upx 0 30upx 0" @click="setBatchUrlImages()">批量插入</tn-button>
			</view>
		</tn-modal>
		<!--视频-->
		<tn-modal v-model="videoBtShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<!--#ifdef APP-PLUS || H5 -->
				<view class="tn-flex justify-content-center" style="flex-direction: column;">
					<tn-button padding="40rpx 30rpx" backgroundColor="tn-main-gradient-blue"
						style="margin:30upx 0 20upx 0" @click="chooseVideo()">相册上传</tn-button>
					<tn-button padding="40rpx 30rpx" backgroundColor="tn-main-gradient-blue--light"
						style="margin:10upx 0 30upx 0" @click="getUrlVideoShow()">外链插入</tn-button>
				</view>
				<!--#endif -->
				<!--#ifdef MP-WEIXIN -->
				<view>
					<tn-button padding="40rpx 30rpx" backgroundColor="tn-main-gradient-blue"
						style="margin:30upx 0 20upx 0" width="100%" @click="chooseVideo()">相册上传</tn-button>
				</view><view>
					<tn-button padding="40rpx 30rpx" backgroundColor="tn-main-gradient-blue--light"
						style="margin:10upx 0 30upx 0" width="100%" @click="getUrlVideoShow()">外链插入</tn-button>
				</view>
				<!--#endif -->
				
			</view>
		</tn-modal>
		<tn-modal v-model="UrlVideoShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="margin-top">
						<view class="title" style="color: #333;">视频链接</view>
						<input v-model="videourl"
							style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
							placeholder="请输入视频链接" maxlength="1000" name="url"></input>


						<view class="title margin-top" style="color: #333;">封面链接</view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<input v-model="videoPoster"
								style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
								placeholder="请输入封面链接(选填)" maxlength="1000" name="url2"></input>
							<tn-button padding="10rpx 15rpx" width="100upx"
								backgroundColor="tn-main-gradient-indigo--light" style="margin:10upx"
								@click="toPhotoUpload()">上传</tn-button>
						</view>
					</view>
					<br>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
					style="margin:10upx 0 30upx 0" @click="setUrlVideo()">插入</tn-button>
			</view>
		</tn-modal>
		<!--音乐-->
		<tn-modal v-model="UrlAudioShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="margin-top">
						<view class="title margin-top" style="color: #333;">音乐链接</view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<input v-model="audiourl"
								style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
								placeholder="请输入音乐链接" maxlength="1000" name="url"></input>
							<tn-button padding="10rpx 15rpx" width="100upx"
								backgroundColor="tn-main-gradient-indigo--light" style="margin:10upx"
								@click="toMusicUpload()">上传</tn-button>
						</view>
						<view class="title margin-top" style="color: #333;">封面链接</view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<input v-model="audioPosterUrl"
								style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
								placeholder="请输入封面链接(选填)" maxlength="1000" name="url1"></input>
							<tn-button padding="10rpx 15rpx" width="100upx"
								backgroundColor="tn-main-gradient-indigo--light" style="margin:10upx"
								@click="toPhotoUpload()">上传</tn-button>
						</view>
						<view class="title margin-top" style="color: #333;">音乐名称</view>
						<input v-model="musicName"
							style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
							placeholder="请输入音乐名称(选填)" name="url2"></input>
						<view class="title margin-top" style="color: #333;">音乐作者</view>
						<input v-model="musicAuthor"
							style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
							placeholder="请输入音乐作者(选填)" name="url3"></input>

					</view>
					<br>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
					style="margin:10upx 0 30upx 0" @click="setUrlAudio()">插入</tn-button>
			</view>
		</tn-modal>
		<!--附件-->
		<tn-modal v-model="UrlFileShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="margin-top">
						<view class="title margin-top" style="color: #333;">附件链接</view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<input v-model="fileurl"
								style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
								placeholder="请输入附件链接" name="url" maxlength="1000"></input>
							<tn-button padding="10rpx 15rpx" width="100upx"
								backgroundColor="tn-main-gradient-indigo--light" style="margin:10upx"
								@click="toFileUpload()">上传</tn-button>
						</view>
						<view class="title margin-top" style="color: #333;">附件名称</view>
						<input v-model="filename"
							style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
							placeholder="请输入附件名称(选填)" name="url2"></input>
						<view class="title margin-top" style="color: #333;">提取码</view>
						<input v-model="filepw"
							style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
							placeholder="请输入提取码(选填)" name="url2"></input>

					</view>
					<br>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
					style="margin:10upx 0 30upx 0" @click="setUrlFile()">插入</tn-button>
			</view>
		</tn-modal>
		<!--超链接-->
		<tn-modal v-model="UrlShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="margin-top">
						<view class="title margin-top" style="color: #333;">文本</view>
						<input v-model="aText"
							style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
							placeholder="请输入文本(选填)" name="url2"></input>
						<view class="title margin-top" style="color: #333;">链接</view>
						<input v-model="aUrl"
							style="margin-top:10upx;border-bottom: 2rpx solid #666;color: #666;font-size: 27upx"
							placeholder="请输入链接" maxlength="1000" name="url3"></input>
					</view>
					<br>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
					style="margin:10upx 0 30upx 0" @click="setUrl()">插入</tn-button>
			</view>
		</tn-modal>
		<!--会员可见-->
		<tn-modal v-model="VipTextShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="margin-top">
						<view class="title margin-bottom" style="font-size: 30upx;">VIP可见内容</view>
						<textarea maxlength="1000" style="font-size: 30upx;" v-model="vipText" placeholder="请输入VIP可见内容"
							name="viptext"></textarea>
					</view>
					<br>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
					style="margin:10upx 0 30upx 0" @click="setVipText()">插入</tn-button>
			</view>
		</tn-modal>
		<!--回复可见-->
		<tn-modal v-model="ReplyTextShow" showCloseBtn :custom="true">
			<view class="custom-modal-content">
				<form>
					<view class="margin-top">
						<view class="title margin-bottom" style="font-size: 30upx;">回复可见内容</view>
						<textarea maxlength="1000" style="font-size: 30upx;" v-model="ReplyText" placeholder="请输入回复可见内容"
							name="replytext"></textarea>
					</view>
					<br>
				</form>
				<tn-button padding="40rpx 30rpx" width="100%" backgroundColor="tn-main-gradient-indigo--light"
					style="margin:10upx 0 30upx 0" @click="setReplyText()">插入</tn-button>
			</view>
		</tn-modal>
		<tn-modal v-model="uploadShow" :radius="40" :maskCloseable="false" inactiveColor="#e7e7e7" :showCloseBtn="false"
			:custom="true">
			<view class="custom-modal-content">
				<view class="margin-bottom" style="display: flex;justify-content: space-between;">
					<view>{{uploadMessage}}</view>
					<view>总进度：{{progressNum}}%</view>
				</view>
				<tn-line-progress :striped="true" :percent="progressNum" activeColor="#01BEFF"></tn-line-progress>
			</view>
		</tn-modal>

	</view>

</template>

<script>
	import {
		handleHtmlImage
	} from './util'

	export default {
		name: 'cuEditor',
		props: {
			//editor属性，提示信息
			placeholder: {
				type: String,
				default: '请输入内容'
			},
			//editor属性，点击图片时显示图片大小控件
			showImgSize: {
				type: Boolean,
				default: false
			},
			//editor属性，点击图片时显示工具栏控件
			showImgToolbar: {
				type: Boolean,
				default: false
			},
			//editor属性，点击图片时显示修改尺寸控件
			showImgResize: {
				type: Boolean,
				default: false
			},
			//编辑器内容，必填
			content: {
				type: String,
				default: ''
			},
			//chooseImage参数，最多可以选择的图片张数
			count: {
				type: Number,
				default: 9
			},
			//chooseImage参数，所选的图片的尺寸
			sizeType: {
				type: Array,
				default () {
					return ['compressed','original'] //['original', 'compressed']
				}
			},
			//chooseImage参数，选择图片的来源
			sourceType: {
				type: Array,
				default () {
					return ['album', 'camera']
				}
			},
			//不允许上传的图片类型
			noAllowType: {
				type: Array,
				default () {
					return [] //['gif']
				}
			},
			//uploadFile参数，必填
			url: {
				type: String,
				default: ''
			},
			editorId: {
				type: String,
				default: 'editor'
			},
			NavBar: {
				type: Number,
				default: 0
			},
			//uploadFile参数
			header: {
				type: Object,
				default () {
					return {}
				}
			},
			isWz: {
				type: Boolean,
				default: false
			},
			//uploadFile参数
			formData: {
				type: Object,
				default () {
					return {}
				}
			},
			//uploadFile参数
			name: {
				type: String,
				default: 'file'
			},
			mrPoster: {
				type: String,
				default: ''
			},
			SPimg: {
				type: String,
				default: ''
			},
			MusicUpload: {
				type: String,
				default: ''
			},
			musicpic: {
				type: String,
				default: ''
			},
			isPay: {
				type: Boolean,
				default: false
			},
			uploadTime: {//上传超时秒数
				type: Number,
				default: 60
			},
		},
		data() {
			return {
				isFixed: true,
				iphoneXBottomH: 0,
				scrollHeightDefault: 0,
				keyboardHeight: 0,
				InputBottom: 0,
				ScrollBox: 350,
				readOnly: true,
				isDefaultFormat: true, // 首次聚集时设置默认格式
				isIos: false,
				inputFocus: false,
				formats: {},
				poster: "",
				photoBtShow: false,
				UrlImageShow: false,
				imgurl: "",
				videoBtShow: false,
				UrlVideoShow: false,
				videourl: "",
				uploadMessage: "正在上传中...",
				videoPoster: "",
				batchUrlImageShow: false,
				batchImgUrls: '',
				audioBtShow: false,
				UrlAudioShow: false,
				audiourl: '',
				musicName: '',
				musicAuthor: '',
				audioPosterUrl: '',
				uploadUrl: '',
				UrlFileShow: false,
				fileurl: '',
				filepw: '',
				UrlShow: false,
				progressNum: 0,
				uploadShow: false,
				timeoutId: null,
				aText: '',
				aUrl: '',
				filename: '',
				fileflag: 1,
				VipTextShow: false,
				vipText: '',
				ReplyTextShow: false,
				intervalId: null,
				ReplyText: '',
				formatArray: [{
						type: 'feature',
						array: [{
								name: 'chooseImage',
								icon: 'image'
							},
							{
								name: 'chooseImagebyCamera',
								icon: 'photo'
							},
							{
								name: 'insertDivider',
								icon: 'line'
							}
						]
					},
					{
						type: 'tool',
						array: [{
								name: 'text',
								label: 'icon',
								items: [{
										name: 'bold',
										icon: 'bold'
									},
									{
										name: 'italic',
										icon: 'italic'
									},
									{
										name: 'underline',
										icon: 'underline'
									},
									{
										name: 'strike',
										icon: 'strikethrough'
									},
									{
										name: 'backgroundColor',
										value: 'yellow',
										icon: 'fontbgcolor'
									}
								]
							},
							{
								name: 'defaultFormat',
								items: [{
										title: '标题',
										format: {
											fontSize: '18px',
											bold: 'strong'
										},
										style: {
											fontSize: '18px',
											fontWeight: 'bold'
										}
									},
									{
										title: '小标题',
										format: {
											fontSize: '16px',
											bold: 'strong'
										},
										style: {
											fontSize: '16px',
											fontWeight: 'bold'
										}
									},
									{
										title: '正文',
										format: {
											fontSize: '14px'
										},
										style: {
											fontSize: '14px'
										}
									},
									{
										title: '注释',
										format: {
											fontSize: '12px',
											color: '#888888'
										},
										style: {
											fontSize: '12px',
											color: '#888888',
										}
									}
								]
							},
							{
								name: 'fontSize',
								items: [{
										title: '18',
										value: '18px'
									},
									{
										title: '16',
										value: '16px'
									},
									{
										title: '14',
										value: '14px'
									},
									{
										title: '12',
										value: '12px'
									},
									{
										title: '11',
										value: '11px'
									},
									{
										title: '10',
										value: '10px'
									}
								]
							},
							{
								name: 'color',
								items: [{
										value: '#000000'
									},
									{
										value: '#888888'
									},
									{
										value: '#ffffff'
									},
									{
										value: '#f6de41'
									},
									{
										value: '#f68c41'
									},
									{
										value: '#fd3136'
									},
									{
										value: '#5ad8a6'
									}
								]
							}
						]
					},
					{
						type: 'tool',
						array: [{
								name: 'align',
								label: 'icon',
								items: [{
										value: 'left',
										icon: 'align-left'
									},
									{
										value: 'center',
										icon: 'align-center'
									},
									{
										value: 'right',
										icon: 'align-right'
									}
								]
							},
							{
								name: 'text',
								label: 'icon',
								items: [{
										name: 'list',
										value: 'ordered',
										icon: 'orderedlist'
									},
									{
										name: 'list',
										value: 'bullet',
										icon: 'unorderedlist'
									},
									{
										name: 'indent',
										icon: 'outdent',
										value: '+1'
									},
									{
										name: 'indent',
										icon: 'indent',
										value: '-1'
									}
								]
							},
							{
								name: 'lineHeight',
								items: [{
										value: 1
									},
									{
										value: 1.3
									},
									{
										value: 1.5
									},
									{
										value: 2
									},
									{
										value: 3
									}
								]
							}
						]
					}
				],
				isShop: 0,
				curLength: 0,
				swiperCurrent: 0,
				toolbarShow: false,
				toolBarContentShow: false,
				fixedTopHeight: 0, // 顶部工具栏高度
				toolBarHeight: 100, // 工具栏高度
				toolBarContentHeight: 530, // 工具栏内容高度
				progress: true //判断是否监听上传进度变化
			};
		},
		computed: {
			fullToolBarHeight() {
				let height = 0
				this.toolbarShow ? height += this.toolBarHeight : ''
				this.toolBarContentShow ? height += this.toolBarContentHeight : ''
				return uni.upx2px(height)
			},
			scrollHeight() {
				return this.scrollHeightDefault - this.fixedTopHeight - this.fullToolBarHeight;
			},
			scrollViewHeight() {
				let scrollViewHeight = (this.scrollHeight - this.toolBarHeight) - this.keyboardHeight;
				return this.keyboardHeight > 0 ? scrollViewHeight + this.iphoneXBottomH : scrollViewHeight;
			}

		},
		watch: {
			content(newText) {
				this.content = newText
				this.onEditorReady();
			},
			keyboardHeight(newVal, oldVal) {
				if (newVal > 0) {
					this.toolBarContentShow = false
				}

				// this.updatePosition(newVal)
			},
			toolbarShow(val) {
				if (!val) this.toolBarContentShow = val
				if (val) {
					this.inputFocus = true;
					this.$emit('editorFocus');
				} else {
					this.inputFocus = false;
					this.$emit('editorBlur');
				}
			}
		},
		created() {
			this.index = 0
			this.createdAt = Date.now()
			this.getUid = () => `wux-upload--${this.createdAt}-${++this.index}`
			this.uploadTask = {}
			this.tempFilePaths = []
		},
		mounted() {

			const query = wx.createSelectorQuery().in(this)
			query.select('#fixed-top').boundingClientRect(res => {
				this.fixedTopHeight = res.height
			}).exec()

			const system = uni.getSystemInfo({
				success: e => {
					this.isIos = e.platform == 'ios'
					let isIphoneX = (e.platform == 'devtools' || this.isIos) && e.safeArea.top == 44
					this.iphoneXBottomH = isIphoneX ? 34 : 0
					this.scrollHeightDefault = e.windowHeight - 34
				}
			})

			uni.onKeyboardHeightChange(res => {
				
				let keyboardHeight = this.keyboardHeight
				if (res.height === keyboardHeight) {
					return;
				}
				// #ifdef H5 || APP
				this.keyboardHeight = res.height;
				// #endif
				// #ifdef MP-WEIXIN
				if(this.isIos){
					this.keyboardHeight = res.height;
				}else{
					this.keyboardHeight = 0;
				}
				// #endif
				
				const duration = res.height > 0 ? res.duration * 1000 : 0
				keyboardHeight = res.height;
				
				setTimeout(() => {
					
					uni.pageScrollTo({
						scrollTop: 0,
						success: () => {
							// #ifdef H5 || APP
							this.updatePosition(keyboardHeight)
							// #endif
							// fuck，wx！
							// #ifdef MP-WEIXIN
							if(this.isIos){
								this.updatePosition(keyboardHeight)
							}else{
								this.updatePosition(0)
							}
							
							// #endif
							this.editorCtx.scrollIntoView() //使得编辑器光标处滚动到窗口可视区域内
						}
					})
				}, duration)
			})
		},
		beforeDestroy() {
			console.log("editor beforeDestroy")
			this.stopTimer()
		},
		methods: {
			getReplyBtShow() {
				this.hideToolbar();
				this.ReplyTextShow = true
			},

			getVipBtShow() {
				this.hideToolbar();
				this.VipTextShow = true
			},
			getUrlBtShow() {
				this.hideToolbar();
				this.UrlShow = true
			},
			getVideoBtShow() {
				this.hideToolbar();
				this.videoBtShow = true;
			},

			getPhotoBtShow() {
				this.hideToolbar();
				this.photoBtShow = true;
			},
			getUrlAudioShow() {
				this.hideToolbar();
				this.UrlAudioShow = true
			},
			getUrlFileShow() {
				this.hideToolbar();
				this.UrlFileShow = true
			},
			setShop() {
				this.hideToolbar();
				this.$emit('custom-event');
			},
			handleSwiperChange(event) {
				this.swiperCurrent = event.detail.current;
			},
			isActive(item, pitem) {
				let {
					name,
					value,
					format
				} = item
					!name ? name = pitem.name : ''
				if (format) {
					for (let name in format) {
						if (this.formats[name] !== format[name]) {
							return false
						}
					}
					return true
				} else {
					return value ? this.formats[name] === value : this.formats[name]
				}
			},

			hideKeyboard() {
				// uni.hideKeyboard() //uni-app提供了隐藏软键盘的api，但是没有生效
				this.editorCtx.blur()
			},
			changeSwiper(current) {
				this.toolBarContentShow = true
				this.swiperCurrent = current
				this.hideKeyboard()
			},
			updatePosition(keyboardHeight) {
				this.keyboardHeight = keyboardHeight
			},
			onEditorReady() {
				const that = this
				uni.createSelectorQuery()
					.in(this)
					.select('#editor')
					.context(function(res) {
						that.editorCtx = res.context
						that.setValue(that.content)
						// //设置默认格式
						// // that.editorCtx.format('header', '4')
						// that.editorCtx.format('fontSize', '14px')
						// that.editorCtx.format('align', 'left')
						// that.editorCtx.format('lineHeight', '1.3')
						//setContents设置内容后editor会自动聚焦，解决：先设置read_only为true,赋值后再把read_only属性设置为false
						that.readOnly = false
					})
					.exec()
			},
			onEditorInput(e) {
				let {
					html,
					text
				} = e.detail
				this.curLength = text.length - 1
			},
			onEditorFocus(e) {
				this.toolbarShow = true;

				if (this.isDefaultFormat) {
					// 设置默认格式
					this.editorCtx.format('fontSize', '14px');
					this.editorCtx.format('align', 'left');
					this.isDefaultFormat = false;
				}
				// 触发自定义聚焦事件
				// this.$emit('editorFocus');
			},
			onEditorBlur() {
				this.editorCtx.blur();
				this.updatePosition(0);


				// 触发自定义失焦事件
				// this.$emit('editorBlur');
			},
			changeKeyBoard() {
				this.toolBarContentShow = false
				this.hideKeyboard()
			},
			hideToolbar() {
				this.hideKeyboard()
				this.toolbarShow = false
			},
			// 修改默认样式
			formatDefault(format) {
				for (let name in format) {
					this.editorCtx.format(name, format[name])
				}
				if (format.bold) {
					this.editorCtx.format('bold', true)
				} else if (this.formats.bold) {
					this.editorCtx.format('bold', '')
				}
				this.editorCtx.format('lineHeight', '') //选择默认样式时，取消当前行高的选择
			},
			formatformat(bind, item = {}, pitem = {}) {
				item.name = item.name || pitem.name || ''
				let {
					name,
					value
				} = item
				switch (bind) {
					case 'format': //改变文本样式
						if (!name) return
						if (name == 'defaultFormat') { //选择标题样式时，取消当前字号的选择
							this.formatDefault(item.format)
						} else {
							this.editorCtx.format(name, value)
						}
						break
					case 'removeFormat': //删除字体样式
						this.editorCtx.removeFormat()
						break
					case 'insertDate': //插入时间
						var date = new Date()
						var formatDate = `${date.getFullYear()} 年${date.getMonth() + 1} 月${date.getDate()} 日`;
						this.editorCtx.insertText({
							text: formatDate
						})
						break
					case 'check': //设置当前行为待办列表格式
						this.editorCtx.format('list', 'check')
						break
					case 'undo': //撤销操作
						this.editorCtx.undo()
						break
					case 'redo': //恢复操作
						this.editorCtx.redo()
						break
					case 'insertDivider': //添加分割线
						this.editorCtx.insertDivider()
						break
					case 'clear': //清除内容
						this.editorCtx.clear()
						break
					case 'chooseImage': //插入相册图片
						this.open()

						break;
					case 'chooseImagebyCamera': //拍摄
						this.chooseImage(true)
						break
				}
			},
			onStatusChange(e) {
				this.formats = e.detail
				
				console.log(this.formats)
			},
			clickInsertDivider() {
				this.editorCtx.insertDivider()
			},
			clickUndo() {
				this.editorCtx.undo()
			},
			clickRedo() {
				this.editorCtx.redo()
			},
			clickChooseImage() {
				this.chooseImage()
			},
			clickChooseImagebyCamera() {
				this.chooseImage(true)
			},
			clickChooseVideo() {
				this.chooseVideo();
			},
			chooseVideo() {
				const success = res => {
					if (res.tempFilePath) {
						this.tempFilePaths = [{
							url: res.tempFilePath,
							size: res.size,
							type: res.tempFilePath.substring(res.tempFilePath.lastIndexOf('.') + 1),
							uid: this.getUid()
						}];
						this.$emit('before', res);
						this.verifyFile();
						this.$nextTick(() => {
							this.uploadFile(this.tempFilePaths.length);
						});
					} else {
						// uni.showToast({
						// 	title: '未能选择视频',
						// 	icon: 'none'
						// });
					}
				};

				setTimeout(() => {
					uni.chooseVideo({
						count: 1, // 默认只选择一个视频
						sourceType: this.sourceType,
						compressed: false, // 关闭压缩
						success,
						fail: () => {
							uni.showToast({
								title: '选择视频失败',
								icon: 'none'
							});
						}
					});
				}, 100);
			},
			insertUrl(src, text) {
				var srcpic = this.SPimg + 'urlpic.png'
				var width = '382px'
				var height = '95px'
				this.editorCtx.insertImage({
					src: srcpic,
					alt: `src=${src}|poster=${srcpic}|text=${text}|type=url`,
					width: width,
					height: height,
					extClass: 'editor--audio-poster',
					success: (res) => {
						this.editorCtx.insertText({
							text: '\n\n'
						})
						this.showLoading = false
					}
				});
			},
			insertFile(src, pw, name) {
				var filepic = this.SPimg + 'fileupload.png'
				var width = '382px'
				var height = '95px'
				this.editorCtx.insertImage({
					src: filepic,
					alt: `src=${src}|poster=${filepic}|pw=${pw}|name=${name}|type=file`,
					width: width,
					height: height,
					extClass: 'editor--audio-poster',
					success: (res) => {
						this.editorCtx.insertText({
							text: '\n\n'
						})
						this.showLoading = false
					}
				});
			},
			insertAudio(src, poster, name, author, musicpic, flag) {
				if (flag == 1) {
					var musicp = poster
					var width = '20%'
					var height = '100%'
				} else {
					var musicp = musicpic
					var width = '382px'
					var height = '95px'
				}
				this.editorCtx.insertImage({
					src: musicp,
					alt: `src=${src}|poster=${poster}|name=${name}|author=${author}|type=audio`,
					width: width,
					height: height,
					extClass: 'editor--audio-poster',
					data: {
						poster: poster,
						src: src,
					},
					success: (res) => {
						this.editorCtx.insertText({
							text: '\n\n'
						})
						this.showLoading = false
					}
				});
			},
			insertVideo(src, poster, file) {
				this.editorCtx.insertImage({
					src: poster,
					alt: `src=${src}|poster=${poster}|type=video`,
					width: '100%',
					extClass: 'editor--video-poster',
					data: {
						poster: poster,
						src: src,
					},
					success: (res) => {
						this.editorCtx.insertText({
							text: '\n\n'
						})
						this.showLoading = false
					}
				});
			},
			// 内容页视频解析
			// replaceEmoji(html) {
			// 	if (html) {
			// 		return html.replace(
			// 			/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|type=video"[^>]*?>/g, (match, src, poster) => {
			// 				return `<div style="border-radius:10px"><video src="${src}" poster="${poster}" object-fit width="100%" style="border-radius:10px" /></div>`
			// 			})
			// 	}

			// },
			getUrlImageShow() {
				this.photoBtShow = false
				this.UrlImageShow = true
			},

			getBatchUrlImageShow() {
				this.photoBtShow = false;
				this.batchUrlImageShow = true;
			},
			setReplyText() {
				if (this.ReplyText == '') {
					this.ReplyTextShow = false;
					uni.showToast({
						title: '回复可见内容不可为空',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				/* 直接插入回复可见内容 start */
				this.insertText("[hide]\n" + this.ReplyText + "\n[/hide]")
				this.insertText("\n\n")
				/* 直接插入回复可见内容 end */
				this.ReplyTextShow = false
				this.ReplyText = ''
			},
			setVipText() {
				if (this.vipText == '') {
					this.VipTextShow = false;
					uni.showToast({
						title: 'VIP可见内容不可为空',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				/* 直接插入VIP可见内容 start */
				this.insertText("[vip]\n" + this.vipText + "\n[/vip]")
				this.insertText("\n\n")
				/* 直接插入VIP可见内容 end */
				this.VipTextShow = false
				this.vipText = ''
			},
			setUrl() {
				if (this.aUrl == '') {
					this.UrlShow = false;
					uni.showToast({
						title: '请输入链接',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				if (this.aText == '') {
					this.aText = this.aUrl
				}
				/* 直接插入超链接 start */
				this.insertUrl(this.aUrl, this.aText)
				/* 直接插入超链接 end */
				this.UrlShow = false
				this.aUrl = ''
				this.aText = ''
			},
			setUrlImage() {
				if (this.imgurl == '') {
					this.UrlImageShow = false;
					uni.showToast({
						title: '请输入链接',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				/* 直接插入图片地址 start */
				this.insertImage(this.imgurl, this.imgurl)
				/* 直接插入图片地址 end */
				this.UrlImageShow = false
				this.imgurl = ''
			},
			setBatchUrlImages() {
				//批量外链
				if (this.imgurl == '') {
					this.batchUrlImageShow = false;
					uni.showToast({
						title: '请输入链接',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				const urls = this.batchImgUrls.split('\n');
				urls.forEach(url => {
					if (url.trim()) {
						this.insertImage(url.trim(), url.trim());
						this.insertText("\n")
						this.insertText("\n")
					}
				});
				this.batchUrlImageShow = false;
				this.batchImgUrls = '';
			},
			getUrlVideoShow() {
				this.videoBtShow = false
				this.UrlVideoShow = true
			},
			setUrlVideo() {
				if (this.videourl == '') {
					this.UrlVideoShow = false
					uni.showToast({
						title: '请输入链接',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				/* 直接插入图片地址 start */
				if (this.videoPoster) {
					this.insertVideo(this.videourl, this.videoPoster, this.videourl); // 有封面
				} else {
					this.insertVideo(this.videourl, this.mrPoster, this.videourl); // 无封面
				}
				/* 直接插入图片地址 end */
				this.UrlVideoShow = false
				this.videourl = ''
				this.videoPoster = ''
			},
			toMusicUpload() {
				this.goWeb(this.MusicUpload + '?token=' + this.formData.token + '&type=music', '上传音乐')
			},
			toPhotoUpload() {
				this.goWeb(this.MusicUpload + '?token=' + this.formData.token + '&type=photo', '上传图片')
			},
			toFileUpload() {
				this.goWeb(this.MusicUpload + '?token=' + this.formData.token + '&type=file', '上传附件')
			},

			goWeb(url, name) {
				var that = this;
				// #ifdef APP-PLUS || MP-WEIXIN
				uni.navigateTo({
					url: '/pages/contents/webview?url=' + url + '&name=' + name
				})
				// plus.runtime.openWeb(url);
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			setUrlFile() {
				if (this.fileurl == '') {
					this.UrlFileShow = false
					uni.showToast({
						title: '请输入附件链接',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				if (this.filename == '') {
					this.filename = '附件' + this.fileflag
				}
				/* 直接插入 start */
				var filepw = '无'
				if (this.filepw) {
					filepw = this.filepw
				}
				this.insertFile(this.fileurl, filepw, this.filename);
				/* 直接插入 end */
				this.UrlFileShow = false
				this.fileflag++
				this.filename = ''
				this.fileurl = ''
				this.filepw = ''
			},
			setUrlAudio() {
				if (this.audiourl == '') {
					this.UrlAudioShow = false
					uni.showToast({
						title: '请输入音乐链接',
						icon: 'none',
						position: 'bottom',
					});
					return false
				}
				/* 直接插入 start */
				var audioPosterUrl = ''
				var musicName = ''
				var musicAuthor = ''
				var flag = 0
				if (this.audioPosterUrl) {
					
					audioPosterUrl = this.audioPosterUrl
					flag = 1
				} else {
					audioPosterUrl = this.SPimg + 'musicposter.png'
					flag = 0
				}
				if (this.musicName) {
					musicName = this.musicName
				} else {
					musicName = '上传的音频'
				}
				if (this.musicAuthor) {
					musicAuthor = this.musicAuthor
				} else {
					musicAuthor = this.formData.username
				}
				this.insertAudio(this.audiourl, audioPosterUrl, musicName, musicAuthor, this.musicpic, flag);
				/* 直接插入 end */
				this.UrlAudioShow = false
				this.audiourl = ''
				this.musicName = ''
				this.musicAuthor = ''
				this.audioPosterUrl = ''
			},
			
			chooseImage(onlyCamera) {
				const success = res => {
					this.tempFilePaths = res.tempFiles.map(item => ({
						url: item.path,
						size: item.size,
						type: item.path.substring(item.path.lastIndexOf('.') + 1, item.path.length),
						uid: this.getUid()
					}))
					// 当前插入图片src地址直接使用临时路径，如果对接接口上传，更改为使用【上传文件】代码片段：

					/* 直接插入临时图片地址 start */

					// this.tempFilePaths.forEach(file => {
					// 	this.insertImage(file.url, file)
					// })

					/* 直接插入临时图片地址 end */


					/* 上传文件 start */

					this.$emit('before', res)
					this.verifyFile()
					this.$nextTick(() => {
						this.uploadFile(this.tempFilePaths.length)
					})

					/* 上传文件 end */
				}

				const {
					count,
					sizeType
				} = this
				setTimeout(() => {
					uni.chooseImage({
						count,
						sizeType,
						sourceType: this.sourceType,
						success
					})
				}, 100)
			},
			insertText(Text) {
				var that = this
				that.editorCtx.insertText({
					text: Text,
					success(e) {
						//真机会自动插入一行空格
					}
				})
			},
			insertImage(src, file) {
				var that = this
				that.editorCtx.insertImage({
					src,
					data: {
						id: file.uid
					},
					// extClass:'editor-img',
					extClass: 'editor--editor-img', //添加到图片 img标签上的类名为editor-img，设置前缀editor--才生效。部分机型点击图片右边的光标时不灵敏，需将样式editor-img宽度调小 max-width:98%;从而在图片右侧中留出部分位置供用户点击聚集。
					success(e) {
						//真机会自动插入一行空格
					}
				})
			},
			/**
			 * 上传文件，支持多图递归上传
			 */
			uploadFile(uploadCount, curIndex) {
				if (!this.tempFilePaths.length) return;
				const {
					url,
					name,
					header,
					formData,
					progress
				} = this;
				const file = this.tempFilePaths.shift();
				curIndex ? (file.index = curIndex + 1) : (file.index = 1);
				let {
					uid,
					url: filePath
				} = file;
				if (!url || !filePath) return;

				this.uploadTask[uid] = uni.uploadFile({
					url,
					filePath,
					name,
					header,
					formData,
					timeout: this.uploadTime*1000,
					success: res => this.onSuccess(file, res),
					fail: res => this.onFail(file, res),
					complete: res => {
						console.log("complete");
						clearTimeout(this.timeoutId);
						this.timeoutId = null
						this.timeoutId = setTimeout(() => {
							this.handleTimeout();
						}, this.uploadTime*1000); // 上传超时
						delete this.uploadTask[uid];
						this.$emit('complete', res);
						if (!this.tempFilePaths.length) return;
						this.verifyFile();
						this.uploadFile(uploadCount, file.index);
					}
				});

				if (progress) {
					this.photoBtShow = false
					this.videoBtShow = false
					this.uploadShow = true
					this.uploadTask[uid].onProgressUpdate(res => this.onProgress(file, res, uploadCount));
				}
			},
			/**校验图片格式和大小是否符合规则 */
			verifyFile() {
				var {
					size: tempFilesSize,
					type
				} = this.tempFilePaths[0] //获取图片的大小，单位B
				this.noAllowType.map(item => {
					if (type == item) {
						uni.showToast({
							title: `不支持上传${item}图片`,
							icon: 'none'
						})
						this.tempFilePaths.shift()
					}
				})

			},
			onSuccess(file, res) {
				let json = JSON.parse(res.data);
				if (json.code == 1) {
					clearTimeout(this.timeoutId); // 清除超时计时器
					this.timeoutId = null
					const hasPoster = json.data.hasOwnProperty('poster');
					if (hasPoster) {
						// 视频类型
						//console.log(json.data.url + '|' + json.data.poster);
						this.insertVideo(json.data.url, json.data.poster, file);
					} else {
						// 图片类型
						let url = json.data.url;
						let videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.flv', '.wmv', '.webm'];
						let isVideo = videoExtensions.some(ext => url.endsWith(ext));

						if (isVideo) {
							//console.log("mrPoster" + this.mrPoster);
							this.insertVideo(url, this.mrPoster, file); // 无封面
							
						} else {
							this.insertImage(url, file);
							this.editorCtx.insertText({
								text: '\n\n'
							})
						}
					}
					this.uploadUrl = json.data.url;
				} else {
					clearTimeout(this.timeoutId);
					this.timeoutId = null
					this.uploadUrl = 'error';
					uni.showToast({
						title: '文件上传失败',
						icon: 'none'
					});
				}
			},
			handleTimeout() {
				clearTimeout(this.timeoutId);
				this.timeoutId = null
				this.uploadUrl = 'timeout';
				uni.showToast({
					title: '请求超时',
					icon: 'none'
				});
			},
			onFail(file, res) {
				clearTimeout(this.timeoutId);
				this.timeoutId = null
				this.uploadUrl = 'error';
				uni.showToast({
					title: '图片上传失败！',
					icon: 'none'
				})
				this.uploadUrl = '';
			},
			getUploadMsg() {
				this.stopTimer();
				this.intervalId = setInterval(() => {
					//console.log("外"+this.uploadUrl);
					if (this.uploadUrl == 'error') {
						this.stopTimer();
						this.progressNum = 100
						this.uploadMessage = "上传失败"
						this.uploadShow = false
						this.progressNum = 0
						this.uploadMessage = "正在上传中..."
						this.uploadUrl = ''
						//console.log("内"+this.uploadUrl);
						uni.showToast({
							title: '文件上传失败',
							icon: 'none'
						});
						clearTimeout(this.timeoutId);
						this.timeoutId = null
					} else if (this.uploadUrl == 'timeout') {
						this.stopTimer();
						this.progressNum = 100
						this.uploadMessage = "上传失败"
						this.uploadShow = false
						this.progressNum = 0
						this.uploadMessage = "正在上传中..."
						this.uploadUrl = ''
						uni.showToast({
							title: '文件上传超时',
							icon: 'none'
						});
						clearTimeout(this.timeoutId);
						this.timeoutId = null
					} else if (this.uploadUrl != '') {
						this.stopTimer();
						this.progressNum = 100
						this.uploadMessage = "上传成功"
						this.uploadShow = false
						this.progressNum = 0
						this.uploadMessage = "正在上传中..."
						this.uploadUrl = ''
						uni.showToast({
							title: '上传成功',
							icon: 'none'
						});
						clearTimeout(this.timeoutId);
						this.timeoutId = null
					} else {
						this.uploadMessage = "正在处理中..."
						this.progressNum = 99
					}
				}, 100);
			},
			stopTimer() {
				if (this.intervalId) {
					clearInterval(this.intervalId);
					this.intervalId = null;
				}
			},
			/**
			 * 监听上传进度变化的回调函数
			 * @param {Object} file 文件对象
			 * @param {Object} res 请求响应对象
			 * @param {Number} uploadCount 选择图片总数量
			 */
			onProgress(file, res, uploadCount) {
				//console.log(this.progressNum);
				if (res.progress == 100) {
					this.getUploadMsg()
				} else {
					this.progressNum = res.progress

				}

				const targetItem = {
					...file,
					progress: res.progress,
					res
				}
				const info = {
					file: targetItem
				}

				this.$emit('progress', info)
			},
			bytesToSize: function(bytes) {
				if (bytes === 0) return '0 B'
				var k = 1024,
					sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
					i = Math.floor(Math.log(bytes) / Math.log(k))

				return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
			},
			setValue(value) {
				if (this.editorCtx) {
					this.editorCtx.setContents({
						html: value,
						success: () => {
							this.getContents(res => {
								this.onEditorInput({
									detail: {
										html: res.html,
										text: res.text
									}
								})
								this.$emit('update', res)
							})
						}
					})
				}
			},
			getContents(callback) {
				//由于获取编辑器内容getContents为异步，因此需要使用callback回调
				this.editorCtx.getContents({
					success: res => {
						callback(res)
					}
				})
			},
			save() {
				this.editorCtx.getContents({
					success: res => {
						//console.log(res)
						res.html = handleHtmlImage(res.html, true)
						//console.log('html:'+res.html)
						this.$emit('save', res)
					},
					complete: res => {
						console.log('getContents complete')
					}
				})
			}
		}

	}
</script>

<style lang="scss" scoped>
	@import 'iconfont.scss';

	$bg-color: #f7f7f7;
	$bg-color-hover: #eaeaea;
	$main-color: #5b8ff9;

	.fixed-top {
		position: fixed;
		top: -88rpx;
		line-height: 88rpx;
		border-bottom: solid 1rpx #F1F1F1;
		padding: 0 30rpx;
		box-sizing: border-box;
		transition: all 0.3s ease;
		background-color: #FFFFFF;
		z-index: 999;

		&.isFixed {
			top: 0;
		}

		.btn {
			width: 100%;
			position: relative;
			border: 0 !important;
			border-radius: 6rpx;
			padding-left: 0;
			padding-right: 0;
			overflow: visible;
			float: right;
			width: 100rpx;
			height: 60rpx;
			line-height: 60rpx;
			font-size: 24rpx;
			margin: 14rpx 0;
			text-align: center;
		}

		.btn-primary {
			background: #5677fc !important;
			color: #fff;
		}

		.btn-primary:hover {
			opacity: 0.8;
		}
	}

	.fixed-top,
	.fixed-top__place {
		width: 100%;
		height: 88rpx;
	}

	.flex {
		display: flex;
	}

	.editor-container {
	}

	.cu-editor {
		box-sizing: border-box;
		width: 100%;
		height: 100%;
		font-size: 28rpx;
		line-height: 1.5;
		overflow: auto;
		padding: 35rpx 0rpx;
		min-height: unset !important;
	}

	.editor-img {
		max-width: 100% !important;
	}

	.ql-container ::v-deep img {
		border-radius: 20rpx;
		display: block;
	}

	.video-poster {
		position: relative;
		object-fit: cover;
		border-radius: 20rpx;
		width: 100%;
		height: 200px;
	}

	.audio-poster {
		position: relative;
		object-fit: cover;
		border-radius: 20rpx;
		width: 100%;
		height: 50px;
	}

	.ql-active {
		background-color: $bg-color-hover;

		.color-circle {
			border: solid 1px;
		}
	}

	.noBgColor {
		background-color: none !important;
	}

	.fixed-bottom {
		position: fixed;
		left: 0;
		width: 100%;
		right: 100%;
		bottom: 0;
		z-index: 99999;
	}

	.toolbar {
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border: 1rpx solid #e5e5e5;
		border-left: none;
		border-right: none;
		background: #fff;

		.iconfont {
			display: inline-block;
			cursor: pointer;
			font-size: 40rpx;
			text-align: center;
			position: relative;

			&.active::after {
				content: '';
				left: 0;
				bottom: 0;
				width: 100%;
				position: absolute;
				height: 6rpx;
				border-bottom: solid 6rpx #000000;
			}
		}

		.toolbar-item {
			height: 100rpx;
			line-height: 100rpx;
			flex: 1;
			text-align: center;

			&:active {
				opacity: 0.4;
			}
		}

		.toolbar-item-header,
		.toolbar-item-footer {
			width: 108rpx;
			text-align: center;
		}

		.toolbar-item-header {
			border-right: solid 1rpx $uni-border-color;
		}

		.toolbar-item-footer {
			border-left: solid 1rpx $uni-border-color;
			color: #5b8ff9;
			font-weight: bold;
		}
	}

	.toolbar-content {
		background-color: #ffffff;
	}

	.swiper-item {
		box-sizing: border-box;
		padding: 0 30rpx;
	}

	.tool-items {
		background-color: $bg-color;
		color: #323232;
		height: 80rpx;
		line-height: 80rpx;
		margin: 32rpx 0;
		border-radius: 16rpx;
		overflow: hidden;

		.tool-item {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;

			&:active {
				background-color: $bg-color-hover;
			}

		}

		.iconfont {
			display: inline-block;
			width: 80rpx;
			height: 80rpx;
			line-height: 80rpx;
			cursor: pointer;
			font-size: 40rpx;
			text-align: center;
		}

		.color-circle {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
		}

		.txt {
			font-size: 14px;
		}
	}

	.feature {
		margin: 30rpx;
		text-align: center;

		.icon {
			background-color: $bg-color;
			width: 120rpx;
			height: 120rpx;
			line-height: 120rpx;
			color: #323232;
			margin-bottom: 10rpx;

			&:active {
				background-color: $bg-color-hover;
			}

			.iconfont {
				font-size: 42rpx;
			}
		}
	}
</style>
