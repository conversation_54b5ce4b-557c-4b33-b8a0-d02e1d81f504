# medalItem.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/components/medalItem.vue.md`
- **组件说明**：此组件用于展示用户佩戴的勋章列表，可能作为用户头像或昵称旁的附加信息显示。

---

<template>
	<view class="medal-display" v-if="isPluginEnabled && hasMedals">
		<image v-for="medal in wearingMedals" 
			:key="medal.id"
			class="medal-icon" 
			:src="medal.icon_url"
			mode="aspectFit"
			:title="medal.name">
		</image>
	</view>
</template>

<script>
	export default {
		name: "medalItem",
		props: {
			uid: {
				type: [Number, String],
				required: false,
				default: ''
			}
		},
		data() {
			return {
				medals: [],
				wearingMedals: [],
				isPluginEnabled: false,
				hasMedals: false
			}
		},
		created() {
			try {
				const cachedPlugins = uni.getStorageSync('getPlugins')
				if (cachedPlugins) {
					const pluginList = JSON.parse(cachedPlugins)
					this.isPluginEnabled = pluginList.includes('xqy_medal')
					//console.log('勋章插件状态:', this.isPluginEnabled)
				}
			} catch (error) {
				//console.error('检查插件状态失败:', error)
				this.isPluginEnabled = false
			}
			if (this.uid) {
				this.loadUserMedals()
			}
		},
		watch: {
			uid: {
				handler(newVal) {
					if (newVal && this.isPluginEnabled) {
						this.loadUserMedals()
					}
				},
				immediate: true
			}
		},
		methods: {
			async loadUserMedals() {
				try {
					if (!this.isPluginEnabled || !this.uid) return;
					
					const res = await new Promise((resolve, reject) => {
						uni.request({
							url: this.$API.PluginLoad('xqy_medal'),
							data: {
								action: 'getMedals',
								plugin: 'xqy_medal',
								type: 'user',
								uid: this.uid
							},
							method: 'GET',
							success: (res) => resolve(res),
							fail: (err) => reject(err)
						})
					});

					if (res.data.code === 200) {
						this.medals = res.data.data.medals || [];
						// 只显示正在佩戴的勋章
						this.wearingMedals = this.medals;
						this.hasMedals = this.wearingMedals.length > 0;
						this.$emit('medal-loaded', this.hasMedals ? this.wearingMedals : null);
					}
				} catch (error) {
					console.error('加载用户勋章失败:', error);
					this.hasMedals = false;
					this.$emit('medal-loaded', null);
				}
			}
		}
	}
</script>

<style>
	.medal-display {
		display: inline-flex;
		align-items: center;
		text-align: left;
		gap: 0;
		flex-wrap: nowrap;
		overflow-x: auto;
		margin-top: 4rpx;
		padding: 2rpx 0;
	}

	.medal-icon {
		height: 35rpx;
		width: auto;
		min-width: 55rpx;
		max-width: 110rpx;
		flex-shrink: 0;
		object-fit: contain;
		margin-left: 2rpx;
		vertical-align: middle;
		transform: translateY(2rpx);
	}
</style> 