<template>
	<view class="page-container" :class="[AppStyle, isDark?'dark':'']" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<!-- 自定义导航栏 -->
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :class="isDark?'bg-black':'bg-white'" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action">
					<text class="cuIcon-back" @tap="back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					短视频
				</view>
				<view class="action">
					<text class="cuIcon-cameraadd text-primary" @tap="goUpload"></text>
				</view>
			</view>
		</view>
		
		<!-- 统一滚动容器 -->
		<scroll-view
			class="main-scroll"
			scroll-y
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="handleRefresh"
			@scrolltolower="loadMore"
			:style="{'padding-top': CustomBar + 'px'}"
		>
			<!-- 搜索区域 -->
			<view class="search-wrapper" :class="isDark ? 'bg-black' : 'bg-white'">
				<view class="search-bar" @tap="focusSearch" :style="{'background-color': isDark ? 'rgba(255, 255, 255, 0.1)' : '#f5f5f5'}">
					<text class="search-icon cuIcon-search" :style="{'color': isDark ? 'rgba(255, 255, 255, 0.6)' : '#999'}"></text>
					<input 
						type="text" 
						placeholder="搜索视频" 
						v-model="searchKeyword"
						@confirm="searchVideos"
						confirm-type="search"
						:style="{'color': isDark ? '#fff' : '#333'}"
					/>
				</view>
			</view>

			<!-- 分区筛选 -->
			<view class="category-wrapper" :class="isDark ? 'bg-black' : 'bg-white'">
				<scroll-view class="category-scroll" scroll-x show-scrollbar="false">
					<view class="category-list">
						<view 
						class="category-item" 
						:class="{active: currentCategory === 'all'}"
						@tap="selectCategory('all')"
					>
						<text class="category-text">最新</text>
					</view>
					<view 
						class="category-item" 
						:class="{active: currentCategory === 'recommend'}"
						@tap="selectCategory('recommend')"
						v-if="showRecommend"
					>
						<text class="category-text">推荐</text>
					</view>
					<view 
						class="category-item" 
						:class="{active: currentCategory === 'hot'}"
						@tap="selectCategory('hot')"
					>
						<text class="category-text">热门</text>
					</view>
					<view 
						class="category-item" 
						v-for="category in categoryList" 
						:key="category.id"
						:class="{active: currentCategory === category.id}"
						@tap="selectCategory(category.id)"
					>
						<text class="category-icon" :class="category.icon"></text>
						<text class="category-text">{{category.name}}</text>
					</view>
					</view>
				</scroll-view>
			</view>

			<!-- 视频内容区域 -->
			<view class="video-grid">
				<view
					class="video-card"
					v-for="(item, index) in videoList"
					:key="item.id"
					@tap="goDetail(item.id)"
				>
					<view class="video-cover">
						<image :src="item.cover_url" mode="aspectFill"></image>
						<view class="video-duration">{{item.duration_text || formatDuration(item.duration)}}</view>
						<view class="play-count">
							<view class="play-icon">
								<text class="cuIcon-video"></text>
							</view>
							<text class="count-text">{{formatNumber(item.view_count)}}</text>
						</view>
					</view>
					<view class="video-info">
						<view class="video-title">{{item.title}}</view>
						<view class="author-info">
							<image class="author-avatar" :src="item.author_avatar" mode="aspectFill"></image>
							<text class="author-name">{{item.author_name}}</text>
							<view class="stats">
								<text class="like-count">
									<text class="cuIcon-appreciatefill"></text>
									{{formatNumber(item.like_count)}}
								</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading-more" v-if="loading">
				<view class="loading-spinner"></view>
				<text>加载中...</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { localStorage } from '../../../js_sdk/mp-storage/mp-storage/index.js'
import darkModeMixin from '@/utils/darkModeMixin.js'

export default {
	mixins: [darkModeMixin],
	data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				token: '',
				isLogin: false,
				loading: true,
				videoList: [],
				page: 1,
				limit: 12,
				hasMore: true,
				submitStatus: false,
				isRefreshing: false,
				refreshTimer: null,
				searchKeyword: '',
				categoryList: [],
				currentCategory: 'all',
				showRecommend: false // 是否显示推荐分区，默认隐藏，等待配置加载
			}
		},
	onLoad() {
		// 获取token
		this.token = localStorage.getItem('token') || '';
		this.isLogin = !!this.token;

		// 获取视频设置
		this.getVideoSettings();

		// 加载分区列表
		this.getCategoryList();

		// 加载视频列表
		this.getVideoList();
	},
	onPullDownRefresh() {
		this.handleRefresh();
	},
	onReachBottom() {
		// 上拉加载更多
		if (this.hasMore && !this.loading) {
			this.loadMore();
		}
	},
	methods: {
		// 返回上一页
		back() {
			uni.navigateBack({
				delta: 1
			});
		},

		// 获取视频列表
		getVideoList() {
			return new Promise((resolve, reject) => {
				if (this.submitStatus) {
					resolve();
					return;
				}

				if (!this.hasMore && this.page > 1) {
					resolve();
					return;
				}

				const that = this;
				that.loading = true;
				that.submitStatus = true;

				uni.request({
				url: that.$API.PluginLoad('xqy_video'),
				data: {
					action: 'getVideoList',
					plugin: 'xqy_video',
					page: that.page,
					limit: that.limit,
					status: 1,
					token: that.token,
					keyword: that.searchKeyword,
					category_id: (that.currentCategory === 'all' || that.currentCategory === 'recommend' || that.currentCategory === 'hot') ? '' : that.currentCategory,
					is_recommend: that.currentCategory === 'recommend' ? 1 : '',
					order_by: that.currentCategory === 'hot' ? 'view_count' : 'created_at',
					order_type: that.currentCategory === 'hot' ? 'DESC' : 'DESC'
				},
					method: 'GET',
					success: function(res) {
						that.loading = false;
						that.submitStatus = false;

						if (res.data.code === 200) {
							const videos = res.data.data.videos || [];

							if (that.page === 1) {
								that.videoList = videos;
							} else {
								that.videoList = [...that.videoList, ...videos];
							}

							that.hasMore = videos.length >= that.limit;
							resolve();
						} else {
							uni.showToast({
								title: res.data.msg || '获取视频列表失败',
								icon: 'none'
							});
							reject(new Error(res.data.msg));
						}
					},
					fail: function(err) {
						that.loading = false;
						that.submitStatus = false;

						uni.showToast({
							title: '网络错误，请稍后重试',
							icon: 'none'
						});
						reject(err);
					},
					complete: function() {
						// 确保在完成时重置刷新状态
						that.isRefreshing = false;
						uni.stopPullDownRefresh();
					}
				});
			});
		},

		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return;
			this.page++;
			this.getVideoList();
		},

		// 跳转到视频详情页
		goDetail(id) {
			uni.navigateTo({
				url: `/pages/plugins/xqy_video/detail?id=${id}`
			});
		},

		// 跳转到视频上传页
		goUpload() {
			if (!this.isLogin) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				return;
			}

			uni.navigateTo({
				url: '/pages/plugins/xqy_video/upload'
			});
		},

		// 格式化数字
		formatNumber(num) {
			if (!num) return '0';
			if (num < 1000) return num.toString();
			if (num < 10000) return (num / 1000).toFixed(1) + 'K';
			return (num / 10000).toFixed(1) + 'W';
		},

		// 格式化时长
		formatDuration(seconds) {
			if (!seconds) return '00:00';
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = Math.floor(seconds % 60);
			return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
		},

		// 添加搜索视频方法
		searchVideos(e) {
			const keyword = e.detail.value.trim();
			if (!keyword) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}

			this.searchKeyword = keyword;
			this.page = 1;
			this.videoList = [];
			this.hasMore = true;
			this.getVideoList();
		},

		// 添加新的刷新处理方法
		async handleRefresh() {
			if (this.isRefreshing) return;

			this.isRefreshing = true;
			this.page = 1;
			this.videoList = [];
			this.hasMore = true;

			try {
				await this.getVideoList();
			} catch (error) {
				// 刷新失败处理
			} finally {
				// 延迟结束刷新状态，提供更好的视觉反馈
				setTimeout(() => {
					this.isRefreshing = false;
					uni.stopPullDownRefresh();
				}, 500);
			}
		},
		
		// 聚焦搜索框
		focusSearch() {
			// 点击搜索框时的处理逻辑
			// 这里可以添加额外的搜索相关功能
		},

		// 获取分区列表
		getCategoryList() {
			const that = this;
			uni.request({
				url: that.$API.PluginLoad('xqy_video'),
				data: {
					action: 'getCategoryList',
					plugin: 'xqy_video',
					type: 'all',
					limit: 50
				},
				method: 'GET',
				success: function(res) {
					if (res.data && res.data.code === 200) {
						that.categoryList = res.data.data || [];
					}
				},
				fail: function(err) {
					// 网络错误处理
				}
			});
		},

		// 获取视频设置
		getVideoSettings() {
			const that = this;
			const requestData = {
				action: 'getVideoSettings',
				plugin: 'xqy_video',
				token: that.token
			};
			uni.request({
				url: that.$API.PluginLoad('xqy_video'),
				data: requestData,
				method: 'POST',
				header: {
					'content-type': 'application/x-www-form-urlencoded'
				},
				success: function(res) {
					if (res.data.code === 200) {
						const settings = res.data.data;
						// 兼容字符串和数字类型，确保正确判断
						that.showRecommend = settings.show_recommend == 1 || settings.show_recommend === '1';
					}
				},
				fail: function(err) {
					// 默认隐藏推荐分区，避免配置获取失败时错误显示
					that.showRecommend = false;
				}
			});
		},

		// 选择分区
		selectCategory(categoryId) {
			if (this.currentCategory === categoryId) return;
			
			this.currentCategory = categoryId;
			this.page = 1;
			this.videoList = [];
			this.hasMore = true;
			this.getVideoList();
		}
	},
	mounted() {
		// 确保样式正确应用
		this.$nextTick(() => {
			// 强制更新视图
			this.$forceUpdate();
		});
	},
	beforeDestroy() {
		if (this.refreshTimer) {
			clearTimeout(this.refreshTimer);
		}
	}
}
</script>

<style lang="scss">
.page-container {
	min-height: 100vh;
	background: #f8f8f8;
	position: relative;
}

/* 导航栏样式 */
.header {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 100;
	
	.cu-bar {
		.content {
			font-size: 32rpx;
		}
		
		.action {
			.cuIcon-back, .cuIcon-cameraadd {
				font-size: 36rpx;
			}
		}
	}
}

	/* 搜索区域 */
	.search-wrapper {
		padding: 20rpx 30rpx 10rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);

		.search-bar {
			height: 72rpx;
			background: #f5f5f5;
			border-radius: 36rpx;
			display: flex;
			align-items: center;
			padding: 0 24rpx;
			transition: all 0.3s ease;

			&:active {
				background: #ebebeb;
			}

			.search-icon {
				font-size: 32rpx;
				color: #999;
				margin-right: 16rpx;
			}

			input {
				flex: 1;
				height: 72rpx;
				color: #333;
				font-size: 30rpx;

				&::placeholder {
					color: #999;
				}
			}
		}
	}

	/* 分区筛选区域 */
	.category-wrapper {
		padding: 10rpx 0 20rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.category-scroll {
			white-space: nowrap;

			.category-list {
				display: flex;
				padding: 0 30rpx;
				gap: 20rpx;

				.category-item {
					display: flex;
					align-items: center;
					padding: 12rpx 24rpx;
					border-radius: 30rpx;
					background: #f8f8f8;
					transition: all 0.3s ease;
					flex-shrink: 0;
					white-space: nowrap;

					&.active {
						background: #FF6B6B;
						color: #fff;

						.category-text {
							color: #fff;
						}

						.category-icon {
							color: #fff !important;
						}
					}

					&:active {
						transform: scale(0.95);
					}

					.category-icon {
						font-size: 28rpx;
						margin-right: 8rpx;
					}

					.category-text {
						font-size: 28rpx;
						color: #333;
						font-weight: 500;
					}
				}
			}
		}
	}

/* 主滚动区域 */
.main-scroll {
	flex: 1;
	height: 100vh;
	background: #f8f8f8;

	&::-webkit-scrollbar {
		display: none;
	}
}

/* 暗黑模式适配 */
.dark {
	.page-container {
		background: #000;
	}

	.header {
		.cu-bar {
			.content {
				color: #fff;
			}
			
			.action {
				.cuIcon-back, .cuIcon-cameraadd {
					color: #fff;
				}
			}
		}
	}
	
	.search-wrapper {
		background: #1c1c1e;
		box-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.05);
		
		.search-bar {
			background: rgba(255, 255, 255, 0.1);

			&:active {
				background: rgba(255, 255, 255, 0.15);
			}

			input {
				color: #fff;

				&::placeholder {
					color: rgba(255, 255, 255, 0.6);
				}
			}

			.search-icon {
				color: rgba(255, 255, 255, 0.6);
			}
		}
	}

	.category-wrapper {
		background: #1c1c1e;
		border-bottom: 1rpx solid #333;

		.category-list {
			.category-item {
				background: rgba(255, 255, 255, 0.1);

				.category-text {
					color: #fff;
				}

				&.active {
					background: #FF6B6B;

					.category-text {
						color: #fff;
					}

					.category-icon {
						color: #fff !important;
					}
				}
			}
		}
	}
	
	.main-scroll {
		background: #1c1c1c;
	}
}

/* iOS风格动画 */
@keyframes scale-up {
	from {
		transform: scale(0.95);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

/* 视频网格样式 */
.video-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 10px;
	padding: 10px;

	.video-card {
		background: #fff;
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		transform: translateY(0);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2px);
		}
		
		.dark & {
			background: #2c2c2c;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
		}

		.video-cover {
			position: relative;
			padding-top: 56.25%; // 16:9 比例

			image {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.video-duration {
				position: absolute;
				bottom: 8px;
				right: 8px;
				background: rgba(0, 0, 0, 0.6);
				color: #fff;
				padding: 2px 6px;
				border-radius: 4px;
				font-size: 12px;
			}

			.play-count {
				position: absolute;
				bottom: 8px;
				left: 8px;
				color: #fff;
				font-size: 12px;
				display: flex;
				align-items: center;
				background: rgba(0, 0, 0, 0.5);
				padding: 2px 6px;
				border-radius: 12px;

				.play-icon {
					display: flex;
					align-items: center;
					margin-right: 4px;

					.cuIcon-video {
						font-size: 14px;
						color: #fff;
					}
				}

				.count-text {
					font-size: 12px;
					color: #fff;
					font-weight: 500;
				}
			}
		}

		.video-info {
			padding: 10px;

			.video-title {
				font-size: 14px;
				font-weight: 500;
				line-height: 1.3;
				margin-bottom: 8px;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				
				.dark & {
					color: #ddd;
				}
			}

			.author-info {
				display: flex;
				align-items: center;

				.author-avatar {
					width: 24px;
					height: 24px;
					border-radius: 50%;
					margin-right: 6px;
				}

				.author-name {
					flex: 1;
					font-size: 12px;
					color: #666;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					
					.dark & {
						color: #aaa;
					}
				}

				.stats {
					font-size: 12px;
					color: #999;
					
					.dark & {
						color: #888;
					}

					.like-count {
						display: flex;
						align-items: center;

						.cuIcon-appreciatefill {
							margin-right: 2px;
							font-size: 14px;
						}
					}
				}
			}
		}
	}
}

/* 加载更多样式 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16px 0;
	opacity: 0;
	transition: opacity 0.3s ease;

	&.visible {
		opacity: 1;
	}

	.loading-spinner {
		width: 18px;
		height: 18px;
		border: 2px solid #f3f3f3;
		border-top: 2px solid #FF6B6B;
		border-radius: 50%;
		animation: spin 0.8s linear infinite;
	}
}
</style>

