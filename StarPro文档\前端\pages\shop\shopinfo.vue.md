<template>
	<view class="template-product tn-safe-area-inset-bottom bg-white">
		<!-- 顶部自定义导航 -->
		<view class="header" :style="[{height:CustomBar + 'px'}]" style="z-index: 1300;">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					商品详情
				</view>
				<view class="action">
					<!--  #ifdef H5 || APP-PLUS -->

					<text class="cuIcon-moreandroid" @click="popup()"></text>
					<!--  #endif -->
				</view>
			</view>
		</view>
		<tn-popup v-model="show" mode="bottom" :zIndex="1200" :closeBtn="true" height="20%" :borderRadius="20">
			<view class="center-container tn-margin-top-xxl">
				<view class="">
					<block v-if="myuid==userInfo.uid||mygroupKeyz=='administrator'||mygroupKeyz=='editor'">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="editShop(shopinfo)">
							<text class="tn-icon-edit" style="margin-right: 5px;"></text>编辑商品
						</view>
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="deleteShop(sid)">
							<text class="tn-icon-delete" style="margin-right: 5px;"></text>删除商品
						</view>
					</block>
					<block v-else>
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="getPrivateChat(userJson)">
							<text class="tn-icon-service" style="margin-right: 5px;"></text>联系卖家
						</view>
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" @tap="toJb(title)">
							<text class="tn-icon-warning" style="margin-right: 5px;"></text>举报商品
						</view>
					</block>
				</view>
			</view>
		</tn-popup>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>

		<view class="shopinfo-img">
			<image :src="imgurl" mode="widthFix" @tap="previewImage(imgurl)"></image>
		</view>
		<view class="tn-margin bg-white">
			<view class="tn-flex tn-flex-row-between">
				<view class="justify-content-item tn-text-bold tn-text-xxl">
					{{title}}
				</view>
				<!-- <view class="shopinfo-tips" v-if="integral>0">
			<text class="text-green">该商品支持{{integral}}积分抵扣</text>
		</view> -->
			</view>
			<view class="tn-flex tn-flex-row-between tn-margin-top">
				<view class="justify-content-item tn-text-bold tn-color-purplered">
					<text class="" style="font-size: 50rpx;">{{price}}</text>
					<text class="tn-text-sm">{{currencyName}}</text>
				</view>
				<view class="justify-content-item tn-color-gray tn-padding-top-xs">
					<view class="">已售 {{sellNum}}</view>
				</view>
			</view>
		</view>

		<!-- 边距间隔 -->
		<view class="tn-strip-bottom"></view>


		<!-- 更多信息-->
		<tn-list-cell :hover="true" :unlined="true" :radius="true" :fontSize="34">
			<view class="tn-flex tn-flex-col-center">
				<view class="tn-flex-1">价格</view>
				<view class="tn-margin-left-sm" style="font-size: 28rpx;">
					<view class="action">
					<text v-if="isvip==1">
					<text class="tn-margin-right-xl" style="text-decoration-line: line-through;color: #666;">原价 {{shopinfo.price}}{{currencyName}}</text>
					<text class="tn-margin-right-sm" style="color: #666;">VIP特权</text>
					</text>
						<text class="text-red text-bold text-lg margin-right-xs">
							<block v-if="isvip==1">
								{{parseInt(shopinfo.price * shopinfo.vipDiscount)}}
							</block>
							<block v-else>
							{{shopinfo.price}}
							</block>
						</text>
						{{currencyName}}
						
					</view>
					
				</view>
			</view>
		</tn-list-cell>
		<tn-list-cell :hover="true" :unlined="true" :radius="true" :fontSize="34">
			<view class="tn-flex tn-flex-col-center">
				<view class="tn-flex-1">销量</view>
				<view class="tn-margin-left-sm" style="font-size: 28rpx;">{{sellNum}}</view>
			</view>
		</tn-list-cell>
		<tn-list-cell :hover="true" :unlined="true" :radius="true" :fontSize="34">
			<view class="tn-flex tn-flex-col-center">
				<view class="tn-flex-1">库存</view>
				<view class="tn-margin-left-sm" style="font-size: 28rpx;">{{num}}</view>
			</view>
		</tn-list-cell>

		<!--      <tn-list-cell :hover="true" :unlined="true" :radius="true" :fontSize="34">
        <view class="tn-flex tn-flex-col-center">
          <view class="tn-flex-1">活动时长</view>
          <view class="tn-margin-left-sm" style="font-size: 28rpx;">90天</view>
        </view>
      </tn-list-cell> -->
		<!-- 边距间隔 -->

		<!-- 图标logo/头像 -->
		<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-margin bg-white">
			<view class="justify-content-item" @click="toUserContents(userInfo)">
				<view class="tn-flex tn-flex-col-center tn-flex-row-left">
					<view class="logo-pic tn-shadow">
						<view class="logo-image">
							<view class="tn-shadow-blur" style="width: 110rpx;height: 110rpx;background-size: cover;"
								:style="{'background-image': 'url('+userInfo.avatar+')'}">
							</view>
						</view>
					</view>
					<view class="tn-padding-right tn-color-black">
						<view class="tn-padding-right tn-padding-left-sm tn-text-xl tn-text-bold">
							<block v-if="userInfo.screenName!=''">{{userInfo.screenName}}</block>
							<block v-else>{{userInfo.name}}</block>
						</view>
						<view class="tn-padding-right tn-padding-top-xs tn-text-ellipsis tn-padding-left-sm">
						    <block v-if="userInfo.groupKey === 'administrator' || (smrz === 1 && lvrz === 1)">
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-brown tn-padding-left-xs tn-text-bold">5.0</text>
						    </block>
						    <block v-else-if="userInfo.groupKey === 'editor' || (smrz === 1 && lvrz !== 1) || (smrz !== 1 && lvrz === 1)">
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower tn-text-lg"></text>
						        <text class="tn-color-brown tn-padding-left-xs tn-text-bold">4.0</text>
						    </block>
						    <block v-else-if="userInfo.groupKey === 'contributor' && smrz !== 1 && lvrz !== 1">
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower-fill tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower tn-text-lg"></text>
						        <text class="tn-color-purplered tn-icon-flower tn-text-lg"></text>
						        <text class="tn-color-brown tn-padding-left-xs tn-text-bold">3.0</text>
						    </block>
						</view>
					</view>
				</view>
			</view>
			<view class="justify-content-item tn-flex-row-center" @click="getPrivateChat(userInfo)">
				<view class="tn-cool-bg-color-15 tn-padding-xs tn-color-white tn-round tn-shadow-blur">
					<text class="tn-padding-left-xs">联系卖家</text>
					<text class="tn-icon-send-fill tn-padding-xs"></text>
				</view>
			</view>
		</view>


		<!-- 边距间隔 -->
		<!--    <view class="tn-strip-bottom"></view>
    
    <view class="tn-margin">
      <view class="tn-flex tn-flex-row-between">
        <view class="justify-content-item tn-text-bold tn-text-xl">
          产品标签
        </view>
      </view>
    </view>
    
    <view class="">
      <view class="tn-tag-content tn-margin tn-text-justify">
        <view v-for="(item, index) in tagList" :key="index" class="tn-tag-content__item tn-margin-right tn-round tn-text-sm tn-text-bold" :class="[`tn-bg-${item.color}--light tn-color-${item.color}`]">
          <text class="tn-tag-content__item--prefix">#</text> {{ item.title }}
        </view>
      </view>
    </view> -->

		<!-- 边距间隔 -->
		<view class="tn-strip-bottom"></view>


		<view class="tn-margin bg-white">
			<view class="tn-flex tn-flex-row-between">
				<view class="justify-content-item tn-text-bold tn-text-xl">
					商品详情
				</view>
			</view>
		</view>

		<view class="content-backgroup tn-margin bg-white">
			<block v-if="shopIsMd==1">
				<mp-html :content="html" :selectable="true" :show-img-menu="true" :lazy-load="true" :ImgCache="true"
					:markdown="true" />
			</block>
			<block v-if="shopIsMd==0">
				<mp-html :content="html" :selectable="true" :show-img-menu="true" :lazy-load="true" :ImgCache="true"
					:markdown="false" />
			</block>
		</view>




		<!-- 边距间隔 -->
		<view class="tn-strip-bottom"></view>

		<view class="tn-margin-top-sm bg-white">
			
			<!-- #ifdef APP || MP -->
			<tn-sticky :zIndex="999" :offsetTop="NavBar*2-22">
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<tn-sticky :zIndex="999">
			<!-- #endif -->
			<view class="bg-white">
				<tn-tabs :list="fixedList" :current="current" :isScroll="false" activeColor="#000" :bold="true"
					:fontSize="32" :badgeOffset="[20, 50]" @change="tabChange"></tn-tabs>
			</view>
				
			</tn-sticky>
		</view>

		<view class="info-operate-bg" :class="isShare?'show':''" @tap="isShare=false"></view>
		<view class="info-operate" :class="isShare?'show':''">
			<view class="info-operate-main grid col-2">
				<view class="index-sort-box">
					<view class="index-sort-main" @tap="copyShare">
						<view class="index-sort-i" style="background: rgb(158 158 158 / 20%)">
							<text class="tn-icon-link" style="color:  rgba(19, 19, 19);"></text>
						</view>
						<view class="index-sort-text">
							复制商品链接
						</view>
					</view>
				</view>
				<view class="index-sort-box">
					<view class="index-sort-main" @tap="ToShare">
						<view class="index-sort-i" style="background: rgb(158 158 158 / 20%)">
							<text class="tn-icon-share-triangle" style="color: rgba(19, 19, 19);"></text>
						</view>
						<view class="index-sort-text">
							分享到其他应用
						</view>
					</view>
				</view>
			</view>

		</view>
		<!-- 推荐 -->
		<view class="bg-white" v-if="current==0">
			<view class="">
				<view class="nav_title--wrap">
					<view class="nav_title tn-cool-bg-color-6">
						<text class="tn-icon-discover-planet tn-padding-right-sm tn-text-xxl"></text>
						<text class="tn-text-xl">好物推荐 · 同款商品</text>
						<text class="tn-icon-buy tn-padding-left-sm tn-text-xxl"></text>
					</view>
				</view>
			</view>
		</view>
		<view class="bg-white" v-if="current==1">
			<view class="">
				<view class="nav_title--wrap">
					<view class="nav_title tn-cool-bg-color-4">
						<text class="tn-icon-discover-planet tn-padding-right-sm tn-text-xxl"></text>
						<text class="tn-text-xl">好物推荐 · 同类商品</text>
						<text class="tn-icon-shopbag tn-padding-left-sm tn-text-xxl"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- 热卖 -->
		<view class="bg-white" v-if="current==2">
			<view class="">
				<view class="nav_title--wrap">
					<view class="nav_title tn-cool-bg-color-15">
						<text class="tn-icon-discover-planet tn-padding-right-sm tn-text-xxl"></text>
						<text class="tn-text-xl">好物推荐 · 热卖商品</text>
						<text class="tn-icon-fire tn-padding-left-sm tn-text-xxl"></text>
					</view>
				</view>
			</view>
		</view>

		<view class="bg-white" v-if="current==3">
			<view class="">
				<view class="nav_title--wrap">
					<view class="nav_title tn-cool-bg-color-2">
						<text class="tn-icon-discover-planet tn-padding-right-sm tn-text-xxl"></text>
						<text class="tn-text-xl">好物推荐 · 最新商品</text>
						<text class="tn-icon-cart tn-padding-left-sm tn-text-xxl"></text>
					</view>
				</view>
			</view>
		</view>
		<view class="shop-list">
			<view class="tn-flex tn-flex-wrap">

				<block v-for="(item,index) in shopList" :key="index">
					<shopItem :item="item"></shopItem>
				</block>
			</view>

		</view>
		<view class="load-more" @tap="loadMore" v-if="shopList.length>0">
			<text>{{moreText}}</text>
		</view>




	<view class="footerfixed dd-glass tn-padding-left-sm tn-padding-right tn-padding-top-xs tn-padding-bottom-sm">
		<block v-if="shareof==1">
			<tn-goods-nav :options="sharCountOptions" :buttonGroups="customButtonGroups" buttonType="round"
				:safeAreaInsetBottom="true" @optionClick="onOptionClick" @buttonClick="onButtonClick"></tn-goods-nav>
		</block>
		<block v-else>
			<tn-goods-nav :options="countOptions" :buttonGroups="customButtonGroups" buttonType="round"
				:safeAreaInsetBottom="true" @optionClick="onOptionClick" @buttonClick="onButtonClick"></tn-goods-nav>
		</block>
	</view>

	<view class='tn-tabbar-height'></view>
	<!--加载遮罩-->
	<view class="loading" style="z-index: 2000;" v-if="isLoading==0">
		<view class="loading-main">
			<image src="../../static/loading.gif"></image>
		</view>
	</view>
	<!--加载遮罩结束-->
	</view>

</template>





<script>
	import template_page_mixin from '@/tuniao-ui/mixin/template_page_mixin.js'
	import mpHtml from '@/components/mp-html/mp-html'
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		mixins: [template_page_mixin],
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				isShare: false,
				userInfo: "",
				myuid: 0,
				smrz: 0,
				lvrz: 0,
				show: false,
				mygroupKeyz: null,
				sharCountOptions: [{
					icon: 'shop',
					text: '卖家'
				}, {
					icon: 'warning',
					text: '举报'
				}, {
					icon: 'share-circle',
					text: '分享',
				}],
				customButtonGroups: [{
					text: '咨询卖家',
					backgroundColor: 'tn-cool-bg-color-5',
					color: '#FFFFFF'
				}, {
					text: '立即购买',
					backgroundColor: 'tn-cool-bg-color-15--reverse',
					color: '#FFFFFF'
				}],
				countOptions: [{
					icon: 'shop',
					text: '卖家'
				}, {
					icon: 'warning',
					text: '举报'
				}],
				customButtonGroups: [{
					text: '咨询卖家',
					backgroundColor: 'tn-cool-bg-color-5',
					color: '#FFFFFF'
				}, {
					text: '立即购买',
					backgroundColor: 'tn-cool-bg-color-15--reverse',
					color: '#FFFFFF'
				}],
				page: 1,
				sid: 0,
				title: "",
				html: "",
				markdownData: {},
				price: "",
				num: "",
				sellNum: "",
				imgurl: "",
				integral: 0,
				current: 0,
				fixedList: [{
						name: '同款'
					},
					{
						name: '同类'
					},
					{
						name: '热卖'
					},
					{
						name: '新品'
					}
				],
				lvRz: 0,
				yhSm: 0,
				mjuid: 0,
				isLoading: 0,
				isLoad: 0,
				moreText: "",
				shopIsMd: -1,
				token: "",
				subtype: 0,
				sort: 0,
				isBuy: 0,
				shopinfo: {},
				shareof: 0,
				vipDiscount: 0,
				vipPrice: 0,
				scale: 0,
				isvip: 0,
				vip: 0,
				shopList: [],
				currencyName: ""

			}
		},
		components: {
			mpHtml,
		},
		onReachBottom() {
			//触底后执行的方法，比如无限加载之类的
			var that = this;
			if (that.isLoad == 0) {
				that.loadMore();
			}
		},

		onShow() {
			var that = this;
			// #ifdef APP-PLUS

			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			that.getVipInfo();
			that.userStatus();
			
			if (localStorage.getItem('userinfo')) {
				var userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.myuid = userInfo.uid;
			}
			if (localStorage.getItem('isShopEdit')=='true') {
				that.getInfo(that.sid);
				localStorage.removeItem('isShopEdit');
			}

		},
		onPullDownRefresh() {
			var that = this;
			that.getInfo(that.sid);
			that.isBuyShop(that.sid);
		},
		onLoad(res) {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			if (res.sid) {
				that.sid = res.sid;
				that.getInfo(that.sid);
			}
			
			


		},
		mounted() {
			var that = this;
			that.getleiji()
		},

		methods: {
			getleiji() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.currencyName = res.data.assetsname;
						that.shareof = res.data.shareof;
					},
					fail(error) {
						console.log(error);
					}

				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			popup() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
					that.myuid = userInfo.uid;
					that.mygroupKeyz = userInfo.group;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				that.show = true;

			},
			loadMore() {
				var that = this;
				that.moreText = "正在加载中...";
				that.isLoad = 1;
				that.getShopList(true, that.current);

			},
			getPrivateChat(userdata) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
					var myuid = userInfo.uid;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				var mjname = userdata.name;
				var mjuid = userdata.uid;
				if (userdata.screenName) {
					mjname = userdata.screenName
				}
				if (mjuid == myuid) {
					uni.showToast({
						title: "不能私信你自己",
						icon: 'none'
					})
					return false;
				}
				var data = {
					"touid": mjuid,
					"token": token
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.getPrivateChat(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res));
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);

						if (res.data.code == 1) {
							var name = mjname;
							var uid = myuid;
							var chatid = res.data.data
							uni.redirectTo({
								url: '/pages/chat/chat?uid=' + mjuid + "&name=" + mjname + "&chatid=" +
									chatid
							});
							that.show = false
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toJb(title) {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/help?type=shop&title='+title
				});
				that.show = false
			},
			deleteShop(sid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					var timer = setTimeout(function() {
						uni.navigateTo({
							url: '/pages/user/login'
						});
						clearTimeout('timer')
					}, 1000)
					return false
				}
				var data = {
					key: sid,
					token: token,
				}
				uni.showModal({
					title: '确定要删除此商品吗?',
					content: ' ',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							that.$Net.request({

								url: that.$API.deleteShop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.$emit('updateList', 'updateList');
								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
									uni.stopPullDownRefresh()
								}
							})
						}
					}
				});
				that.show = false

			},
			editShop(data) {
				var that = this;
				var sid = data.id;
				var isMd = data.isMd;
				that.show = false
				if (that.mygroupKeyz !== 'administrator' && that.mygroupKeyz !== 'editor') {

					uni.showModal({
						title: '编辑后商品需要重新审核，你确定要编辑吗?',
						content: ' ',
						success: function(res) {
							if (res.confirm) {

								if (isMd === 1) {
									uni.navigateTo({
										url: `/pages/edit/addshop?type=edit&id=${sid}`
									});
								} else {
									// 富文本编辑器  
									uni.navigateTo({
										url: `/pages/edit/addshop?type=edit&id=${sid}`
									});
								}
								
							} else if (res.cancel) {
								return false;
							}
						},
						fail: function(res) {
							console.error('模态框显示失败:', res);
						}
					});
				} else {
					if (isMd === 1) {
						uni.navigateTo({
							url: `/pages/edit/addshop?type=edit&id=${sid}`
						});
					} else {
						// 富文本编辑器  
						uni.navigateTo({
							url: `/pages/edit/addshop?type=edit&id=${sid}`
						});
					}
					that.show = false
				}

			},
			quillHtml(text){
				var that = this;
				text = that.replaceAll(text,"hljs","hl");
				text = that.replaceAll(text,"ql-syntax","hl-pre");
				
				text = that.markExpand(text);
				return text;
			},
			replaceAll(string, search, replace) {
			  return string.split(search).join(replace);
			},
			markHtml(text){
				var that = this;
				//下面奇怪的代码是为了解决可执行代码区域问题
				text = that.replaceAll(text,"@!!!","@@@@");
				
				text = that.replaceAll(text,"!!!","");
				text = that.replaceAll(text,"@@@@","@!!!");
				text = that.markExpand(text);
				//text = text.replace(/(?<!\r)\n(?!\r)/g, "\n\n");
				//兼容垃圾的Safari浏览器
				text = text.replace(/([^\r])\n([^\r])/g, "$1\n\n$2");
				text = that.replaceAll(text,"||rn||","\n\n");
				return text;
				
			},
			markExpand(text) {
				var that = this;
				//视频
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|type=video"[^>]*?>/g,
					(match, src, poster) => {
						return `<div style="border-radius:10px"><video src="${src}" poster="${poster}" object-fit width="100%" style="border-radius:10px" /></div>`;
					}
				);
				//超链接
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|text=([^"]+)\|type=url"[^>]*?>/g,
					(match, src, poster, text) => {
						return `<div><a href="${src}">${text}</a></div>`;
					}
				);
				//音乐
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|name=([^"]+)\|author=([^"]+)\|type=audio"[^>]*?>/g,
					(match, src, poster, name, author) => {
						return `<div><audio src="${src}" poster="${poster}" name="${name}" author="${author}" loop width="100%"></audio></div>`;
					}
				);
				//附件
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|pw=([^"]+)\|name=([^"]+)\|type=file"[^>]*?>/g,
					(match, src, poster, pw, name) => {
						var tqm = ''
						if(pw=='无'){
							tqm = ''
						}else{
							tqm = '提取码：'+pw
						}
						return `
						<div style='background: #f0f0f0;width:100%;padding:15px 15px;color:#666666;border:solid 1px black;box-sizing: border-box;border-radius: 20px;word-break:break-all;'/>
							<div style="display: flex;justify-content: space-between;align-items: center;">
								<div>
									<div style="font-size: 16px;font-weight: bold;color: black;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">${name}</div>
									<div style="font-size: 15px;">${tqm}</div>
								</div>
								<a href='${src}' style="color:#000000">
								<div>
									<span style='font-size:30px' class='tn-icon-download-simple' />
								</div>
								</a>
							</div>
						</div>
						`;
					}
				);
				
				//链接自动识别
				text = text.replace(/(?![^<>]*>)(src=['"]|poster=['"]|https?:\/\/[^\s'"<>]+)/gi, function(match, group1) {
					if (group1 && !/(src=['"]|poster=['"])/i.test(group1)) {
						var lastChar = match.charAt(match.length - 1);
						if (!/['"<>]/.test(match.charAt(4)) && !/[^a-zA-Z0-9\/]/.test(lastChar)) {
							return '<a href="' + match + '" target="_blank">' + match + '</a>';
						} else {
							return match;
						}
					} else {
						return match;
					}
				});
			
				return text;
			},
			getInfo(sid) {
				var that = this;
				var data = {
					"key": that.sid,
				}
				that.$Net.request({
					url: that.$API.shopInfo(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						that.shopinfo = res.data;
						that.shopIsMd = res.data.isMd;
						that.title = res.data.title;
						that.type = res.data.type;
						var html = res.data.text;
						if(res.data.isMd==1){
							html =that.markHtml(res.data.text);
						}else{
							html =that.quillHtml(res.data.text);
						}
						that.html = html;
						that.imgurl = res.data.imgurl;
						that.subtype = res.data.subtype
						that.sort = res.data.sort
						that.integral = res.data.integral;
						that.price = res.data.price;
						that.num = res.data.num;
						that.lvrz = res.data.lvrz;
						that.smrz = res.data.smrz;
						that.sellNum = res.data.sellNum;
						that.vipDiscount = res.data.vipDiscount;
						that.mjuid = res.data.uid;
						if (res.data.type != 1) {
							that.isBuyShop(that.sid);
						}
						that.getUserInfo(res.data.uid);
						that.getShopList(false, 0);
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.stopPullDownRefresh();
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			previewImage(image) {
				var imgArr = [];
				imgArr.push(image);
				//预览图片
				uni.previewImage({
					urls: imgArr,
					current: imgArr[0]
				});
			},
			shopBuy() {
				var that = this;
				uni.navigateTo({
					url: '/pages/shop/orderpay?sid=' + that.sid
				});
			},
			getUserInfo(id) {
				var that = this;
				var data = {
					"key": id,
				}

				that.$Net.request({
					url: that.$API.getUserInfo(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.userInfo = res.data.data;
							that.userInfo.style = "background-image:url(" + res.data.data.avatar + ");"
						}
					},
					fail: function(res) {}
				});
			},
			toInfo() {
				var that = this;
				var data = that.shopinfo;
				if (data.type == 1) {
					uni.showToast({
						title: "实体商品请留意快递信息，有问题联系卖家",
						icon: 'none'
					})
				} else {
					uni.navigateTo({
						url: '/pages/shop/shoptext?sid=' + data.id
					});
				}
			},
			isBuyShop(sid) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"sid": that.sid,
					"token": token
				}
				that.$Net.request({
					url: that.$API.isBuyShop(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.isBuy = 1;
						}

					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toUserContents(data) {
				var that = this;
				var name = data.name;
				var title = data.name + "的信息";
				if (data.screenName) {
					title = data.screenName + " 的信息";
					name = data.screenName
				}
				var id = data.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
			getShopList(isPage, shopType) {
				var that = this;
				var data = {
					"status": "1",
					"isView": "1"
				}
				var orderKey = ""
				if (shopType == 0) {
					data.subtype = that.subtype;
				}
				if (shopType == 1) {
					data.sort = that.sort;
				}
				if (shopType == 2) {
					orderKey = "sellNum"
				}
				var page = that.page;
				if (isPage) {
					page++;
				}
				that.$Net.request({
					url: that.$API.shopList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"searchKey": "",
						"limit": 6,
						"page": page,
						"order": orderKey
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
						that.isLoad = 0;
						that.moreText = "加载更多";
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length == 0) {
								that.moreText = "没有更多数据了";
							}
							if (isPage) {
								if (list.length > 0) {
									that.page++;
									that.shopList = that.shopList.concat(list);
								}
							} else {

								that.shopList = list;
							}
						}
					},
					fail: function(res) {
						that.moreText = "加载更多";
						that.isLoad = 0;
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)

					}
				})
			},
			getVipInfo() {
				var that = this;
				that.$Net.request({
					url: that.$API.getVipInfo(),
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							//that.vipDiscount=res.data.data.vipDiscount;
							that.vipPrice = res.data.data.vipPrice;
							that.scale = res.data.data.scale;
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			userStatus() {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				that.$Net.request({

					url: that.$API.userStatus(),
					data: {
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.vip = res.data.data.vip;
							that.isvip = res.data.data.isvip;
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},

			ToCopy(text) {
				var that = this;
				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: text,
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: "链接复制成功"
						})
					}
				});
				// #endif
				// #ifdef H5 
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select();
				textarea.setSelectionRange(0, text.length);
				uni.showToast({ //提示
					title: "链接复制成功"
				})
				var result = document.execCommand("copy")
				textarea.remove();

				// #endif
			},
			copyShare() {
				var that = this;
				if (that.shareof != 1) {
					uni.showToast({
						title: "管理员未开启分享功能",
						icon: 'none'
					})
					return false
				}
				var linkRule = that.$API.GetshopStar();

				var appname = that.$API.GetAppName()

				var url = linkRule.replace("{sid}", that.sid);

				var text = that.title + ' - ' + appname + " | 商品链接：" + url
				that.ToCopy(text);
				that.isShare = false
			},
			ToShare() {

				var that = this;
				if (that.shareof != 1) {
					uni.showToast({
						title: "管理员未开启分享功能",
						icon: 'none'
					})
					return false
				}
				var linkRule = that.$API.GetshopStar();
				var appname = that.$API.GetAppName()

				var url = linkRule.replace("{sid}", that.sid);
				// #ifdef APP-PLUS
				uni.shareWithSystem({
					href: url,
					summary: that.title + ' | ' + appname,
					success() {
						// 分享完成，请注意此时不一定是成功分享

					},
					fail() {
						// 分享失败
					}
				});
				// #endif
				// #ifdef H5
				var text = that.title + ' - ' + appname + " | 商品链接：" + url
				that.ToCopy(text);
				// #endif
				that.isShare = false
			},
			// cardSwiper
			cardSwiper(e) {
				var that = this;
				that.cardCur = e.detail.current
			},
			// 跳转
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			// tab选项卡切换
			tabChange(index) {
				var that = this;
				that.current = index
				if (index == 0) {
					that.page = 1
					that.getShopList(false, 0);
				} else if (index == 1) {
					that.page = 1
					that.getShopList(false, 1)
				} else if (index == 2) {
					that.page = 1
					that.getShopList(false, 2)

				} else if (index == 3) {
					that.page = 1
					that.getShopList(false, 3)

				}
			},
			getRandomCoolBg() {
				var that = this;
				return that.$t.colorUtils.getRandomCoolBgClass()
			},
			onOptionClick(e) {
				var that = this;
				if (e.index == 0) {
					that.toUserContents(that.userInfo)
				} else if (e.index == 1) {
					that.toJb(that.title)
				} else if (e.index == 2) {
					that.isShare = !that.isShare
				}
			},
			// 按钮点击事件
			onButtonClick(e) {
				var that = this;
				if (e.index == 0) {
					that.getPrivateChat(that.userInfo)
				} else if (e.index == 1) {
					that.shopBuy()
				}
			},
		},

	}
</script>

<style lang="scss" scoped>
	.template-product {}

	.tn-tabbar-height {
		min-height: 120rpx;
		height: calc(140rpx + env(safe-area-inset-bottom) / 2);
	}

	/* 用户头像 start */
	.logo-image {
		width: 110rpx;
		height: 110rpx;
		position: relative;
	}

	.logo-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 10rpx;
		overflow: hidden;
		// background-color: #FFFFFF;
	}

	/* 胶囊*/
	.tn-custom-nav-bar__back {
		width: 100%;
		height: 100%;
		position: relative;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		box-sizing: border-box;
		background-color: rgba(0, 0, 0, 0.15);
		border-radius: 1000rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.5);
		color: #FFFFFF;
		font-size: 18px;

		.icon {
			display: block;
			flex: 1;
			margin: auto;
			text-align: center;
		}

		&:before {
			content: " ";
			width: 1rpx;
			height: 110%;
			position: absolute;
			top: 22.5%;
			left: 0;
			right: 0;
			margin: auto;
			transform: scale(0.5);
			transform-origin: 0 0;
			pointer-events: none;
			box-sizing: border-box;
			opacity: 0.7;
			background-color: #FFFFFF;
		}
	}

	/* 轮播视觉差 start */
	.card-swiper {
		height: 750rpx !important;
	}

	.card-swiper swiper-item {
		width: 750rpx !important;
		left: 0rpx;
		box-sizing: border-box;
		// padding: 0rpx 30rpx 90rpx 30rpx;
		overflow: initial;
	}

	.card-swiper swiper-item .swiper-item {
		width: 100%;
		display: block;
		height: 100%;
		transform: scale(1);
		transition: all 0.2s ease-in 0s;
		overflow: hidden;
	}

	.card-swiper swiper-item.cur .swiper-item {
		transform: none;
		transition: all 0.2s ease-in 0s;
	}

	.image-banner {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.image-banner image {
		width: 100%;
		height: 100%;
	}

	/* 轮播指示点 start*/
	.indication {
		z-index: 9999;
		width: 100%;
		height: 36rpx;
		position: absolute;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.spot {
		background-color: #FFFFFF;
		opacity: 0.6;
		width: 10rpx;
		height: 10rpx;
		border-radius: 20rpx;
		top: -60rpx;
		margin: 0 8rpx !important;
		position: relative;
	}

	.spot.active {
		opacity: 1;
		width: 30rpx;
		background-color: #FFFFFF;
	}

	/* 间隔线 start*/
	.tn-strip-bottom-min {
		width: 100%;
		border-bottom: 1rpx solid #F8F9FB;
	}

	/* 间隔线 start*/
	.tn-strip-bottom {
		width: 100%;
		border-bottom: 20rpx solid rgba(241, 241, 241, 0.8);
	}

	/* 间隔线 end*/
	/* 标题 start */
	.nav_title {
		-webkit-background-clip: text;
		color: transparent;

		&--wrap {
			position: relative;
			display: flex;
			height: 120rpx;
			font-size: 46rpx;
			align-items: center;
			justify-content: center;
			font-weight: bold;
			background-size: cover;
		}
	}

	/* 标题 end */

	/* 底部tabbar start*/
	.footerfixed {
		position: fixed;
		width: 100%;
		bottom: 0;
		z-index: 999;
		background-color: #FFFFFF;
		box-shadow: 0rpx 0rpx 30rpx 0rpx rgba(0, 0, 0, 0.07);
	}

	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 10rpx 30rpx;
			margin: 20rpx 20rpx 5rpx 0rpx;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/

	/* 内容图 start */
	.content-backgroup {
		z-index: -1;

		.backgroud-image {
			border-radius: 15rpx;
			width: 100%;
		}
	}

	/* 内容图 end */

	/* 商家商品 start*/
	.tn-blogger-content {
		&__wrap {
			box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
			border-radius: 20rpx;
			margin: 15rpx;
		}

		&__info {
			&__btn {
				margin-right: -12rpx;
				opacity: 0.5;
			}
		}

		&__label {
			&__item {
				line-height: 45rpx;
				padding: 0 10rpx;
				margin: 5rpx 18rpx 0 0;

				&--prefix {
					color: #E83A30;
					padding-right: 10rpx;
				}
			}

			&__desc {
				line-height: 35rpx;
			}
		}

		&__main-image {
			border-radius: 16rpx 16rpx 0 0;

			&--1 {
				max-width: 690rpx;
				min-width: 690rpx;
				max-height: 400rpx;
				min-height: 400rpx;
			}

			&--2 {
				max-width: 260rpx;
				max-height: 260rpx;
			}

			&--3 {
				height: 212rpx;
				width: 100%;
			}
		}

		&__count-icon {
			font-size: 24rpx;
			padding-right: 5rpx;
		}
	}

	.image-book {
		padding: 150rpx 0rpx;
		font-size: 16rpx;
		font-weight: 300;
		position: relative;
	}

	.image-picbook {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border-radius: 15rpx 15rpx 0 0;
	}

	/* 毛玻璃*/
	.dd-glass {
		width: 100%;
		backdrop-filter: blur(20rpx);
		-webkit-backdrop-filter: blur(20rpx);
	}

	.index-sort-i {
		border-radius: 15px;
	}

	.tn-margin-top-xxl {
		margin-top: 60upx;
	}

	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
</style>