<?php
initializeActions();

// 获取插件启用状态
$currentDir = __DIR__;
$configFile = str_replace('Actions', '', $currentDir) . 'config.ini';

if (file_exists($configFile)) {
    $configData = parse_ini_file($configFile, true);
    $enabled = isset($configData['plugin']['enabled']) ? $configData['plugin']['enabled'] : 'false';
    $installed = isset($configData['plugin']['installed']) ? $configData['plugin']['installed'] : 'false';
    
    // 如果插件已安装且启用，还需要检查数据库配置
    $captchaEnabled = false;
    if ($enabled === 'true' && $installed === 'true') {
        $sql = "SELECT enabled FROM Xqy_Plugin_captcha_config WHERE id = 1";
        $result = mysqli_query($connect, $sql);
        if ($result && mysqli_num_rows($result) > 0) {
            $config = mysqli_fetch_assoc($result);
            $captchaEnabled = intval($config['enabled']) === 1;
        }
    }
    
    $status = [
        'plugin_enabled' => $enabled === 'true',
        'plugin_installed' => $installed === 'true',
        'captcha_enabled' => $captchaEnabled,
        'overall_enabled' => $enabled === 'true' && $installed === 'true' && $captchaEnabled
    ];
    
    send_json(200, "获取状态成功", $status);
} else {
    send_json(404, "配置文件不存在");
}
?>
