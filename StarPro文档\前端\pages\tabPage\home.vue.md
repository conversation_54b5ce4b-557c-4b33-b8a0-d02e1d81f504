<template>
	<view :class="$store.state.AppStyle" v-if="tabCurTab==isLoging" style="background-color: #f6f6f6;height: 100%;">
		<view class="header" :style="[{height:CustomBar + 'px'}]" v-if="tabCurTab==isLoging">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<!--  #ifdef MP -->
				<view class="action" @tap="toSearch">
					<text class="cuIcon-search"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					首页
				</view>
				<!--  #endif -->
				<!--  #ifdef H5 || APP-PLUS -->
				<view class="user-rz">

					<tn-avatar style="margin-left: 16upx;" badgeBgColor="#ff4f4f" :src="avatarurl" :badge="noticeSumof"
						:badgeText='noticeSum' v-if="token!=''&&tabCurTab==isLoging" @tap="goMsg()"></tn-avatar>
					<tn-avatar style="margin-left: 16upx;font-size: 20upx;" backgroundColor="#f5f5f5" text="登录" v-else
						@tap="goUserInfo()"></tn-avatar>
					<image class="user-rz-icon-htop" :src="rzImg"
						v-if="token!=''&&identifyCompany==1&&tabCurTab==isLoging" mode="aspectFill"></image>
				</view>

				<view class="search-form radius" @tap="toSearch">
					<text class="cuIcon-search"></text>
					<view class="search-form-text">{{sousuok}}</view>
				</view>
				<view class="action header-btn">
					<!-- <text @tap="changeMode()">
						<text class="tn-icon-sequence"></text>
					</text> -->
					<text @tap="goCategory">
						<text class="tn-icon-menu-circle tn-margin-left-sm"></text>
					</text>
				</view>
				<!--  #endif -->
			</view>
		</view>

		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="data-box bg-white" :class="{
		'data-box-2-banner-1': !swiperStyle&&homeStyle==2,
		'data-box-2-banner-3': swiperStyle&&homeStyle==2
		}" v-if="lunbo_of==1&&tabCurTab==isLoging&&homeMode!=3">
			<tn-swiper :list="swiperList" :class="swiperStyle?'':'uni-swiper-slides-1'"
				style="margin: 0;padding: 0;border-radius: 20upx;" :effect3d="swiperStyle" @click="swiperclick"
				:backgroundColor="swiperBgcolor" :title="true" :height="swiperHeight"
				:effect3dPreviousSpacing="80"></tn-swiper>
		</view>
		
		<view class="data-box bg-white" :class="{
		'data-box-2-banner-1': !swiperStyle&&homeStyle==2,
		'data-box-2-banner-3': swiperStyle&&homeStyle==2,
		'bg-h': homeStyle==2
		}" v-if="lunbo_of==1&&tabCurTab==isLoging&&homeMode==3">
		<!-- #ifdef APP-PLUS || H5 -->
			<swiper class="custom-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
				:circular="true" :radius="true" indicator-active-color="#ffffff" @change="swiperChange">
				<swiper-item v-for="(item, index) in swiperList" :key="index" @click="swiperclick(index)">
					<view class="swiper-item">
						<view class="swiper-bg">
							<image class="bg-image" :src="item.url" mode="aspectFill"></image>
							<view class="dark-overlay"></view>
						</view>
						<view class="swiper-content">
							<view class="app-info">
								<image class="app-icon" :src="item.url" mode="aspectFill"></image>
								<view class="app-text">
									<text class="app-name" style="margin-top: 0rpx;">{{item.title}}</text>
									<view class="app-rating">
										<tn-rate v-model="item.scoreNum" :count="5" :allowHalf="true" :disabled="true"
											:size="28" activeColor="#57d1b1" inactiveColor="#cecece"
											activeIcon="star-fill" inactiveIcon="star" :gutter="10"></tn-rate>
									</view>
									<text class="app-desc">{{item.intro}}</text>
								</view>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		<!-- #endif -->
		</view>
		<block v-if="homeStyle==2&&tabCurTab==isLoging">
			<view class="data-box tn-margin-bottom-sm bg-white" :class="{'data-box-2': homeStyle==2}"
				v-if="gonggao_of==1">
				<uni-notice-bar :key="noticeBarKey" single :text="noticeList" showIcon scrollable color='#000'
					background-color="#fff"></uni-notice-bar>
			</view>
		</block>
		<view class="bg-white" :class="{'data-box-2': homeStyle==2}" v-if="top_of==1&&tabCurTab==isLoging">
			<tn-scroll-list :indicator="false">
				<view class="tn-flex">
					<view class="tn-flex-1 tn-padding-sm tn-margin-xs tn-radius" v-for="(item, index) in iconimg"
						:key="index" @tap="toLink(item.link,item.lgof)">
						<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center">
							<view class="icon5__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-shadow-blur"
								:style="'background-image: url(' + item.url + ')'" style="background-size:100% 100%;">
							</view>
							<view class="tn-color-black tn-text-center">
								<text class="tn-text-ellipsis">{{item.name}}</text>
							</view>
						</view>
					</view>
				</view>
			</tn-scroll-list>

		</view>
		<block v-if="homeStyle==1&&tabCurTab==isLoging">
			<view class="tn-margin-bottom-sm bg-white" v-if="gonggao_of==1">
				<uni-notice-bar :key="noticeBarKey" single :text="noticeList" showIcon scrollable color='#000'
					background-color="#fff"></uni-notice-bar>
			</view>
		</block>
		<block v-if="wzof==1&&homeMode==1&&tabCurTab==isLoging">
			<view class="data-box all-box" :class="{'data-box-2 bg-hui': homeStyle==2,'bg-white': homeStyle==1}"
				:style="TabCur!=0?'margin-top:0;':''">
				<view class="cu-bar bg-white" v-if="TabCur==0">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text>文章<text class="tn-icon-image-text tn-padding-sm"
							style="padding-top: 12px;"></text>
					</view>
					<view class="action more" @tap='toTopContents("全部文章","commentsNum")'>
						<text>更多</text><text class="cuIcon-right"></text>
					</view>
				</view>
				<view class="dataLoad" v-if="!dataLoad">
					<image src="../../static/loading.gif"></image>
				</view>
				<view :class="{'tn-margin-left-sm tn-margin-right-sm': homeStyle==1}">
					<block v-for="(item,index) in topContents" :key="'top'+index"
						v-if="TabCur==0&&dataLoad&&topStyle==1&&hometop==1">
						<articleItemA :item="item" :isTop="true"></articleItemA>
					</block>
					<block v-for="(item,index) in topContents" :key="'top'+index"
						v-if="TabCur==0&&dataLoad&&topStyle==2&&hometop==1">
						<articleItemB :item="item" :isTop="true"></articleItemB>
					</block>
					<block v-for="(item,index) in contentsList" :key="index" v-if="dataLoad&&actStyle==1">
						<articleItemA :item="item"></articleItemA>
					</block>
					<block v-for="(item,index) in contentsList" :key="index" v-if="dataLoad&&actStyle==2">
						<articleItemB :item="item"></articleItemB>
					</block>
				</view>

			</view>
			<block v-if="dataLoad&&actStyle==3">
				<view :class="{'bg-hui': homeStyle==2,'bg-white': homeStyle==1}">
					<tn-waterfall ref="waterfall" v-model="contentsList" @finish="handleWaterFallFinish"
						style="margin: 0upx 10upx;">
						<template v-slot:left="{ leftList }">
							<view v-for="(item,index) in leftList" :key="index">
								<articleItemWfA :item="item"></articleItemWfA>
							</view>
						</template>
						<template v-slot:right="{ rightList }">
							<view v-for="(item,index) in rightList" :key="index">
								<articleItemWfB :item="item"></articleItemWfB>
							</view>
						</template>
					</tn-waterfall>
				</view>
			</block>
			<view class="load-more" @tap="loadMore" v-if="dataLoad">
				<text>{{moreText}}</text>
			</view>
		</block>
		<block v-if="tzof==1&&homeMode==2&&tabCurTab==isLoging">
			<view class="data-box tn-margin-top-sm" :class="{'data-box-2': homeStyle==2}"
				v-if="circleOf==1&&tabCurTab==isLoging">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						精选圈子<text class="tn-icon-moments tn-padding-sm" style="padding-top: 12px;"></text>
					</view>
					<view class="action more" @tap="goAllSection">
						<text>全部</text><text class="cuIcon-right"></text>
					</view>
				</view>
				<!-- 方式16 start-->
				<view class="tn-flex tn-flex-wrap">
					<block v-for="(item, index) in recommendSectionList.slice(0, 4)" :key="index">
						<view class="" style="width: 25%;" @click="goSection(item.id)">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center ">
								<view class="tn-radius tn-padding-sm">
									<view class="image-pic" :style="'background-image:url('+ item.pic +')'">
										<view class="image-circle">
										</view>
									</view>
									<view class="tn-text-center tn-text-bold tn-padding-top-xs">{{item.name}}</view>
									<view class="tn-text-center tn-text-xs tn-color-gray--dark tn-padding-top-xs">
										{{item.followNum}}人加入
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>
				<!-- 方式16 end-->
			</view>
			<view class="tn-margin-top-sm" :class="{'bg-white': homeStyle==1}">
				<!-- #ifdef APP || MP -->
				<tn-sticky :zIndex="999" :offsetTop="NavBar*2-22" @fixed="handleSticky" @unfixed="handleUnsticky">
				<!-- #endif -->
					<!-- #ifdef H5 -->
					<tn-sticky :zIndex="999" @fixed="handleSticky" @unfixed="handleUnsticky">
					<!-- #endif -->
						<view
							:class="{'data-box': !isSticky, 'bg-white': isSticky, 'data-box-2':  !isSticky&&homeStyle==2}">
							<tn-tabs :list="topList" :isScroll="true" :current="current" name="tab-name"
								@change="change"></tn-tabs>
						</view>
					</tn-sticky>
					<view class="forum-list-main">
						<view class="no-data" v-if="postList.length==0">
							<text class="cuIcon-text"></text>暂时没有数据
						</view>
						<block v-for="(item,index) in postList" :key="index">
							<forumItem :item="item" :myPurview="0"></forumItem>
						</block>
						<view class="load-more" @tap="loadMore" v-if="dataLoad&&postList.length>0">
							<text>{{moreText}}</text>
						</view>
					</view>
			</view>
		</block>
		<block v-if="homeMode==3&&tabCurTab==isLoging">
		<!-- #ifdef APP-PLUS || H5 -->
		
			<view class="bg-white" :class="{'data-box-2': homeStyle==2}" v-if="tabCurTab==isLoging&&appTopOf==1">
				<view style="display: flex;justify-content: space-between;">
					<view style="padding: 20rpx;font-weight: bold">小编推荐</view>
				</view>
				<tn-scroll-list :indicator="true">
					<view class="tn-flex">
						<view class="tn-flex-1" v-for="(item, index) in appiconimg" :key="index"
							style="margin: 20rpx 10rpx 0rpx 10rpx;padding: 20rpx 10rpx;">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								style="width: 140rpx;" @tap="toAppInfo(item.id)">
								<view
									class="icon5__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-shadow-blur"
									:style="'background-image: url(' + item.logo + ')'"
									style="background-size:100% 100%;">
								</view>
								<view class="tn-color-black tn-text-center">
									<text class="tn-text-ellipsis tn-text-ellipsis2">{{item.name}}</text>
								</view>
								<view class="tn-color-black tn-text-center tn-margin-top-sm">
									<text class="down-button">立即下载</text>
								</view>
							</view>
						</view>
					</view>
				</tn-scroll-list>
			</view>
			<view class="bg-white margin-top-sm" :class="{'data-box-2': homeStyle==2}" v-if="tabCurTab==isLoging">
				<!-- 添加筛选栏 -->
				<view class="filter-bar">
					<!-- 排序选择 -->
					<view class="filter-item" @tap="showOrderPicker">
						<text>{{orderText}}</text>
						<text class="cuIcon-unfold"></text>
					</view>

					<!-- 系统筛选 -->
					<view class="filter-item" @tap="showSystemFilter">
						<text>{{systemText}}</text>
						<text class="cuIcon-unfold"></text>
					</view>

					<!-- 类型筛选 -->
					<view class="filter-item" @tap="showTypeFilter">
						<text>{{typeText}}</text>
						<text class="cuIcon-unfold"></text>
					</view>
				</view>
				<!-- 添加加载动画 -->


				<!-- 应用列表 -->

				<block v-if="applist.length>0">
					<view class="loading-container" v-if="apploading">
						<u-loading mode="circle" size="36"></u-loading>
					</view>
					<block v-else>
						<view class="app-box" style="padding: 20rpx;" v-for="(item, index) in applist" :key="index">
							<view class="app-box-body" @tap="toAppInfo(item.id)">
								<view class="app-box-logo">
									<u-image :src="item.logo" width="110rpx" height="110rpx" mode="aspectFill"
										:lazy-load="true" :fade="true" duration="450" border-radius="28rpx">
										<u-loading slot="loading"></u-loading>
									</u-image>
								</view>
								<view class="app-box-content">
									<view class="app-box-title text-cut">{{item.name}}</view>
									<view class="app-box-info">
										<text :style="{color: item.tagInfo.color}"
											:class="item.score>=3?'tn-icon-star-fill':'tn-icon-star'"></text>
										<text :style="{color: item.tagInfo.color}">{{item.score}}</text>
										<text>{{item.size}}</text>
										<text>v{{item.version}}</text>
										<text :class="item.system=='ios'?'tn-icon-iphone':''"></text>
									</view>
									<view class="app-box-tags">
										<text class="app-tag"
											:style="{backgroundColor: item.tagInfo.color}">{{item.tagInfo.text}}</text>
										<text v-for="(category, idx) in item.sortJson" :key="idx"
											class="app-category-tag">{{category.name}}</text>
									</view>
								</view>
							</view>
							<view class="app-box-down" @tap="toAppInfo(item.id)">下载</view>
						</view>
					</block>
					<block v-else>
						<view class="margin-top-sm">
							<u-empty text="暂无数据" mode="data" icon-size="100" font-size="24"></u-empty>
						</view>

					</block>
				</block>
				<view class="load-more" @tap="loadMore" v-if="dataLoad&&applist.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>

			<!-- 修改排序选择器 -->
			<u-picker v-model="showOrder" :show="showOrder" :columns="[orderOptions]" @confirm="confirmOrder"
				@cancel="cancelOrder" mode="selector" :range="orderOptions" range-key="text"></u-picker>

			<!-- 系统筛选弹窗 -->
			<u-popup v-model="showSystem" mode="bottom" :mask-close-able="true" :safe-area-inset-bottom="true"
				@close="showSystem = false">
				<view class="filter-popup">
					<view class="filter-title">选择系统</view>
					<view class="filter-options">
						<view class="filter-option" v-for="(sys, index) in systemOptions" :key="index"
							:class="{'active': selectedSystem === sys.value}" @tap="selectSystem(sys.value)">
							{{sys.label}}
						</view>
					</view>
					<view class="filter-buttons">
						<view class="btn-reset" @tap="resetSystem">重置</view>
						<view class="btn-confirm" @tap="confirmSystem">确定</view>
					</view>
				</view>
			</u-popup>

			<!-- 类型筛选弹窗 -->
			<u-popup v-model="showType" mode="bottom" :mask-close-able="true" :safe-area-inset-bottom="true"
				@close="showType = false">
				<view class="filter-popup">
					<view class="filter-title">选择类型</view>
					<view class="filter-options">
						<view class="filter-option" v-for="(type, index) in typeOptions" :key="index"
							:class="{'active': selectedType === type.value}" @tap="selectType(type.value)">
							{{type.label}}
						</view>
					</view>
					<view class="filter-buttons">
						<view class="btn-reset" @tap="resetType">重置</view>
						<view class="btn-confirm" @tap="confirmType">确定</view>
					</view>
				</view>
			</u-popup>
		<!-- #endif -->
		</block>





		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0&&!isLoginding">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
		<!--登录遮罩-->
		<view class="full-noLogin" v-if="noLogin">
			<view class="full-noLogin-main">
				<view class="full-noLogin-text">
					您需要登录后才可以查看内容哦！
				</view>
				<view class="full-noLogin-btn">
					<view class="cu-btn bg-blue" @tap="goLogin()">
						立即登录
					</view>
				</view>
			</view>
		</view>
		<!--登录遮罩结束-->

		<view style="width: 100%; height: 100upx;background-color: #f6f6f6;"></view>
	</view>
</template>

<script>
	import template_page_mixin from '@/components/template_page_mixin.js'

	import waves from '@/components/xxley-waves/waves.vue';
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'

	export default {

		props: {
			curPage: {
				type: Number,
				default: 0
			}
		},
		name: "home",
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				iconimg: [],
				isLoginding: false,
				sqshow: true,
				sq1: true,
				sq2: false,
				sq3: false,
				isLogin: false,
				apploading: false,
				cardCur: 0,
				swiperList: [],
				rightList: [],
				leftList: [],
				contentsList: [],
				topContents: [],
				metaList: [],
				Topic: [],
				dotStyle: false,
				homeStyle: 0,
				swiperHeight: this.$API.SPhomeSwiperHeight(),
				towerStart: 0,
				direction: '100000',
				noticeSumof: false,
				noticeList: "",
				TabCur: 0,
				scrollLeft: 0,
				avatarurl: "../../static/user/avatar.png",
				page: 1,
				apppage: 1,
				moreText: "加载更多",
				isLoad: 0,
				noticeBarKey: 0,
				isLoging: 1,
				tabCurTab: 1,
				token: "",
				homeMode: 0, //首页模式
				topStyle: 0,
				actStyle: 0,
				swiperStyle: true,
				swiperOf: 0,
				iconOf: 0,
				noticeOf: 1,
				rzImg: this.$API.SPRz(),
				circleOf: 0,
				isLoading: 0,
				sousuok: "搜索...",
				dataLoad: true,
				hometop: 0,
				lunbo_of: 0,
				gonggao_of: 0,
				top_of: 0,
				pushAds: [],
				pushAdsInfo: null,
				bannerAds: [],
				bannerAdsInfo: null,
				submitStatus1: false,
				submitStatus2: false,
				submitStatus4: false,
				submitStatus5: false,
				current: 1,
				topList: [{
						order: "follow",
						name: "关注",
						parent: 0
					}, {
						order: "replyTime",
						name: "回复",
						parent: 0
					},
					{
						order: "created",
						name: "最新",
						parent: 0
					},

					{
						order: "views",
						name: "最热",
						parent: 0
					},
					{
						order: "likes",
						name: "获赞",
						parent: 0
					}

				],
				isHuaWei: this.$API.isHuaWei(),
				wzof: 0,
				isShowTop: this.$API.GetisShowTop(),
				tzof: 0,
				shopof: 0,
				orderCur: "replyTime",
				isSticky: false,
				postList: [],
				recommendSectionList: [],
				noticeSum: 0,
				swiperList2: [],
				userInfo: null,
				token: "",
				gonggao: "",
				isTy: false,
				noLogin: false,
				sy_appbox: false,
				appTopOf: 1,
				homeModApp: 0,
				isvip: 0,
				uid: 0,
				swiperBgcolor: "",
				scrollTop: 0,
				identifyCompany: 0,
				//软件库
				appiconimg: [],
				applist: [],

				// 排序相关
				showOrder: false,
				orderOptions: [{
						text: '最新投稿',
						value: 'created'
					},
					{
						text: '好评如潮',
						value: 'score'
					},
					{
						text: '讨论火热',
						value: 'commentsNum'
					},
					{
						text: '随便看看',
						value: 'random'
					}
				],
				selectedOrder: 'created',
				orderText: '最新投稿',

				// 系统筛选
				showSystem: false,
				systemOptions: [{
						label: '全部',
						value: ''
					},
					{
						label: 'Android',
						value: 'android'
					},
					{
						label: 'iOS',
						value: 'ios'
					}
				],
				selectedSystem: '',
				systemText: '系统',

				// 类型筛选  
				showType: false,
				typeOptions: [{
						label: '全部',
						value: ''
					},
					{
						label: '搬运',
						value: '1'
					},
					{
						label: '原创',
						value: '2'
					},
					{
						label: '金标',
						value: '3'
					},
					{
						label: '官方',
						value: '4'
					}
				],
				tagMap: {
					1: {
						text: '搬运',
						color: '#7c72ff'
					},
					2: {
						text: '原创',
						color: '#19be6b'
					},
					3: {
						text: '金标',
						color: '#ff6600'
					},
					4: {
						text: '官方',
						color: '#2979ff'
					}
				},
				selectedType: '',
				typeText: '类型',
				loadStatus: 'nomore'
			}
		},

		mounted() {
			var that = this;
			uni.$on('onShow', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				if (that.homeStyle == 1) {
					that.swiperBgcolor = "#ffffff"
				} else {
					that.swiperBgcolor = "#f4f4f4"
				}
				if (that.isHuaWei == 1) {
					that.openAuth();
				}
				console.log("onShow");
				uni.$emit('tOnLazyLoadReachBottom');
				that.updateNoticeList();
				localStorage.setItem('topList', JSON.stringify(that.topList));
				console.log("触发Tab-" + data + "||页面下标" + that.curPage);
				if (localStorage.getItem('userinfo')) {

					that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.userInfo.style = "background-image:url(" + that.userInfo.avatar + ");"
					that.group = that.userInfo.group;
					that.avatarurl = that.userInfo.avatar
					that.uid = that.userInfo.uid
					that.isvip = that.userInfo.isvip;
				} else {
					that.userInfo = null;
				}
				if (localStorage.getItem('token')) {

					that.token = localStorage.getItem('token');
				} else {
					that.token = "";
				}
				// #ifdef APP-PLUS
				//如果启动图还没有缓存过，第一次进来就不显示启动图了
				if (!localStorage.getItem('appStart')) {
					that.isStart = true;
				}
				////plus.navigator.setStatusBarStyle("dark")
				// #endif
				//获取缓存


				// 获取已开启的插件列表
				var cachedPlugins = localStorage.getItem('getPlugins');
				if (cachedPlugins) {
					const pluginList = JSON.parse(cachedPlugins);
					// 检查插件是否存在于插件列表中 
					// #ifdef APP-PLUS || H5
					that.sy_appbox = pluginList.includes('sy_appbox');
					// #endif
				}
				if (localStorage.getItem('token')) {

					that.token = localStorage.getItem('token');
				}
				//清除应用详情标识缓存
				if (localStorage.getItem('isinfoback')) {
					localStorage.removeItem('isinfoback');
				}
				that.userStatus();
				that.unreadNum();
				if (that.noticeSum > 0 && that.noticeSum <= 99) {
					that.noticeSumof = true;
				} else if (that.noticeSum > 99) {
					that.noticeSumof = true;
					noticeSum = "99+"
				}

			});
			uni.$on('onReachBottom', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				console.log("触发触底刷新");

				if (that.isLoad == 0) {
					that.loadMore();
				}
			});

			uni.$on('onPullDownRefresh', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				that.page = 1;
				if (that.sy_appbox) {
					that.getAppBoxInfo();
				} else {
					that.getSet();
				}
				that.getGongg();
				that.getadimg();
				that.getIconimg();
				that.userStatus();
				that.unreadNum();
				console.log("触发下拉刷新");

			});

			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif

		},
		beforeDestroy() {
			uni.$off('onReachBottom')
			uni.$off('onShow')
			uni.$off('onPullDownRefresh')
		},
		created() {
			var that = this;
			var cachedPlugins = localStorage.getItem('getPlugins');
			if (cachedPlugins) {
				const pluginList = JSON.parse(cachedPlugins);
				// 检查插件是否存在于插件列表中 
				// #ifdef APP-PLUS || H5
				that.sy_appbox = pluginList.includes('sy_appbox');
				// #endif
			}
			if (that.sy_appbox) {
				that.getAppBoxInfo();
			} else {
				that.getSet();
			}
			that.getGongg();

			that.getadimg();
			that.getIconimg();
			that.allCache();
			that.getCID();

			// #ifdef APP-PLUS || H5
			if (that.isvip == 0) {
				that.getAdsCache();
				that.getAds();
			}
			// #endif
			if (that.homeStyle == 1) {
				that.swiperBgcolor = "#ffffff"
			} else {
				that.swiperBgcolor = "#f4f4f4"
			}
			console.log("created");
		},
		methods: {
			updateNoticeList() {
				var that = this;
				if (localStorage.getItem('noticeList')) {
					that.noticeList = localStorage.getItem('noticeList');
				}
				that.noticeBarKey += 1;
			},
			getIconimg() {
				var that = this;
				uni.request({
					url: that.$API.SPiconimg(),
					method: 'GET',
					dataType: "json",
					success(res) {
						if (res.data instanceof Array) {
							that.iconimg = res.data;
						} else {
							that.iconimg = Object.values(res.data);
						}
						localStorage.setItem('iconimg', JSON.stringify(that.iconimg));
					},
					fail(error) {
						console.error(error);
					}
				});
			},
			getGongg() {
				var that = this;
				uni.request({
					url: that.$API.SPgonggao(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.flag = res.data.flag;
						that.findtop = res.data.findtop;
						that.hometop = res.data.hometop;
						that.noticeList = res.data.gonggao;
						that.sousuok = res.data.sousuok;
						that.lunbo_of = res.data.lunbo_of;
						that.gonggao_of = res.data.gonggao_of;
						that.top_of = res.data.top_of;
						that.act_of = res.data.act_of;
						that.weburl = res.data.weburl;
						that.gonggaotime = res.data.ggtime;
						localStorage.setItem('noticeList', that.noticeList);
					},
					fail(error) {}
				})

			},
			getSet() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						if (that.sy_appbox && that.homeModApp == 1) {
							// #ifdef APP-PLUS || H5
							that.homeMode = 3;
							// #endif
							// #ifdef MP
							that.homeMode = res.data.homeMode;
							// #endif
						} else {
							that.homeMode = res.data.homeMode;
						}
						console.log("首页：" + that.homeMode);
						that.topStyle = res.data.topStyle;
						that.actStyle = res.data.actStyle;
						that.circleOf = res.data.circleOf;
						that.tabCurTab = res.data.swiperinfo;
						that.swiperStyle = res.data.swiperStyle;
						that.swiperOf = res.data.swiperOf;
						that.iconOf = res.data.iconOf;
						that.homeStyle = res.data.homeStyle;
						that.wzof = res.data.wzof;
						that.tzof = res.data.tzof;
						that.shopof = res.data.shopof;
						localStorage.setItem('tabCurTab', that.tabCurTab);
						that.isLoginding = true;
						that.loading();
					},
					fail(error) {}
				})

			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			//获取客户端id用于消息通知
			getCID() {
				var that = this;
				let cid = ''
				// #ifdef APP-PLUS
				let pinf = plus.push.getClientInfo();
				cid = pinf.clientid;
				if (cid) {
					that.setClientId(cid);
				}
				// #endif
			},

			getRz(uid) {
				var that = this;
				that.$Net.request({

					url: that.$API.identifyStatus(),
					data: {
						"uid": uid
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						if (res.data.code == 1) {
							that.identifyCompany = res.data.data.identifyCompany;
							localStorage.setItem('identifyCompany', that.identifyCompany);
							console.log(that.identifyCompany);
						}
						var timer = setTimeout(function() {
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						console.log("网络开小差了哦");
						var timer = setTimeout(function() {
							clearTimeout('timer')
						}, 300)
					}
				})
			},

			handleSticky() {
				this.isSticky = true;
			},
			handleUnsticky() {
				this.isSticky = false;
			},
			setClientId(cid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('token')) {

					token = localStorage.getItem('token');
				} else {
					return false;
				}
				that.$Net.request({

					url: that.$API.setClientId(),
					data: {
						"clientId": cid,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {


						}
					},
					fail: function(res) {
						console.log(res)
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			// 瀑布流加载完毕事件
			handleWaterFallFinish() {

			},
			//获取并缓存广告
			getAds() {
				var that = this;
				// #ifdef APP-PLUS || H5
				//获取推流广告
				that.getAdsList(0);
				//获取横幅广告
				that.getAdsList(1);
				//#endif
				// #ifdef APP-PLUS
				//获取启动图广告
				that.getAdsList(2);
				//#endif
			},
			getAdsCache() {
				var that = this;
				if (localStorage.getItem('pushAds')) {
					that.pushAds = JSON.parse(localStorage.getItem('pushAds'));
				}

				if (localStorage.getItem('bannerAds')) {
					that.bannerAds = JSON.parse(localStorage.getItem('bannerAds'));

					var num = that.bannerAds.length;
					if (num > 0) {
						var rand = Math.floor(Math.random() * num);
						that.bannerAdsInfo = that.bannerAds[rand];
					}
				}
			},

			getAdsList(type) {
				var that = this;
				var data = {
					"type": type,
				}
				that.$Net.request({
					url: that.$API.adsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 100,
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							if (type == 0) {
								that.pushAds = res.data.data;
								localStorage.setItem('pushAds', JSON.stringify(that.pushAds));
							}
							if (type == 1) {
								that.bannerAds = res.data.data;

								localStorage.setItem('bannerAds', JSON.stringify(that.bannerAds));

							}
							if (type == 2) {
								that.startAds = res.data.data;
								localStorage.setItem('startAds', JSON.stringify(that.startAds));
							}
						}


					},
					fail: function(res) {

					}
				})
			},
			toScan() {
				var that = this;
				uni.scanCode({
					onlyFromCamera: false,
					scanType: ['barCode', 'qrCode'],
					success: function(res) {
						var text = res.result;
						var strUrl = "^((https|http|ftp|rtsp|mms)?://)" +
							"?(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?" +
							"(([0-9]{1,3}\.){3}[0-9]{1,3}" +
							"|" +
							"([0-9a-z_!~*'()-]+\.)*" +
							"([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\." +
							"[a-z]{2,6})" +
							"(:[0-9]{1,4})?" +
							"((/?)|" +
							"(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$";
						var urlDemo = new RegExp(strUrl);
						if (urlDemo.test(text)) {
							var linkRule = that.$API.GetLinkRule();
							var linkRuleArr = linkRule.split("{cid}");
							if (text.indexOf(linkRuleArr[0]) != -1) {
								//是本站链接
								var cid = text;
								for (var i in linkRuleArr) {
									cid = cid.replace(linkRuleArr[i], "");
								}
								uni.navigateTo({
									url: '/pages/contents/info?cid=' + cid
								});
							} else {
								// #ifdef MP
								uni.setClipboardData({
									data: href,
									success: () =>
										uni.showToast({
											title: '链接已复制'
										})
								})
								// #endif
								// #ifdef APP-PLUS
								plus.runtime.openWeb(href)
								// #endif
							}
						} else {
							that.scanLogin(text);
						}
					}
				});
			},
			swiperclick(index) {
				const item = this.swiperList[index];
				if(this.homeMode==3){
					this.toAppInfo(item.id);
				}else{
					if (item.type === 'image') {
						this.toInfo(item)
					} else {
						this.goAds2(item.zt);
					}
				}
				
			},
			goAds2(url) {
				var that = this;
				// #ifdef APP-PLUS
				plus.runtime.openWeb(url);
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			goMsg() {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.$emit('goMsg', 2);
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			//全部请求
			loading() {
				var that = this;
				// that.page = 1;
				//that.getSwiper();
				if (that.homeMode == 1) {
					that.getTopPic();
					that.getSwiper();
					that.getMetaList();
					that.getTopContents();
					that.getContentsList(false);
				}
				if (that.homeMode == 2) {
					that.getRecommendSectionList();
					that.getSwiperList();
					that.getPostList(false, false);
				}
				if (that.homeMode == 3) { //软件库模式
					that.getAppSwiperList();
					that.getAppList();
					that.getTopAppList();
				}
			},
			tabSelect(e) {
				var that = this;
				// that.TabCur = e.currentTarget.dataset.id;
				that.page = 1;
				that.scrollLeft = (e.currentTarget.dataset.id - 1) * 60;
				that.contentsList = [];
				that.dataLoad = false;
				if (that.TabCur == 0) {
					that.getContentsList(false);
				} else {
					that.getMetaContents(false, that.TabCur);
				}
			},
			loadMore() {
				var that = this;
				that.moreText = "努力加载中...";
				that.isLoad = 1;
				if (that.homeMode == 1) {
					console.log("加载调试");
					that.getContentsList(true);
				}
				if (that.homeMode == 2) {
					that.getPostList(true, false);
				}
				if (that.homeMode == 3) {
					that.getAppList(true);
				}
			},
			//公共缓存
			allCache() {
				var that = this;
				var meta = that.TabCur;

				if (localStorage.getItem('tabCurTab')) {
					that.tabCurTab = localStorage.getItem('tabCurTab');
				}
				if (localStorage.getItem('swiperList2')) {
					that.swiperList2 = JSON.parse(localStorage.getItem('swiperList2'));
				}
				if (localStorage.getItem('swiperList')) {
					that.swiperList = JSON.parse(localStorage.getItem('swiperList'));
					var timer = setTimeout(function() {
						that.isLoading = 1;
						clearTimeout('timer')
					}, 300)
				}
				if (localStorage.getItem('identifyCompany')) {
					that.identifyCompany = localStorage.getItem('identifyCompany');
				} else {
					that.getRz()
				}
				if (localStorage.getItem('iconimg')) {
					that.iconimg = JSON.parse(localStorage.getItem('iconimg'));
				}
				if (localStorage.getItem('noticeList')) {
					that.noticeList = localStorage.getItem('noticeList');
				}
				if (localStorage.getItem('recommendSectionList')) {
					that.recommendSectionList = JSON.parse(localStorage.getItem('recommendSectionList'));
				}
				if (localStorage.getItem('postList')) {
					that.postList = JSON.parse(localStorage.getItem('postList'));
				}
				if (localStorage.getItem('metaList')) {
					that.metaList = JSON.parse(localStorage.getItem('metaList'));
				}
				if (localStorage.getItem('contentsList_0')) {
					that.contentsList = JSON.parse(localStorage.getItem('contentsList_0'));
				}
				if (localStorage.getItem('topContents')) {
					that.topContents = JSON.parse(localStorage.getItem('topContents'));
				}

				if (localStorage.getItem('Topic')) {
					that.Topic = JSON.parse(localStorage.getItem('Topic'));
				}
			},
			change(index) {
				var that = this;
				if (index == 0) {
					if (!localStorage.getItem('token')) {
						uni.showToast({
							title: "请先登录",
							icon: 'none'
						})
						return false;
					}
				}
				that.current = index;
				that.orderCur = that.topList[that.current].order;
				console.log(that.orderCur + index);
				that.page = 1;
				if (index == 0) {
					that.getFollowPostList(false);
				} else {
					that.getPostList(false, false);
				}

			},
			goAllSection() {
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/section'
				});
			},
			goSection(id) {
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/home?id=' + id
				});
			},
			getAppSwiperList() {
				var that = this;
				var data = {
					isswiper: '1'
				};

				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getAppList",
						"getapp_page": 1,
						"getapp_limit": 20,
						"getapp_order": "created",
						"getapp_if": JSON.stringify(that.$API.removeObjectEmptyKey(data))
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if (res.data.code == 200) {
							const list = res.data.data || [];
							// 转换数据格式以适配轮播图组件
							that.swiperList = list.map(item => {
								return {
									id: item.id,
									type: 'image',
									url: item.logo,
									title: item.name.length > 10 ? item.name.substring(0,10) + '...' : item.name,
									score: item.score,
									scoreNum: Number(item.score),
									intro: (item.versionInfo || '').length > 10 ? (item.versionInfo || '').substring(0,10) + '...' : (item.versionInfo || '')
								};
							});

							that.swiperList = that.swiperList;

							localStorage.setItem('swiperList', JSON.stringify(that.swiperList));
						} else {
							console.log(res.data.msg);
						}
					},
					fail(error) {
						console.log(error);
						uni.showToast({
							title: "网络开小差了",
							icon: 'none'
						});
					}
				});
			},
			getAppList(isPage) {
				var that = this;
				if (that.apploading) return;
				if (!isPage) {
					that.apppage = 1;
					that.dataLoad = true;
					that.apploading = true;
				}
				var page = that.apppage;

				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				if (isPage) {
					page++;
				}
				var data = {
					type: that.selectedType,
					system: that.selectedSystem,
				};
				// commentsNum
				// score
				// random
				// created
				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getAppList",
						"getapp_page": page,
						"getapp_limit": 10,
						"getapp_order": that.selectedOrder,
						"getapp_if": JSON.stringify(that.$API.removeObjectEmptyKey(data))
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if (res.data.code == 200) {
							// 处理返回数据
							const data = res.data.data || [];
							const list = data.map(item => {
								// 处理标签类型

								return {
									...item,
									tagInfo: that.tagMap[item.type] || {
										text: '未知',
										color: '#999'
									},
									size: that.formatSize(item.size)
								};
							});
							that.isLoad = 0;
							if (isPage) {
								if (data.length < 1) {
									that.moreText = "没有更多数据了";
								}
								that.applist = [...that.applist, ...list];
								that.apppage = page;

							} else {
								that.applist = list;
								that.apppage = 1;
							}

						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
						that.apploading = false;
					},
					fail(error) {
						that.apploading = false;
						console.log(error);
						uni.showToast({
							title: "网络开小差了",
							icon: 'none'
						});
					}
				});
			},
			getTopAppList() {
				var that = this;

				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					// type
					// system
					istop: '1'
					// isswiper
					// authorId
				};
				var orderCur = "created";
				// commentsNum
				// score
				// random
				// created
				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getAppList",
						"getapp_page": 1,
						"getapp_limit": 10,
						"getapp_order": orderCur,
						"getapp_if": JSON.stringify(that.$API.removeObjectEmptyKey(data))
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if (res.data.code == 200) {
							// 处理返回数据
							const list = res.data.data;

							that.appiconimg = list;
							console.log(that.appiconimg);

						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					},
					fail(error) {
						console.log(error);
						uni.showToast({
							title: "网络开小差了",
							icon: 'none'
						});
					}
				});
			},
			getDowns(downs) {
				var that = this;
				if (downs <= 999) {
					return downs;
				} else if (downs > 999 && downs <= 9999) {
					return (downs / 1000).toFixed(1) + "千";
				} else if (downs > 9999) {
					return (downs / 10000).toFixed(1) + "万";
				}
			},
			getSectionList() {
				var that = this;
				that.$Net.request({
					url: that.$API.sectionList(),
					data: {
						"limit": 20,
						"page": 1,
						"searchKey": that.searchText,
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh()
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							var parentList = [];
							for (var i in list) {
								if (list[i].parent == 0) {
									list[i].subList = [];
									parentList.push(list[i]);
								}
							}
							for (var j in list) {
								if (list[j].parent != 0) {
									for (var p in parentList) {
										if (list[j].parent == parentList[p].id) {
											parentList[p].subList.push(list[j]);
										}
									}
								}
							}
							that.sectionList = parentList;
							localStorage.setItem('sectionList', JSON.stringify(that.sectionList));
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.stopPullDownRefresh()
						that.moreText = "加载更多";
						that.isLoad = 0;
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getFollowPostList(isPage) {
				var that = this;
				var page = that.page;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				if (isPage) {
					page++;
				}
				that.$Net.request({
					url: that.$API.followPosts(),
					data: {
						"limit": 10,
						"page": page,
						"order": 'created',
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								var num = res.data.data.length;
								var rand = Math.floor(Math.random() * num);
								var pushAdsInfo = null;
								// #ifdef APP-PLUS || H5
								if (that.isvip == 0) {
									if (localStorage.getItem('pushAds')) {
										var pushAds = JSON.parse(localStorage.getItem('pushAds'));
										var adsNum = pushAds.length;
										if (adsNum > 0) {
											var adsRand = Math.floor(Math.random() * adsNum);
											pushAdsInfo = pushAds[adsRand];
											pushAdsInfo.isAds = 1;
										}
									}
								}
								// #endif
								var postList = [];
								for (var i in list) {

									list[i].isAds = 0;
									postList.push(list[i]);
									// #ifdef APP-PLUS || H5
									var isAds = Math.round(Math.random());
									if (isAds == 1) {
										if (i == rand && pushAdsInfo != null) {
											postList.push(pushAdsInfo);
										}
									}
									// #endif
								}
								if (isPage) {
									that.page++;
									that.postList = that.postList.concat(postList);
								} else {
									that.postList = postList;
								}
							} else {
								if (isPage) {
									that.moreText = "没有更多数据了";
								} else {
									that.postList = [];
								}

							}
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
						that.isLoading = 1;
					},
					fail: function(res) {
						that.isLoad = 0;
						that.moreText = "加载更多";
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
						that.isLoading = 1;
					}
				})
			},
			getSwiperList() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"isswiper": '1'
				}
				that.$Net.request({
					url: that.$API.postList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 5,
						"page": 1,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							var list = res.data.data;
							var swiper = [];
							if (list.length > 0) {
								for (var i in list) {
									if (list[i].images.length > 0) {
										var arr = {
											id: list[i].id,
											type: 'image',
											url: list[i].images[0],
											title: list[i].title,
											intro: that.subText(list[i].text, 20),
										}
										swiper.push(arr);
									}

								}
								that.swiperList = swiper.concat(that.swiperList2);
							} else {
								that.swiperList = that.swiperList2;

							}
							localStorage.setItem('swiperList', JSON.stringify(that.swiperList));
						}

					},
					fail: function(res) {

					}
				})
			},
			getRecommendSectionList(isLogin) {
				var that = this;
				if (that.submitStatus4) {
					return false;
				}
				that.submitStatus4 = true;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}
				var data = {
					"isrecommend": 1,
				}
				that.$Net.request({
					url: that.$API.sectionList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 8,
						"page": 1,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus4 = false;
						uni.stopPullDownRefresh()
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							that.recommendSectionList = res.data.data;
							localStorage.setItem('recommendSectionList', JSON.stringify(that
								.recommendSectionList));
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true
								} else {
									that.getRecommendSectionList(true);
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus4 = false;
						uni.stopPullDownRefresh()

					}
				})
			},

			getadimg() {
				var that = this;
				uni.request({
					url: that.$API.SPadimg(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.adimage_sl = res.data.adimage_sl;

						// 清空之前的数据
						that.swiperList2 = [];

						// 动态设置swiperList2数组内容
						for (let i = 1; i <= that.adimage_sl; i++) {
							that.swiperList2.push({
								url: res.data['adimage' + i],
								zt: res.data['link_url' + i]
							});
						}
						localStorage.setItem('swiperList2', JSON.stringify(that.swiperList2));
					},
					fail(error) {
						console.log(error);
					}
				});
			},
			getPostList(isPage, isLogin) {
				var that = this;
				if (that.submitStatus2) {
					return false;
				}
				that.submitStatus2 = true;
				var page = that.page;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				if (isPage) {
					page++;
				}

				var data = {
					"status": "1",
					"isTop": '0'
				};
				var order = that.order;
				if (order == 'recommend') {
					data.isrecommend = 1;
				} else {
					data.order = order;
				}

				that.$Net.request({
					url: that.$API.postList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 10,
						"page": page,
						"order": that.orderCur,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus2 = false;
						that.isLoad = 0;
						if (res.data.code == 1) {
							that.noLogin = false;
							var list = res.data.data;
							list.forEach(item => item.isTop = '0');

							if (!isPage && that.isShowTop) {
								var globalData = {
									"status": "1",
									"isTop": '2'
								};
								that.$Net.request({
									url: that.$API.postList(),
									data: {
										"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(
											globalData)),
										"limit": 5,
										"page": 1,
										"token": token
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "get",
									dataType: 'json',
									success: function(globalRes) {
										if (globalRes.data.code == 1) {
											var globalTopList = globalRes.data.data;
											globalTopList.forEach(item => item.isTop = '2');

											that.postList = globalTopList.concat(list);
										}
										localStorage.setItem('postList', JSON.stringify(that
											.postList));
									},
									fail: function(globalRes) {}
								});
							} else {
								if (isPage) {
									that.page++;
									that.postList = that.postList.concat(list);
								} else {
									that.postList = list;
								}
								localStorage.setItem('postList', JSON.stringify(that.postList));
							}
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true;
								} else {
									that.getPostList(isPage, true);
								}
							}
						}
						that.isLoading = 1;
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus2 = false;
						that.isLoad = 0;
						that.moreText = "加载更多";
						that.isLoading = 1;
					}
				});
			},

			getSwiper(isLogin) {
				var that = this;
				if (that.submitStatus5) {
					return false;
				}
				that.submitStatus5 = true;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				var data = {
					"type": "post",
					"isswiper": 1
				}


				that.$Net.request({
					url: that.$API.getContentsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 8,
						"page": 1,
						"order": "modified",
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus5 = false;
						if (res.data.code == 1) {
							that.noLogin = false;
							var list = res.data.data;
							var swiper = [];
							if (list.length > 0) {
								for (var i in list) {
									if (list[i].images.length > 0) {
										var arr = {
											cid: list[i].cid,
											type: 'image',
											url: list[i].images[0],
											title: list[i].title,
											intro: that.subText(list[i].text, 20),
										}
										swiper.push(arr);
									}

								}
								that.swiperList = swiper.concat(that.swiperList2);
							} else {
								that.swiperList = that.swiperList2;
							}
							localStorage.setItem('swiperList', JSON.stringify(that.swiperList));
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true
								} else {
									that.getSwiper(true)
								}
							}
						}
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus5 = false;

					}
				})
			},

			getMetaList() {
				var that = this;
				var data = {
					"type": "category"
				}
				that.$Net.request({
					url: that.$API.getMetasList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 15,
						"page": 1,
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								var meta = [{
									mid: 0,
									name: "推荐",
									parent: 0
								}];
								that.metaList = meta.concat(list);

							} else {
								that.metaList = [];
							}
							localStorage.setItem('metaList', JSON.stringify(that.metaList));
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getTopPic() {
				var that = this;
				var data = {
					"isrecommend": "1"
				}
				that.$Net.request({
					url: that.$API.getMetasList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 4,
						"page": 1,
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								that.Topic = list;

							} else {
								that.Topic = [];
							}
							localStorage.setItem('Topic', JSON.stringify(that.Topic));
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getTopContents() {
				var that = this;
				var data = {
					"type": "post",
					"istop": 1,
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				that.$Net.request({
					url: that.$API.getContentsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 5,
						"page": 1,
						"order": "modified",
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {

								var contentsList = [];
								//将自定义字段获取并添加到数据
								var curFields = that.$API.GetFields();
								for (var i in list) {
									var fields = list[i].fields;
									if (fields.length > 0) {
										for (var j in fields) {
											if (curFields.indexOf(fields[j].name) != -1) {
												list[i][fields[j].name] = fields[j].strValue;
											}
										}
									}
									contentsList.push(list[i]);
								}

								that.topContents = contentsList;
							} else {
								that.topContents = [];
							}
							localStorage.setItem('topContents', JSON.stringify(that.topContents));
						}
					},
					fail: function(res) {}
				})
			},
			getContentsList(isPage) {
				var that = this;

				if (that.submitStatus1) {
					return false;
				}
				that.submitStatus1 = true;
				var data = {
					"type": "post",
					"istop": 0,
				}

				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var page = that.page;
				if (isPage) {
					page++;
				}

				that.$Net.request({
					url: that.$API.getContentsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 5,
						"page": page,
						"order": "created",
						"token": token
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.submitStatus1 = false;
						that.isLoad = 0;
						that.moreText = "加载更多";
						if (!isPage) {
							that.dataLoad = true;
						}
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {

								var num = res.data.data.length;
								var rand = Math.floor(Math.random() * num);
								var pushAdsInfo = null;
								// #ifdef APP-PLUS || H5
								if (localStorage.getItem('pushAds')) {
									var pushAds = JSON.parse(localStorage.getItem('pushAds'));
									var adsNum = pushAds.length;
									if (adsNum > 0) {
										var adsRand = Math.floor(Math.random() * adsNum);
										pushAdsInfo = that.pushAds[adsRand];
										pushAdsInfo.isAds = 1;
									}
								}
								// #endif
								var contentsList = [];
								//将自定义字段获取并添加到数据
								var curFields = that.$API.GetFields();
								for (var i in list) {
									var fields = list[i].fields;
									if (fields.length > 0) {
										for (var j in fields) {
											if (curFields.indexOf(fields[j].name) != -1) {
												list[i][fields[j].name] = fields[j].strValue;
											}
										}
									}
									contentsList.push(list[i]);
									// #ifdef APP-PLUS || H5
									var isAds = Math.round(Math.random());
									if (isAds == 1) {
										if (i == rand && pushAdsInfo != null) {
											contentsList.push(pushAdsInfo);
										}
									}

									// #endif

								}
								var num = contentsList.length;
								if (isPage) {
									that.page++;
									that.contentsList = that.contentsList.concat(contentsList);
								} else {
									that.contentsList = contentsList;
								}


								localStorage.setItem('contentsList_0', JSON.stringify(that.contentsList));
							} else {
								that.moreText = "—— 到底啦 ——";
							}
						}
					},
					fail: function(res) {
						that.submitStatus1 = false;
						that.moreText = "加载更多";
						that.isLoad = 0;
					}
				})
			},
			getMetaContents(isPage, meta) {
				var that = this;
				var data = {
					"mid": meta,
					"type": "post"
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var page = that.page;
				if (isPage) {
					page++;
				}
				that.$Net.request({
					url: that.$API.getMetaContents(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 5,
						"page": page,
						"order": "created",
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isPage) {
							that.dataLoad = true;
						}
						that.isLoad = 0;

						that.moreText = "加载更多";
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								var contentsList = [];
								//将自定义字段获取并添加到数据
								var curFields = that.$API.GetFields();
								for (var i in list) {
									var fields = list[i].fields;
									if (fields.length > 0) {
										for (var j in fields) {
											if (curFields.indexOf(fields[j].name) != -1) {
												list[i][fields[j].name] = fields[j].strValue;
											}
										}
									}
									contentsList.push(list[i]);
								}
								if (isPage) {
									that.page++;
									that.contentsList = that.contentsList.concat(contentsList);
								} else {
									that.contentsList = contentsList;
								}


								localStorage.setItem('contentsList_' + meta, JSON.stringify(that
									.contentsList));
							} else {
								that.moreText = "—— 到底啦 ——";
							}
						}
					},
					fail: function(res) {

						that.moreText = "加载更多";
						that.isLoad = 0;
					}
				})
			},
			userStatus() {
				var that = this;
				that.$Net.request({

					url: that.$API.userStatus(),
					data: {
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 0) {
							localStorage.removeItem('userinfo');
							localStorage.removeItem('token');
							that.token = "";
							that.userinfo = null;
						} else if (res.data.code == 1) {
							that.avatarurl = res.data.data.avatar;
							if (localStorage.getItem('identifyCompany')) {
								that.identifyCompany = localStorage.getItem('identifyCompany');
							} else {
								var myuid = res.data.data.uid
								that.getRz(myuid)
							}
							var userInfo = JSON.parse(localStorage.getItem('userinfo'));
							if (res.data.data.isvip) {
								userInfo.isvip = res.data.data.isvip;
							}
							if (res.data.data.vip) {
								userInfo.vip = res.data.data.vip;
							}
							if (res.data.data.avatar) {
								userInfo.avatar = res.data.data.avatar;
							}
							localStorage.setItem('userinfo', JSON.stringify(userInfo));
						}
					},
					fail: function(res) {
						console.log(res)
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			unreadNum() {
				var that = this;
				if (localStorage.getItem('noticeSum')) {
					that.noticeSum = Number(localStorage.getItem('noticeSum'));
				}
			},

			toForeverblog() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/foreverblog'
				});

			},
			toComments() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/comments'
				});
			},
			toSearch() {
				var that = this;
				if (that.noLogin) {
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				uni.navigateTo({
					url: '/pages/contents/search'
				});
			},
			toUsers() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/userlist'
				});
			},
			toCategoryContents(title, id) {
				var that = this;
				var type = "meta";
				uni.navigateTo({
					url: '/pages/contents/contentlist?title=' + title + "&type=" + type + "&id=" + id
				});
			},

			toAllContents() {
				var that = this;
				var type = "all";
				var title = "全部文章";
				uni.navigateTo({
					url: '/pages/contents/contentlist?title=' + title + "&type=" + type + "&id=0"
				});
			},
			toInfo(data) {
				var that = this;
				if (that.homeMode == 1) {
					uni.navigateTo({
						url: '/pages/contents/info?cid=' + data.cid + "&title=" + data.title
					});
				}
				if (that.homeMode == 2) {
					uni.navigateTo({
						url: '/pages/forum/info?id=' + data.id
					});
				}
			},
			subText(text, num) {
				if (text.length < null) {
					return text.substring(0, num) + "……"
				} else {
					return text;
				}

			},
			toShop() {
				var that = this;
				uni.navigateTo({
					url: '/pages/shop/shop'
				});
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				return result;
			},
			toGroup() {
				var url = that.$API.GetGroupUrl();
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},

			toImagetoday() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/imagetoday'
				});
			},
			toTopContents(title, id) {
				var that = this;
				var type = "meta";
				uni.navigateTo({
					url: '/pages/contents/contentlist?title=' + title + "&type=top&id=" + id
				});
			},
			goUserInfo() {

				var that = this;
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				uni.$emit('goUser', 0);
			},
			toMetas() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/metas'
				});
			},

			goCategory() {
				var that = this;
				if (that.homeMode == 1) {
					uni.navigateTo({
						url: '/pages/contents/allcategory'
					});
				}
				if (that.homeMode == 2) {
					uni.navigateTo({
						url: '/pages/forum/section'
					});
				}
				if (that.homeMode == 3) {
					uni.$emit('goFind', 1);
				}

			},
			closeUpdate() {
				var that = this;
				that.Update = 0;
			},
			toRand() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/randlist'
				});
			},
			goPage() {
				var that = this;
				uni.navigateTo({
					url: '/pages/tabPage/allSection'
				});
			},
			replaceSpecialChar(text) {
				text = text.replace(/&quot;/g, '"');
				text = text.replace(/&amp;/g, '&');
				text = text.replace(/&lt;/g, '<');
				text = text.replace(/&gt;/g, '>');
				text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			toLink(text, lgof) {
				var that = this;
				if (lgof == "true") {
					if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
						uni.showToast({
							title: "请先登录哦",
							icon: 'none'
						})
						return false;
					}
				}
				uni.navigateTo({
					url: text
				});
			},
			scanLogin(text) {
				var that = this;
				var token;
				if (!localStorage.getItem('token')) {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/user/scan?text=' + text
				});
			},

			goLogin() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/login'
				});
			},
			openAuth() {
				this.$refs['authpup'].open();
			},
			// changeMode() {
			// 	this.homeMode = (this.homeMode % 3) + 1;
			// 	switch (this.homeMode) {
			// 		case 1:
			// 			uni.showToast({
			// 				title: '已切换为文章',
			// 				icon: 'success',
			// 				duration: 2000
			// 			});
			// 			break;
			// 		case 2:
			// 			uni.showToast({
			// 				title: '已切换为帖子',
			// 				icon: 'success',
			// 				duration: 2000
			// 			});
			// 			break;
			// 		case 3:
			// 			uni.showToast({
			// 				title: '已切换为应用',
			// 				icon: 'success',
			// 				duration: 2000
			// 			});
			// 			break;
			// 		default:
			// 			break;
			// 	}
			// 	this.loading();
			// },
			formatSize(size) {
				if (!size) {
					return '未知大小';
				}

				if (size >= 1024 * 1024) {
					return (size / (1024 * 1024)).toFixed(1) + 'Gb';
				} else if (size >= 1024) {
					return (size / 1024).toFixed(1) + 'Mb';
				} else {
					return size.toFixed(1) + 'Kb';
				}
			},
			// 显示排序选择器
			showOrderPicker() {
				console.log('showOrderPicker called');
				this.showOrder = true;
			},

			// 确认排序方式
			confirmOrder(e) {
				const selectedOption = this.orderOptions[e[0]];
				if (selectedOption) {
					this.selectedOrder = selectedOption.value;
					this.orderText = selectedOption.text;
					this.showOrder = false;

					// 重新获取数据
					this.page = 1;
					this.getAppList(false);
				}
			},

			// 取消选择
			cancelOrder() {
				this.showOrder = false;
			},

			// 显示系统筛选
			showSystemFilter() {
				console.log('showSystemFilter called');
				this.showSystem = true;
			},

			// 选择系统
			selectSystem(value) {
				this.selectedSystem = value;
			},

			// 重置系统筛选
			resetSystem() {
				this.selectedSystem = '';
				this.systemText = '系统';
				this.showSystem = false;
				this.getAppList();
			},

			// 确认系统筛选
			confirmSystem() {
				const option = this.systemOptions.find(item => item.value === this.selectedSystem);
				this.systemText = option ? option.label : '系统';
				this.showSystem = false;

				// 重新获取数据
				this.page = 1;
				this.getAppList(false);
			},

			// 显示类型筛选
			showTypeFilter() {
				console.log('showTypeFilter called');
				this.showType = true;
			},

			// 选择类型
			selectType(value) {
				this.selectedType = value;
			},

			// 重置类型筛选
			resetType() {
				this.selectedType = '';
				this.typeText = '类型';
				this.showType = false;
				this.getAppList(false);
			},

			// 确认类型筛选
			confirmType() {
				const option = this.typeOptions.find(item => item.value === this.selectedType);
				this.typeText = option ? option.label : '类型';
				this.showType = false;

				// 重新获取数据
				this.page = 1;
				this.getAppList(false);
			},
			toAppInfo(id) {
				uni.navigateTo({
					url: '/pages/plugins/sy_appbox/info?id=' + id
				});
			},
			getAppBoxInfo() {
				var that = this;
				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getConfig",
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if (res.data.code == 200) {
							that.appTopOf = res.data.data.appTopOf;
							that.homeModApp = res.data.data.homeModApp;
						} else {
							console.log(res.data);
						}
						that.getSet();
					},
					fail(error) {
						console.log(error);
						uni.showToast({
							title: "网络开小差了",
							icon: 'none'
						})
						that.getSet();
					}
				})

			},
			// 轮播图切换事件
			swiperChange(e) {
				this.current = e.detail.current;
			},
		},

		// #ifdef APP-PLUS
		components: {
			waves
		},
		// #endif

		// #ifdef H5 || MP
		components: {
			waves
		},
		// #endif

	}
</script>
<style lang="scss" scoped>
	@import "@/static/styles/home.scss";

	body {
		background-color: #f6f6f6;
	}

	.bg-hui {
		background-color: #f6f6f6;
	}

	/* 自定义导航栏内容 end */
	.image-circle {
		// padding: 95rpx;
		width: 120rpx;
		height: 120rpx;
		font-size: 40rpx;
		font-weight: 300;
		position: relative;
		box-shadow: 0rpx 4rpx 20rpx #e7e7e7;
		border-radius: 40rpx;
	}

	.data-box-2 {
		padding: 0;
		margin: 20upx 20upx;
		border-radius: 20upx;
		box-shadow: 0 0 50upx 0 rgba(0, 0, 0, .04);
	}

	.data-box-2-banner {
		padding: 0;
		margin: 20upx 0;
		border-radius: 20upx;
		box-shadow: 0 0 50upx 0 rgba(0, 0, 0, .04);
	}

	.data-box-2 .cu-bar {
		border-radius: 20upx;
	}

	.image-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border-radius: 40rpx;
	}

	/* 间隔线 start*/
	.tn-strip-bottom {
		width: 100%;
		border-bottom: 1rpx solid rgba(241, 241, 241, 0.8);
	}

	/* 间隔线 end*/
	.header-icon {
		font-size: 36rpx;
	}

	.bq-box {
		padding: 10rpx 40rpx;
		background-color: #c9edff;
		color: #3ca9c9;
		margin: 30rpx 10rpx 0rpx 10rpx;
	}

	.down-button {
		border-radius: 40rpx;
		background-color: #3cc9a4;
		padding: 10rpx 20rpx;
		color: white;
		font-size: 24rpx;
	}

	.icon5__item--icon {
		width: 100rpx;
		height: 100rpx;
	}

	.app-box {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.app-box-body {
		flex: 1;
		display: flex;
		margin-right: 20rpx;
		min-width: 0; // 防止flex子元素溢出
		align-items: center;
	}

	.app-box-logo {
		width: 110rpx;
		height: 110rpx;
		flex-shrink: 0; // 防止图片缩小
	}

	.app-box-content {
		flex: 1;
		margin-left: 20rpx;
		min-width: 0; // 防止flex子元素溢出
	}

	.app-box-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		width: 400rpx; // 限制标题宽度
	}

	.app-box-down {
		background-color: #3cc9a4;
		color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 100rpx;
	}

	.app-box-tag {

		color: #fff;
		padding: 6rpx 6rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		margin-right: 10rpx;
	}

	.text-cut {
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}

	.app-box-info {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;

		text {
			margin-right: 10rpx;
		}
	}

	.app-box-down {
		background-color: #3cc9a4;
		color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 100rpx;
		white-space: nowrap; // 防止文字换行
	}

	.app-tag-1 {
		background-color: deepskyblue;
	}

	.app-tag-2 {
		background-color: #57d1b1;
	}

	.app-box-tags {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		font-size: 28rpx;
	}

	.app-tag {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		color: #ffffff;
		margin-right: 12rpx;
		font-size: 24rpx;
	}

	.app-category-tag {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		background-color: #f5f5f5;
		color: #666666;
		margin-right: 12rpx;
		font-size: 24rpx;
	}

	/* 筛选栏样式 */
	.filter-bar {
		display: flex;
		padding: 20rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.filter-item {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #333;
	}

	.filter-item .cuIcon-unfold {
		margin-left: 4rpx;
		font-size: 24rpx;
		color: #999;
	}

	/* 筛选弹窗样式 */
	.filter-popup {
		background: #fff;
		padding: 30rpx;
		border-radius: 20rpx 20rpx 0 0;
	}

	.filter-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.filter-options {
		display: flex;
		flex-wrap: wrap;
	}

	.filter-option {
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		border: 1px solid #eee;
		border-radius: 30rpx;
		margin: 0 20rpx 20rpx 0;
		font-size: 28rpx;
	}

	.filter-option.active {
		background: #3cc9a4;
		color: #fff;
		border-color: #3cc9a4;
	}

	.filter-buttons {
		display: flex;
		margin-top: 40rpx;
	}

	.btn-reset,
	.btn-confirm {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		margin: 0 20rpx;
		font-size: 30rpx;
	}

	.btn-reset {
		background: #f5f5f5;
		color: #666;
	}

	.btn-confirm {
		background: #3cc9a4;
		color: #fff;
	}

	.text-title-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	// 修改样式部分
	.tn-text-ellipsis2 {
		width: 140rpx; // 限制宽度与父容器一致
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: block; // 改为块级元素
	}

	/* 添加加载动画相关样式 */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;

		.loading-text {
			font-size: 26rpx;
			color: #909399;
			margin-top: 20rpx;
		}
	}

	.custom-swiper {
		height: 360rpx;
		margin: 20rpx;
		border-radius: 32rpx;
		overflow: hidden;

		.swiper-item {
			position: relative;
			width: 100%;
			height: 100%;
			border-radius: 32rpx;
			overflow: hidden;

			// 背景图层
			.swiper-bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				overflow: hidden;
				border-radius: 32rpx;

				// 背景图片
				.bg-image {
					position: absolute;
					top: -20%;
					left: -20%;
					width: 140%;
					height: 140%;
					filter: blur(5px);
					transform: scale(1.5);
					opacity: 0.8;
				}

				// 渐变遮罩
				.dark-overlay {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0.6) 100%);
					border-radius: 32rpx;
					backdrop-filter: blur(5px);
				}
			}

			// 内容层
			.swiper-content {
				position: absolute;
				bottom: 0;
				left: 0;
				width: 100%;
				padding: 40rpx;
				z-index: 1;

				.app-info {
					display: flex;
					align-items: center;

					.app-icon {
						width: 120rpx;
						height: 120rpx;
						border-radius: 24rpx;
						box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
					}

					.app-text {
						margin-left: 24rpx;

						.app-name {
							display: block;
							font-size: 32rpx;
							font-weight: bold;
							color: #ffffff;
							margin-bottom: 8rpx;
							text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.4);
						}

						.app-rating {
							display: flex;
							align-items: center;
							margin-bottom: 8rpx;

							.score {
								color: #ffffff;
								font-size: 28rpx;
								margin-right: 8rpx;
								text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
							}

							.star {
								color: rgba(255, 255, 255, 0.5);
								font-size: 28rpx;

								&.active {
									color: #ffd700;
									text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.4);
								}
							}
						}

						.app-desc {
							display: block;
							font-size: 24rpx;
							color: rgba(255, 255, 255, 0.8);
							width: 400rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
						}
					}
				}
			}
		}
	}

	// 指示点样式
	/deep/ .uni-swiper-dots {
		bottom: 24rpx;

		.uni-swiper-dot {
			width: 6rpx;
			height: 6rpx;
			border-radius: 3rpx;
			margin: 0 4rpx;
			background: rgba(255, 255, 255, 0.4);
			transition: all 0.3s ease;

			&.uni-swiper-dot-active {
				width: 18rpx;
				background: #ffffff;
				box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.4);
			}
		}
		
	}
	.bg-h{
		background-color: #f3f3f3 !important;
	}
</style>