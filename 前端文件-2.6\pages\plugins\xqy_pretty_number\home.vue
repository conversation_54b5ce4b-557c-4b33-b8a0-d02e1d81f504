<template>
  <view class="pretty-number-center" :class="$store.state.AppStyle">
    <!-- 顶部导航栏优化 -->
    <view class="nav-bar" :style="[{height: CustomBar + 'px'}]">
      <view class="nav-content" :style="{'height': CustomBar + 'px','padding-top': StatusBar + 'px'}">
        <view class="nav-back" @tap="back" hover-class="nav-back-hover">
          <text class="cuIcon-back"></text>
        </view>
        <text class="nav-title normal-text">{{ currentNumber && !canApplyNewNumber ? '我的靓号' : '申请靓号' }}</text>
      </view>
    </view>

    <!-- 主内容区优化 -->
    <scroll-view class="main-content" 
                 :style="[{paddingTop: NavBar + 'px'}]"
                 scroll-y>
      <view class="content-wrapper">
        <!-- 当前靓号视图 -->
        <view class="number-card" v-if="showCurrentNumberView">
          <view class="card-header">
            <text class="section-title">我的靓号</text>
            <view :class="['number-status', currentNumber.status === 'cooldown' ? 'cancelled' : 'active']">
              {{ currentNumber.status === 'cooldown' ? '已注销' : '使用中' }}
            </view>
          </view>
          
          <!-- 靓号显示部分 -->
          <view class="number-display">
            <view class="number-wrapper">
              <text class="number" 
                    :class="currentNumber.type"
              >
                <span class="number-text">{{ currentNumber.number }}</span>
              </text>
            </view>
            <view class="number-meta">
              <text class="meta-item">申请时间：{{ currentNumber.created_at }}</text>
              <text v-if="currentNumber.status === 'cooldown'" class="meta-item">
                注销时间：{{ currentNumber.deleted_at }}
              </text>
            </view>
          </view>

          <!-- 使用中状态 -->
          <block v-if="currentNumber.status !== 'cooldown'">
            <!-- 靓号操作按钮组 -->
            <view class="number-actions">
              <!-- 普通靓号升级按钮 -->
              <button class="action-button upgrade" 
                      v-if="currentNumber.type === 'normal'"
                      @tap="upgradeToBlackGold" 
                      :disabled="upgrading">
                <text class="button-icon cuIcon-crown"></text>
                {{ upgrading ? '升级中...' : '升级黑金靓号' }}
              </button>
              
              <!-- 更换靓号按钮 -->
              <button class="action-button change" 
                      @tap="showChangeNumber"
                      :disabled="changing">
                <text class="button-icon cuIcon-refresh"></text>
                {{ changing ? '更换中...' : '更换靓号' }}
              </button>
              
              <!-- 注销靓号按钮 -->
            <button class="action-button cancel" 
                    @tap="cancelNumber" 
                    :disabled="canceling">
                <text class="button-icon cuIcon-close"></text>
              {{ canceling ? '注销中...' : '注销靓号' }}
            </button>
            </view>
            <text class="warning-text">注销后需等待{{ getCooldownText }}天冷却期才能再次申请</text>
          </block>
        </view>

        <!-- 冷却期视图 -->
        <view class="cooldown-section" v-if="showCooldownView">
          <view class="cooldown-header">
            <text class="cooldown-title">靓号冷却中</text>
            <text class="cooldown-days">{{ currentNumber.remaining_days }}天</text>
          </view>
          
          <view class="cooldown-card">
            <view class="number-display">
              <view class="number-wrapper">
                <text class="number"
                      :class="currentNumber.type"
                >
                  <span class="number-text">{{ currentNumber.number }}</span>
                </text>
              </view>
            </view>
            
            <view class="cooldown-info">
              <text class="info-label">注销时间</text>
              <text class="info-value">{{ formatDate(currentNumber.deleted_at) }}</text>
            </view>
            <view class="cooldown-info">
              <text class="info-label">冷却结束</text>
              <text class="info-value">{{ formatDate(currentNumber.cooldown_end) }}</text>
              </view>
            
            <view class="progress-container">
              <view class="progress-track">
                <view class="progress-fill" 
                      :class="currentNumber.type"
                      :style="{ width: currentNumber.cooldown_progress + '%' }">
                </view>
              </view>
              <view class="progress-labels">
                <text class="progress-start">已冷却</text>
                <text class="progress-percent" :class="currentNumber.type">
                  {{ Math.round(currentNumber.cooldown_progress) }}%
                </text>
                <text class="progress-end">剩余{{ currentNumber.remaining_days }}天</text>
            </view>
          </view>
        </view>

          <view class="cooldown-message">
            <text class="message-icon">i</text>
            <text class="message-text">冷却期结束后，您可以重新申请靓号</text>
          </view>
        </view>

        <!-- 靓号申请视图 -->
        <view v-if="showApplyView">
          <!-- 靓号类型选择 -->
          <view class="type-selector">
            <view class="type-options">
              <view class="type-option" 
                    :class="{ active: selectedType === 'normal' }"
                    @tap="selectedType = 'normal'">
                <view class="type-icon">
                  <text class="cuIcon-roundcheck"></text>
                </view>
                <view class="type-info">
                  <text class="type-name">普通靓号</text>
                  <text class="type-desc">标准靓号服务</text>
                </view>
              </view>
              <view class="type-option" 
                    :class="{ active: selectedType === 'black_gold' }"
                    @tap="selectedType = 'black_gold'">
                <view class="type-icon">
                  <text class="cuIcon-crown"></text>
                </view>
                <view class="type-info">
                  <text class="type-name">黑金靓号</text>
                  <text class="type-desc">尊贵靓号服务，带专属图标</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 靓号输入框 -->
          <view class="input-section">
            <view class="input-container">
              <view class="input-wrapper" :class="{ 'is-focused': isFocused }">
            <input class="number-input"
                   v-model="number"
                   placeholder="请输入数字/字母靓号"
                       :maxlength="getMaxLength"
                       :disabled="applying"
                       @focus="isFocused = true"
                       @blur="isFocused = false"/>
              </view>
              <view class="input-counter">
                <text>{{ number.length }}/{{ getMaxLength }}</text>
              </view>
            </view>
          </view>

          <!-- 申请须知 -->
          <view class="apply-notice">
            <view class="notice-header">
              <text class="notice-title">申请须知</text>
            </view>
            <view class="notice-list">
              <view class="notice-item">
                <text class="notice-icon cuIcon-info"></text>
                <text class="notice-text">普通用户长度要求：{{ getNormalLengthText }}</text>
            </view>
              <view class="notice-item">
                <text class="notice-icon cuIcon-vip"></text>
                <text class="notice-text">VIP用户长度要求：{{ getVipLengthText }}</text>
              </view>
              <view class="notice-item">
                <text class="notice-icon cuIcon-warn"></text>
                <text class="notice-text">连续3位及以上相同字符需要付费</text>
              </view>
              <view class="notice-item">
                <text class="notice-icon cuIcon-time"></text>
                <text class="notice-text">注销后需等待{{ getCooldownText }}天冷却期</text>
              </view>
            </view>
          </view>

          <!-- 申请按钮 -->
          <button class="action-button apply"
                  @tap="applyNumber"
                  hover-class="button-hover"
                  :disabled="!number || applying || requestThrottled">
            <text class="button-icon" v-if="!applying && !requestThrottled">
              <text class="cuIcon-check"></text>
            </text>
            <text class="button-text">
              {{ getApplyButtonText }}
            </text>
          </button>
      </view>

        <!-- 加载状态 - 只在获取数据时显示 -->
        <view class="loading-state" v-if="isLoading">
        <u-loading mode="circle" size="36"></u-loading>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      StatusBar: this.StatusBar,
      CustomBar: this.CustomBar,
      NavBar: this.StatusBar + this.CustomBar,
      AppStyle: this.$store.state.AppStyle,
      pageReady: false,
      token: '',
      number: '',
      currentNumber: null,
      applying: false,
      canceling: false,
      config: null,
      isVip: false,
      cooldownDays: 30, // 默认冷却期为30天
      lastRequestTime: 0, // 上次请求时间
      requestThrottled: false, // 请求节流标志
      requestCooldown: 2000, // 请求冷却时间（毫秒）
      isLoggedIn: false,
      isLoading: false,
      applySuccess: false, //标记申请是否成功
      autoRefreshTimer: null, //自动刷新计时器
      hasCooldown: false,
      canceledNumber: '',
      canceledAt: null,
      remainingDays: 0,
      cooldownProgressValue: 0, 
      selectedType: 'normal',
      showPayPopup: false,
      selectedPayNumber: '',
      selectedPayPrice: 0,
      selectedPayDesc: '',
      currencyName: '积分', // 添加货币名称
      applicationId: null,
      paying: false,
      payAmount: 0,
      payDescription: '',
      isPayConfirm: false, // 添加支付确认标志
      applyDebounceTimer: null,  // 申请防抖定时器
      applyDebounceDelay: 2000,  // 申请防抖延迟时间(ms)
      pageSize: 20,
      currentPage: 1,
      loading: false,
      hasMore: true,
      newNumber: '',
      upgrading: false,
      changing: false,
      checkTimer: null,
      isFocused: false,
    }
  },

  computed: {
    cooldownDaysLeft() {
      if (!this.currentNumber || this.currentNumber.status === 1) {
        return 0;
      }
      
      // 使用cooldown_end字段计算剩余天数
      if (this.currentNumber.cooldown_end) {
        const cooldownEnd = new Date(this.currentNumber.cooldown_end).getTime();
        const now = Date.now();
        
        // 如果冷却期已过，返回0
        if (now >= cooldownEnd) {
          return 0;
        }
        
        const diffTime = cooldownEnd - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
      }
      
      //使用deleted_at计算
      if (this.currentNumber.deleted_at) {
        return this.calculateCooldownDays(this.currentNumber.deleted_at);
      }
      
      return 0;
    },
    
    cooldownProgress() {  // 保留计算属性名称
      if (!this.currentNumber || this.currentNumber.status === 1) {
        return 0;
      }
      
      // 使用cooldown_end字段计算进度
      if (this.currentNumber.cooldown_end && this.currentNumber.deleted_at) {
        const cooldownEnd = new Date(this.currentNumber.cooldown_end).getTime();
        const deletedAt = new Date(this.currentNumber.deleted_at).getTime();
        const now = Date.now();
        
        const totalTime = cooldownEnd - deletedAt;
        const elapsedTime = now - deletedAt;
        
        if (totalTime <= 0) return 100;
        
        const progress = (elapsedTime / totalTime) * 100;
        return Math.min(Math.max(progress, 0), 100);
      }
      
      return 0;
    },
    
    // 是否可以申请新靓号
    canApplyNewNumber() {
      // 只有在以下情况才能申请新靓号：
      // 1. 没有靓号信息
      // 2. 冷却期已结束
      return !this.currentNumber || 
             (this.currentNumber.status === 'cooldown' && 
              this.currentNumber.remaining_days === 0);
    },

    // 获取普通用户长度要求文本
    getNormalLengthText() {
      /* console.log('获取普通用户长度要求，当前配置:', this.config); */
      if (!this.config) return '9-10位'; 
      const min = this.config.min_length;
      const max = this.config.max_length;
      /* console.log('普通用户长度范围:', min, '-', max); */
      return `${min}-${max}位`;
    },
    
    // 获取VIP用户长度要求文本
    getVipLengthText() {
      /* console.log('获取VIP用户长度要求，当前配置:', this.config); */
      if (!this.config) return '8-9位'; 
      const min = this.config.vip_min_length;
      const max = this.config.vip_max_length;
      /* console.log('VIP用户长度范围:', min, '-', max); */
      return `${min}-${max}位`;
    },
    
    // 获取冷却天数
    getCooldownDays() {
      return this.config ? this.config.cooldown_days : 30; 
    },

    // 获取冷却期文本
    getCooldownText() {
      return this.config ? this.config.cooldown_days : 30;
    },
    
    // 获取最大长度
    getMaxLength() {
      if (!this.config) return 10;
      return this.isVip ? this.config.vip_max_length : this.config.max_length;
    },

    // 是否显示冷却期视图
    showCooldownView() {
      /* console.log('检查是否显示冷却期视图:', {
        currentNumber: this.currentNumber,
        status: this.currentNumber?.status,
        cooldown_end: this.currentNumber?.cooldown_end
      }); */
      return this.currentNumber && this.currentNumber.status === 'cooldown';
    },
    
    // 是否显示靓号申请视图
    showApplyView() {
      /* console.log('检查是否显示申请视图:', {
        currentNumber: this.currentNumber,
        status: this.currentNumber?.status,
        remaining_days: this.currentNumber?.remaining_days
      }); */
      return !this.currentNumber && !this.showCooldownView;
    },
    
    // 是否显示当前靓号视图
    showCurrentNumberView() {
      /* console.log('检查是否显示当前靓号视图:', {
        currentNumber: this.currentNumber,
        status: this.currentNumber?.status
      }); */
      return this.currentNumber && this.currentNumber.status === 'active';
    },

    // 获取申请按钮文本
    getApplyButtonText() {
      if (this.applying) return '申请中...';
      if (this.requestThrottled) return '请稍后再试';
      if (this.selectedType === 'black_gold') {
        const price = this.config?.black_gold_price;
        return `立即购买 (${price}${this.currencyName})`;
      }
      return '立即申请';
    }
  },

  async mounted() {
    try {
      // 从本地存储获取用户token
      await this.getUserTokenFromStorage();
      
      // 如果已登录，获取配置和靓号信息
      if (this.isLoggedIn) {
        await this.getConfig();
        await this.getCurrentNumber();
      }
    } finally {
      this.pageReady = true;
    }
    // 每30秒检查一次靓号状态
    this.checkTimer = setInterval(() => {
      this.getCurrentNumber();
    }, 30000);
  },

  beforeDestroy() {
    // 组件销毁前清除计时器
    if (this.autoRefreshTimer) {
      clearTimeout(this.autoRefreshTimer);
    }
    if (this.applyDebounceTimer) {
      clearTimeout(this.applyDebounceTimer);
    }
    // 组件销毁时清除定时器
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
  },

  created() {
    this.checkCooldown()
    this.initDebounce()
  },

  methods: {
    // 返回上一页
    back() {
      uni.navigateBack();
    },

    // 从本地存储获取用户token
    async getUserTokenFromStorage() {
      try {
        // #ifdef H5
        if(localStorage.getItem('userinfo')){
          const userInfo = JSON.parse(localStorage.getItem('userinfo'));
          this.token = userInfo.token;
          this.isLoggedIn = true;
        }
        // #endif
        
        // #ifdef APP-PLUS || MP
        const userInfo = uni.getStorageSync('userinfo');
        if(userInfo) {
          const parsedUserInfo = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;
          this.token = parsedUserInfo.token;
          this.isLoggedIn = true;
        }
        // #endif
        
        ////console.log('从本地存储获取用户token成功');
      } catch (error) {
        ////console.error('从本地存储获取用户token失败:', error);
        this.isLoggedIn = false;
      }
    },
    
    // 检查请求节流
    checkThrottle() {
      const now = Date.now();
      if (now - this.lastRequestTime < this.requestCooldown) {
        if (!this.requestThrottled) {
        this.requestThrottled = true;
          uni.showToast({
            title: '操作太频繁，请稍后再试',
            icon: 'none'
          });
        setTimeout(() => {
          this.requestThrottled = false;
          }, this.requestCooldown);
        }
        return false;
      }
      this.lastRequestTime = now;
      return true;
    },
    
    // 获取当前靓号信息
    async getCurrentNumber() {
      try {
        this.isLoading = true;
        
        /* console.log('开始获取当前靓号信息'); */
        const [err, res] = await uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'get_current_number',
            token: this.token,
            _t: Date.now()  // 添加时间戳
          },
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }
        });
        
        /* console.log('获取靓号响应:', res); */
        if (!err && res.data.code === 200) {
          this.currentNumber = res.data.data;
          /* console.log('更新当前靓号:', this.currentNumber); */
        } else {
          this.currentNumber = null;
          /* console.log('清空当前靓号'); */
        }
      } catch (error) {
        /* console.error('获取靓号信息失败:', error); */
        this.currentNumber = null;
      } finally {
        this.isLoading = false;
      }
    },
    
    // 获取配置
    async getConfig() {
      try {
        /* console.log('开始获取配置...'); */
        // 确保用户已登录
        if (!this.isLoggedIn || !this.token) {
          /* console.log('用户未登录，使用默认配置'); */
          this.config = {
            min_length: 9,
            max_length: 10,
            cooldown_days: 7,
            vip_min_length: 8,
            vip_max_length: 9,
            require_approval: 1,
            black_gold_price: 99
          };
          /* console.log('使用默认配置:', this.config); */
        return false;
      }

        const res = await uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'pretty_number_settings',
            op: 'get',
            token: this.token
          },
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }
        });

        /* console.log('获取配置响应:', res); */

        if (!res[0] && res[1].data.code === 200) {
          /* console.log('原始配置数据:', res[1].data.data); */
          // 直接使用后端返回的数据，只做类型转换
          this.config = {
            min_length: parseInt(res[1].data.data.min_length),
            max_length: parseInt(res[1].data.data.max_length),
            cooldown_days: parseInt(res[1].data.data.cooldown_days),
            vip_min_length: parseInt(res[1].data.data.vip_min_length),
            vip_max_length: parseInt(res[1].data.data.vip_max_length),
            require_approval: parseInt(res[1].data.data.require_approval),
            black_gold_price: parseFloat(res[1].data.data.black_gold_price)
          };
          // 获取货币名称
          this.currencyName = res[1].data.data.currency_name || '积分';
          /* console.log('处理后的配置数据:', this.config); */
          
          // 获取用户VIP状态
          const [vipErr, userRes] = await uni.request({
            url: this.$API.PluginLoad('xqy_pretty_number'),
            method: 'POST',
            data: {
              plugin: 'xqy_pretty_number',
              action: 'apply_pretty_number',
              op: 'check_vip',
              token: this.token
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            }
          });
          
          /* console.log('VIP状态响应:', userRes); */
          if (!vipErr && userRes.data.code === 200) {
            this.isVip = userRes.data.data.is_vip;
            /* console.log('设置用户VIP状态:', this.isVip); */
          }
          /* console.log('最终VIP状态:', this.isVip); */
          return true;
        } 
        
        if (res && res.data.code === 429) {
          this.requestThrottled = true;
          setTimeout(() => {
            this.requestThrottled = false;
          }, this.requestCooldown);
          //console.error('获取配置失败: 请求过于频繁');
        }
        
        // 设置默认配置，仅在完全失败时使用
        if (!this.config) {
        this.config = {
            min_length: 8,
          max_length: 10,
            cooldown_days: 7,
            vip_min_length: 7,
            vip_max_length: 9,
            require_approval: 1
          };
        }
        return true;
      } catch (error) {
        //console.error('获取配置失败:', error);
        // 设置默认配置，仅在完全失败时使用
        if (!this.config) {
        this.config = {
            min_length: 8,
          max_length: 10,
            cooldown_days: 7,
            vip_min_length: 7,
            vip_max_length: 9,
            require_approval: 1
          };
        }
        return false;
      }
    },

    // 验证靓号格式
    async validateNumber() {
      //console.log('开始验证靓号格式，用户VIP状态:', this.isVip);
      if (!this.number) {
        uni.showToast({
          title: '请输入靓号',
          icon: 'none'
        });
        return false;
      }

      // 确保配置已加载
      if (!this.config) {
        const configLoaded = await this.getConfig();
        if (!configLoaded) {
        uni.showToast({
            title: '获取配置失败，请稍后重试',
          icon: 'none'
        });
        return false;
        }
      }

      // 检查字符类型
      if (!(/^[a-zA-Z0-9]+$/.test(this.number))) {
        uni.showToast({
          title: '靓号只能包含数字和字母',
          icon: 'none'
        });
        return false;
      }

      // 检查连续字符
      if (/(.)\1{2,}/.test(this.number)) {
          uni.showToast({
          title: '连续3位及以上相同字符需要付费',
            icon: 'none'
          });
          return false;
      }

      // 检查长度
      const length = this.number.length;
      let minLength, maxLength;
      
      if (this.isVip) {
        minLength = this.config.vip_min_length;
        maxLength = this.config.vip_max_length;
        //console.log('使用VIP用户长度限制:', minLength, '-', maxLength);
      } else {
        minLength = this.config.min_length;
        maxLength = this.config.max_length;
        //console.log('使用普通用户长度限制:', minLength, '-', maxLength);
      }
      
      //console.log('当前靓号长度:', length);
      //console.log('长度限制:', minLength, '-', maxLength);

      if (length < minLength || length > maxLength) {
        //console.log('靓号长度不符合要求');
        //console.log('用户类型:', this.isVip ? 'VIP' : '普通用户');
        //console.log('实际长度:', length);
        //console.log('允许范围:', `${minLength}-${maxLength}`);
        uni.showToast({
          title: this.isVip ? 
            `VIP用户靓号长度必须在${minLength}-${maxLength}位之间` :
            `普通用户靓号长度必须在${minLength}-${maxLength}位之间`,
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 防抖函数
    debounce(fn, delay = 500) {
      let timer = null;
      return function(...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, args);
        }, delay);
      };
    },
    
    // 初始化防抖
    initDebounce() {
      this.debouncedApplyNumber = this.debounce(this.applyNumber, 800);
      this.debouncedCancelNumber = this.debounce(this.cancelNumber, 800);
    },

    // 申请靓号
    async applyNumber() {
      if (this.applyDebounceTimer) {
        clearTimeout(this.applyDebounceTimer);
      }
      
      this.applyDebounceTimer = setTimeout(() => {
        if (!this.checkThrottle()) return;
        
        if (!this.number) {
        uni.showToast({
            title: '请输入靓号',
          icon: 'none'
        });
        return;
      }
      
        /* console.log('[靓号详细追踪] 开始申请靓号:', this.number, '类型:', this.selectedType); */
      
      this.applying = true;
        
        uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'apply_pretty_number',
            number: this.number,
            type: this.selectedType,
            token: this.token
          },
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          success: (res) => {
            /* console.log('[靓号详细追踪] 申请响应:', res.data); */

            if (res.data.code === 200) {
              /* console.log('[靓号详细追踪] 申请成功'); */
              // 申请成功
          this.applySuccess = true;
          
          uni.showToast({
                title: '申请成功',
            icon: 'success'
          });
          
              setTimeout(() => {
                this.getCurrentNumber();
              }, 1000);
            } else if (res.data.code === 201) {
              /* console.log('[靓号详细追踪] 需要支付:', res.data.data); */
              // 需要支付
              this.payAmount = res.data.data.price;
              this.payDescription = res.data.data.description;
              
              // 显示支付确认弹窗
              console.log('显示支付确认弹窗', this.payAmount, this.currencyName);
              uni.showModal({
                title: '靓号需要支付',
                content: `该靓号需要支付${this.payAmount}${this.currencyName}`,
                confirmText: '确认支付',
                cancelText: '取消',
                showCancel: true,
                success: (res) => {
                  console.log('弹窗结果:', res);
                  /* console.log('[靓号详细追踪] 支付确认结果:', res); */
                  if (res.confirm) {
                    // 用户确认支付
                    console.log('用户确认支付');
                    this.confirmPayment();
                  } else {
                    console.log('用户取消支付');
                    this.applying = false;
                  }
                }
              });
            } else if (res.data.code === 202) {
              /* console.log('[靓号详细追踪] 需要购买连数:', res.data.data); */
              // 需要购买连数
              this.payAmount = res.data.data.price;
              
              // 显示连数购买确认弹窗
              uni.showModal({
                title: '连数靓号',
                content: `${res.data.msg}，需要支付${this.payAmount}${this.currencyName}`,
                confirmText: '确认支付',
                cancelText: '取消',
                success: (res) => {
                  /* console.log('[靓号详细追踪] 连数购买确认结果:', res); */
                  if (res.confirm) {
                    // 用户确认支付
                    this.confirmPayment();
                  } else {
                    this.applying = false;
                  }
                }
              });
            } else {
              /* console.log('[靓号详细追踪] 申请失败:', res.data.msg); */
              // 其他错误
          uni.showToast({
                title: res.data.msg,
            icon: 'none'
          });
              this.applying = false;
            }
          },
          fail: (err) => {
            /* console.log('[靓号详细追踪] 请求失败:', err); */
            uni.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
            this.applying = false;
          }
        });
      }, this.applyDebounceDelay);
    },
    
    // 注销靓号
    async cancelNumber() {
      // 使用 uni.showModal 代替 confirm
      const [err, res] = await uni.showModal({
        title: '确认注销',
        content: '注销后需要等待冷却期才能再次申请靓号，确定要注销吗？',
        confirmText: '确认注销',
        cancelText: '取消'
      });
      
      if (!res.confirm) {
        return;
      }
      
      this.canceling = true;
      
      try {
        const [err, res] = await uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'cancel_pretty_number',
            token: this.token
          },
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }
        });
        
        if (!err && res.data.code === 200) {
          uni.showToast({
            title: '注销成功',
            icon: 'success'
          });
          
          // 保存当前靓号信息用于显示冷却期
          const canceledNumber = this.currentNumber.number;
          const canceledAt = new Date().toISOString();
          
          // 立即更新当前靓号状态
          this.currentNumber = {
            ...this.currentNumber,
            status: 'cooldown',
            number: canceledNumber,
            deleted_at: canceledAt,
            cooldown_end: new Date(Date.now() + this.getCooldownDays * 24 * 60 * 60 * 1000).toISOString(),
            remaining_days: this.getCooldownDays
          };
          
          // 重新获取当前靓号
          this.getCurrentNumber();
        } else {
          uni.showToast({
            title: res.data.msg || '注销失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('注销靓号失败:', error);
        uni.showToast({
          title: '注销失败',
          icon: 'none'
        });
      } finally {
        this.canceling = false;
      }
    },
    
    // 开始自动刷新
    startAutoRefresh() {
      // 清除可能存在的计时器
      if (this.autoRefreshTimer) {
        clearTimeout(this.autoRefreshTimer);
      }
      
      // 设置自动刷新计时器，每1秒刷新一次，最多刷新5次
      let refreshCount = 0;
      const maxRefreshes = 5;
      
      const doRefresh = async () => {
        refreshCount++;
        //console.log(`自动刷新数据 (${refreshCount}/${maxRefreshes})...`);
        
        await this.getCurrentNumber();
        
        // 如果已经获取到靓号数据或达到最大刷新次数，停止刷新
        if (this.currentNumber || refreshCount >= maxRefreshes) {
          //console.log('自动刷新完成');
          clearTimeout(this.autoRefreshTimer);
          this.autoRefreshTimer = null;
          
          // 如果仍未获取到数据，提示用户
          if (!this.currentNumber && refreshCount >= maxRefreshes) {
            uni.showToast({
              title: '靓号申请成功，请稍后查看',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          // 继续刷新
          this.autoRefreshTimer = setTimeout(doRefresh, 1000);
        }
      };
      
      // 开始第一次刷新
      this.autoRefreshTimer = setTimeout(doRefresh, 500);
    },
    
    // 显示成功动画
    showSuccessAnimation() {
      // 可以在这里添加一些视觉效果，比如闪烁、高亮等
      // 这里使用简单的toast提示
      uni.showToast({
        title: '靓号设置成功！',
        icon: 'success',
        duration: 2000
      });
      
      // 也可以添加一些DOM动画，例如：
      // 1. 获取靓号元素
      // 2. 添加一个CSS类来触发动画
      // 3. 动画结束后移除该类
      
      // 示例：如果有一个靓号卡片元素，可以添加高亮动画
      setTimeout(() => {
        const numberCard = document.querySelector('.number-card');
        if (numberCard) {
          numberCard.classList.add('highlight-animation');
          setTimeout(() => {
            numberCard.classList.remove('highlight-animation');
          }, 2000);
        }
      }, 500);
    },
    
    // 检查冷却期 - 简洁实现
    checkCooldown() {
      const token = uni.getStorageSync('token') || ''
      
      uni.request({
        url: this.$API.PluginLoad('xqy_pretty_number'),
        method: 'POST',
        data: {
          plugin: 'xqy_pretty_number',
          action: 'get_cooldown_info',
          token: token
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          if (res.data && res.data.code === 200 && res.data.data) {
            const cooldownInfo = res.data.data
            this.hasCooldown = true
            this.canceledNumber = cooldownInfo.number
            this.canceledAt = new Date(cooldownInfo.deleted_at)
            this.cooldownDays = cooldownInfo.cooldown_days || 30
            
            // 计算剩余天数和进度
            this.calculateCooldown()
          } else {
            this.hasCooldown = false
          }
        }
      })
    },
    
    // 计算冷却期剩余天数和进度
    calculateCooldown() {
      if (!this.canceledAt) return
      
      const now = new Date()
      const canceledTime = this.canceledAt.getTime()
      const cooldownDays = 30 // 免费版固定30天
      const cooldownEndTime = canceledTime + (cooldownDays * 24 * 60 * 60 * 1000)
      const remainingTime = cooldownEndTime - now.getTime()
      
      // 计算剩余天数（向上取整，确保显示整数天）
      this.remainingDays = Math.ceil(remainingTime / (24 * 60 * 60 * 1000))
      
      // 如果冷却期已结束
      if (this.remainingDays <= 0) {
        this.hasCooldown = false
        this.remainingDays = 0
        this.cooldownProgressValue = 100
        return
      }
      
      // 计算进度百分比（已经过去的时间占总冷却期的百分比）
      const elapsedTime = now.getTime() - canceledTime
      const totalCooldownTime = cooldownDays * 24 * 60 * 60 * 1000
      this.cooldownProgressValue = Math.min(100, (elapsedTime / totalCooldownTime) * 100)
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 监听靓号输入
    async checkNumber(value) {
      if (!value) return;
      
      // 检查是否包含连续3位及以上相同字符
      if (value.match(/(.)\1{2,}/)) {
        try {
          const res = await this.$http.post('/Plugins/xqy_pretty_number/index.php', {
            plugin: 'xqy_pretty_number',
            action: 'manage_price_rules',
            op: 'check_price',
            number: value
          });
          
          if (res.data.code === 200 && res.data.data.price > 0) {
            this.selectedPayNumber = value;
            this.selectedPayPrice = res.data.data.price;
            this.selectedPayDesc = res.data.data.description || '靓号申请费用';
            this.showPayPopup = true;
          }
        } catch (err) {
          //console.error('检查靓号价格失败:', err);
        }
      }
    },

    // 确认支付
    async confirmPay(number) {
      try {
        //console.log('确认支付靓号:', number);
        this.applying = true;
        
        // 发送确认支付请求
        const [err, res] = await uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'apply_pretty_number',
            token: this.token,
            number: number,
            type: this.selectedType,
            confirm_pay: 1
          },
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }
        });
        
        //console.log('确认支付响应:', err, res);
        
        if (!err && res.data && res.data.code === 200) {
          // 支付成功
          uni.showToast({
            title: '购买成功',
            icon: 'success'
          });
          this.applySuccess = true;
          
          // 延迟获取最新靓号信息
          setTimeout(() => {
            this.getCurrentNumber();
          }, 1000);
        } else {
          //console.error('支付失败:', res.data);
          uni.showToast({
            title: res.data.msg || '支付失败',
            icon: 'none'
          });
        }
      } catch (error) {
        //console.error('支付请求异常:', error);
        uni.showToast({
          title: '支付失败',
          icon: 'none'
        });
      } finally {
        this.applying = false;
      }
    },

    // 分页加载数据
    async loadMoreData() {
      if (this.loading || !this.hasMore) return;
      
      this.loading = true;
      try {
        const [err, res] = await uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'get_current_number',
            token: this.token,
            page: this.currentPage,
            limit: this.pageSize
          }
        });
        
        if (!err && res.data.code === 200) {
          // 处理数据
          this.currentPage++;
          this.hasMore = res.data.data.length === this.pageSize;
        }
      } finally {
        this.loading = false;
      }
    },

    // 下拉刷新
    async onPullDownRefresh() {
      this.currentPage = 1;
      this.hasMore = true;
      await this.loadMoreData();
      uni.stopPullDownRefresh();
    },

    // 触底加载
    async onReachBottom() {
      await this.loadMoreData();
    },

    // 升级为黑金靓号
    async upgradeToBlackGold() {
      if (this.upgrading) return;
      
      // 获取黑金靓号价格
      const price = this.config?.black_gold_price || 99;
      //console.log('准备升级黑金靓号，价格:', price, '当前靓号:', this.currentNumber?.number);
      
      // 显示确认框
      uni.showModal({
        title: '升级黑金靓号',
        content: `升级为黑金靓号需要支付 ${price}${this.currencyName}，是否继续？`,
        success: async (res) => {
          if (res.confirm) {
            this.upgrading = true;
            //console.log('用户确认升级黑金靓号');
            
            try {
              // 使用表单数据方式发送请求
              const formData = {
                plugin: 'xqy_pretty_number',
                action: 'upgrade_number',
                token: this.token,
                number: this.currentNumber.number
              };
              //console.log('发送升级请求:', formData);
              
              const [err, res] = await uni.request({
                url: this.$API.PluginLoad('xqy_pretty_number'),
                method: 'POST',
                data: formData,
                header: {
                  'content-type': 'application/x-www-form-urlencoded'
                }
              });
              
              //console.log('升级请求响应:', err, res);
              
              if (!err && res.data.code === 200) {
                uni.showToast({
                  title: '升级成功',
                  icon: 'success'
                });
                await this.getCurrentNumber();
              } else {
                //console.error('升级失败:', res.data);
                uni.showToast({
                  title: res.data.msg || '升级失败',
                  icon: 'none'
                });
              }
            } catch (error) {
              //console.error('升级请求异常:', error);
              uni.showToast({
                title: '升级失败',
                icon: 'none'
              });
            } finally {
              this.upgrading = false;
            }
          }
        }
      });
    },

    // 显示更换靓号弹窗
    showChangeNumber() {
      uni.showModal({
        title: '更换靓号',
        editable: true,
        placeholderText: `请输入${this.isVip ? this.config?.vip_min_length : this.config?.min_length}-${this.isVip ? this.config?.vip_max_length : this.config?.max_length}位靓号`,
        content: '',
        // 添加字数统计
        placeholderStyle: 'color: #999999',
        // 添加字数限制
        maxLength: this.isVip ? this.config?.vip_max_length : this.config?.max_length,
        // 添加字数统计显示
        count: true,
        success: (res) => {
          if (res.confirm && res.content) {
            this.newNumber = res.content.trim();
            this.checkChangePrice();
          }
        }
      });
    },

    // 检查更换靓号价格
    async checkChangePrice() {
      if (!this.newNumber) {
        uni.showToast({
          title: '请输入靓号',
          icon: 'none'
        });
        return;
      }
      
      // 验证新靓号格式
      if (!/^[a-zA-Z0-9]+$/.test(this.newNumber)) {
        uni.showToast({
          title: '靓号只能包含字母和数字',
          icon: 'none'
        });
        return;
      }
      
      // 验证靓号长度
      const minLength = this.isVip ? (this.config?.vip_min_length || 7) : (this.config?.min_length || 8);
      const maxLength = this.isVip ? (this.config?.vip_max_length || 10) : (this.config?.max_length || 10);
      
      if (this.newNumber.length < minLength || this.newNumber.length > maxLength) {
        uni.showToast({
          title: `靓号长度必须在${minLength}-${maxLength}位之间`,
          icon: 'none'
        });
        return;
      }

      try {
        const [checkErr, checkRes] = await uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'check_change_price',
            token: this.token,
            new_number: this.newNumber
          },
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }
        });

        if (!checkErr && checkRes.data.code === 200) {
          const priceInfo = checkRes.data.data;
          const price = priceInfo.price || 0;
          const currencyName = priceInfo.currency_name || '积分';
          
          // 显示确认弹窗
          uni.showModal({
            title: '更换靓号',
            content: `确定要花费${price}${currencyName}更换为${this.newNumber}吗？`,
            success: async (res) => {
              if (res.confirm) {
                this.changing = true;
                try {
                  const [err, res] = await uni.request({
                    url: this.$API.PluginLoad('xqy_pretty_number'),
                    method: 'POST',
                    data: {
                      plugin: 'xqy_pretty_number',
                      action: 'change_number',
                      token: this.token,
                      new_number: this.newNumber
                    },
                    header: {
                      'content-type': 'application/x-www-form-urlencoded'
                    }
                  });
                  
                  if (!err && res.data.code === 200) {
                    uni.showToast({
                      title: '更换成功',
                      icon: 'success'
                    });
                    await this.getCurrentNumber();
                  } else {
                    uni.showToast({
                      title: res.data.msg || '更换失败',
                      icon: 'none'
                    });
                  }
                } catch (error) {
                  uni.showToast({
                    title: '更换失败',
                    icon: 'none'
                  });
                } finally {
                  this.changing = false;
                }
              }
            }
          });
        } else {
          uni.showToast({
            title: checkRes.data.msg || '获取价格信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.showToast({
          title: '获取价格信息失败',
          icon: 'none'
        });
      }
    },

    // 确认支付方法
    async confirmPayment() {
      console.log('执行confirmPayment方法');
      if (!this.number || !this.payAmount) {
        console.log('支付信息无效', this.number, this.payAmount);
        uni.showToast({
          title: '支付信息无效',
          icon: 'none'
        });
        return;
      }
      
      try {
        console.log('发送支付确认请求', this.number, this.selectedType);
        const [err, res] = await uni.request({
          url: this.$API.PluginLoad('xqy_pretty_number'),
          method: 'POST',
          data: {
            plugin: 'xqy_pretty_number',
            action: 'apply_pretty_number',
            token: this.token,
            number: this.number,
            type: this.selectedType,
            pay: true,
            confirm_pay: 1
          },
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }
        });
        
        console.log('支付确认响应:', err, res?.data);
        if (!err && res.data.code === 200) {
          uni.showToast({
            title: '申请成功',
            icon: 'success'
          });
          
          // 延迟获取最新靓号信息
          setTimeout(() => {
            this.getCurrentNumber();
          }, 1000);
        } else {
          uni.showToast({
            title: res.data.msg || '支付失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('支付请求失败:', error);
        uni.showToast({
          title: '支付失败，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.applying = false;
        this.payAmount = 0;
        this.payDescription = '';
      }
    }
  },

  watch: {
    // 监听靓号输入变化
    number(newVal) {
      this.checkNumber(newVal);
    }
  }
}
</script>

<style lang="scss" scoped>
.pretty-number-center {
  min-height: 100vh;
  background-color: #f5f5f7;
  
  .normal-text {
    font-size: inherit !important;
    
    &::first-letter {
      font-size: inherit !important;
      font-weight: inherit !important;
    }
  }
  
  .nav-bar {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    
    .nav-content {
      display: flex;
      align-items: center;
      padding: 0 16px;
      
      .nav-back {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        
        &.nav-back-hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
      
      .nav-title {
        flex: 1;
        text-align: center;
        font-size: 17px;
        font-weight: 600;
        color: #000;
        margin-right: 32px;
      }
    }
  }

  .main-content {
    height: calc(100vh - 44px);
    
    .content-wrapper {
      padding: 16px;
      
      .section {
        background: #fff;
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .number-card, .apply-card {
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
  }

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #000;
  }

  .number-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    
    &.active {
      background: #e8f5e9;
      color: #2e7d32;
    }
    
    &.cancelled {
      background: #f5f5f5;
      color: #757575;
    }
  }

  .number-display {
    text-align: center;
    margin: 20px 0;
    
    .number-wrapper {
      display: flex;
      justify-content: center;
      margin: 10px 0;
      position: relative;
    }
    
    .number {
      font-size: 24px;
      font-weight: bold;
      padding: 8px 20px;
      min-width: 200px;
      text-align: left;
      display: inline-block;
      position: relative;
      border-radius: 50px;
      padding-left: 85px;
      padding-right: 30px;
      font-family: -apple-system, SF Pro Display, Roboto, sans-serif;
      letter-spacing: 0.5px;
      
      &.normal {
        color: #8B4513;  // 暖棕色
        background: linear-gradient(135deg, #FFF3E0, #FFE0B2);  // 温暖的渐变
        border: 1px solid rgba(139, 69, 19, 0.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05),
                    0 1px 2px rgba(0, 0, 0, 0.04);
        backdrop-filter: blur(20px);
        
        &::before {
          content: "靓号";
          position: absolute;
          left: 20px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 14px;
          color: #D4AF37;  // 金色
          background: linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(218, 165, 32, 0.1));
          padding: 4px 12px;
          border-radius: 20px;
          font-weight: normal;
      letter-spacing: 1px;
          border: 1px solid rgba(212, 175, 55, 0.2);
        }

        // 添加微妙的光泽效果
        &::after {
          content: '';
          position: absolute;
          inset: 0;
          background: linear-gradient(120deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%);
          border-radius: 50px;
          opacity: 0.6;
        }
      }
      
      &.black_gold {
        // 主体样式
        color: #FFD700;
        background: linear-gradient(135deg, 
          rgba(28, 28, 28, 0.95) 0%,
          rgba(17, 17, 17, 0.98) 100%
        );
        border: 1px solid rgba(255, 215, 0, 0.3);
        background-size: 200% auto;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3),
                    inset 0 0 20px rgba(255, 215, 0, 0.1);
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
        
        // 光效装饰
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 50%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.15),
            transparent
          );
          transform: skewX(-25deg);
          animation: shine 3s infinite;
        }
        
        &::before {
          content: "靓号";
          position: absolute;
          left: 20px;
          top: 50%;
          transform: translateY(-50%);
        font-size: 14px;
          color: #111;
          background: linear-gradient(135deg, #FFD700, #FDB931);
          padding: 4px 12px;
          border-radius: 20px;
          font-weight: normal;
          letter-spacing: 1px;
          border: none;
          box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
          animation: pulse 2s infinite;
        }

        // 数字文本渐变效果
        .number-text {
          background: linear-gradient(90deg,
            #FFD700 0%,
            #FFF8DC 50%,
            #FFD700 100%
          );
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          animation: shimmer 3s infinite;
        }
      }

      //动画
      @keyframes shine {
        0% {
          left: -100%;
        }
        20% {
          left: 100%;
        }
        100% {
          left: 100%;
        }
      }
      
      @keyframes pulse {
        0%, 100% {
          transform: translateY(-50%) scale(1);
          opacity: 1;
        }
        50% {
          transform: translateY(-50%) scale(1.05);
          opacity: 0.9;
        }
      }
      
      @keyframes shimmer {
        0% {
          background-position: -200% center;
        }
        100% {
          background-position: 200% center;
        }
      }

      &:hover {
        &.black_gold {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                      0 0 30px rgba(255, 215, 0, 0.3),
                      inset 0 0 30px rgba(255, 215, 0, 0.2);
          
          &::after {
            animation-duration: 1.5s;
          }
          
          &::before {
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.7);
          }
          
          .number-text {
            animation-duration: 2s;
          }
        }
      }
    }
    
    .number-meta {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
    }
  }

  .cooldown-section {
    margin-top: 24px;
    
    .cooldown-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .cooldown-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      
      .cooldown-days {
        font-size: 18px;
        font-weight: 600;
        color: #ff6b6b;
        background: rgba(255, 107, 107, 0.1);
        padding: 4px 12px;
        border-radius: 16px;
      }
    }
    
    .cooldown-card {
      background: #fff;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      margin-bottom: 16px;
      
      .number-display {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        text-align: center;
        margin-bottom: 20px;
        letter-spacing: 1px;
      }
      
      .cooldown-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        
        .info-label {
          color: #666;
          font-size: 14px;
        }
        
        .info-value {
          color: #333;
          font-size: 14px;
          font-weight: 500;
        }
      }
      
      .progress-container {
        margin-top: 24px;
        
    .progress-track {
          height: 8px;
          background: #f1f3f5;
          border-radius: 4px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            border-radius: 4px;
            transition: width 0.5s ease;
            
            &.normal {
              background: linear-gradient(90deg, #4facfe, #00f2fe);
            }
            
            &.black_gold {
              background: linear-gradient(90deg, #FFD700, #FDB931);
              box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
            }
          }
        }
        
        .progress-labels {
          display: flex;
          justify-content: space-between;
          margin-top: 8px;
          
          .progress-start, .progress-end {
            font-size: 12px;
            color: #666;
          }
          
          .progress-percent {
            font-size: 14px;
            font-weight: 600;
            
            &.normal {
              color: #4facfe;
            }
            
            &.black_gold {
              color: #FFD700;
              text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
            }
          }
        }
      }
    }
  }

  .cooldown-message {
    display: flex;
    align-items: center;
    background: rgba(79, 172, 254, 0.1);
    padding: 12px 16px;
    border-radius: 12px;
    
    .message-icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #4facfe;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      margin-right: 12px;
    }
    
    .message-text {
      font-size: 14px;
      color: #4facfe;
    }
  }

  .input-section {
    margin: 32px 16px;
    
    .input-container {
      position: relative;
      background: #fff;
      border-radius: 16px;
      padding: 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
      .input-wrapper {
        background: #f8f8fa;
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      
        &.is-focused {
          background: #fff;
          border-color: #0066ff;
          box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
        }
    
    .number-input {
      width: 100%;
      height: 48px;
      padding: 0 16px;
          font-size: 17px;
          font-weight: 500;
          color: #1d1d1f;
          background: transparent;
          border: none;
          outline: none;
          letter-spacing: 0.5px;
        
          &::placeholder {
            color: #86868b;
            font-weight: normal;
          }
          
          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }
    
    .input-counter {
      position: absolute;
      right: 16px;
      bottom: -22px;
      font-size: 12px;
      color: #86868b;
      font-weight: 500;
    }
  }

  .apply-notice {
    background: #ffffff;
    border-radius: 16px;
    margin: 16px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    
    .notice-header {
      margin-bottom: 16px;
      
      .notice-title {
        font-size: 16px;
        font-weight: 600;
        color: #1d1d1f;
        position: relative;
        padding-left: 12px;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 16px;
          background: #0066ff;
          border-radius: 2px;
        }
      }
    }
    
    .notice-list {
      .notice-item {
      display: flex;
      align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        
        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }
        
        .notice-icon {
          width: 24px;
          height: 24px;
          border-radius: 12px;
          background: rgba(0, 102, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
        font-size: 14px;
          color: #0066ff;
        }
        
        .notice-text {
          flex: 1;
          font-size: 14px;
          color: #1d1d1f;
        }
      }
    }
  }

  .action-button {
    width: 100%;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 32px;
    
    &.apply {
      background: #0066ff;
      color: #fff;
      box-shadow: 0 2px 8px rgba(0, 102, 255, 0.3);
      
      &:active {
        transform: scale(0.98);
        background: #0055d6;
      }
      
      &:disabled {
        background: #999;
        transform: none;
        box-shadow: none;
      }
    }
  }

  .warning-text {
    font-size: 13px;
    color: #86868b;
    text-align: center;
    margin-top: 12px;
  }

  .loading-state {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 添加高亮动画样式
@keyframes highlight {
  0% { box-shadow: 0 0 0 rgba(26, 115, 232, 0); }
  50% { box-shadow: 0 0 20px rgba(26, 115, 232, 0.8); }
  100% { box-shadow: 0 0 0 rgba(26, 115, 232, 0); }
}

.highlight-animation {
  animation: highlight 2s ease-in-out;
}

.type-selector {
  margin: 24px 0;
  
  .type-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    
    .type-option {
      padding: 24px;
      border-radius: 16px;
      background: #f8f8fa;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      border: 1px solid rgba(0, 0, 0, 0.1);
      
      &.active {
        background: #0066ff;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 102, 255, 0.2);
        
        .type-name, .type-desc {
          color: #fff;
        }
        
        .type-icon {
          background: rgba(255, 255, 255, 0.2);
          color: #fff;
        }
      }
      
      .type-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        background: rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 20px;
        color: #0066ff;
      }
      
      .type-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
      }
      
      .type-name {
        font-size: 17px;
        font-weight: 600;
        color: #000;
        margin-bottom: 4px;
        text-align: center;
      }
      
      .type-desc {
        font-size: 13px;
        color: #86868b;
        text-align: center;
        padding: 0 8px;
        line-height: 1.4;
      }
    }
  }
}

.number-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 16px;
  
  .action-button {
    width: 100%;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    
    .button-icon {
      margin-right: 8px;
      font-size: 18px;
    }
    
    &.upgrade {
      background: linear-gradient(135deg, #FFD700, #F7B731);
      color: #000;
      box-shadow: 0 2px 6px rgba(247, 183, 49, 0.25);
      
      &:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #F7C500, #E5A82C);
      }
    }
    
    &.change {
      background: #0066ff;
      color: #fff;
      box-shadow: 0 2px 6px rgba(0, 102, 255, 0.15);
      
      &:active {
        transform: scale(0.98);
        background: #0055d6;
      }
    }
    
    &.cancel {
      background: #fff;
      color: #ff4d4f;
      border: 1px solid rgba(255, 77, 79, 0.2);
      
      &:active {
        transform: scale(0.98);
        background: #fff5f5;
      }
    }
    
    &:disabled {
      opacity: 0.6;
      transform: none !important;
      box-shadow: none;
    }
  }
  
  .warning-text {
    font-size: 13px;
    color: #86868b;
    text-align: center;
    margin-top: 8px;
  }
}

.notice-section {
  background: #f8f8fa;
  border-radius: 16px;
  padding: 20px;
  margin: 24px 0;
  
  .notice-title {
    font-size: 15px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 16px;
  }
  
  .notice-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  
    .notice-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: rgba(0, 102, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 14px;
        color: #0066ff;
    }
    
    .notice-text {
      flex: 1;
      font-size: 14px;
      color: #1d1d1f;
    }
  }
}
</style>

