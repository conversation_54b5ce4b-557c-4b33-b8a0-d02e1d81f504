<template>
	<view>
		<view class="header gpt-header" :style="[{height:CustomBar + 'px'}]" :class="scrollTop>40?'goScroll':''">
			<view class="cu-bar" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}" >
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content" :style="[{top:StatusBar + 'px'}]">
					<text class="text-bold">{{name}}</text>
				</view>
				<view class="action">
					<view class="cu-avatar round" :style="avatarstyle" v-if="avatarstyle!=''"></view>
					<view class="cu-avatar round" v-else>
						<text class="home-noLogin"></text>
					</view>
				</view>
			</view>
		</view>
		
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="gpt-bg">
			<image src="./style/gpt-bg.png"></image>
		</view>
		<view class="gpt-app-info">
			<view class="data-box">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text>内容输入
					</view>
					
					<view class="action">
						<template v-if="isWaiting==0">
							<text class="cu-btn sm bg-blue radius" @tap="sendText()">提交</text>
						</template>
						<template v-if="isWaiting==1">
							<text class="cu-btn sm bg-blue radius">提交中...</text>
						</template>
					</view>
				</view>
				<view class="gpt-app-form">
					<view class="gpt-app-form-intro">
						{{intro}}
					</view>
					<view class="gpt-app-form-intro">
						价格：<text class="text-orange">{{price}}</text>{{currencyName}}
					</view>
					<view class="gpt-app-form-input">
						<textarea v-model="text" placeholder="请输入你想要让AI处理的内容" maxlength="1500"></textarea>
					</view>
				</view>
				
			</view>
			<view class="data-box">
				<view class="cu-bar bg-white">
					<view class="action data-box-title">
						<text class="cuIcon-titles text-rule"></text>AI输出
					</view>
					<view class="action">
						<text class="text-blue" @tap="ToCopy(aiMsg)">复制结果</text>
					</view>
					
				</view>
				<view class="gpt-app-form">
					<view class="gpt-app-form-intro">
						下列为AI的输出结果，可点击按钮复制。
					</view>
					<view class="gpt-app-form-input">
						<!-- <textarea :value="aiMsg" placeholder="等待你的输入" disabled></textarea> -->
						<mp-html :content="aiMsg" :selectable="true" :show-img-menu="true"  :scroll-table="true" :markdown="true"/>
					</view>
				</view>
				
			</view>
		</view>
		
	</view>
</template>

<script>
	import { localStorage } from '@/js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				name:"未知应用",
				avatar:"",
				price:0,
				intro:"",
				id:0,
				token:'',
				avatarstyle:"",
				aiMsg:"",
				text:"",
				currencyName:"",
				isWaiting:0,
				scrollTop:0,
				
			}
		},
		onPageScroll(res){
			var that = this;
			that.scrollTop = res.scrollTop;
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onHide() {
			localStorage.removeItem('getuid')
		},
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			
			if(localStorage.getItem('getuid')){
				that.toid = localStorage.getItem('getuid');
			}
			
		},
		onLoad(res) {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			uni.request({
				url:that.$API.SPset(),
				method:'GET',
				dataType:"json",
				success(res) {
					that.currencyName = res.data.assetsname;
				},
				fail(error) {
				  console.log(error);
				}
				
			})
			if(res.id){
				that.id = res.id;
				that.getGptInfo();
			}
		},
		methods: {
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			getGptInfo(){
				var that = this;
				var data = {
					"id":that.id,
				}
				
				that.$Net.request({
					url: that.$API.gptInfo(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if(res.data.code==1){
							var gptInfo = res.data.data;
							that.avatarstyle = "background-image:url("+gptInfo.avatar+");"
							that.avatar = gptInfo.avatar;
							that.name = gptInfo.name;
							that.price = gptInfo.price;
							that.intro =  gptInfo.intro;
						}
					},
					fail: function(res) {
					}
				});
			},
			ToCopy(text) {
				var that = this;
				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: text,
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: "复制成功"
						})
					}
				});
				// #endif
				// #ifdef H5 
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select();
				textarea.setSelectionRange(0, text.length) ;
				uni.showToast({ //提示
					title: "复制成功"
				})
				var result = document.execCommand("copy") 
				textarea.remove();
				
				// #endif
			},
			sendText(){
				var that = this;
				var token = "";
				if(that.text==""){
					return false;
				}
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}else{
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				if(that.text.length>1500){
					uni.showToast({
						title: "最大字符数为1500",
						icon: 'none'
					})
					return false
				}
				
				var data={
					"gptid":that.id,
					"token":token,
					"msg":that.text,
					
				}
				that.aiMsg = "AI正在思考中..."
				that.isWaiting = 1;
				
				that.$Net.request({
					
					url: that.$API.gptSendText(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						that.isWaiting = 0;
						if(res.data.code==1){
							//that.getMsgList();
							//var aiMsgtime = Date.parse(new Date());
							that.aiMsg = res.data.data.text;
						}else{
							that.aiMsg = res.data.msg
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						that.isWaiting = 0;
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
		}
	}
</script>

<style>
</style>
