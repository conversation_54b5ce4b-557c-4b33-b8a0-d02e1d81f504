(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-plugins-xqy_gpt-history"],{"2cc4":function(t,a,e){"use strict";e.d(a,"b",(function(){return s})),e.d(a,"c",(function(){return n})),e.d(a,"a",(function(){return i}));var i={mpHtml:e("efec").default},s=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("v-uni-view",{staticClass:"user",class:t.$store.state.AppStyle},[i("v-uni-view",{staticClass:"header",style:[{height:t.CustomBar+"px"}]},[i("v-uni-view",{staticClass:"cu-bar bg-white",style:{height:t.CustomBar+"px","padding-top":t.<PERSON><PERSON><PERSON>+"px"}},[i("v-uni-view",{staticClass:"action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.back.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"cuIcon-back"})],1),i("v-uni-view",{staticClass:"content text-bold",style:[{top:t.StatusBar+"px"}]},[t._v("历史聊天消息")]),i("v-uni-view",{staticClass:"action"})],1)],1),i("v-uni-view",{style:[{padding:t.NavBar+"px 10px 0px 10px"}]}),i("v-uni-view",{staticClass:"cu-card dynamic no-card",staticStyle:{"margin-top":"20upx"}},[i("v-uni-view",{staticClass:"cu-bar bg-white search"},[i("v-uni-view",{staticClass:"search-form round"},[i("v-uni-text",{staticClass:"cuIcon-search"}),i("v-uni-input",{attrs:{type:"text",placeholder:"输入搜索关键字"},on:{input:function(a){arguments[0]=a=t.$handleEvent(a),t.searchTag.apply(void 0,arguments)}},model:{value:t.searchText,callback:function(a){t.searchText=a},expression:"searchText"}})],1)],1),t._l(t.msgList,(function(a,e){return i("v-uni-view",{key:e,staticClass:"cu-item chat-history",class:0==a.isAI?"self":""},[i("v-uni-view",{staticClass:"chat-history-user"},[1==a.isAI?[i("v-uni-view",{staticClass:"cu-avatar radius",style:"background-image:url("+(a.gptJson?a.gptJson.avatar:t.avatar)+");"})]:[i("v-uni-view",{staticClass:"cu-avatar radius",style:"background-image:url("+a.userJson.avatar+");"})]],2),i("v-uni-view",{staticClass:"chat-history-main"},[i("v-uni-view",{staticClass:"chat-history-user"},[1==a.isAI?[t._v(t._s(a.gptJson?a.gptJson.name:t.name))]:[t._v(t._s(a.userJson.name))]],2),i("v-uni-view",{staticClass:"content shadow break-all",on:{longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.ToCopy(a.text)}}},[i("mp-html",{attrs:{content:a.text,selectable:!0,"show-img-menu":!0,"scroll-table":!0,markdown:!0}})],1),i("v-uni-view",{staticClass:"date"},[t._v(t._s(t.formatDate(a.created)))])],1)],1)})),t.msgList.length>=t.limit?i("v-uni-view",{staticClass:"load-more",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.loadMore.apply(void 0,arguments)}}},[i("v-uni-text",[t._v(t._s(t.moreText))])],1):t._e()],2),0==t.isLoading?i("v-uni-view",{staticClass:"loading"},[i("v-uni-view",{staticClass:"loading-main"},[i("v-uni-image",{attrs:{src:e("0b62")}})],1)],1):t._e()],1)},n=[]},"3fbd":function(t,a,e){"use strict";e.r(a);var i=e("6b71"),s=e.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(n);a["default"]=s.a},"6b71":function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("5c47"),e("dfcf"),e("0c26"),e("aa9c"),e("c223"),e("e966"),e("f7a5"),e("a1c1");var i=e("9254"),s={data:function(){return{StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,id:0,msgList:[],moreText:"加载更多",page:1,limit:10,searchText:"",isLoading:0,isLoad:!1,avatar:"",name:"",price:"",intro:"",model_id:0,msgLoading:null}},onPullDownRefresh:function(){this.page=1,this.getMsgList(!1);setTimeout((function(){uni.stopPullDownRefresh()}),1e3)},onReachBottom:function(){this.loadMore()},onShow:function(){this.page=1},onLoad:function(t){this.model_id=t.model_id||t.id||0,this.name=t.name||"聊天记录",this.model_id>0?this.getMsgList(!1):uni.showToast({title:"请选择AI模型",icon:"none"})},methods:{getGptInfo:function(){var t=this,a={id:t.id};t.$Net.request({url:t.$API.gptInfo(),data:a,header:{"Content-Type":"application/x-www-form-urlencoded"},method:"get",dataType:"json",success:function(a){if(1==a.data.code){var e=a.data.data;t.avatar=e.avatar,t.name=e.name,t.price=e.price,t.intro=e.intro}},fail:function(t){}})},back:function(){clearInterval(this.msgLoading),this.msgLoading=null,uni.navigateBack({delta:1})},replaceAll:function(t,a,e){return t.split(a).join(e)},loadMore:function(){this.moreText="正在加载中...",this.isLoad||(this.isLoad=!0,this.getMsgList(!0))},toInfo:function(t,a){uni.navigateTo({url:"/pages/contents/info?cid="+t+"&title="+a})},searchTag:function(){this.searchText;this.page=1,this.getMsgList()},getMsgList:function(t){var a=this,e="";if(!a.model_id||a.model_id<=0)return uni.showToast({title:"请选择AI模型",icon:"none"}),a.isLoading=1,void(a.isLoad=!1);if(!i.localStorage.getItem("userinfo"))return uni.showToast({title:"请先登录",icon:"none"}),setTimeout((function(){uni.navigateTo({url:"/pages/user/login"})}),1500),void(a.isLoad=!1);var s=JSON.parse(i.localStorage.getItem("userinfo"));e=s.token;var n=a.page;t&&n++,a.isLoading=0,a.$Net.request({url:a.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"history",limit:a.limit,page:n,model_id:a.model_id,search_key:a.searchText,isAI:-1,scene:"chat",token:e},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(e){if(a.isLoad=!1,200==e.data.code||1==e.data.code){var i=[];if(e.data.data&&Array.isArray(e.data.data)?i=e.data.data:e.data.data&&e.data.data.list&&Array.isArray(e.data.data.list)&&(i=e.data.data.list),i.length>0){var s=[];for(var n in i){var o=i[n];if(1==o.isAI){if(o.gptJson||(o.gptJson={avatar:a.avatar,name:a.name}),!o.text||""===o.text.trim())continue}else o.userJson||(o.userJson={}),o.userJson.avatar||(o.userJson.avatar=o.avatar),o.userJson.name||(o.userJson.name="用户");s.push(o)}t?(a.page++,a.msgList=a.msgList.concat(s)):a.msgList=s}else a.moreText="没有更多数据了",t||(a.msgList=i)}else uni.showToast({title:e.data.msg||"获取历史记录失败",icon:"none"});var r=setTimeout((function(){a.isLoading=1,clearTimeout(r)}),300)},fail:function(t){a.isLoad=!1,a.moreText="加载更多",uni.showToast({title:"网络连接失败，请稍后重试",icon:"none"});var e=setTimeout((function(){a.isLoading=1,clearTimeout(e)}),300)}})},toStatus:function(t){this.status=t,this.page=1,this.moreText="加载更多",this.isLoad=0,this.getCommentsList(!1)},formatDate:function(t){t=new Date(parseInt(1e3*t));var a=t.getFullYear(),e=("0"+(t.getMonth()+1)).slice(-2),i=("0"+t.getDate()).slice(-2),s=("0"+t.getHours()).slice(-2),n=("0"+t.getMinutes()).slice(-2),o=a+"-"+e+"-"+i+" "+s+":"+n;return o},replaceSpecialChar:function(t){return!!t&&(t=t.replace(/&quot;/g,'"'),t=t.replace(/&amp;/g,"&"),t=t.replace(/&lt;/g,"<"),t=t.replace(/&gt;/g,">"),t=t.replace(/&nbsp;/g," "),t)}}};a.default=s},bb61:function(t,a,e){"use strict";e.r(a);var i=e("2cc4"),s=e("3fbd");for(var n in s)["default"].indexOf(n)<0&&function(t){e.d(a,t,(function(){return s[t]}))}(n);var o=e("828b"),r=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"319a6e9e",null,!1,i["a"],void 0);a["default"]=r.exports}}]);