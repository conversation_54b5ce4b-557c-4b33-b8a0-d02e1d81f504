<template>
	<view class="user" :class="isDark?'dark':''" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					<block v-if="type=='add'">
						添加大模型
					</block>
					<block v-else>
						大模型修改
					</block>
					
				</view>
				<!--  #ifdef H5 || APP-PLUS -->
				<view class="action" @tap="edit" v-if="type=='edit'">
					<button class="cu-btn round bg-blue">保存</button>
				</view>
				<view class="action" @tap="add" v-if="type=='add'">
					<button class="cu-btn round bg-blue">提交</button>
				</view>
				<!--  #endif -->
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		
		<form>
			<view class="user-edit-header margin-top">
				<image :src="avatar"></image>
				<!--  #ifdef H5 || APP-PLUS -->
				<!-- <text class="cu-btn bg-blue radius" @tap="showModal" data-target="DialogModal1">设置头像</text> -->
				<text class="cu-btn bg-blue radius" @tap="toAvatar" >设置大模型图标</text>
				<!--  #endif -->
			</view>
			<view class="cu-form-group margin-top"  v-if="type=='edit'">
				<view class="title">ID</view>
				<input name="input" disabled :value="id"></input>
			</view>
		
			<view class="cu-form-group">
				<view class="title">名称</view>
				<input name="input" type="text" v-model="name"></input>
			</view>
			<view class="cu-form-group align-start">
				<view class="title">简介</view>
				<textarea v-model="intro" placeholder="请输入大模型角色简介"></textarea>
			</view>
			<view class="cu-form-group"  v-if="type=='add'">
				<view class="title">类型</view>
				<view class="action">
					<text class="meta-type" :class="gptType==0?'act':''" @tap="gptType=0">聊天大模型</text>
					<text class="meta-type" :class="gptType==1?'act':''" @tap="gptType=1">AI应用</text>
				</view>
			</view>
			
			<view class="cu-form-group align-start" v-if="gptType==1">
				<view class="title">Prompt</view>
				<textarea v-model="prompt" placeholder="请输入AI应用Prompt"></textarea>
			</view>
			<view class="cu-form-group">
				<view class="title">模型源</view>
				<view class="picker" @tap="showModal" data-target="sourceList">
					{{sourceText}}
					<text class="cuIcon-right"></text>
				</view>
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">仅VIP可用</view>
				<switch @change="SwitchVIP" :class="switchVip?'checked':''" :checked="switchVip?true:false"></switch>
			</view>
			<view class="cu-form-group">
				<view class="title">单次请求价格（整数）</view>
				<input name="input" v-model="price" type="Number"></input>
			</view>
			<view class="cu-form-group align-start">
				<view class="title">渠道appId</view>
				<textarea v-model="appId" placeholder="请输入大模型渠道appId"></textarea>
			</view>
			<view class="cu-form-group align-start">
				<view class="title">渠道apiKey</view>
				<textarea v-model="apiKey" placeholder="请输入大模型渠道apiKey"></textarea>
			</view>

		</form>
		<!--  #ifdef MP -->
		<view class="post-update bg-blue" @tap="edit" v-if="type=='edit'">
			<text class="cuIcon-upload"></text>
		</view>
		<view class="post-update bg-blue" @tap="add" v-if="type=='add'">
			<text class="cuIcon-upload"></text>
		</view>
		<!--  #endif -->
		<view class="cu-modal" :class="modalName=='sourceList'?'show':''" @tap="hideModal">
			<view class="cu-dialog" @tap.stop="">
				<radio-group class="block" @change="RadioChange">
					<view class="cu-list menu text-left">
						<view class="cu-item" v-for="(item,index) in sourceList" :key="index">
							<label class="flex justify-between align-center flex-sub" @tap="setSource(item)">
								<view class="flex-sub">{{item.name}}</view>
								<radio class="round" :class="source==item.id?'checked':''" :checked="source==item.id?true:false"></radio>
							</label>
						</view>
					</view>
				</radio-group>
			</view>
		</view>
	</view>
</template>

<script>
	import { localStorage } from '@/js_sdk/mp-storage/mp-storage/index.js'
	import darkModeMixin from '@/utils/darkModeMixin.js'
	// #ifdef H5 || APP-PLUS
	import { pathToBase64, base64ToPath } from '@/js_sdk/mmmm-image-tools/index.js'
	// #endif
	export default {
		mixins: [darkModeMixin],
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				token:"",
				id:0,
				name:'',
				avatar:'',
				isVip:0,
				switchVip:false,
				picNew:"",
				price:0,
				intro:'',
				prompt:"",
				gptType:0,
				appId:'',
				apiKey:'',
				source:"Qwen",
				sourceText:"",
				
				type:"add",
				
				sourceList:[
					{
						'id':"Qwen",
						'name':'通义千问'
					},
				],
				modalName: null,
				
				//数据提交拦截，防止重复提交
				submitStatus:false,
				
				
			}
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onShow(){
			var that = this;
			if(localStorage.getItem('toAvatar')){
				var toAvatar = JSON.parse(localStorage.getItem('toAvatar'));
				that.avatarUpload(toAvatar.dataUrl);
			}else{
				console.log("没有图片缓存")
			}
			
		},
		onLoad(res) {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			if(localStorage.getItem('token')){
				
				that.token = localStorage.getItem('token');
			}
			
			if(res.type){
				that.type = res.type;
				if(that.type=="edit"){
					if(res.id){
						that.id = res.id;
						that.getGptInfo(that.id)
					}else{
						uni.showToast({
							title: "无参数访问",
							icon: 'none'
						})
					}
					
				}
			}
			if(that.sourceList.length>0){
				that.setSource(that.sourceList[0]);
			}
		},
		methods: {
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			SwitchVIP(e) {
				this.switchVip = e.detail.value;
				if(this.switchVip){
					this.isVip = 1;
				}else{
					this.isVip = 0;
				}
			},
			showModal(e) {
				this.modalName = e.currentTarget.dataset.target
			},
			hideModal(e) {
				this.modalName = null
			},
			RadioChange(e) {
				this.radio = e.detail.value
			},
			setSource(data){
				let that = this	
				that.source = data.id;
				that.sourceText = data.name;
				that.hideModal();
			},
			avatarUpload(base64){
				
				var that = this;
				base64ToPath(base64)
				  .then(path => {
					var file = path;
					const uploadTask = uni.uploadFile({
					  url : that.$API.upload(),
					  filePath:file,
					 //  header: {
						// "Content-Type": "multipart/form-data",
					 // },
					  name: 'file',
					  formData: {
					   'token': that.token
					  },
					  success: function (uploadFileRes) {
						  setTimeout(function () {
						  	uni.hideLoading();
						  }, 1000);
						  
							var data = JSON.parse(uploadFileRes.data);
							//var data = uploadFileRes.data;
							
							
							if(data.code==1){
								// uni.showToast({
								// 	title: data.msg,
								// 	icon: 'none'
								// })
								that.avatar = data.data.url;
								that.picNew = data.data.url;
								localStorage.removeItem('toAvatar');
								// that.userEdit();
								//console.log(that.avatar)
								
							}else{
								uni.showToast({
									title: "图片上传失败，请检查接口",
									icon: 'none'
								})
							}
						},fail:function(){
							setTimeout(function () {
								uni.hideLoading();
							}, 1000);
						}
						
					   
					});
				  })
				  .catch(error => {
					console.error("失败"+error)
				  })
			},
			add(){
				var that = this;
				if(that.submitStatus){
					return false;
				}
				that.submitStatus = true;
				if(that.parent==0){
					uni.showToast({
						title:"未选择大类",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if (that.name == ""||that.order == "") {
					uni.showToast({
						title:"请完成表单填写",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				var data = {
					'name':that.name,
					'source':that.source,
					'isVip':that.isVip,
					'price':that.price,
					'avatar':that.avatar,
					'intro':that.intro,
					'appId':that.appId,
					'apiKey':that.apiKey,
					"type":that.gptType,
					"prompt":that.prompt
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.gptAdd(),
					data:{
						"params":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"token":that.token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						that.submitStatus = false;
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==1){
							var timer = setTimeout(function() {
								that.back();
							}, 1000)
							
						}
					},
					fail: function(res) {
						that.submitStatus = false;
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			edit(){
				var that = this;
				if(that.parent==0){
					uni.showToast({
						title:"未选择大类",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if (that.name == ""||that.order == "") {
					uni.showToast({
						title:"请完成表单填写",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				var data = {
					'id':that.id,
					'name':that.name,
					'source':that.source,
					'isVip':that.isVip,
					'price':that.price,
					'avatar':that.avatar,
					'intro':that.intro,
					'appId':that.appId,
					'apiKey':that.apiKey,
					"prompt":that.prompt
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.gptEdit(),
					data:{
						"params":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"token":that.token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==1){
							var timer = setTimeout(function() {
								that.back();
							}, 1000)
							
						}
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			toAvatar(){
				// #ifdef APP-PLUS || H5
				const that = this;
				  uni.navigateTo({
					url: "/uni_modules/buuug7-img-cropper/pages/cropper",
					events: {
					  imgCropped(event) {
						console.log(event);
					  },
					},
				  });
				// #endif
			},
			getGptInfo(id){
				var that = this;
				var data = {
					"id":id,
					"token":that.token
				}
				
				that.$Net.request({
					url: that.$API.gptInfo(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if(res.data.code==1){
							var gptInfo = res.data.data;
							that.name = gptInfo.name;
							that.source = gptInfo.source;
							that.isVip = gptInfo.isVip;
							if(that.isVip==1){
								that.switchVip = true;
							}else{
								that.switchVip = false;
							}
							that.price = gptInfo.price;
							that.avatar = gptInfo.avatar;
							that.intro = gptInfo.intro;
							that.appId = gptInfo.appId;
							that.apiKey = gptInfo.apiKey;
							that.gptType = gptInfo.type;
							that.prompt = gptInfo.prompt;
						}
					},
					fail: function(res) {
					}
				});
			}
		}
	}
</script>

<style>
</style>
