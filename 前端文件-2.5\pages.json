{
	"easycom": {
		"^tn-(.*)": "@/tuniao-ui/components/tn-$1/tn-$1.vue",
		"^u-(.*)": "@/tuniao-ui/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [
		{
			"path": "pages/home/<USER>",
			"style": {
				"enablePullDownRefresh": true
			},
			"app-plus": {
				"pullToRefresh": {
					"color": "#54b5db",
					"style": "circle"
				}
			}
		}
		// #ifdef APP-PLUS || H5
		,
		{
			"path": "uni_modules/buuug7-img-cropper/pages/cropper",
			"style": {
				"navigationBarTitleText": "图标上传"
			}
		},
		{
			"path": "pages/user/userbind",
			"style": {}
		},
		{
			"path": "pages/manage/comments",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/user/manage",
			"style": {}
		},
		{
			"path": "pages/manage/contents",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/recharge",
			"style": {}
		},
		{
			"path": "pages/manage/shop",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/shoptype",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/shopTypeAdd"
		},
		{
			"path": "pages/manage/users",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/selfDelete",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/usersedit"
		},
		{
			"path": "pages/manage/withdraw",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/finance",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/tokenpay"
		},
		{
			"path": "pages/manage/invitation"
		},
		{
			"path": "pages/manage/metas",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/metasedit"
		},
		{
			"path": "pages/manage/clean"
		},
		{
			"path": "pages/manage/metasedit"
		},
		{
			"path": "pages/manage/ads"
		},
		{
			"path": "pages/manage/senduser"
		},
		{
			"path": "pages/manage/banuser"
		},
		{
			"path": "pages/manage/chat",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/addGroup"
		},
		{
			"path": "pages/manage/userClean"
		},
		{
			"path": "pages/manage/endException"
		},
		{
			"path": "pages/manage/giftVIP"
		},
		{
			"path": "pages/manage/section"
		},
		{
			"path": "pages/manage/moderator"
		},
		{
			"path": "pages/manage/categoryEdit"
		},
		{
			"path": "pages/manage/sectionEdit"
		},
		{
			"path": "pages/manage/setModerator"
		},
		{
			"path": "pages/manage/postReview",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/postComment",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/space",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/consumer",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/manage/company",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/ads/home"
		},
		{
			"path": "pages/ads/adsPost",
			"app-plus": {
				"softinputMode": "adjustResize"
			}
		},
		{
			"path": "pages/ads/myAds"
		}
		// #endif
	],
	"subPackages": [
		{
			"root": "pages/user/",
			"name": "user",
			"pages": [
				{
					"path": "qrcodeLogin",
					"style": {}
				},
				{
					"path": "userAvatar",
					"style": {}
				},
				{
					"path": "switchAccounts",
					"style": {}
				},
				{
					"path": "manage",
					"style": {}
				},
				{
					"path": "userlist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "fanList",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "followList",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "useredit",
					"style": {}
				},
				{
					"path": "phoneedit",
					"style": {}
				},
				{
					"path": "mailedit",
					"style": {}
				},
				{
					"path": "media",
					"style": {}
				},
				{
					"path": "scan",
					"style": {}
				},
				{
					"path": "inbox",
					"style": {}
				},
				{
					"path": "userpost",
					"style": {
						"enablePullDownRefresh": true
					},
					"app-plus": {
						"pullToRefresh": {
							"color": "#54b5db",
							"style": "circle"
						}
					}
				},
				{
					"path": "draftList",
					"style": {},
					"app-plus": {
						"softinputMode": "adjustResize"
					}
				},
				{
					"path": "login"
				},
				{
					"path": "foget",
					"style": {
						"softinputMode": "adjustResize"
					}
				},
				{
					"path": "register",
					"style": {
						"softinputMode": "adjustResize"
					}
				},
				{
					"path": "setup",
					"style": {}
				},
				{
					"path": "task",
					"style": {}
				},
				{
					"path": "signin",
					"style": {}
				},
				{
					"path": "usercomments",
					"style": {}
				},
				{
					"path": "buyvip",
					"style": {}
				},
				{
					"path": "clothes",
					"style": {}
				},
				{
					"path": "usermark",
					"style": {}
				},
				{
					"path": "address",
					"style": {}
				},
				{
					"path": "pay",
					"style": {}
				},
				{
					"path": "myshop",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order",
					"style": {}
				},
				{
					"path": "sellorder",
					"style": {}
				},
				{
					"path": "assets",
					"style": {}
				},
				{
					"path": "userwithdraw",
					"style": {}
				},
				{
					"path": "userrecharge",
					"style": {}
				},
				{
					"path": "userwithdrawlist",
					"style": {}
				},
				{
					"path": "userBg",
					"style": {}
				},
				{
					"path": "agreement",
					"style": {}
				},
				{
					"path": "privacy",
					"style": {}
				},
				{
					"path": "help",
					"style": {}
				},
				{
					"path": "rebate",
					"style": {}
				},
				{
					"path": "inviteList",
					"style": {}
				},
				{
					"path": "identifyblue",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "identify",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/contents/",
			"name": "contents",
			"pages": [
				{
					"path": "webview",
					"style": {}
				},
				{
					"path": "comments",
					"style": {}
				},
				{
					"path": "contentlist",
					"style": {}
				},
				{
					"path": "imagetoday",
					"style": {
						"enablePullDownRefresh": true
					},
					"app-plus": {
						"pullToRefresh": {
							"color": "#54b5db",
							"style": "circle"
						}
					}
				},
				{
					"path": "rewardLog"
				},
				{
					"path": "info",
					"style": {
						"enablePullDownRefresh": true
					},
					"app-plus": {
						"pullToRefresh": {
							"color": "#54b5db",
							"style": "circle"
						}
					}
				},
				{
					"path": "alltag",
					"style": {}
				},
				{
					"path": "randlist",
					"style": {}
				},
				{
					"path": "allcategory",
					"style": {}
				},
				{
					"path": "metas",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "recommend",
					"style": {}
				},
				{
					"path": "search",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "userinfo",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "foreverblog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "blackhouse",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/forum/",
			"name": "forum",
			"pages": [
				{
					"path": "info",
					"style": {}
				},
				{
					"path": "home",
					"style": {}
				},
				{
					"path": "section",
					"style": {}
				},
				{
					"path": "rewardLog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "myPost",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "followPost"
				},
				{
					"path": "draftList",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "moderators"
				}
			]
		},
		{
			"root": "pages/shop/",
			"name": "shop",
			"pages": [
				// #ifdef H5 || APP-PLUS
				{
					"path": "shop",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shopinfo",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				// #endif
				{
					"path": "shoptext"
				},
				{
					"path": "orderpay",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/space/",
			"name": "space",
			"pages": [
				{
					"path": "info",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "post",
					"style": {}
				},
				{
					"path": "reply",
					"style": {}
				},
				{
					"path": "mySpace",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/edit/",
			"name": "edit",
			"pages": [
				{
					"path": "addPost"
				},
				{
					"path": "addshop"
				},
				{
					"path": "articlePost"
				},
				{
					"path": "payText"
				}
			]
		},
		{
			"root": "pages/inbox/",
			"name": "inbox",
			"pages": [
				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "message",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "group",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/chat/",
			"name": "chat",
			"pages": [
				{
					"path": "chat",
					"style": {
						"app-plus": {
							// 将回弹属性关掉
							"bounce": "none"
						}
					}
				},
				{
					"path": "history"
				}
			]
		},
		{
			"root": "pages/identify/",
			"name": "identify",
			"pages": [
				{
					"path": "company"
				},
				{
					"path": "consumer"
				}
			]
		},
		//插件开始
		{
			"root": "pages/plugins/sy_gpt",
			"name": "sy_gpt",
			"pages": [
				{
					"path": "chat"
				},
				{
					"path": "history"
				},
				{
					"path": "app"
				},
				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "all",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "manage/gpt",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "manage/gptchat",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "manage/gptAdd"
				}
			]
		}
		// #ifdef APP-PLUS || H5
		,
		{
			"root": "pages/plugins/sy_appbox",
			"name": "sy-软件库",
			"pages": [
				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "info",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "post",
					"style": {}
				},
				{
					"path": "tipList",
					"style": {}
				},
				{
					"path": "replyinfo",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		}
		// #endif
		,
		{
			"root": "pages/plugins/Fanstey_medal",
			"name": "Fanstey-勋章",
			"pages": [
				{
					"path": "medalShop",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/plugins/Fanstey_AvatarFrame",
			"name": "Fanstey-头像框",
			"pages": [
				{
					"path": "index",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/plugins/Fanstey_Topic",
			"name": "Fanstey-话题",
			"pages": [
				{
					"path": "topicInfo",
					"style": {
						"enablePullDownRefresh": true
					}
				}
			]
		},
		{
			"root": "pages/plugins/xqy_rank",
			"name": "xqy—发帖排行榜",
			"pages": [
				{
					"path": "index",
					"style": {
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/plugins/xqy_medal",
			"name": "xqy-勋章",
			"pages": [
				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "myMedals",
					"style": {
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/plugins/xqy_avatar_frame",
			"name": "xqy-头像框",
			"pages": [
				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "myFrame",
					"style": {
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/plugins/dor_pretty",
			"name": "xqy-靓号",
			"pages": [
				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/plugins/xqy_video",
			"name": "xqy-短视频",
			"pages": [

				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "detail",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "upload",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "myVideos",
					"style": {
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/plugins/xqy_gpt",
			"name": "xqy_gpt",
			"pages": [
				{
					"path": "chat"
				},
				{
					"path": "history"
				},
				{
					"path": "app"
				},
				{
					"path": "home",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "all",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "manage/gpt",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "manage/gptchat",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "manage/gptAdd"
				}
			]
		}
	],
	"globalStyle": {
		"mp-alipay": {
			"transparentTitle": "always",
			"allowsBounceVertical": "NO"
		},
		"navigationBarBackgroundColor": "#3cc9a4",
		"navigationBarTitleText": "小祈愿", //H5标题
		"navigationStyle": "custom",
		"navigationBarTextStyle": "black"
	},
	"usingComponts": true,
	"condition": {
		"current": 0,
		"list": [
			{
				"name": "首页",
				"path": "pages/home/<USER>",
				"query": ""
			}
		]
	}
}