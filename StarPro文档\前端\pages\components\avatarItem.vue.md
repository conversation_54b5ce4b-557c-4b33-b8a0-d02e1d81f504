# avatarItem.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/components/avatarItem.vue.md`
- **组件说明**：此组件用于展示用户头像，可能包含VIP标识、认证标识或等级标识等附加信息。

---

<template>
	<view class="user-header" style="width: 180upx;">
		<view class="user-rz" :style="{ backgroundImage: 'url(' + avatar + ')' }" style="border-radius: 50%;">
			<image  v-if="fanstey_avatarframe" style="width:220rpx;height:220rpx;border-radius:0px;z-index: 90;right:-7px;max-width: initial;" :src="AvatarItem" mode=""></image>
		
			<image v-if="lvrz==1" class="user-rz-icon" :src="rzImg" mode="aspectFill" style="z-index: 100;"></image>
		</view>
	</view>
</template>

<script>
import {
	localStorage
} from '../../js_sdk/mp-storage/mp-storage/index.js'
export default {
    props: {
        avatar: {
		  type: String,
		  default: ''
		},
		lvrz: {
		  type: Number,
		  default: 0
		},
        uid: {
          type: [String, Number],
          default: ''
        }
    },
	watch: {
	    lvrz(newVal) {
	      //console.log('lvrz updated to: ', newVal);
	    }
	  },
	name: "avatarItem",
	data() {
		return {
			AvatarItem:"",
			userinfo:null,
			fanstey_avatarframe:false,
			rzImg: this.$API.SPRz(),
            // 添加缓存相关变量
            frameCache: {},
            cacheExpiration: 5 * 60 * 1000, // 5分钟缓存
            requestInProgress: false
		};
	},
	mounted() {
		const that = this;
		if (localStorage.getItem('userinfo')) {
			that.userinfo = JSON.parse(localStorage.getItem('userinfo'));
		} else {
			that.userinfo = null;
		}
		
		// 获取已开启的插件列表
		var cachedPlugins = localStorage.getItem('getPlugins');
		if (cachedPlugins) {
			const pluginList = JSON.parse(cachedPlugins);
			// 检查插件是否存在于插件列表中
			that.fanstey_avatarframe = pluginList.includes('xqy_avatar_frame');
		}
		if(that.fanstey_avatarframe){//如果开启了插件
			that.getAvatarFrameWithCache();
		}
	},
	methods: {
        // 新增：带缓存的头像框获取方法
        getAvatarFrameWithCache() {
            const that = this;
            // 确定要查询的用户ID
            const queryUid = that.uid || (that.userinfo ? that.userinfo.uid : '');
            if (!queryUid) return;
            
            // 检查是否有缓存
            const cacheKey = `avatar_frame_${queryUid}`;
            const cachedData = localStorage.getItem(cacheKey);
            
            if (cachedData) {
                try {
                    const data = JSON.parse(cachedData);
                    // 检查缓存是否过期
                    if (data.timestamp && (Date.now() - data.timestamp < that.cacheExpiration)) {
                        that.AvatarItem = data.frameUrl || '';
                        return;
                    }
                } catch (e) {
                    // 缓存解析错误，继续获取新数据
                }
            }
            
            // 防止重复请求
            if (that.requestInProgress) return;
            that.requestInProgress = true;
            
            // 缓存不存在或已过期，发起请求
            that.getAvatarFrameByid();
        },
		
		getAvatarFrameByid(){
			var that = this;
			// 确定要查询的用户ID
			const queryUid = that.uid || (that.userinfo ? that.userinfo.uid : '');
			if (!queryUid) {
				return;
			}
			that.$Net.request({
				url:that.$API.PluginLoad('xqy_avatar_frame'),
				data: {
					"action":"get_user_frames",
					"op": "list",
					"type": "view",
					"uid": queryUid
				},
				method: "post",
				dataType: 'json',
				success: function(res) {
				if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
					const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
					if (wearingFrame) {
						that.AvatarItem = wearingFrame.frame_url;
                        // 缓存结果
                        localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
                            frameUrl: wearingFrame.frame_url,
                            timestamp: Date.now()
                        }));
					} else {
                        // 缓存空结果
                        localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
                            frameUrl: '',
                            timestamp: Date.now()
                        }));
                    }
				} else {
                    // 缓存空结果
                    localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
                        frameUrl: '',
                        timestamp: Date.now()
                    }));
                }
                that.requestInProgress = false;
				},
				fail: function(res) {
                    that.requestInProgress = false;
				}
			})
		},
	}
}
</script>

<style>
	.text-content {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 10;
		-webkit-box-orient: vertical;
	}
	
	.text-shojo2 {
		color: #ff6c3e;
	}
	
	.user-info-data {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.xyy {
		margin-left: 0px;
	}
	
	.search-type {
		display: flex;
		position: relative;
		align-items: center;
		justify-content: space-around;
		border-bottom: solid 4upx #f3f3f3;
	}
	
	.search-type-box.active {
		border-bottom: solid 4upx #000000;
		color: #000000;
	}
	
	.user-info-data-box {
		flex-grow: 1;
		text-align: center;
	}
	
	.user-data-num {
		margin-right: 0px;
		font-size: 36upx;
	}
	
	.user-data-label {
		font-size: 28upx;
	}
	
	.sup-script {
		font-size: 28upx;
		font-weight: 400;
	}
	.user-rz image{
		background-color: initial!important;
		border: initial!important;
		position: absolute;
		bottom: -5px;
		right: 6upx;
		
	}
	
	.cu-bar .action2 {
		background: #00000057;
		width: 80upx;
		height: 80upx;
		border-radius: 50%;
		/* text-align: center; */
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.grid2 {
		display: flex;
		flex-direction: row;
		justify-content: normal;
	}
	
	.cu-bar2 {
		margin-top: 10upx;
	}
	
	.userIndex .header .action {
		font-size: 36upx;
	}
	
	.user-rz {
		width: 200upx;
		height: 200upx;
		position: relative;
		display: inline-block;
		background-size: contain;
		background-repeat: no-repeat;
		border: 3px solid white; 
		background-position: center;
	}
	
	.user-rz-icon {
		position: absolute;
		right: 0upx;
		bottom: 0upx;
		width: 60upx;
		height: 60upx;
	}
	
	/*  #ifdef MP || APP-PLUS */
	.user-header image {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
	}
	
	/*  #endif */
	.user-info-main {
		/*  #ifdef H5 || APP-PLUS */
		height: 480rpx;
		/*  #endif */
		/*  #ifdef MP */
		height: 450rpx;
		/*  #endif */
	}
	
	.tn-margin-top-xxl {
		margin-top: 60upx;
	}
	
	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
</style>