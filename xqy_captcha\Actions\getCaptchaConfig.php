<?php
initializeActions();

// 获取前端验证配置（不包含敏感信息）
$sql = "SELECT captcha_type, geetest_id, cloudflare_site_key, recaptcha_site_key, enabled FROM Xqy_Plugin_captcha_config WHERE id = 1";
$result = mysqli_query($connect, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    $config = mysqli_fetch_assoc($result);
    
    // 只返回前端需要的公开配置
    $frontendConfig = [
        'captcha_type' => $config['captcha_type'],
        'enabled' => intval($config['enabled'])
    ];
    
    // 根据验证类型添加对应的公开密钥
    switch ($config['captcha_type']) {
        case 'geetest':
            $frontendConfig['geetest_id'] = $config['geetest_id'];
            break;
        case 'cloudflare':
            $frontendConfig['site_key'] = $config['cloudflare_site_key'];
            break;
        case 'recaptcha':
            $frontendConfig['site_key'] = $config['recaptcha_site_key'];
            break;
    }
    
    send_json(200, "获取配置成功", $frontendConfig);
} else {
    send_json(404, "配置不存在");
}
?>
