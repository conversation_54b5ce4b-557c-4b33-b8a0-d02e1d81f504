<?php
initializeActions();

if ($installed == "true") {
    // 已安装过
    send_json(402, "已安装，无需再次安装");
} else {
    $msg = "";
    // ------------未安装过，开始执行安装--------------
    
    // 1. 创建头像框表
    $sql = "CREATE TABLE IF NOT EXISTS `Sy_Plugin_xqy_avatar_frames` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL COMMENT '头像框名称',
        `description` varchar(200) DEFAULT NULL COMMENT '头像框描述',
        `frame_url` varchar(500) NOT NULL COMMENT '头像框图片URL',
        `max_holders` int(11) DEFAULT 0 COMMENT '最大持有人数，0表示无限制',
        `status` tinyint(1) DEFAULT 1 COMMENT '状态：0禁用，1启用',
        `frame_type` varchar(20) DEFAULT 'static' COMMENT '头像框类型：static静态，dynamic动态',
        `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='头像框表';";
    
    if(mysqli_query($connect, $sql)) {
        $msg .= "头像框表创建成功<br>";
    } else {
        $msg .= "头像框表创建失败: " . mysqli_error($connect) . "<br>";
    }
    
    // 创建头像框获取条件表
    $sql = "CREATE TABLE IF NOT EXISTS `Sy_Plugin_xqy_frame_conditions` (
        `frame_id` int(11) NOT NULL COMMENT '头像框ID',
        `need_review` tinyint(1) NOT NULL DEFAULT '1' COMMENT '获取方式: 0无需审核 1需要审核 2需要购买 3仅限授予',
        `currency_amount` int(11) NOT NULL DEFAULT '0' COMMENT '需要的货币数量',
        `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`frame_id`),
        CONSTRAINT `fk_frame_conditions` FOREIGN KEY (`frame_id`) REFERENCES `Sy_Plugin_xqy_avatar_frames` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='头像框获取条件表';";
    
    if(mysqli_query($connect, $sql)) {
        $msg .= "头像框获取条件表创建成功<br>";
    } else {
        $msg .= "头像框获取条件表创建失败: " . mysqli_error($connect) . "<br>";
    }
    
    // 2. 创建用户头像框关联表
    $sql = "CREATE TABLE IF NOT EXISTS `Sy_Plugin_xqy_user_frames` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `uid` varchar(32) NOT NULL COMMENT '用户ID',
        `frame_id` int(11) NOT NULL COMMENT '头像框ID',
        `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1生效,0已撤销)',
        `is_wearing` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否佩戴中(1佩戴中,0未佩戴)',
        `obtain_type` varchar(20) NOT NULL DEFAULT 'admin' COMMENT '获取方式(admin:管理员颁发,purchase:货币购买,apply:申请通过)',
        `award_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '颁发时间',
        PRIMARY KEY (`id`),
        KEY `idx_uid` (`uid`),
        KEY `idx_frame` (`frame_id`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户头像框关联表';";
    
    if(mysqli_query($connect, $sql)) {
        $msg .= "用户头像框关联表创建成功<br>";
    } else {
        $msg .= "用户头像框关联表创建失败: " . mysqli_error($connect) . "<br>";
    }
    
    // 创建头像框撤销历史表
    $sql = "CREATE TABLE IF NOT EXISTS `Sy_Plugin_xqy_frame_revoke_history` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `uid` varchar(32) NOT NULL COMMENT '用户ID',
        `frame_id` int(11) NOT NULL COMMENT '头像框ID',
        `revoke_time` datetime NOT NULL COMMENT '撤销时间',
        `revoke_reason` varchar(255) DEFAULT NULL COMMENT '撤销原因',
        PRIMARY KEY (`id`),
        KEY `idx_uid` (`uid`),
        KEY `idx_frame` (`frame_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='头像框撤销历史表';";
    
    if(mysqli_query($connect, $sql)) {
        $msg .= "头像框撤销历史表创建成功<br>";
    } else {
        $msg .= "头像框撤销历史表创建失败: " . mysqli_error($connect) . "<br>";
    }
    
    // 3. 创建头像框申请表
    $sql = "CREATE TABLE IF NOT EXISTS `Sy_Plugin_xqy_frame_applications` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `uid` varchar(32) NOT NULL COMMENT '申请用户ID',
        `frame_id` int(11) NOT NULL COMMENT '申请头像框ID',
        `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0待审核,1已通过,2已拒绝)',
        `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
        `review_time` datetime DEFAULT NULL COMMENT '审核时间',
        PRIMARY KEY (`id`),
        KEY `idx_uid` (`uid`),
        KEY `idx_frame` (`frame_id`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='头像框申请表';";
    
    if(mysqli_query($connect, $sql)) {
        $msg .= "头像框申请表创建成功<br>";
    } else {
        $msg .= "头像框申请表创建失败: " . mysqli_error($connect) . "<br>";
    }
    
    // 4. 创建系统设置表
    $sql = "CREATE TABLE IF NOT EXISTS `Sy_Plugin_xqy_frame_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `key` varchar(50) NOT NULL COMMENT '设置项键名',
        `value` varchar(255) NOT NULL COMMENT '设置项值',
        `description` varchar(255) DEFAULT NULL COMMENT '设置项描述',
        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_key` (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='头像框系统设置表';";
    
    if(mysqli_query($connect, $sql)) {
        $msg .= "系统设置表创建成功<br>";
    } else {
        $msg .= "系统设置表创建失败: " . mysqli_error($connect) . "<br>";
    }
    
    // 5. 插入默认设置
    $sql = "INSERT INTO `Sy_Plugin_xqy_frame_settings` (`key`, `value`, `description`) VALUES
        ('apply_cooldown', '3600', '头像框申请被拒绝后的冷却秒数')
    ON DUPLICATE KEY UPDATE `value` = VALUES(`value`);";
    
    if(mysqli_query($connect, $sql)) {
        $msg .= "默认设置插入成功<br>";
    } else {
        $msg .= "默认设置插入失败: " . mysqli_error($connect) . "<br>";
    }
     // -------------------安装完成，以下勿动---------------------
    
    //【勿动】 将插件安装状态设置为已安装
    $currentDir = __DIR__;
    $configFile = str_replace('Actions', '', $currentDir) . 'config.ini';
    if (file_exists($configFile)) {
        $configData = parse_ini_file($configFile, true);
        $configData['plugin']['installed'] = "true";
        // 写回配置文件
        $newConfig = "";
        foreach ($configData as $section => $values) {
            $newConfig .= "[$section]\n";
            foreach ($values as $key => $value) {
                $newConfig .= "$key = \"$value\"\n";
            }
        }

        if (file_put_contents($configFile, $newConfig) !== false) {
            $msg .= "更新配置文件成功。";
        } else {
            $msg .= "更新配置文件失败。";
        }
    } else {
        $msg .= "配置文件不存在。";
    }
    $data['log'] = $msg;
    send_json(200, "插件安装完成", $data);
}