<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :class="isDark?'dark':'bg-white'" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px','background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					全站用户
				</view>
				<view class="action">
					
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		
		<view class="cu-list menu-avatar userList" style="margin-top: 20upx;">
			<view class="no-data" v-if="userList.length==0">
				<text class="cuIcon-text"></text>暂时没有数据
			</view>
			<view class="cu-item" v-for="(item,index) in userList" :key="index" @tap="toUserContents(item)">
				<view class="cu-avatar round lg user-avatar-container" :style="item.style">
					<!-- 头像框 -->
					<image v-if="frameUrls[item.uid]" class="avatar-frame" :src="frameUrls[item.uid]" mode="aspectFit" @error="onFrameLoadError"></image>
				</view>
				<view class="content">
					<view class="text-black">
						<block  v-if="item.screenName">{{item.screenName}}</block>
						<block  v-else>{{item.name}}</block>
						 
						<!--  #ifdef H5 || APP-PLUS -->
						<block v-if="item.isvip>0">
							<block v-if="item.vip==1">
								<text class="isVIP bg-gradual-red">VIP</text>
							</block>
							<block v-else>
								<text class="isVIP bg-yellow">VIP</text>
							</block>
						</block>
						<!--  #endif -->
						
						<!-- 勋章组件 -->
						<medal-item :uid="item.uid" @medal-loaded="onMedalLoaded"></medal-item>
					</view>
					<view class="text-gray text-sm flex">
						<view class="text-cut">
							{{subText(item.introduce,100)}}
						</view> </view>
				</view>
				<view class="action goUserIndex">
						<view class="cu-btn bg-gradual-orange" style="font-size: 26upx;height: 55upx;border-radius: 100upx;">主页</view>
					
				</view>
			</view>
			<view class="load-more" @tap="loadMore" v-if="userList.length>0">
				<text>{{moreText}}</text>
			</view>

		</view>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import medalItem from '../components/medalItem.vue'
	export default {
		mixins: [darkModeMixin],
		components: {
			medalItem
		},
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
			AppStyle:this.$store.state.AppStyle,
				
				userList:[],
				
				page:1,
				moreText:"加载更多",
				isLoad:0,
				isLoading:0,
				
				// 头像框相关
				frameUrls: {},
				fanstey_avatarframe: false,
				// 勋章相关
				userMedals: {}
			}
		},
		onPullDownRefresh(){
			var that = this;
			that.page=1;
			that.getUserList(false);
			setTimeout(function () {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		onReachBottom() {
		    //触底后执行的方法，比如无限加载之类的
			var that = this;
			if(that.isLoad==0){
				that.loadMore();
			}
		},
		onShow(){
			var that = this;
			that.page=1;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			// 检查头像框插件是否启用
			this.checkAvatarFramePlugin();
			that.getUserList(false);
		},
		methods:{
			// 勋章加载完成回调
			onMedalLoaded(medals) {
				this.userMedals = medals;
			},
			
			// 检查头像框插件是否启用
			checkAvatarFramePlugin() {
				try {
					const cachedPlugins = uni.getStorageSync('getPlugins');
					if (cachedPlugins) {
						const pluginList = JSON.parse(cachedPlugins);
						this.fanstey_avatarframe = pluginList.includes('xqy_avatar_frame');
					}
				} catch (error) {
					console.error('检查插件状态失败:', error);
				}
			},
			
			// 加载用户头像框
			loadUserFrame(uid) {
				if (!uid || !this.fanstey_avatarframe) return;
				
				const that = this;
				that.$Net.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": uid
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame) {
								// 使用Vue的响应式更新
								that.$set(that.frameUrls, uid, wearingFrame.frame_url);
							}
						}
					}
				});
			},
			
			// 头像框加载错误处理
			onFrameLoadError() {
				console.error('头像框加载失败');
			},
			
			allCache(){
				var that = this;
				if(localStorage.getItem('userList')){
					that.userList = JSON.parse(localStorage.getItem('userList'));
				}
			},
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				// 获取年月日时分秒值  slice(-2)过滤掉大于10日期前面的0
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				//second = ("0" + date.getSeconds()).slice(-2);
				// 拼接
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				// 返回
				return result;
			},
			loadMore(){
				var that = this;
				that.moreText="正在加载中...";
				that.isLoad=1;
				that.getUserList(true);
				
			},
			getUserList(isPage){
				var that = this;
				var page = that.page;
				if(isPage){
					page++;
				}
				var token = ""
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				
				}
				that.$Net.request({
					url: that.$API.getUserList(),
					data:{
						"searchParams":"",
						"limit":10,
						"page":page,
						"order":"created",
						"token":token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isLoad=0;
						if(res.data.code==1){
							var list = res.data.data;
							if(list.length>0){
								
								var userList = [];
								for(var i in list){
									var arr = list[i];
									arr.style = "background-image:url("+list[i].avatar+");"
									userList.push(arr);
								}
								if(isPage){
									that.page++;
									that.userList = that.userList.concat(userList);
								}else{
									that.userList = userList;
								}
								localStorage.setItem('userList',JSON.stringify(that.userList));
								
								// 为每个用户加载头像框
								if (that.fanstey_avatarframe) {
									userList.forEach(user => {
										if (user.uid) {
											that.loadUserFrame(user.uid);
										}
									});
								}
							}else{
								that.moreText="没有更多数据了";
							}
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						that.isLoad=0;
						that.moreText="加载更多";
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			toUserContents(data){
				var that = this;
				var name = data.name;
				var title = data.name+"的信息";
				if(data.screenName){
					title = data.screenName+" 的信息";
					name = data.screenName
				}
				var id= data.uid;
				var type="user";
				uni.navigateTo({
				    url: '/pages/contents/userinfo?title='+title+"&name="+name+"&uid="+id+"&avatar="+encodeURIComponent(data.avatar)
				});
			},
			subText(text,num){
				if(text){
					if(text.length>num){
						text = text.substring(0,num);
						return text+"……";
					}else{
						return text;
					}
				}else{
					return "Ta还没有个人介绍哦"
				}
			}
		}
	}
	
</script>

<style>
/* 头像框样式 */
.user-avatar-container {
	position: relative;
	overflow: visible !important;
}

.avatar-frame {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 140%;
	height: 140%;
	transform: translate(-50%, -50%);
	z-index: 1;
}

/* 确保头像在头像框上方 */
.cu-avatar.round:after {
	position: relative;
	z-index: 0;
}
</style>
