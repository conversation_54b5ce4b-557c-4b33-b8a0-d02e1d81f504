# userinfo.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/contents/userinfo.vue.md`
- **页面说明**：此页面用于展示用户的个人主页信息，包括基本资料、发布的文章、动态、帖子等。

---

<template>
	<view class="userpost userIndex" :class="AppStyle">
		<view class="header" :style="[{height:CustomBar*2 + 'upx'}]" :class="scrollTop>40?'goScroll':''">
			<view class="cu-bar" :style="{'height': CustomBar*2 + 'upx','padding-top':StatusBar*2 + 'upx'}">
				<view :class="scrollTop<40 && isLoading !== 0 ?'action2 cu-bar2':''" class="action"
					style="width: 60upx;height: 60upx;" @tap="back">
					<text class="tn-icon-left" style="margin-left: 0;margin-right: 0;"></text>
				</view>
				<view class="content text-bold" style="height: 60upx;" :style="[{top:StatusBar*2 + 'upx'}]">
					<block v-if="scrollTop>40">
						<text :class="isvip ? 'text-shojo2' : ''">
							{{name}}
						</text>
					</block>
				</view>
				<!--  #ifdef H5 || APP-PLUS -->
				<view class="action" style="margin-right: 0upx;">
					<view :class="scrollTop<40 && isLoading !== 0 ?'action2 cu-bar2':''" class="action"
						style="width: 60upx;height: 60upx;margin-right: 20upx;margin-left: 0upx;" @tap="toSearch">
						<text class="tn-icon-search" style="margin-left: 0;margin-right: 0;"></text>
					</view>
					<view :class="scrollTop<40 && isLoading !== 0 ?'action2 cu-bar2':''" class="action"
						style="width: 60upx;height: 60upx;" @click="moreShow = true">
						<text class="tn-icon-align" style="margin-left: 0;margin-right: 0;"></text>
					</view>
				</view>
				<!--  #endif -->
			</view>
		</view>
		<!--  #ifdef H5 || APP-PLUS -->

		<tn-popup v-model="moreShow" mode="bottom" :closeBtn="true" :height="mySelf?'30%':'20%'" :borderRadius="20">
			<view class="center-container tn-margin-top-xxl">
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="!mySelf"
					@tap="toJb(name),moreShow=false">
					<text class="tn-icon-warning" style="margin-right: 5px;"></text>举报用户
				</view>
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
					v-if="isFollow==0&&!mySelf" @tap="follow(1),moreShow=false">
					<text class="tn-icon-my-add" style="margin-right: 5px;"></text>立即关注
				</view>
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
					v-if="isFollow==1&&!mySelf" @tap="follow(0),moreShow=false">
					<text class="tn-icon-my-reduce" style="margin-right: 5px;"></text>取消关注
				</view>
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="!mySelf"
					@tap="getPrivateChat(),moreShow=false">
					<text class="tn-icon-email" style="margin-right: 5px;"></text>发送私信
				</view>
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="mySelf"
					@tap="toUserEdit(),moreShow=false">
					<text class="tn-icon-identity" style="margin-right: 5px;"></text>编辑资料
				</view>
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="mySelf"
					@tap="toRz(),moreShow=false">
					<text class="tn-icon-trusty" style="margin-right: 5px;"></text>蓝V申请
				</view>
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="mySelf"
					@tap="tosetSafe(),moreShow=false">
					<text class="tn-icon-safe" style="margin-right: 5px;"></text>账号安全
				</view>
				<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="mySelf"
					@tap="toSet(),moreShow=false">
					<text class="tn-icon-set" style="margin-right: 5px;"></text>系统设置
				</view>
			</view>
		</tn-popup>
		<!--  #endif -->
		<!-- <view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view> -->
		<view class="all-box" style="margin-top:-10upx">

			<view class="user-info" :style="'padding-top:'+NavBar*2 + 'upx;height:480upx;position:initial'">
				<view class="user-info-bg" v-if="userBg==''">
					<tn-lazy-load :image="avatar" imgMode="aspectFill" style="height:450upx"></tn-lazy-load>
				</view>
				<view class="user-info-bg" v-else>
					<tn-lazy-load :image="userBg" imgMode="aspectFill" style="height:450upx"></tn-lazy-load>
				</view>
				<view class="user-info-main" style="background-color: transparent;">
					<view class="user-info-content"
						style="background: transparent;padding: 0upx 0upx 0upx 60upx;z-index: 99;">
						<view class="user-info-col" style="overflow:visible">
							<!-- <view class="user-header" style="width: 180upx;">
								<view class="user-rz">
									<image :src="avatar"
										style="width: 180upx;height: 180upx;border: 6upx solid white;border-radius:90upx;background-color:white;">
									</image>
									<image class="user-rz-icon" :src="rzImg" mode="aspectFill"
										v-if="lvrz==1"></image>
								</view>
							</view> -->
							<avatarItem :avatar="avatar" :lvrz="lvrz" :uid="uid"></avatarItem>
						</view>
						<div class="user-text" style="width: calc(100% - 200upx);color: black;text-align: center;">
							<div class="user-info-data col-3" style="padding:65upx 20upx 0upx 20upx;">
								<div class="user-info-data-box" @tap="goFanList(uid)">
									<div class="user-data-num">{{formatNumber(fanNum)}}<text v-if="fanNum>9999"
											class="sup-script">万</text></div>
									<div class="user-data-label" style="opacity:1">粉丝</div>
								</div>
								<div class="user-info-data-box" @tap="toLink('/pages/user/followList?uid='+uid)">
									<div class="user-data-num">{{formatNumber(fancount)}}<text v-if="fancount>9999"
											class="sup-script">万</text></div>
									<div class="user-data-label" style="opacity:1">关注</div>
								</div>
								<div class="user-info-data-box">
									<div class="user-data-num">{{formatNumber(likesall)}}<text v-if="likesall>9999"
											class="sup-script">万</text></div>
									<div class="user-data-label" style="opacity:1">获赞</div>
								</div>
							</div>
						</div>
					</view>
					<view class="bg-white padding-tb bg-white"
						style="position: absolute; width: 100%; bottom:0upx;border-radius: 32upx 32upx 0 0;height:120upx;">
					</view>
				</view>
			</view>

			<view class="user-name" style="padding: 10upx 60upx 0upx 60upx;">
				<view class="user-info-name"
					style="font-size: 45upx;font-weight: 600;color: black;display: flex;align-items: center;"
					@click="copyName">
					<view :class="isvip ? 'text-shojo2' : ''">
						{{name}}
					</view>
					<view style="line-height: 0px;">
						<image v-if="xb=='man'" :src="manImg"
							style="border-radius: 0px;margin-left: 12upx;margin-right: 0upx;width: 44upx;"
							mode="widthFix"></image>
						<image v-if="xb=='woman'" :src="womanImg"
							style="border-radius: 0px;margin-left: 12upx;margin-right: 0upx;width: 44upx;"
							mode="widthFix"></image>
					</view>
				</view>

				<!-- 靓号和勋章并排显示 -->
				<view class="user-badges-row">
					<!-- 靓号组件 -->
					<pretty-number-item 
						:uid="uid" 
						:showLabel="false"
						@number-loaded="onNumberLoaded"
					/>
					
					<!-- 勋章组件 -->
					<medal-item :uid="uid" @medal-loaded="onMedalLoaded"/>
				</view>

				<view class="user-info-data-box" style="text-align: left;margin-top: 8upx;">
					<text class="user-data-label" @click="copyUid">{{appname}}: </text>
					<text class="user-data-label" style="margin-right: 10upx;" @click="copyUid">{{uid}}</text>
					<text class="tn-icon-copy mirror" @click="copyUid"></text>
				</view>
				<view class="user-info-data-box" v-if="fansteyMedal"
					style="text-align: left;margin-top: 8upx;display: flex;align-items: center;">
					<text class="user-data-label">勋章: </text>
					<view class="medals" style="display: flex;width: 18px;height: 30px;margin-left: 5px;"
						v-for="(item,index) in list">
						<img style="width: 100%;height: 100%;" :src="item.pic" alt="" />
					</view>
				</view>
				<view class="user-data-label text-content" style="margin-top: 10upx;word-wrap: break-word">
					<block v-if="introduce!=''&&introduce">
						{{introduce}}
					</block>
					<block v-else>
						Ta还没有个人介绍哦
					</block>
				</view>

				<view class="user-info-data-box"
					style="margin-top: 10upx;text-align: left;display: flex;align-items: center;">

					<image v-if="isvip>0" :src="vipImg" 
						style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;height: 35upx;" 
						mode="heightFix">
					</image>
					<image :src="lvImg+getLv(experience)+'.png'"
						style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;height: 35upx;"
						mode="heightFix">
					</image>
					<text class="userlv customize"
						style="border: 3upx solid black;color:black;padding: 0upx 8upx;border-radius: 40upx;background-color: transparent;font-size: 22upx;height: 35upx;line-height: 35upx;"
						v-if="customize&&customize!=''">{{customize}}</text>
					<text class="userlv customize"
						style="border: 3upx solid #00BCD4;color:#00BCD4;padding: 0upx 8upx;border-radius: 40upx;background-color: transparent;font-size: 22upx;height: 35upx;line-height: 35upx;"
						v-if="smrz==1">已实名</text>
				</view>


				<view class="user-info-data-box" style="margin-top: 10upx;text-align: center;margin-right: 0upx;">
					<view v-if="uid==vid" class="userInfo-bottom-main grid2 col-2">

						<view class="userInfo-bottom-box" style="padding:0upx 15upx 0upx 0upx;width: 70%;">
							<button class="cu-btn bg-gray" style="border-radius: 100upx" @tap="toUserEdit()"><text
									class="tn-icon-edit-form"></text>编辑资料</button>
						</view>
						<view class="userInfo-bottom-box" style="padding:0upx 0upx 0upx 15upx;width: 30%;">
							<button class="cu-btn"
								style="border-radius: 100upx;background-color: #f0f0f0;color: #333333;"
								@tap="toSet()"><text class="tn-icon-set"></text>设置</button>
						</view>
					</view>
					<view v-else class="userInfo-bottom-main grid col-2">
						<view class="userInfo-bottom-box" style="padding:0upx 15upx 0upx 0upx;">
							<button class="cu-btn bg-gray" style="border-radius: 100upx;" @tap="follow(0)"
								v-if="isFollow==1"><text class="cuIcon-check"></text>已关注</button>
							<button class="cu-btn bg-gray"
								style="border-radius: 100upx;background: #3cc9a4;color: white;" @tap="follow(1)"
								v-else><text class="cuIcon-add"></text>关注</button>
						</view>
						<view class="userInfo-bottom-box" style="padding:0upx 0upx 0upx 15upx;">
							<button class="cu-btn"
								style="border-radius: 100upx;background-color: #f0f0f0;color: #333333;"
								@tap="getPrivateChat()"><text class="cuIcon-mark"></text>私信</button>
						</view>
					</view>
				</view>
			</view>
			<medalItem v-if="fansteyMedal" :list="list"></medalItem>
			<view :class="sy_appbox?'search-type grid col-4':'search-type grid col-3'">
				<view class="search-type-box" v-if="sy_appbox&&appModOrder==1" @tap="toType(4)"
					:class="type==4?'active':''">
					<text>应用</text>
				</view>
				<view class="search-type-box" v-if="wzof==1&&modOrder==2" @tap="toType(0)" :class="type==0?'active':''">
					<text>文章</text>
				</view>
				<view class="search-type-box" v-if="tzof==1" @tap="toType(3)" :class="type==3?'active':''">
					<text>帖子</text>
				</view>
				<view class="search-type-box" v-if="wzof==1&&modOrder==1" @tap="toType(0)" :class="type==0?'active':''">
					<text>文章</text>
				</view>
				<view class="search-type-box" v-if="sy_appbox&&appModOrder==0" @tap="toType(4)"
					:class="type==4?'active':''">
					<text>应用</text>
				</view>
				<view class="search-type-box" @tap="toType(2)" :class="type==2?'active':''">
					<text>动态</text>
				</view>

			</view>
			<view class="margin-top-sm bg-white padding-sm" v-if="type==4">
				<!-- 添加加载动画 -->
				<view class="loading-container" v-if="apploading">
					<u-loading mode="circle" size="36"></u-loading>
				</view>
				
				<!-- 应用列表 -->
				<block v-if="applist.length>0">
					<view class="app-box" v-for="(item, index) in applist" :key="index">
						<view class="app-box-body" @tap="toAppInfo(item.id)">
							<view class="app-box-logo">
								<u-image :src="item.logo" width="110rpx" height="110rpx" mode="aspectFill" 
									:lazy-load="true" :fade="true" duration="450" border-radius="28rpx">
									<u-loading slot="loading"></u-loading>
								</u-image>
							</view>
							<view class="app-box-content">
								<view class="app-box-title text-cut">{{item.name}}</view>
								<view class="app-box-info">
									<text :style="{color: item.tagInfo.color}" 
										:class="item.score>=3?'tn-icon-star-fill':'tn-icon-star'"></text>
									<text :style="{color: item.tagInfo.color}">{{item.score}}</text>
									<text>{{item.size}}</text>
									<text :class="item.system=='ios'?'tn-icon-iphone':''"></text>
									</view>
									<view class="app-box-tags">
										<text class="app-tag"
											:style="{backgroundColor: item.tagInfo.color}">{{item.tagInfo.text}}</text>
										<text v-for="(category, idx) in item.sortJson" :key="idx"
											class="app-category-tag">{{category.name}}</text>
									</view>
							</view>
						</view>
						<view class="app-box-down" @tap="toAppInfo(item.id)">下载</view>
					</view>
				</block>
				
				<!-- 空数据提示 -->
				<block v-else>
					<view class="margin-top-sm">
						<u-empty text="暂无应用" mode="data" icon-size="100" font-size="24"></u-empty>
					</view>
				</block>
				
				<!-- 加载更多 -->
				<view class="load-more" @tap="loadMore" v-if="applist.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
			<view class="cu-card article no-card" v-if="type==0">
				<view class="padding-left-sm padding-right-sm">
					<block v-for="(item,index) in contentsList" :key="index" v-if="type==0&&actStyle==1">
						<articleItemA :item="item"></articleItemA>
					</block>
					<block v-for="(item,index) in contentsList" :key="index" v-if="type==0&&actStyle==2">
						<articleItemB :item="item"></articleItemB>
					</block>
				</view>
				<block v-if="type==0&&actStyle==3">
					<tn-waterfall ref="waterfall" v-model="contentsList" @finish="handleWaterFallFinish"
						style="margin: 0upx 10upx;">
						<template v-slot:left="{ leftList }">
							<view v-for="(item,index) in leftList" :key="index">
								<articleItemWfA :item="item"></articleItemWfA>
							</view>
						</template>
						<template v-slot:right="{ rightList }">
							<view v-for="(item,index) in rightList" :key="index">
								<articleItemWfB :item="item"></articleItemWfB>
							</view>
						</template>
					</tn-waterfall>
				</block>
				<view class="load-more" @tap="loadMore" v-if="contentsList.length>0">
					<text>{{moreText}}</text>
				</view>
				<view class="no-data" v-if="contentsList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有数据
				</view>

			</view>
			<view class="search-space" v-if="type==2">
				<view class="no-data bg-white" v-if="spaceList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有动态
				</view>
				<spaceItem :spaceList="spaceList"></spaceItem>
				<view class="load-more" @tap="loadMore" v-if="spaceList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
			<!--论坛帖子-->
			<view class="forum-list-main" v-if="type==3">
				<view class="no-data" v-if="postList.length==0">
					<text class="cuIcon-text"></text>暂时没有帖子
				</view>
				<block v-for="(item,index) in postList" :key="index">
					<forumItem :item="item" :myPurview="0" :mySelf="mySelf"></forumItem>
				</block>
				<view class="load-more" @tap="loadMore" v-if="postList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
			<!--论坛帖子结束-->
			<!--评论-->
			<view class="cu-list menu-avatar" v-if="type==1">
				<view class="no-data" v-if="commentsList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有评论
				</view>
				<view class="cu-card dynamic no-card" style="margin-top: 20upx;">
					<block v-for="(item,index) in commentsList" :key="index" v-if="commentsList.length>0">
						<commentItem :item="item" :isHead="false"></commentItem>
					</block>

				</view>

				<view class="load-more" @tap="loadMore" v-if="commentsList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
			<!--评论结束-->

			<!--占位区域
			<view style="width: 100%;height: 100upx; background-color: #f6f6f6;"></view>-->
		</view>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
		<view class="full-noLogin" v-if="isuserlogin">
			<view class="full-noLogin-main">
				<view class="full-noLogin-text">
					您需要登录后才可以查看内容哦！
				</view>
				<view class="full-noLogin-btn">
					<view class="cu-btn bg-blue" @tap="goLogin()">
						立即登录
					</view>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	// #ifdef APP-PLUS
	import owo from '../../static/app-plus/owo/OwO.js'
	// #endif
	// #ifdef H5
	import owo from '../../static/h5/owo/OwO.js'
	// #endif
	// #ifdef MP
	var owo = [];
	// #endif
	import medalItem from '../components/medalItem.vue'
	import prettyNumberItem from '../components/prettyNumberItem.vue'
	export default {
		components: {
			medalItem,
			prettyNumberItem
		},
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				sy_appbox: false, 
				appModOrder: 2, 
				applist: [], 
				apppage: 1,
				apploading: false, 
				contentsList: [],
				commentsList: [],
				spaceList: [],
				postList: [],
				isuserlogin: false,
				userList: [],
					owo: owo,
					owoList: [],
					wzof: 0,
					tzof: 0,
					modOrder: 0,
					type: 0,
					submitStatus1: false,
					page: 1,
					moreText: "加载更多",
					list: [],
					isLoad: 0,
					rzImg: this.$API.SPRz(),
					isLoading: 0,
					actStyle: 0,
					title: "",
					uid: 0,
					manImg: this.$API.SPman(),
					womanImg: this.$API.SPwoman(),
					vipImg: this.$API.SPvip(),
					lvImg: this.$API.SPLv(),
					avatar: "",
					name: "",
					customize: "",
					lv: "",
					vip: "",
					xb: "",
					moreShow: false,
					isvip: "",
					experience: 0,
					introduce: "",
					userBg: "",
					fanNum: 0,
					fansteyMedal: false,
					contentsNum: 0,
					commentsNum: 0,
					scrollTop: 0,
					isFollow: 0,
					likesall: 0,
					list: [],
					fancount: 0,
					userinfo: null,
					vid: 0,
					appname: this.$API.GetuserID(),
					local: "",
					mySelf: false,
					lvrz: 0,
					smrz: 0,
					hasPrettyNumber: false,
					hasMedals: false
			}
		},
		onPageScroll(res) {
			var that = this;
			that.scrollTop = res.scrollTop;
		},
		onShow() {
			var that = this;
			uni.$emit('tOnLazyLoadReachBottom');
			

		},
		onReachBottom() {
			//触底后执行的方法，比如无限加载之类的
			var that = this;
			if (that.isLoad == 0) {
				that.loadMore();
			}

		},
		onPullDownRefresh() {
			var that = this;
			var i = that.type;
			that.getUserInfo();
			that.page = 1;
			that.moreText = "加载更多";
			that.isLoad = 0;
			if (i == 0) {
				that.getContentsList(false);
			} else if (i == 1) {
				that.getCommentsList(false)
			} else if (i == 3) {
				that.getPostList(false)
			} else {
				that.getSpaceList(false)
			}
			var timer = setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000)
		},
		onLoad(res) {
			var that = this;
			var cachedPlugins = localStorage.getItem('getPlugins');
			if (cachedPlugins) {
				var pluginList = JSON.parse(cachedPlugins);
				// 检查插件是否存在于插件列表中
				
				// #ifdef APP-PLUS || H5
				that.sy_appbox = pluginList.includes('sy_appbox');
				// #endif
				
				that.fansteyMedal = pluginList.includes('Fanstey_medal');
			}
			
			if (that.fansteyMedal) { //如果开启了插件
				that.getMedalsByid();
			}
			if(that.sy_appbox){
				that.getAppBoxInfo();
			}else{
				that.getSetCC();
			}
			
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif

			that.title = res.title;
			that.uid = res.uid;
			that.avatar = res.avatar;
			
			// 获取基础数据
			
			that.getLike();
			that.identifyStatus();
			that.getIsFollow();
			that.getUserInfo();
			that.getUserInfo();
			that.getSet();
			
			// #ifdef APP-PLUS || H5
			var owo = that.owo.data;
			var owoList = [];
			for (var i in owo) {
				owoList = owoList.concat(owo[i].container);
			}
			that.owoList = owoList;
			// #endif

			// 根据不同类型加载对应数据
			if(that.type == 0) {
				that.getContentsList(false);
			} else if(that.type == 1) {
				that.getCommentsList(false);
			} else if(that.type == 3) {
				that.getPostList(false);
			} else if(that.type == 4) {
				that.getAppList(false);
			} else {
				that.getSpaceList(false);
			}
			
			if (localStorage.getItem('userinfo')) {
				var userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.vid = userInfo.uid;
				that.userinfo = userInfo;
			}
			
			
			// #ifdef APP-PLUS
			if (that.uid == that.vid) {
				that.mySelf = true;
			}
			// #endif
		},
		mounted() {
			var that = this;
			// #ifdef APP-PLUS
			that.initStatusBarStyle();
			// #endif
			console.log('用户页面加载,用户ID:', this.uid)
		},
		methods: {
			// Fanstey-勋章插件
			getMedalsByid() {
				var that = this;
				var data = {
					"action": "getMedalByid",

					"fa_uid": that.uid
				};
				uni.request({
					url: that.$API.PluginLoad('Fanstey_medal'), //括号中填插件名
					data: {
						"action": "getMedalByid",

						"fa_uid": that.uid
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						console.log(res);
						that.list = res.data.data;
						// console.log(that.allMedalList);
					},
					fail: function(res) {

					}
				})
			},
			getSet() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.actStyle = res.data.actStyle;
					},
					fail(error) {}
				})

			},
			getSetCC() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.wzof = res.data.wzof;
						that.tzof = res.data.tzof;
						that.modOrder = res.data.modOrder;
						
						// 设置初始type
						if (that.tzof == 1 && that.wzof == 1 && that.modOrder == 2 && that.appModOrder == 0) {
							that.type = 0;
						} else if (that.tzof == 1 && that.appModOrder == 0) {
							that.type = 3;
						} else if (that.sy_appbox && that.appModOrder == 1) {
							that.type = 4;
						} else {
							that.type = 0;
						}
						console.log("type:"+that.type);

						// 直接加载对应数据
						if(that.type == 0) {
							that.getContentsList(false);
						} else if(that.type == 1) {
							that.getCommentsList(false);
						} else if(that.type == 3) {
							that.getPostList(false);
						} else if(that.type == 4) {
							that.getAppList(false);
						} else {
							that.getSpaceList(false);
						}
					},
					fail(error) {}
				})

			},
			// #ifdef APP-PLUS
			initStatusBarStyle() {
				if (uni.getSystemInfoSync().platform === 'android') {
					plus.navigator.setStatusBarStyle('light'); // Android 平台
				} else {
					plus.navigator.setStatusBarStyle('light'); // iOS 平台
				}

			},
			onPageScroll(event) {
				this.scrollTop = event.scrollTop;
				if (this.scrollTop > 40) {
					if (uni.getSystemInfoSync().platform === 'android') {
						plus.navigator.setStatusBarStyle('dark'); // Android 平台
					} else {
						plus.navigator.setStatusBarStyle('dark'); // iOS 平台
					}
				} else {
					if (uni.getSystemInfoSync().platform === 'android') {
						plus.navigator.setStatusBarStyle('light'); // Android 平台
					} else {
						plus.navigator.setStatusBarStyle('light'); // iOS 平台
					}
				}
			},
			// #endif
			getLike() {
				var that = this;
				uni.request({
					url: that.$API.SPlikeall(),
					method: 'GET',
					data: {
						uid: that.uid
					},
					dataType: "json",
					success(res) {
						that.likesall = res.data.likesall;
						that.fanNum = res.data.fscount;
						that.fancount = res.data.gzcount;
						//console.log(res);
					},
					fail(error) {
						console.log(error);
					}
				})

			},
			getDowns(downs) {
				var that = this;
				if (downs <= 999) {
					return downs;
				} else if (downs > 999 && downs <= 9999) {
					return (downs / 1000).toFixed(1) + "千";
				} else if (downs > 9999) {
					return (downs / 10000).toFixed(1) + "万";
				}
			},
			toType(i) {
				var that = this;
				// 如果切换到相同类型，不重新加载
				if(that.type === i) {
					return;
				}
				
				that.type = i;
				that.page = 1;
				that.moreText = "加载更多";
				that.isLoad = 0;
				
				if(i == 0) {
					that.getContentsList(false);
				} else if(i == 1) {
					that.getCommentsList(false);
				} else if(i == 3) {
					that.getPostList(false);
				} else if(i == 4) {
					that.getAppList(false);
				} else {
					that.getSpaceList(false);
				}
			},
			goFanList(uid) {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/fanList?uid=' + uid
				});
			},

			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			getUserLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			getUserLvStyle(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle = "color:#fff;background-color: " + rankStyle[i];
				return userlvStyle;
			},
			identifyStatus() {
				var that = this;
				that.$Net.request({

					url: that.$API.identifyStatus(),
					data: {
						"uid": that.uid
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						if (res.data.code == 1) {

							that.identifyCompany = res.data.data.identifyCompany;
							that.identifyConsumer = res.data.data.identifyConsumer;
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			copyName() {
				var that = this;
				uni.setClipboardData({
					data: that.name,
					success: function() {
						uni.showToast({
							title: '昵称已复制',
							icon: 'success'
						})
					}
				})
			},
			copyUid() {
				var that = this;
				uni.setClipboardData({
					data: that.uid,
					success: function() {
						uni.showToast({
							title: 'ID已复制',
							icon: 'success'
						})
					}
				})
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				// if(that.uid==that.vid){
				// }
				uni.navigateTo({
					url: text
				});
			},
			copyName() {
				var that = this;
				uni.setClipboardData({
					data: that.name,
					success: function() {
						uni.showToast({
							title: '昵称已复制',
							icon: 'success'
						})
					}
				})
			},
			getUserInfo() {
				var that = this;
				that.$Net.request({

					url: that.$API.getUserInfo(),
					data: {
						"key": that.uid
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							if (res.data.data.screenName) {
								that.name = res.data.data.screenName;
							} else {
								that.name = res.data.data.name;
							}
							that.xb = res.data.data.url;
							that.local = res.data.data.local;
							that.vip = res.data.data.vip;
							that.isvip = res.data.data.isvip;
							that.lv = res.data.data.lv;
							that.avatar = res.data.data.avatar;
							that.customize = res.data.data.customize;
							that.lvrz = res.data.data.lvrz;
							that.smrz = res.data.data.smrz;
							that.introduce = res.data.data.introduce;
							that.experience = res.data.data.experience;
							if (res.data.data.userBg) {
								that.userBg = res.data.data.userBg;
							}

						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			getLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var lv = that.$API.getLever(i);
				return lv;
			},
			loadMore() {
				var that = this;
				that.moreText = "正在加载中...";
				that.isLoad = 1;
				if (that.type == 0) {
					that.getContentsList(true);
				} else if (that.type == 1) {
					that.getCommentsList(true)
				} else if (that.type == 3) {
					that.getPostList(true)
				} else if (that.type == 4) {
					that.getAppList(true)
				} else {
					that.getSpaceList(true)
				}

			},
			goAppInfo() {
				var that = this;
				uni.navigateTo({
					url: '/pages/plugins/sy_appbox/info'
				});
			},
			toJb(title) {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/help?type=user&title=' + title
				});
			},
			reload() {
				var that = this;
				if (that.type == 0) {
					that.getContentsList(false);
				} else if (that.type == 1) {
					that.getCommentsList(false)
				} else if (that.type == 3) {
					that.getPostList(false)
				} else if (that.type == 4) {
					that.getAppList(false)
				} else {
					that.getSpaceList(false)
				}

			},
			getPrivateChat() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				var touid = that.uid;
				var data = {
					"touid": touid,
					"token": token
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.getPrivateChat(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res));
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);

						if (res.data.code == 1) {
							var name = that.name;
							var uid = that.uid;
							var chatid = res.data.data
							uni.redirectTo({
								url: '/pages/chat/chat?uid=' + uid + "&name=" + name + "&chatid=" +
									chatid
							});
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			// getUserData() {
			// 	var that = this;
			// 	that.$Net.request({

			// 		url: that.$API.getUserData(),
			// 		data: {
			// 			"uid": that.uid
			// 		},
			// 		header: {
			// 			'Content-Type': 'application/x-www-form-urlencoded'
			// 		},
			// 		method: "get",
			// 		dataType: 'json',
			// 		success: function(res) {
			// 			//console.log(JSON.stringify(res));
			// 			if (res.data.code == 1) {
			// 				that.fanNum = res.data.data.fanNum;
			// 				that.contentsNum = res.data.data.contentsNum;
			// 				that.commentsNum = res.data.data.commentsNum;
			// 			}
			// 		},
			// 		fail: function(res) {
			// 			uni.showToast({
			// 				title: "网络开小差了哦",
			// 				icon: 'none'
			// 			})
			// 		}
			// 	})
			// },
			goFanList(uid) {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/fanList?uid=' + uid
				});
			},
			getContentsList(isPage) {
				var that = this;
				var token = ""
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;

				}
				var data = {
					"type": "post",
					"authorId": that.uid,
				}
				var page = that.page;
				if (isPage) {
					page++;
				}
				that.$Net.request({
					url: that.$API.getContentsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 8,
						"page": page,
						"order": "created",
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						that.isLoad = 0;
						that.moreText = "加载更多";
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								//that.contentsList = list;
								if (isPage) {
									that.page++;
									that.contentsList = that.contentsList.concat(list);
								} else {
									that.contentsList = list;
								}


							} else {
								that.moreText = "没有更多数据了";
							}
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.stopPullDownRefresh();
						that.moreText = "加载更多";
						that.isLoad = 0;
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			// 瀑布流加载完毕事件
			handleWaterFallFinish() {

			},
			getCommentsList(isPage) {
				var that = this;
				var data = {
					"type": "comment",
					"authorId": that.uid,
				}
				var page = that.page;
				if (isPage) {
					page++;
				}
				that.$Net.request({
					url: that.$API.getCommentsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 5,
						"page": page,
						"order": "created"
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								var commentsList = [];
								for (var i in list) {
									var arr = list[i];
									arr.style = "background-image:url(" + list[i].avatar + ");"
									commentsList.push(arr);
								}
								if (isPage) {
									that.page++;
									that.commentsList = that.commentsList.concat(commentsList);
								} else {
									that.commentsList = commentsList;
								}
							} else {
								that.moreText = "没有更多数据了";
							}

						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.stopPullDownRefresh();
						that.isLoad = 0;
						that.moreText = "加载更多";
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getIsFollow() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					token: token,
					touid: that.uid,
				}
				that.$Net.request({

					url: that.$API.isFollow(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isFollow = res.data.code;
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			goLogin() {
				uni.navigateTo({
					url: '/pages/user/login'
				});
			},
			follow(type) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				var data = {
					token: token,
					touid: that.uid,
					type: type,
				}
				that.isFollow = type;
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.follow(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							if (type == 1) {
								uni.request({
									url: that.$API.SPguanzhu(),
									method: 'GET',
									data: {
										uid: that.vid,
									},
									dataType: "json",
									success(res) {},
									fail() {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络不太好哦",
											icon: 'none'
										})
									}


								})
							} else {
								uni.request({
									url: that.$API.SPquguan(),
									method: 'GET',
									data: {
										uid: that.vid,
									},
									dataType: "json",
									success(res) {},
									fail() {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络不太好哦",
											icon: 'none'
										})
									}


								})
							}
						}
						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						that.getIsFollow();
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						that.getIsFollow();
					}
				})
			},

			commentsAdd(title, coid, reply) {
				var that = this;
				var cid = that.cid;
				uni.navigateTo({
					url: '/pages/contents/commentsadd?cid=' + cid + "&coid=" + coid + "&title=" + title +
						"&isreply=" + reply
				});
			},
			toPost() {
				var that = this;

				uni.navigateTo({
					url: '/pages/edit/articlePost'
				});
			},
			toRz() {
				var that = this;
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/user/identifyblue'
				});
			},
			toUserBg() {
				var that = this;
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/user/userBg'
				});
			},

			toUserEdit() {
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				var that = this;
				uni.navigateTo({
					url: '/pages/user/useredit?backif=1&type=setInfo'
				});
			},
			tosetSafe() {
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				var that = this;
				uni.navigateTo({
					url: '/pages/user/useredit?backif=1&type=setSafe'
				});
			},
			toSet() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/setup'
				});
			},
			toEdit(cid) {
				var that = this;
				uni.navigateTo({
					url: '/pages/edit/articlePost?type=edit' + '&cid=' + cid
				});
			},
			getUserLv(i) {
				var that = this;
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			getUserLvStyle(i) {
				var that = this;
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle = "color:#fff;background-color: " + rankStyle[i];
				return userlvStyle;
			},
			toInfo(data) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/info?cid=' + data.cid + "&title=" + data.title
				});
			},
			toInfoComment(cid, title) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/info?cid=' + cid + "&title=" + title
				});
			},
			toSearch() {
				var that = this;

				uni.redirectTo({
					url: '/pages/contents/search'
				});
			},
			toEdit() {
				var that = this;

				uni.redirectTo({
					url: '/pages/user/useredit'
				});
			},
			ToCopy(text) {
				var that = this;
				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: text,
					success: () => {
						uni.showToast({
							title: "复制成功"
						})
					}
				});
				// #endif
				// #ifdef H5 
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select();
				textarea.setSelectionRange(0, text.length);
				uni.showToast({ //提示
					title: "复制成功"
				})
				var result = document.execCommand("copy")
				textarea.remove();

				// #endif
			},
			formatNumber(num) {
				return num >= 1e4 ? (num / 1e4).toFixed(1) + '' : num
			},
			replaceSpecialChar(text) {
				if (!text) {
					return false;
				}
				text = text.replace(/&quot;/g, '"');
				text = text.replace(/&amp;/g, '&');
				text = text.replace(/&lt;/g, '<');
				text = text.replace(/&gt;/g, '>');
				text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			subText(text, num) {
				if (text) {
					if (text.length > num) {
						text = text.substring(0, num);
						return text + "……";
					} else {
						return text;
					}
				} else {
					return "Ta还没有个人介绍哦"
				}
			},
			getSpaceList(isPage) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;

				}
				var page = that.page;
				if (isPage) {
					page++;
				}
				var data = {
					"uid": that.uid
				}
				that.$Net.request({
					url: that.$API.spaceList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 10,
						"page": page,
						"order": "created",
						"token": token
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.changeLoading = 1;
						that.isLoad = 0;
						that.moreText = "加载更多";
						if (!isPage) {
							that.dataLoad = true;
						}
						if (res.data.code == 1) {
							var list = res.data.data;
							var spaceList = [];
							for (var i in list) {
								if (list[i].type == 0) {
									if (list[i].pic) {
										var pic = list[i].pic;
										list[i].picList = pic.split("||");
									} else {
										list[i].picList = [];
									}

								}
								if (list[i].type == 2) {
									if (list[i].forwardJson.pic) {
										var pic = list[i].forwardJson.pic;
										list[i].forwardJson.picList = pic.split("||");
									} else {
										list[i].forwardJson.picList = [];
									}

								}
							}
							spaceList = list;
							if (list.length > 0) {
								if (isPage) {
									that.page++;
									that.spaceList = that.spaceList.concat(spaceList);
								} else {
									that.spaceList = spaceList;
								}

							} else {
								that.moreText = "没有更多动态了";
							}
						}
					},
					fail: function(res) {

						that.changeLoading = 1;
						that.isLoad = 0;
						that.moreText = "加载更多";
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getPostList(isPage, isLogin) {
				var that = this;
				if (that.submitStatus1) {
					return false;
				}
				that.submitStatus1 = true;
				var page = that.page;
				var token = "";
				if (!isLogin) {
					localStorage.setItem('isbug', '1');
				}

				if (isPage) {
					page++;
				}
				var data = {
					"status": "1",
					"authorId": that.uid,
				}
				that.$Net.request({
					url: that.$API.postList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 10,
						"page": page,
						"order": 'created',
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								var postList = list;
								for (var i in postList) {
									postList[i].isAds = 0;
								}
								if (isPage) {
									that.page++;
									that.postList = that.postList.concat(postList);
								} else {
									that.postList = postList;
								}
							} else {
								if (isPage) {
									that.moreText = "没有更多数据了";
								} else {
									that.postList = [];
								}

							}
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.isuserlogin = true
								} else {
									that.getPostList(isPage, true);
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						if (!isLogin) {
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						that.changeLoading = 1;
						that.isLoad = 0;
						that.moreText = "加载更多";
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getLocal(local) {
				var that = this;
				if (local && local != '') {
					var local_arr = local.split("|");
					if (!local_arr[3] || local_arr[3] == 0) {
						return local_arr[2];
					} else {
						return local_arr[3];
					}

				} else {
					return "未知"
				}

			},
			getAppBoxInfo(){
				var that = this;
				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getConfig"
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if(res.data.code == 200) {
							that.appModOrder = res.data.data.appModOrder;
							// that.appTopOf = res.data.data.appTopOf;
							// that.homeModApp = res.data.data.homeModApp;
							// that.appAudit = res.data.data.appAudit;
							// that.scoreAudit = res.data.data.scoreAudit;
						} else {
							console.log(res.data.msg)
						}
						that.getSetCC();
					},
					fail(error) {
						that.apploading = false;
						console.log(error);
						uni.showToast({
							title: "网络开小差了",
							icon: 'none'
						});
					}
				});
			},
			// 获取用户应用列表
			getAppList(isPage) {
				const that = this;
				if(that.apploading) return;
				
				if(!isPage) {
					that.apppage = 1;
					that.dataLoad = true;
					that.apploading = true;
				}
				
				let page = that.apppage;
				if(isPage) {
					page++;
				}

				const data = {
					authorId: that.uid
				};

				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getAppList",
						"getapp_page": page,
						"getapp_limit": 10,
						"getapp_order": "created",
						"getapp_if": JSON.stringify(that.$API.removeObjectEmptyKey(data))
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if(res.data.code == 200) {
							const list = res.data.data || []; // 添加空数组作为默认值
							if(list.length > 0) {
								const mappedList = list.map(item => {
									return {
										...item,
										tagInfo: {
											text: item.type == 1 ? '搬运' : 
												item.type == 2 ? '原创' : 
												item.type == 3 ? '金标' : 
												item.type == 4 ? '官方' : '未知',
											color: item.type == 1 ? '#7c72ff' : 
												item.type == 2 ? '#19be6b' : 
												item.type == 3 ? '#ff6600' : 
												item.type == 4 ? '#2979ff' : '#999'
										},
										size: that.formatSize(item.size)
									};
								});
								
								that.isLoad = 0;
								if(isPage) {
									if(list.length < 1) {
										that.moreText = "没有更多数据了";
									}
									that.applist = [...that.applist, ...mappedList];
									that.apppage = page;
								} else {
									that.applist = mappedList;
									that.apppage = 1;
								}
							} else {
								if(!isPage) {
									that.applist = [];
								}
								that.moreText = "没有更多数据了";
							}
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
						that.apploading = false;
					},
					fail(error) {
						that.apploading = false;
						console.log(error);
						uni.showToast({
							title: "网络开小差了",
							icon: 'none'
						});
					}
				});
			},

			// 格式化文件大小
			formatSize(size) {
				if(!size) return '未知大小';
				if(size >= 1024 * 1024) {
					return (size / (1024 * 1024)).toFixed(1) + 'Gb';
				} else if(size >= 1024) {
					return (size / 1024).toFixed(1) + 'Mb';  
				} else {
					return size.toFixed(1) + 'Kb';
				}
			},
			toAppInfo(id) {
				uni.navigateTo({
					url: '/pages/plugins/sy_appbox/info?id=' + id
				});
			},
			onMedalLoaded(medals) {
				//console.log('用户勋章加载完成:', medals)
				this.hasMedals = !!medals
			},
			onNumberLoaded(numberData) {
				//console.log('用户靓号加载完成:', numberData)
				this.hasPrettyNumber = !!numberData
			}
		}
	}
</script>

<style>
	.text-content {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 10;
		-webkit-box-orient: vertical;
	}

	.text-shojo2 {
		color: #ff6c3e;
	}

	.user-info-data {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.xyy {
		margin-left: 0px;
	}

	.search-type {
		display: flex;
		position: relative;
		align-items: center;
		justify-content: space-around;
		border-bottom: solid 4upx #f3f3f3;
	}

	.search-type-box.active {
		border-bottom: solid 4upx #000000;
		color: #000000;
	}

	.user-info-data-box {
		flex-grow: 1;
		text-align: center;
	}

	.user-data-num {
		margin-right: 0px;
		font-size: 36upx;
	}

	.user-data-label {
		font-size: 28upx;
	}

	.sup-script {
		font-size: 28upx;
		font-weight: 400;
	}

	.cu-bar .action2 {
		background: #00000057;
		width: 80upx;
		height: 80upx;
		border-radius: 50%;
		/* text-align: center; */
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.grid2 {
		display: flex;
		flex-direction: row;
		justify-content: normal;
	}

	.cu-bar2 {
		margin-top: 10upx;
	}

	.userIndex .header .action {
		font-size: 36upx;
	}

	.user-rz {
		position: relative;
		display: inline-block;
	}

	.user-rz-icon {
		position: absolute;
		right: 0upx;
		bottom: 0upx;
		width: 60upx;
		height: 60upx;
	}

	/*  #ifdef MP || APP-PLUS */
	.user-header image {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
	}

	/*  #endif */
	.user-info-main {
		/*  #ifdef H5 || APP-PLUS */
		height: 480rpx;
		/*  #endif */
		/*  #ifdef MP */
		height: 450rpx;
		/*  #endif */
	}

	.tn-margin-top-xxl {
		margin-top: 60upx;
	}

	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.userlv {
		margin-top: 0px;
	}

	.app-box {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.app-box-body {
		flex: 1;
		display: flex;
		margin-right: 20rpx;
		min-width: 0;
	}

	.app-box-logo {
		width: 110rpx;
		height: 110rpx;
		flex-shrink: 0;
	}

	.app-box-content {
		flex: 1;
		margin-left: 20rpx;
		min-width: 0;
	}

	.app-box-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		width: 400rpx;
	}

	.app-box-info {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;
	}

	.app-box-info text {
		margin-right: 10rpx;
	}

	.app-box-tags {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.app-tag {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		color: #ffffff;
		margin-right: 12rpx;
		font-size: 24rpx;
	}
	.app-category-tag {
		 padding: 4rpx 12rpx;
		 border-radius: 8rpx;
		 background-color: #f5f5f5;
		 color: #666666;
		 margin-right: 12rpx;
		 font-size: 24rpx;
	 }

	.app-box-down {
		background-color: #3cc9a4;
		color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 100rpx;
		white-space: nowrap;
	}

	.text-cut {
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}

	.user-badges-row {
		display: flex;
		align-items: center;
		margin-top: 8rpx;
		min-height: 0;  // 允许容器高度自适应
	}

	.badge-item {
		display: flex;
		align-items: center;
	}
</style>