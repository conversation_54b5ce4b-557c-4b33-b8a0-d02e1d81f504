# forumIndex.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/components/forumIndex.vue.md`
- **组件说明**：此组件可能是用于论坛首页或特定版块首页展示帖子列表的条目，通常包含帖子标题、摘要、作者、发帖时间、浏览/回复数等关键信息。

---

<template>
	<view class="forumIndex">
		<view v-if="swiperType==1" :class="{'data-box-2-banner-1': !swiperStyle}">
		<tn-swiper :list="swiperList" :effect3d="swiperStyle" :class="swiperStyle?'':'uni-swiper-slides-1'" @click="swiperclick" backgroundColor="#fff" :height="swiperHeight"
			:effect3dPreviousSpacing="80"></tn-swiper>
		</view>
		 <swiper class="screen-swiper" v-if="isSwiper==1&&swiperType==2" :class="dotStyle?'square-dot':'round-dot'" :indicator-dots="true" :circular="true"
		 :autoplay="true" interval="5000" duration="500">
			<swiper-item v-for="(item,index) in swiperList" :key="index" @click="swiperclick(index)" >
				<image :src="item.url" mode="aspectFill"></image>
				<view class="forum-swiper">
					<view class="forum-swiper-bg"></view>
					<view class="forum-swiper-title clamp-text-1">{{item.title}}</view>
				</view>
			</swiper-item>
		</swiper>
		<view class="data-box section-box" style="margin-top: 0;" v-if="recommendOf==1">
			<view class="section-box-title ">
				<text class="left-title"></text>圈子推荐
			</view>
		  <!-- 方式16 start-->
		  <view class="tn-flex tn-flex-wrap">
		    <block v-for="(item, index) in recommendSectionList" :key="index">
		      <view class="" style="width: 25%;" @click="goSection(item.id)">
		        <view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center ">
		          <view class="tn-radius tn-padding-sm">
		            <view class="image-pic" :style="'background-image:url('+ item.pic +')'">
		              <view class="image-circle">
		              </view>
		            </view>
		            <view class="tn-text-center tn-padding-top-xs text-content-1" style="font-size: 27upx;">{{item.name}}</view>
		            <view class="tn-text-center tn-text-xs tn-color-gray--dark tn-padding-top-xs">{{formatNumber(item.followNum)}}人加入</view>
		          </view>
		        </view>
		      </view>
		    </block>
		  </view>
		  <!-- 方式16 end-->
		</view>
		<view class="section">
			<view class="section-box" v-if="kuaijie == 1">
				<view class="section-main">
					<view class="section-box-title">
						<text class="left-title"></text>快捷入口
					</view>
					<view class="section-box-list grid col-2" :style="fatherTitle == 1?'':'margin-top:30upx'">
						<view class="section-item forum-shadow"  @tap="goLinks('/pages/forum/followPost')"
						:class="{
						  'border-radius-10': radiusBoxStyle == 1,
						  'border-radius-32 ': radiusBoxStyle == 2,
						  'border-radius-50': radiusBoxStyle == 3
						}">
							<view class="section-ico">
								<view class="section-ico-no bg-pink"
								:class="{
							  'border-radius-10': radiusStyle == 1,
							  'border-radius-32 ': radiusStyle == 2,
							  'border-radius-50': radiusStyle == 3
							}">
									<text class="cuIcon-likefill"></text>
								</view>
							</view>
							<view class="section-intro">
								<view class="section-item-tile">
									关注人
								</view>
								<view class="section-item-value">
									关注人帖子
								</view>
							</view>
						</view>
						<view class="section-item forum-shadow" @tap="goLinks('/pages/forum/section')"
						:class="{
						  'border-radius-10': radiusBoxStyle == 1,
						  'border-radius-32 ': radiusBoxStyle == 2,
						  'border-radius-50': radiusBoxStyle == 3
						}">
							<view class="section-ico">
								<view class="section-ico-no bg-purple"
								:class="{
								  'border-radius-10': radiusStyle == 1,
								  'border-radius-32 ': radiusStyle == 2,
								  'border-radius-50': radiusStyle == 3
								}">
									<text class="cuIcon-likefill"></text>
								</view>
							</view>
							<view class="section-intro">
								<view class="section-item-tile">
									圈子列表
								</view>
								<view class="section-item-value">
									查看所有圈子
								</view>
							</view>
						</view>
			
					</view>
				</view>
				
			</view>
			<view class="section-box" v-if="fatherTitle == 0">
				<view class="section-main">
					<view class="section-box-title">
						<text class="left-title"></text>全部圈子
					</view>
				</view>
			</view>
			<view class="section-box" v-for="(item,index) in sectionList" :key="index">
				<view class="section-main">
					<view class="section-box-title" v-if="fatherTitle == 1">
						<text class="left-title"></text>{{item.name}}
					</view>
					<view class="section-box-list grid col-2">
						<view class="section-item forum-shadow" v-for="(data,j) in item.subList" :key="j" @tap="goSection(data.id)"
						 :class="{
						   'border-radius-10': radiusBoxStyle == 1,
						   'border-radius-32 ': radiusBoxStyle == 2,
						   'border-radius-50': radiusBoxStyle == 3
						 }" >
							<view class="section-ico"
							:class="{
							  'img-border-radius-10': radiusStyle == 1,
							  'img-border-radius-32 ': radiusStyle == 2,
							  'img-border-radius-50': radiusStyle == 3
							}">
								<image :src="data.pic" mode="aspectFill" v-if="data.pic&&data.pic!=''"></image>
								<image src="https://starimg.illlt.com/upload/2024/1/27/51139d81-1c5b-4568-a50b-79dae4656a31.png" mode="aspectFill" v-else></image>
							</view>
							<view class="section-intro">
								<view class="section-item-tile clamp-text-1">
									{{data.name}}
								</view>
								<view class="section-item-value clamp-text-1">
									{{formatNumber(data.postNum)}} 帖子<span style="font-size: 20upx;margin: 0upx 10upx;">|</span> {{formatNumber(data.followNum)}} 加入
								</view>
								 
							</view>
						</view>

					</view>
				</view>
				
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		props: {
		    sectionList: {
			  type: Array,
			  default: () => []
			},
		    forumSwiper: {
			  type: Array,
			  default: () => []
			},
			swiperList: {
			  type: Array,
			  default: () => []
			},
			recommendSectionList: {
			  type: Array,
			  default: () => []
			},
			recommendOf: {
			  type: Number,
			  default: 1
			},
			kuaijie: {
			  type: Number,
			  default: 0
			},
			swiperType: {
			  type: Number,
			  default: 1
			},
			fatherTitle: {
			  type: Number,
			  default: 1
			},
			isSwiper: {
			  type: Number,
			  default: 1
			},
			radiusStyle:{
		      type: Number,
			  default: 1
			},
			radiusBoxStyle:{
			  type: Number,
			  default: 1
			},
			swiperStyle:{
				type: Boolean,
				default: false
			}
		},
		name: "forumIndex",
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				cardCur: 0,
				dotStyle: false,
				swiperHeight: this.$API.SPforumSwiperHeight(),
				towerStart: 0,
				direction: ''
			};
		},
		methods: {
			goLogin(){
				uni.navigateTo({
				    url: '/pages/user/login'
				});
			},
			goSection(id){
				var that = this;
				uni.navigateTo({
				    url: '/pages/forum/home?id='+id
				});
			},
			goLinks(link){
				var that = this;
				uni.navigateTo({
				    url: link
				});
			},
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},
			swiperclick(index) {
				//console.log('Clicked on index:', index);
				const data = this.swiperList[index];
				if (data.type=='image') {
					this.goInfo(data)
				}else{
					this.goAds2(data.zt)
				}
				
			},
			goAds2(url) {
				var that = this;
				// #ifdef APP-PLUS
				plus.runtime.openWeb(url);
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			goInfo(data){
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/info?id='+data.id
				});
			},
		}
	}
</script>

<style>
	.text-content-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}
  .image-circle{
    width: 108rpx;
    height: 108rpx;
    font-size: 40rpx;
    font-weight: 300;
    position: relative;
	box-shadow: 0rpx 4rpx 20rpx #e7e7e7;
	border-radius: 40rpx;
  }
  .image-pic{
    background-size: cover;
    background-repeat:no-repeat;
    background-position:top;
    border-radius: 40rpx;
  }
	.left-title{
		border-left: 6px solid #000;
		margin-right: 15upx;
		border-radius: 20upx;
	}
	.grid.col-2>view{
		width: 47%;
	}
	.forum-shadow {
	 box-shadow: 0upx 10upx 20upx rgb(207 207 207 / 40%);
	}
	
	.img-border-radius-10 image{
		border-radius: 20upx;
	}
	.img-border-radius-32 image{
		border-radius: 32upx;
	}
	.img-border-radius-50 image{
		border-radius: 50%;
	}
	.border-radius-10{
		border-radius: 20upx;
	}
	.border-radius-32{
		border-radius: 32upx;
	}
	.border-radius-50{
		border-radius: 100upx;
	}
	.section-item{
		padding: 10upx;
		transition: all 0.2s;
		margin: 12upx 8upx;
	}
	.section-item-value{
		font-size: 22upx;
	}
	.section-item-tile{
		margin-top: 12upx;
	}
	.section-box-title{
		color: #333;
		font-size: 30upx !important;
		font-weight: bold;
		padding:4upx 20upx 0upx 20upx;
		margin-bottom: 6upx;
		margin-top: 15upx;
	}
	.clamp-text-1 {
	  -webkit-line-clamp: 1;
	  display: -webkit-box;
	  -webkit-box-orient: vertical;
	  text-overflow: ellipsis;
	  overflow: hidden;
	}
	.data-box-2-banner-1 {
		padding: 0;
		margin: 0 20upx;
	}
	.forumIndex {
    background-color: #ffffff;
    color: #333333;
}
</style>