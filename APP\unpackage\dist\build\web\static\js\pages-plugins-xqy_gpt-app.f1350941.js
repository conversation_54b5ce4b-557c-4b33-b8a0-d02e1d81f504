(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-plugins-xqy_gpt-app"],{"2cbe":function(t,a,e){"use strict";e.d(a,"b",(function(){return n})),e.d(a,"c",(function(){return s})),e.d(a,"a",(function(){return i}));var i={mpHtml:e("efec").default},n=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("v-uni-view",[i("v-uni-view",{staticClass:"header gpt-header",class:t.scrollTop>40?"goScroll":"",style:[{height:t.CustomBar+"px"}]},[i("v-uni-view",{staticClass:"cu-bar",style:{height:t.CustomBar+"px","padding-top":t.<PERSON>+"px"}},[i("v-uni-view",{staticClass:"action",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.back.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"cuIcon-back"})],1),i("v-uni-view",{staticClass:"content",style:[{top:t.StatusBar+"px"}]},[i("v-uni-text",{staticClass:"text-bold"},[t._v(t._s(t.name))])],1),i("v-uni-view",{staticClass:"action"},[""!=t.avatarstyle?i("v-uni-view",{staticClass:"cu-avatar round",style:t.avatarstyle}):i("v-uni-view",{staticClass:"cu-avatar round"},[i("v-uni-text",{staticClass:"home-noLogin"})],1)],1)],1)],1),i("v-uni-view",{style:[{padding:t.NavBar+"px 10px 0px 10px"}]}),i("v-uni-view",{staticClass:"gpt-bg"},[i("v-uni-image",{attrs:{src:e("667b")}})],1),i("v-uni-view",{staticClass:"gpt-app-info"},[i("v-uni-view",{staticClass:"data-box"},[i("v-uni-view",{staticClass:"cu-bar bg-white"},[i("v-uni-view",{staticClass:"action data-box-title"},[i("v-uni-text",{staticClass:"cuIcon-titles text-rule"}),t._v("内容输入")],1),i("v-uni-view",{staticClass:"action"},[0==t.isWaiting?[i("v-uni-text",{staticClass:"cu-btn sm bg-blue radius",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.sendText()}}},[t._v("提交")])]:t._e(),1==t.isWaiting?[i("v-uni-text",{staticClass:"cu-btn sm bg-blue radius"},[t._v("提交中...")])]:t._e()],2)],1),i("v-uni-view",{staticClass:"gpt-app-form"},[i("v-uni-view",{staticClass:"gpt-app-form-intro"},[t._v(t._s(t.intro))]),i("v-uni-view",{staticClass:"gpt-app-form-intro"},[t._v("价格："),i("v-uni-text",{staticClass:"text-orange"},[t._v(t._s(t.price))]),t._v(t._s(t.currencyName))],1),i("v-uni-view",{staticClass:"gpt-app-form-input"},[i("v-uni-textarea",{attrs:{placeholder:"请输入你想要让AI处理的内容",maxlength:"1500"},model:{value:t.text,callback:function(a){t.text=a},expression:"text"}})],1)],1)],1),i("v-uni-view",{staticClass:"data-box"},[i("v-uni-view",{staticClass:"cu-bar bg-white"},[i("v-uni-view",{staticClass:"action data-box-title"},[i("v-uni-text",{staticClass:"cuIcon-titles text-rule"}),t._v("AI输出")],1),i("v-uni-view",{staticClass:"action"},[i("v-uni-text",{staticClass:"text-blue",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.ToCopy(t.aiMsg)}}},[t._v("复制结果")])],1)],1),i("v-uni-view",{staticClass:"gpt-app-form"},[i("v-uni-view",{staticClass:"gpt-app-form-intro"},[t._v("下列为AI的输出结果，可点击按钮复制。")]),i("v-uni-view",{staticClass:"gpt-app-form-input"},[i("mp-html",{attrs:{content:t.aiMsg,selectable:!0,"show-img-menu":!0,"scroll-table":!0,markdown:!0}})],1)],1)],1)],1)],1)},s=[]},"667b":function(t,a,e){t.exports=e.p+"assets/gpt-bg.356d1f75.png"},"82a9":function(t,a,e){"use strict";e.r(a);var i=e("2cbe"),n=e("9a10");for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(s);var o=e("828b"),u=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"d23db6d0",null,!1,i["a"],void 0);a["default"]=u.exports},"9a10":function(t,a,e){"use strict";e.r(a);var i=e("b382"),n=e.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(s);a["default"]=n.a},b382:function(t,a,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=i(e("fcf3")),s=e("9254"),o={data:function(){return{StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,name:"未知应用",avatar:"",price:0,intro:"",id:0,token:"",avatarstyle:"",aiMsg:"",text:"",currencyName:"",isWaiting:0,scrollTop:0}},onPageScroll:function(t){this.scrollTop=t.scrollTop},onPullDownRefresh:function(){},onHide:function(){s.localStorage.removeItem("getuid")},onShow:function(){s.localStorage.getItem("getuid")&&(this.toid=s.localStorage.getItem("getuid"))},onLoad:function(t){var a=this;uni.request({url:a.$API.SPset(),method:"GET",dataType:"json",success:function(t){a.currencyName=t.data.assetsname},fail:function(t){console.log(t)}}),t.model_id?(a.id=t.model_id,t.name&&(a.name=t.name),a.getGptInfo()):uni.showToast({title:"模型ID不存在",icon:"none"})},methods:{back:function(){uni.navigateBack({delta:1})},getGptInfo:function(){var t=this;t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"models",id:t.id},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(a){if(1==a.data.code||200==a.data.code){var e=null;"object"===(0,n.default)(a.data.data)&&null!==a.data.data?e=a.data.data.info?a.data.data.info:a.data.data:Array.isArray(a.data.data)&&a.data.data.length>0&&(e=a.data.data[0]),e?(t.avatarstyle="background-image:url("+e.avatar+");",t.avatar=e.avatar,t.name=e.name,t.price=e.price,t.intro=e.intro):uni.showToast({title:"无法获取模型信息",icon:"none"})}else uni.showToast({title:a.data.msg||"获取模型信息失败",icon:"none"})},fail:function(t){uni.showToast({title:"网络连接失败",icon:"none"})}})},ToCopy:function(t){var a=document.createElement("textarea");a.value=t,a.readOnly="readOnly",document.body.appendChild(a),a.select(),a.setSelectionRange(0,t.length),uni.showToast({title:"复制成功"});document.execCommand("copy");a.remove()},sendText:function(){var t=this,a="";if(""==t.text)return!1;if(!s.localStorage.getItem("userinfo"))return uni.showToast({title:"请先登录",icon:"none"}),uni.navigateTo({url:"/pages/user/login"}),!1;var e=JSON.parse(s.localStorage.getItem("userinfo"));if(a=e.token,t.text.length>1500)return uni.showToast({title:"最大字符数为1500",icon:"none"}),!1;t.aiMsg="AI正在思考中...",t.isWaiting=1,t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"chat",model_id:t.id,message:t.text,token:a},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(a){t.isWaiting=0,1==a.data.code||200==a.data.code?a.data.data&&a.data.data.response?t.aiMsg=a.data.data.response:(t.aiMsg="AI 回复解析失败",uni.showToast({title:"AI回复解析失败",icon:"none"})):(t.aiMsg=a.data.msg||"请求失败",uni.showToast({title:a.data.msg||"请求失败",icon:"none"}))},fail:function(a){t.isWaiting=0,t.aiMsg="网络请求失败",uni.showToast({title:"网络开小差了哦",icon:"none"})}})}}};a.default=o}}]);