# articleItemWfB.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/components/articleItemWfB.vue.md`
- **组件说明**：推测是另一种文章列表项的瀑布流（Waterfall）样式组件的B版本，可能与A版本在布局或展示信息上有所不同。

---

<template>
	<view v-if="item.isAds" class="product__item home-shadow" @tap="goAds(item)">
		<view class="item__image" v-if="item.img">
			<tn-lazy-load :threshold="6000" height="100%" :image="item.img" :index="item.id"
				imgMode="widthFix"></tn-lazy-load>
		</view>
		<view class="item__data tn-margin-left-sm tn-margin-right-sm">
			<view class="item__title-container">
				<view class=" tn-tag-content__item tn-margin-right tn-margin-top-xs tn-margin-bottom-x tn-text-sm tn-text-bold">
					广告
				</view>
				{{subText(item.name,8)}}
			</view>
			<view class="item__title-container" style="margin-top: 10upx;">
				<text class="item__title tn-color-cat clamp-text-2">{{subText(item.intro,20)}}</text>
			</view>
			<view class="item__tags-container">
			</view>
		</view>
	</view>
	<view v-else class="product__item home-shadow" @tap="toInfo(item)">
		<view class="item__image" v-if="item.images[0]">
			<tn-lazy-load v-if="item.images.length !== 0" :threshold="6000" height="100%" :image="item.images[0]"
				:index="item.id" imgMode="widthFix"></tn-lazy-load>
		</view>
		<view class="item__image" v-else>
			<image class="item__image" :src="no_img" mode="widthFix"></image>
		</view>
		<view class="item__data tn-margin-left-sm tn-margin-right-sm">
			<view class="item__title-container">
				<text class="item__title tn-color-cat clamp-text-2">{{ item.title }}</text><text class="text-red margin-right-xs" v-if="isTop">置顶</text>
			</view>
			<!-- <view class="item__tags-container">
				<view v-if="item.category.length !== 0"
					class="justify-content-item tn-tag-content__item tn-margin-right tn-margin-top-xs tn-margin-bottom-x tn-text-sm tn-text-bold">
					<text class="tn-tag-content__item--prefix" style="color: #00BCD4;">#</text>
					{{item.category[0].name}}
				</view>
			</view> -->
			<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top-xs">
				<view class="justify-content-item">
					<view class="tn-flex tn-flex-col-center tn-flex-row-left">
						<view class="user-rz">
							<image :src="item.authorInfo.avatar" style="width: 40rpx;height: 40rpx;border-radius: 100upx;" mode="widthFix"></image>
							<image class="user-rz-icon-pbl" :src="rzImg" mode="aspectFill" v-if="item.authorInfo.lvrz==1"></image>
						</view>
						<view class="tn-padding-left-xs">
							<text class="tn-text-sm text-bold clamp-text-1" :class="item.authorInfo.isvip>0?'name-vip':'tn-color-gray'">{{item.authorInfo.name}}</text>
						</view>
			
					</view>
				</view>
				<view class="justify-content-item">
					<!-- 		 <text class="tn-icon-fire tn-color-gray" style="padding-right: 8rpx;">{{formatNumber(item.views)}}</text> -->
					<!-- <text class="tn-icon-comment tn-color-gray"
						style="padding-right: 20upx;">{{formatNumber(item.commentsNum)}}</text> -->
					<text class="tn-icon-like-lack tn-color-gray"
						style="padding-right: 20upx;">{{formatNumber(item.likes)}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
			isTop: {
				type: Boolean,
				default: false
			}
		},
		name: "articleItemWfB",
		data() {
			return {
				// #ifdef APP-PLUS || MP
				no_img:"../../static/page/pic.png",
				// #endif
				// #ifdef H5
				no_img:"/h5/static/page/pic.png",
				// #endif
				leftList: [],
				rightList: [],
				rzImg: this.$API.SPRz()
			};
		},
		methods: {
			
			subText(text, num) {
				if (text.length > num) {
					return text.substring(0, num) + "…"
				} else {
					return text;
				}

			},
			replaceSpecialChar(text) {
				text = text.replace(/&quot;/g, '"');
				text = text.replace(/&amp;/g, '&');
				text = text.replace(/&lt;/g, '<');
				text = text.replace(/&gt;/g, '>');
				text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				return result;
			},
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},
			toInfo(data) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/info?cid=' + data.cid + "&title=" + data.title
				});
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
		}
	}
</script>
<style lang="scss" scoped>
	@import "@/static/styles/home.scss";

	.scroll-y {
		overflow-y: scroll;
	}
</style>