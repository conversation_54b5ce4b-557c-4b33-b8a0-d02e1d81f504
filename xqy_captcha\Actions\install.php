<?php
initializeActions();

if ($installed == "true") {
    // 已安装过
    send_json(402, "已安装，无需再次安装");
} else {
    $msg = "";
    // ------------未安装过，开始执行安装--------------


    // -------------------安装完成，以下勿动---------------------
    
    //【勿动】 将插件安装状态设置为已安装
    $currentDir = __DIR__;
    $configFile = str_replace('Actions', '', $currentDir) . 'config.ini';
    if (file_exists($configFile)) {
        $configData = parse_ini_file($configFile, true);
        $configData['plugin']['installed'] = "true";
        // 写回配置文件
        $newConfig = "";
        foreach ($configData as $section => $values) {
            $newConfig .= "[$section]\n";
            foreach ($values as $key => $value) {
                $newConfig .= "$key = \"$value\"\n";
            }
        }

        if (file_put_contents($configFile, $newConfig) !== false) {
            $msg .= "更新配置文件成功。";
        } else {
            $msg .= "更新配置文件失败。";
        }
    } else {
        $msg .= "配置文件不存在。";
    }
    $data['log'] = $msg;
    send_json(200, "插件安装完成", $data);
}