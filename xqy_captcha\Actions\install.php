<?php
initializeActions();

if ($installed == "true") {
    // 已安装过
    send_json(402, "已安装，无需再次安装");
} else {
    $msg = "";
    // ------------未安装过，开始执行安装--------------

    // 创建行为验证配置表
    $sql_1 = "SHOW TABLES LIKE 'Xqy_Plugin_captcha_config'";
    $tcr = mysqli_query($connect, $sql_1);

    if (mysqli_num_rows($tcr) == 0) {
        // 创建 Xqy_Plugin_captcha_config 表
        $sql_2 = "CREATE TABLE `Xqy_Plugin_captcha_config` (
          `id` int(11) NOT NULL,
          `captcha_type` varchar(50) DEFAULT 'geetest' COMMENT '验证类型：geetest, cloudflare, recaptcha',
          `geetest_id` varchar(255) DEFAULT NULL COMMENT '极验ID',
          `geetest_key` varchar(255) DEFAULT NULL COMMENT '极验KEY',
          `cloudflare_site_key` varchar(255) DEFAULT NULL COMMENT 'Cloudflare站点密钥',
          `cloudflare_secret_key` varchar(255) DEFAULT NULL COMMENT 'Cloudflare私钥',
          `recaptcha_site_key` varchar(255) DEFAULT NULL COMMENT 'reCAPTCHA站点密钥',
          `recaptcha_secret_key` varchar(255) DEFAULT NULL COMMENT 'reCAPTCHA私钥',
          `enabled` int(1) DEFAULT 1 COMMENT '是否启用',
          `created_time` int(10) DEFAULT NULL,
          `updated_time` int(10) DEFAULT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        if (mysqli_query($connect, $sql_2)) {
            $msg .= "创建 Xqy_Plugin_captcha_config 表成功。";

            // 插入默认配置
            $sql_3 = "INSERT INTO `Xqy_Plugin_captcha_config` (`id`, `captcha_type`, `enabled`, `created_time`) VALUES
                (1, 'geetest', 1, " . time() . ");
                ";
            if (mysqli_query($connect, $sql_3)) {
                $msg .= "Xqy_Plugin_captcha_config 表默认数据插入成功。";
            } else {
                $msg .= "Xqy_Plugin_captcha_config 表默认数据插入失败：" . mysqli_error($connect) . "。";
            }
        } else {
            $msg .= "创建 Xqy_Plugin_captcha_config 表失败：" . mysqli_error($connect) . "。";
        }
    } else {
        $msg .= "Xqy_Plugin_captcha_config 表已存在。";
    }

    // 设置主键和自增
    $sql_alter_array = [
        "ALTER TABLE `Xqy_Plugin_captcha_config` ADD PRIMARY KEY (`id`)",
        "ALTER TABLE `Xqy_Plugin_captcha_config` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT"
    ];

    $alter_success = true;
    foreach ($sql_alter_array as $sql) {
        if (!mysqli_query($connect, $sql)) {
            $alter_success = false;
            $msg .= "执行SQL失败：" . mysqli_error($connect) . "\n";
        }
    }

    if ($alter_success) {
        $msg .= "主键、自增设置成功。";
    } else {
        $msg .= "部分主键、自增设置失败，请检查错误日志。";
    }

    // -------------------安装完成，以下勿动---------------------
    
    //【勿动】 将插件安装状态设置为已安装
    $currentDir = __DIR__;
    $configFile = str_replace('Actions', '', $currentDir) . 'config.ini';
    if (file_exists($configFile)) {
        $configData = parse_ini_file($configFile, true);
        $configData['plugin']['installed'] = "true";
        // 写回配置文件
        $newConfig = "";
        foreach ($configData as $section => $values) {
            $newConfig .= "[$section]\n";
            foreach ($values as $key => $value) {
                $newConfig .= "$key = \"$value\"\n";
            }
        }

        if (file_put_contents($configFile, $newConfig) !== false) {
            $msg .= "更新配置文件成功。";
        } else {
            $msg .= "更新配置文件失败。";
        }
    } else {
        $msg .= "配置文件不存在。";
    }
    $data['log'] = $msg;
    send_json(200, "插件安装完成", $data);
}