<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]"  :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px', 'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back" :style="{'color':isDark?'#ffffff':'#000000'}"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}, {'color':isDark?'#ffffff':'#000000'}]">
					用户注册
				</view>
				<view class="action">

				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="t-login" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="t-b" :style="{'color':isDark?'#ffffff':'#000000'}">Hi，欢迎加入{{appname}}~</view>
		</view>
		<view class="user-form" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<form>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
					<input name="input" v-model="name" placeholder="请输入账号" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}" v-if="loginType==0">
					<input name="input" v-model="mail"  placeholder="请输入邮箱" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}" v-if="loginType==1">
					<input name="input" v-model="phone" placeholder="请输入手机号" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}" v-if="isEmail>0&&loginType==0">
					<input name="input" v-model="code" placeholder="请输入邮箱验证码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
					<view class="sendcode text-blue" v-if="show" @tap="RegSendCode">发送</view>
					<view class="sendcode text-gray" v-if="!show">{{ times }}s</view>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}" v-if="loginType==1">
					<input name="input" v-model="code" placeholder="请输入短信验证码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
					<view class="sendcode text-blue" v-if="show" @tap="RegSendSms">发送</view>
					<view class="sendcode text-gray" v-if="!show">{{ times }}s</view>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
					<input name="input" v-model="password" type="text" placeholder="请输入密码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}">
					<input name="input" v-model="repassword" type="text" placeholder="再次输入密码" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}" v-if="isInvite>0">
					<input name="input" v-model="inviteCode" type="text" placeholder="请输入邀请码 (必填)" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
					<view class="sendcode text-blue" v-if="BuyCodeUrl!=''" @tap="goBuyCode(BuyCodeUrl)">获取邀请码</view>
				</view>
				<view class="cu-form-group" :style="{'border-radius': '100rpx', 'background-color':isDark?'#1c1c1c':'#ffffff', 'border':isDark?'1px solid #2c2c2c':'1px solid #e5e5e5'}" v-else>
					<input name="input" v-model="inviteCode" type="text" placeholder="邀请码 (可留空)" :style="{'color':isDark?'#ffffff':'#000000'}"></input>
				</view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view class="user-btn flex flex-direction">
					
					<button class="cu-btn bg-blue margin-tb-sm lg" v-if="loginType==0" @tap="userRegister">立即注册</button>
					<button class="cu-btn bg-blue margin-tb-sm lg" v-if="loginType==1" @tap="userRegisterByPhone">立即注册</button>
					<text class="text-right margin-top" :style="{'font-size': '22upx', 'color':isDark?'#888888':'#808080'}" @tap="getAgree">
						<text style="margin-right: 10upx">
							<radio value="r1" :checked="isAgreeck" color="#3ccea7"
								style="transform:scale(0.7)" />
						</text>我同意{{appname}}的<text class="text-blue" @tap="toAgreement">《用户协议》</text>
						和<text class="text-blue" @tap="toPrivacy">《隐私条款》</text>
					</text>
				</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="user-btn flex flex-direction">
					<button class="cu-btn bg-blue margin-tb-sm lg" @tap="usermodelVisible=true">立即注册</button>
					
				</view>
				<!-- #endif -->
				
			</form>
		</view>
		<tn-popup v-model="usermodelVisible" mode="center" :borderRadius="23" :maskCloseable="false">
			<view style="padding: 30rpx 40rpx;">
				<view style="text-align: center;">用户及隐私协议</view>
				<view class="model-body" style="margin-top: 20rpx;">请问你是否同意<text
							class="text-blue" @tap="toAgreement">《用户及隐私协议》</text></view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button backgroundColor="#3ccea7" fontColor="#fff"
						@tap="yesBtn">同意</tn-button>
					<tn-button style="margin-left: 20rpx;" backgroundColor="#37bc99" fontColor="#fff"
						@tap="noBtn">不同意</tn-button>
				</view>
			</view>
		</tn-popup>
		<tn-popup v-model="modelVisible" mode="bottom" :borderRadius="23" :maskCloseable="false">
			<view style="padding: 30rpx 40rpx;">
				<view style="text-align: center;">温馨提示</view>
				<view class="model-body" v-html="registertext" style="margin-top: 20rpx;"></view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button style="margin-left: 20rpx;" backgroundColor="#3cc9a4" fontColor="#fff"
						@tap="okBtn">知道了</tn-button>
				</view>
			</view>
		</tn-popup>
		<view class="cu-modal" :class="modalName=='kaptcha'?'show':''">
			<view class="cu-dialog kaptcha-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">操作验证</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>
				<view class="kaptcha-form">
					<view class="kaptcha-image">
						<image :src="kaptchaUrl" mode="widthFix" @tap="reloadCode()"></image>
					</view>
					<view class="kaptcha-input">
						<input name="input" v-model="verifyCode" placeholder="请输入验证码"></input>
						<view class="cu-btn bg-blue" v-if="loginType==0" @tap="RegSendCode">确定</view>
						<view class="cu-btn bg-blue" v-if="loginType==1" @tap="RegSendSms">确定</view>
					</view>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		mixins: [darkModeMixin],
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				usermodelVisible: false,
				times: 60,
				show: true,
				appname: this.$API.GetAppName(),
				modelVisible: false,
				name: "",
				BuyCodeUrl: this.$API.GetBuyInviteCodeUrl(),
				adminemail: this.$API.GetAppEmail(),
				mail: "",
				code: "",
				password: "",
				phone: "",
				repassword: "",
				isEmail: 0,
				loginType: 0,
				isPhone: 0,
				isInvite: 0,
				inviteCode: "",
				isAgree: 'false',
				isAgreeck: false,
				registertext: "",
				modalName: null,
				kaptchaUrl: "",
				verifyCode: "",
				verifyLevel: 0,
				// 行为验证相关
				captchaPluginEnabled: false,
				captchaConfig: null,
				behaviorCaptchaData: null,
			}
		},
		onPullDownRefresh() {
			var that = this;

		},
		onShow() {
			var that = this;
			// #ifdef APP-PLUS

			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			var that = this;
			if (localStorage.getItem('isAgree')) {
				that.isAgree = localStorage.getItem('isAgree');
				
			}else{
				that.isAgree = 'false'
			}
			if (that.isAgree=='true') {
				that.isAgreeck = true
			} else{
				that.isAgreeck = false
			}
			
			that.kaptchaUrl = that.$API.getKaptcha();
		},
		onLoad(res) {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = that.CustomBar;
			// #endif
			if (res.inviteCode) {
				that.inviteCode = res.inviteCode;
			}
			that.regConfig();

			// 检查行为验证插件状态
			that.checkCaptchaPlugin();
		},
		mounted() {
			this.getset()
		},
		methods: {
			// 检查行为验证插件状态
			checkCaptchaPlugin() {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/getPluginStatus.php',
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							that.captchaPluginEnabled = res.data.data.overall_enabled;
							if (that.captchaPluginEnabled) {
								that.getCaptchaConfig();
							}
						}
					},
					fail: function() {
						that.captchaPluginEnabled = false;
					}
				});
			},

			// 获取行为验证配置
			getCaptchaConfig() {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/getCaptchaConfig.php',
					method: 'POST',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							that.captchaConfig = res.data.data;
						}
					}
				});
			},

			// 验证行为验证码
			verifyBehaviorCaptcha(callback) {
				var that = this;
				uni.request({
					url: that.$API.baseUrl + '/plugins/xqy_captcha/Actions/checkCaptcha.php',
					method: 'POST',
					data: {
						captcha_type: that.captchaConfig.captcha_type,
						captcha_data: JSON.stringify(that.behaviorCaptchaData)
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success: function(res) {
						if (res.data && res.data.code === 200) {
							// 验证成功，执行回调
							callback && callback();
						} else {
							// 验证失败，清除验证数据并提示
							that.behaviorCaptchaData = null;
							uni.showToast({
								title: res.data ? res.data.msg : '验证失败',
								icon: 'none'
							});
						}
					},
					fail: function() {
						// 网络错误，回退到图片验证码
						that.behaviorCaptchaData = null;
						that.captchaPluginEnabled = false;
						uni.showToast({
							title: '验证服务异常，请使用图片验证码',
							icon: 'none'
						});
						if (that.verifyCode == "") {
							that.modalName = 'kaptcha';
						} else {
							callback && callback();
						}
					}
				});
			},

			// 显示行为验证
			showBehaviorCaptcha(action) {
				var that = this;
				that.currentAction = action; // 记录当前操作

				if (that.captchaConfig.captcha_type === 'geetest') {
					that.showGeetestCaptcha();
				} else if (that.captchaConfig.captcha_type === 'cloudflare') {
					that.showCloudflareCaptcha();
				} else if (that.captchaConfig.captcha_type === 'recaptcha') {
					that.showRecaptchaCaptcha();
				}
			},

			// 极验验证（占位符）
			showGeetestCaptcha() {
				// TODO: 集成极验SDK
				console.log('显示极验验证');
				// 模拟验证成功
				setTimeout(() => {
					this.onBehaviorCaptchaSuccess({
						geetest_challenge: 'mock_challenge',
						geetest_validate: 'mock_validate',
						geetest_seccode: 'mock_seccode'
					});
				}, 2000);
			},

			// Cloudflare验证（占位符）
			showCloudflareCaptcha() {
				// TODO: 集成Cloudflare Turnstile
				console.log('显示Cloudflare验证');
				// 模拟验证成功
				setTimeout(() => {
					this.onBehaviorCaptchaSuccess({
						'cf-turnstile-response': 'mock_cf_response'
					});
				}, 2000);
			},

			// reCAPTCHA验证（占位符）
			showRecaptchaCaptcha() {
				// TODO: 集成Google reCAPTCHA
				console.log('显示reCAPTCHA验证');
				// 模拟验证成功
				setTimeout(() => {
					this.onBehaviorCaptchaSuccess({
						'g-recaptcha-response': 'mock_recaptcha_response'
					});
				}, 2000);
			},

			// 处理行为验证结果
			onBehaviorCaptchaSuccess(data) {
				this.behaviorCaptchaData = data;
				// 根据当前操作执行相应的验证
				if (this.currentAction === 'RegSendCode') {
					this.verifyBehaviorCaptcha(() => this.performRegSendCode());
				} else if (this.currentAction === 'RegSendSms') {
					this.verifyBehaviorCaptcha(() => this.performRegSendSms());
				}
			},

			getAgree(){
				var that = this;
				if (localStorage.getItem('isAgree')) {
					that.isAgree = localStorage.getItem('isAgree');
				}else{
					that.isAgree = 'false'
				}
				if (that.isAgree=='true') {
					that.isAgreeck = true
					uni.showToast({
						title: "已同意",
						icon: 'none',
						duration: 3000,
						position: 'center',
					});
				} else{
					that.isAgreeck = false
					that.toAgreement()
				}
			},
			goBuyCode(url) {
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
				// #ifdef MP-WEIXIN
				uni.showToast({
					title: "小程序无法转跳",
					icon: 'none'
				})
				// #endif
			},
			getset() {
			  var that = this;
			      uni.request({
			        url:that.$API.SPset(),
			        method:'GET',
			        dataType:"json",
			        success(res) {
					    that.registertext = res.data.registertext;
						if (that.registertext != "") {
							that.modelVisible = true
						}
			        },
			        fail(error) {
			          console.log(error);
			        }
			      })
			},
			okBtn() {
				this.modelVisible = false;
			},
			back() {
				localStorage.removeItem('isAgree');  
				uni.navigateBack({
					delta: 1
				});
			},
			hideModal(e) {
				this.modalName = null
			},
			PickerChange(e) {
				this.index = e.detail.value
			},
			validatePassword(password) {
			  // 至少包含一个字母和一个数字，长度必须大于6，可以包含特殊符号
			  const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]]{6,}$/;
			  return regex.test(password);
			},
			reloadCode() {
				var that = this;
				var kaptchaUrl = that.$API.getKaptcha();
				var num = Math.ceil(Math.random() * 10);
				kaptchaUrl += "?" + num;
				that.kaptchaUrl = kaptchaUrl;
			},
			userRegisterByPhone() {
				var that = this;
				// #ifdef APP-PLUS || H5
				if (that.isAgree=='false') {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				if (that.name == "" || that.phone == "" || that.password == "" || that.repassword == "") {
					uni.showToast({
						title: "请输入正确的参数",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^[A-Za-z0-9]+$/.test(that.name)) {
					uni.showToast({
						title: "账号只允许为数字和英文",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^1[3-9]\d{9}$/.test(that.phone)) {
					uni.showToast({
						title:"请输入合法的手机号",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false;
				}
				
				if(!that.validatePassword(that.password)){
					uni.showToast({
						title:"密码至少包含字母和数字，且长度必须大于5位",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if (that.password != that.repassword) {
					uni.showToast({
						title: "两次密码不一致",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				
				var data = {
					'name': that.name,
					'code': that.code,
					'password': that.password,
					'phone': that.phone,
					'inviteCode': that.inviteCode
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
			
					url: that.$API.userRegisterByPhone(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						if (res.data.code == 1) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							localStorage.removeItem('isAgree');  
							if(res.data.msg=="注册成功"){
								var data = {
									name: that.name,
									password: that.password,
								}
								uni.showLoading({
									title: "加载中"
								});
								that.$Net.request({
								
									url: that.$API.userLogin(),
									data: {
										"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "post",
									dataType: 'json',
									success: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: res.data.msg,
											icon: 'none'
										})
										if (res.data.code == 1) {
											//保存用户信息
											localStorage.setItem('userinfo', JSON.stringify(res.data.data));
											localStorage.setItem('token', res.data.data.token);
											that.getCID();
											var timer = setTimeout(function() {
												that.back2();
												clearTimeout('timer')
											}, 1000)
										}
									},
									fail: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络开小差了哦",
											icon: 'none'
										})
										uni.stopPullDownRefresh()
									}
								})
							}else{
								that.back();
							}
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			back2() {
				localStorage.removeItem('isAgree');  
				uni.navigateBack({
					delta: 2
				});
			},
			getCID() {
				var that = this;
				let cid = ''
				// #ifdef APP-PLUS
				let pinf = plus.push.getClientInfo();
				cid = pinf.clientid;
				if (cid) {
					that.setClientId(cid);
				}
				// #endif
			},
			setClientId(cid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('token')) {
			
					token = localStorage.getItem('token');
				} else {
					return false;
				}
				that.$Net.request({
			
					url: that.$API.setClientId(),
					data: {
						"clientId": cid,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
			
			
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			userRegister() {
				var that = this;
				// #ifdef APP-PLUS || H5
				if (that.isAgree=='false') {
					uni.showToast({
						title: "请同意用户协议",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				// #endif
				if (that.name == "" || that.mail == "" || that.password == "" || that.repassword == "") {
					uni.showToast({
						title: "请输入正确的参数",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^[A-Za-z0-9]+$/.test(that.name)) {
					uni.showToast({
						title: "账号只允许为数字和英文",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				
				if(!that.validatePassword(that.password)){
					uni.showToast({
						title:"密码至少包含字母和数字，且长度必须大于5位",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false
				}
				if (that.password != that.repassword) {
					uni.showToast({
						title: "两次密码不一致",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				
				var data = {
					'name': that.name,
					'code': that.code,
					'password': that.password,
					'mail': that.mail,
					'inviteCode': that.inviteCode
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.userRegister(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						
						if (res.data.code == 1) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							localStorage.removeItem('isAgree');  
							if(res.data.msg=="注册成功"){
								var data = {
									name: that.name,
									password: that.password,
								}
								uni.showLoading({
									title: "加载中"
								});
								that.$Net.request({
								
									url: that.$API.userLogin(),
									data: {
										"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
									},
									header: {
										'Content-Type': 'application/x-www-form-urlencoded'
									},
									method: "post",
									dataType: 'json',
									success: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: res.data.msg,
											icon: 'none'
										})
										if (res.data.code == 1) {
											//保存用户信息
											localStorage.setItem('userinfo', JSON.stringify(res.data.data));
											localStorage.setItem('token', res.data.data.token);
											that.getCID();
											var timer = setTimeout(function() {
												that.back2();
												clearTimeout('timer')
											}, 1000)
										}
									},
									fail: function(res) {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络开小差了哦",
											icon: 'none'
										})
										uni.stopPullDownRefresh()
									}
								})
							}else{
								that.back();
							}
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			
			yesBtn(){
				this.usermodelVisible = false
				this.isAgree=='false'
				this.userRegister()
			},
			noBtn(){
				this.usermodelVisible = false
				uni.showToast({
					title: "同意再来注册吧",
					icon: 'none',
				});
			},
			RegSendSms() {
				var that = this;
				
				if (that.phone == "") {
					uni.showToast({
						title: "请输入手机号",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (!/^1[3-9]\d{9}$/.test(that.phone)) {
					uni.showToast({
						title:"请输入合法的手机号",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false;
				}
				
				if (that.verifyLevel > 0) {
					// 如果启用了行为验证插件，使用行为验证
					if (that.captchaPluginEnabled && that.captchaConfig && that.captchaConfig.enabled) {
						if (!that.behaviorCaptchaData) {
							// 触发行为验证
							that.showBehaviorCaptcha('RegSendSms');
							return false;
						} else {
							// 先验证行为验证码，成功后再发送短信
							that.verifyBehaviorCaptcha(function() {
								that.performRegSendSms();
							});
							return false;
						}
					} else {
						// 使用原有的图片验证码
						if (that.verifyCode == "") {
							that.modalName = 'kaptcha'
							return false
						}
					}
				}

				that.performRegSendSms();
			},

			// 执行短信发送
			performRegSendSms() {
				var that = this;
				var data = {
					'phone': that.phone,
					'verifyCode': that.captchaPluginEnabled ? '' : that.verifyCode
				}

				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.sendSMS(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.verifyCode = "";
						that.behaviorCaptchaData = null; // 清除行为验证数据
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						if (res.data.code == 1) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							that.getCode();
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
				
					},
					fail: function(res) {
						that.modalName = null;
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			RegSendCode() {
				var that = this;
				if (that.mail == "") {
					uni.showToast({
						title: "请输入邮箱",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					return false
				}
				if (that.verifyLevel > 0) {
					// 如果启用了行为验证插件，使用行为验证
					if (that.captchaPluginEnabled && that.captchaConfig && that.captchaConfig.enabled) {
						if (!that.behaviorCaptchaData) {
							// 触发行为验证
							that.showBehaviorCaptcha('RegSendCode');
							return false;
						} else {
							// 先验证行为验证码，成功后再发送邮件
							that.verifyBehaviorCaptcha(function() {
								that.performRegSendCode();
							});
							return false;
						}
					} else {
						// 使用原有的图片验证码
						if (that.verifyCode == "") {
							that.modalName = 'kaptcha'
							return false
						}
					}
				}

				that.performRegSendCode();
			},

			// 执行邮件发送
			performRegSendCode() {
				var that = this;
				var data = {
					'mail': that.mail
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.RegSendCode(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						'verifyCode': that.captchaPluginEnabled ? '' : that.verifyCode
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.verifyCode = "";
						that.behaviorCaptchaData = null; // 清除行为验证数据
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);

						if (res.data.code == 1) {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							that.getCode();
						}else if(res.data.msg=='授权验证失败，请联系StarPro程序作者QQ：2504531378'){
							uni.showToast({
								title: '程序授权异常，请联系管理员：'+that.adminemail,
								icon: 'none'
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			regConfig() {
				var that = this;
				//获取应用信息
				uni.request({
			
					url: that.$API.getAppinfo(),
					data: {
						"key": that.$API.getAppKey()
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.isEmail = res.data.data.isEmail;
							that.isInvite = res.data.data.isInvite;
							that.loginType = res.data.data.isPhone;
							that.isPhone = res.data.data.isPhone;
							that.verifyLevel = res.data.data.verifyLevel;
							console.log(that.isInvite);
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "获取应用配置信息失败！",
							icon: 'none'
						})
					}
				})
			},
			getCode() {
				this.show = false
				this.timer = setInterval(() => {
					this.times--
					if (this.times === 0) {
						this.show = true
						clearInterval(this.timer);
						this.times = 60;
					}
				}, 1000)
			},
			toAgreement() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/agreement'
				});
			},
		}
	}
</script>

<style>
	
		.t-login {
			position: relative;
			width: 600rpx;
			margin: 0 auto;
			font-size: 28rpx;
			color: #000;
		}
		
		.t-login button {
			font-size: 28rpx;
			background: #5677fc;
			color: #fff;
			height: 90rpx;
			line-height: 90rpx;
			border-radius: 50rpx;
			box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
		}
		
		.t-login input {
			padding: 0 20rpx 0 120rpx;
			height: 90rpx;
			line-height: 90rpx;
			/* margin-bottom: 50rpx; */
			background: #f8f7fc;
			border: 1px solid #e9e9e9;
			font-size: 28rpx;
			border-radius: 50rpx;
		}
		
		.t-login .t-a {
			position: relative;
		}
		
		.t-login .t-a image {
			width: 60rpx;
			height: 40rpx;
			position: absolute;
			left: 40rpx;
			top: 28rpx;
			border-right: 2rpx solid #dedede;
			padding-right: 20rpx;
		}
		
		.t-login .t-b {
			text-align: left;
			font-size: 19px;
			color: #313131;
			padding: 35px 0 0px 0;
			font-weight: bold;
		}
		
		.t-login .t-c {
			position: absolute;
			right: 22rpx;
			top: 22rpx;
			background: #5677fc;
			color: #fff;
			font-size: 24rpx;
			border-radius: 50rpx;
			height: 50rpx;
			line-height: 50rpx;
			padding: 0 25rpx;
		}
		
		.t-login .t-d {
			text-align: center;
			color: #999;
			margin: 80rpx 0;
		}
		
		.t-login .t-e {
			text-align: center;
			width: 250rpx;
			margin: 80rpx auto 0;
		}
		
		.t-login .t-g {
			float: left;
			width: 100%;
		}
		
		.t-login .t-e image {
			width: 50rpx;
			height: 50rpx;
		}
		
		.t-login .t-f {
			text-align: center;
			margin: 200rpx 0 0 0;
			color: #666;
		}
		
		.t-login .t-f text {
			margin-left: 20rpx;
			color: #aaaaaa;
			font-size: 27rpx;
		}
		
		.t-login .uni-input-placeholder {
			color: #000;
		}
		
		.cl {
			zoom: 1;
		}
		.s1{
				
				float: right;
			}
		.cl:after {
			clear: both;
			display: block;
			visibility: hidden;
			height: 0;
			content: '\20';
		}
		.cu-btn{
			border-radius: 50px;
		}
		.cu-form-group{
			border-radius: 50px;
		}
</style>