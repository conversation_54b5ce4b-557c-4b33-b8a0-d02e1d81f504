<template>
  <view class="user" :class="$store.state.AppStyle" style="background-color: #f6f6f6;">
    <!-- 顶部导航栏 -->
    <view class="header" :style="[{height:CustomBar + 'px'}]">
      <view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
        <view class="action" @tap="back">
          <text class="cuIcon-back"></text>
        </view>
        <view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
          勋章馆
        </view>
        <view class="action" @tap="goToMyMedals">
          <text class="text-primary">我的勋章</text>
        </view>
      </view>
    </view>
    <view :style="[{padding:(NavBar+5) + 'px 10px 0px 10px'}]"></view>

    <!-- 内容区域 -->
    <view class="medal-content" v-if="pageReady">
      <!-- 空状态 -->
      <view class="empty-state" v-if="displayMedals.length === 0">
        <image class="empty-icon" src="/static/images/empty-medals.png" mode="aspectFit"></image>
        <text class="empty-text">暂无可申请的勋章</text>
      </view>
      
      <!-- 勋章网格 -->
      <view class="medal-grid" v-else>
        <view 
          class="medal-card" 
          v-for="medal in displayMedals" 
          :key="medal.id"
          :class="{'applying': applying}"
        >
          <view class="medal-content">
            <!-- 勋章名称移到最上面 -->
            <text class="medal-name">{{ medal.name }}</text>
            
            <image 
              :src="medal.icon_url" 
              class="medal-icon" 
              mode="aspectFit"
              :style="{ opacity: applying ? 0.5 : 1 }"
            ></image>
            
            <view class="medal-info">
              <!-- 条件标签保持在这里 -->
              <view class="condition-tags" v-if="medal.conditions">
                <view class="tag review" v-if="medal.conditions.need_review">
                  <text>需审核</text>
                </view>
                <view class="tag currency" v-if="medal.conditions.currency_required">
                  <text>{{ medal.conditions.currency_amount }}{{ medal.conditions.currency_name }}</text>
                </view>
              </view>
              <text class="medal-count">{{ medal.holder_count }}/{{ medal.max_holders ? medal.max_holders : '无限制' }}</text>
            </view>
          </view>
          <button 
            class="apply-btn"
            :class="{'disabled': applying, 'buy-btn': medal.currency_required}"
            @tap="showMedalPreview(medal)"
            :disabled="applying"
          >
            <text class="btn-text">{{ applying ? '申请中...' : (medal.currency_required ? '购买' : '申请') }}</text>
          </button>
        </view>
      </view>

      <!-- 分页控制器 -->
      <view class="pagination" v-if="allMedals.length > pageSize">
        <view class="page-btn" 
          :class="{ disabled: currentPage === 1 }"
          @tap="prevPage"
        >
          <text class="icon">←</text>
        </view>
        
        <view class="page-info">
          <text>{{ currentPage }}/{{ totalPages }}</text>
        </view>
        
        <view class="page-btn"
          :class="{ disabled: !hasMore }"
          @tap="nextPage"
        >
          <text class="icon">→</text>
        </view>
      </view>
    </view>
    <view v-else class="loading-container">
      <u-loading mode="circle" size="36"></u-loading>
    </view>

    <!-- 预览弹窗 -->
    <view class="preview-modal" v-if="showPreview" @tap.stop="closePreview">
      <view class="preview-content" @tap.stop>
        <view class="preview-title">{{ previewMedal ? previewMedal.name : '' }}</view>
        
        <view class="preview-main">
          <view class="preview-image">
            <image 
              :src="previewMedal ? previewMedal.icon_url : ''" 
              class="preview-medal"
              mode="aspectFit"
            ></image>
          </view>
          
          <view class="preview-info">
            <!-- 获取条件(原描述文本) -->
            <view class="preview-section">
              <text class="section-label">获取条件</text>
              <view class="section-content-wrapper" @tap="showFullDescription">
                <text class="section-content ellipsis">{{ previewMedal ? previewMedal.description : '' }}</text>
                <text class="view-more" v-if="hasLongDescription">查看更多</text>
              </view>
            </view>
            
            <!-- 获取条件区域 -->
            <view class="preview-requirements">
              <view class="requirement-item" v-if="previewMedal && previewMedal.conditions">
                <text class="requirement-label">通过方式</text>
                <view class="requirement-tags">
                  <view v-if="previewMedal.conditions.need_review" class="requirement-tag review">
                    需要审核
                  </view>
                  <view v-if="previewMedal.conditions.currency_required" class="requirement-tag currency">
                    {{ previewMedal.conditions.currency_amount }} {{ previewMedal.conditions.currency_name }}
                  </view>
                </view>
              </view>
              
              <view class="requirement-item">
                <text class="requirement-label">持有情况</text>
                <text class="requirement-value">{{ previewMedal ? `${previewMedal.holder_count}/${previewMedal.max_holders || '无限制'}` : '' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部操作按钮 -->
        <view class="preview-actions">
          <view class="action-buttons">
            <button class="action-btn cancel" @tap="closePreview">取消</button>
            <button class="action-btn confirm" @tap="applyMedal">
              {{ previewMedal && previewMedal.conditions && previewMedal.conditions.currency_required ? '购买' : '申请' }}
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 完整描述弹窗 -->
    <view class="full-desc-modal" v-if="showFullDesc" @tap.stop="showFullDesc = false">
      <view class="full-desc-content" @tap.stop>
        <view class="full-desc-title">获取条件</view>
        <text class="full-desc-text">{{ previewMedal ? previewMedal.description : '' }}</text>
        <button class="full-desc-close" @tap="showFullDesc = false">关闭</button>
      </view>
    </view>
  </view>
</template>

<script>
import { localStorage } from '../../../js_sdk/mp-storage/mp-storage/index.js'

export default {
  data() {
    return {
      StatusBar: this.StatusBar,
      CustomBar: this.CustomBar,
      NavBar: this.StatusBar + this.CustomBar,
      AppStyle: this.$store.state.AppStyle,
      medals: [],
      pageSize: 6,
      currentPage: 1,
      allMedals: [],
      applying: false,
      loading: false,
      isLoggedIn: false,
      userInfo: null,
      pageReady: false,
      pendingApplications: new Set(), // 记录正在申请中的勋章ID
      token: '',
      isLogin: false,
      submitStatus: false, // 防止重复提交
      applyDebounce: {}, // 用于存储每个勋章的防抖状态
      currencyName: '',
      showPreview: false,
      previewMedal: null,
      showFullDesc: false,
    }
  },

  computed: {
    displayMedals() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.allMedals.slice(start, end);
    },
    
    hasMore() {
      return this.currentPage * this.pageSize < this.allMedals.length
    },
    
    totalPages() {
      return Math.ceil(this.allMedals.length / this.pageSize)
    },
    hasLongDescription() {
      return this.previewMedal && this.previewMedal.description.length > 30
    }
  },

  async mounted() {
    try {
      await this.checkLoginStatus()
      await this.loadMedals()
    } finally {
      this.pageReady = true
    }
  },

  created() {
    // 获取token
    this.token = localStorage.getItem('token') || '';
    this.isLogin = !!this.token;
  },

  methods: {
    async checkLoginStatus() {
      const userInfo = uni.getStorageSync('userinfo') || localStorage.getItem('userinfo')
      if (userInfo) {
        this.userInfo = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
        this.isLoggedIn = true
      } else {
        this.isLoggedIn = false
      }
    },

    async loadMedals() {
      if (this.loading) return
      this.loading = true
      
      // 检查是否已登录
      const token = uni.getStorageSync('token')
      if (!token) {
        console.log('用户未登录')
        this.medals = []
        this.loading = false
        return
      }

      uni.showLoading({
        title: '加载中...'
      })
      
      try {
        const requestUrl = this.$API.PluginLoad('xqy_medal')
        await new Promise((resolve, reject) => {
          const requestData = {
            action: 'getMedals',
            plugin: 'xqy_medal',
            type: 'all',
            token: token
          }
          //console.log('请求数据:', requestData)
          
          uni.request({
            url: requestUrl,
            data: requestData,
            method: 'GET',
            dataType: 'json',
            success: (res) => {
              //console.log('请求响应:', res)
              if (res.data && res.data.code === 200) {
                this.allMedals = res.data.data.medals
                this.currencyName = res.data.data.currencyName || '积分';
                resolve()
              } else {
                reject(new Error(res.data?.msg || '加载失败'))
              }
            },
            fail: (err) => {
              console.error('请求失败:', err)
              reject(new Error('请求失败'))
            }
          })
        })
      } catch (error) {
        console.error('加载失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
        this.loading = false
      }
    },

    showMedalPreview(medal) {
      this.previewMedal = { ...medal };
      this.showPreview = true;
    },

    closePreview() {
      this.showPreview = false;
      this.previewMedal = null;
    },

    async handleApply() {
      if (!this.previewMedal) return;
      
      // 如果是货币购买,显示确认弹窗
      if (this.previewMedal.conditions && this.previewMedal.conditions.currency_required) {
        uni.showModal({
          title: '购买确认',
          content: `确定要使用 ${this.previewMedal.conditions.currency_amount} ${this.previewMedal.conditions.currency_name} 购买该勋章吗？`,
          success: async (res) => {
            if (res.confirm) {
              await this.applyMedal(this.previewMedal);
            }
          }
        });
      } else {
        await this.applyMedal(this.previewMedal);
      }
    },

    async applyMedal() {
      if (!this.previewMedal || !this.previewMedal.id) {
        uni.showToast({
          title: '勋章信息无效',
          icon: 'none'
        });
        return;
      }
      
      if(this.submitStatus) {
        uni.showToast({
          title: '请勿重复提交',
          icon: 'none'
        });
        return;
      }
      
      // 检查该勋章是否在防抖期
      if (this.applyDebounce[this.previewMedal.id]) {
        return;
      }
      
      this.submitStatus = true;
      // 设置防抖状态
      this.applyDebounce[this.previewMedal.id] = true;

      // 显示加载提示
      uni.showLoading({
        title: '提交申请中...',
        mask: true
      });

      try {
        const [err, res] = await uni.request({
          url: this.$API.PluginLoad('xqy_medal'),
          method: 'POST',
          data: {
            plugin: 'xqy_medal',
            action: 'applyMedal',
            medal_id: this.previewMedal.id,
            token: this.token
          },
          header: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });

        if (err) {
          throw new Error(err.message || '请求失败');
        }

        if (res.data && res.data.code === 200) {
          uni.hideLoading();
          await new Promise(resolve => setTimeout(resolve, 100));
          uni.showToast({
            title: res.data.msg,
            icon: 'success',
            duration: 3000,
            mask: true
          });
          await new Promise(resolve => setTimeout(resolve, 1000));
          this.closePreview();
          this.loadMedals();
        } else {
          uni.hideLoading();
          await new Promise(resolve => setTimeout(resolve, 100));
          uni.showToast({
            title: res.data?.msg || '申请失败',
            icon: 'none',
            duration: 3000,
            mask: true
          });
        }
      } catch (err) {
        /* console.error('申请失败:', err); */
        uni.hideLoading();
        await new Promise(resolve => setTimeout(resolve, 100));
        uni.showToast({
          title: err.message || '请求失败',
          icon: 'none',
          duration: 3000,
          mask: true
        });
      } finally {
        setTimeout(() => {
          this.submitStatus = false;
          this.applyDebounce[this.previewMedal.id] = false;
        }, 3000);
      }
    },

    goToMyMedals() {
      uni.navigateTo({
        url: '/pages/plugins/xqy_medal/myMedals'
      })
    },

    back() {
      uni.navigateBack()
    },

    async nextPage() {
      if (!this.hasMore || this.loading) return
      this.currentPage++
      await this.loadMedals()
    },

    async prevPage() {
      if (this.currentPage <= 1 || this.loading) return
      this.currentPage--
      await this.loadMedals()
    },

    async refresh() {
      this.currentPage = 1
      this.allMedals = []
      await this.loadMedals()
    },

    onShow() {
      this.checkLoginStatus()
    },

    showFullDescription() {
      if (this.hasLongDescription) {
        this.showFullDesc = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.medal-content {
  padding: 16rpx;
}

// 空状态
.empty-state {
  padding: 64px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .empty-icon {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-text {
    color: #8e8e93;
    font-size: 15px;
  }
}

// 勋章网格
.medal-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding-bottom: 32px;
  width: 100%;
  box-sizing: border-box;
}

// 勋章卡片
.medal-card {
  width: calc((100% - 24px) / 3);
  background: #fff;
  border-radius: 16px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  transition: transform 0.2s;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  
  &:active {
    transform: scale(0.98);
  }
  
  &.applying {
    opacity: 0.7;
  }
}

.medal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
}

.medal-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
  transition: opacity 0.2s;
}

.medal-info {
  text-align: center;
  width: 100%;
}

.medal-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.medal-count {
  font-size: 12px;
  color: #8e8e93;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 申请按钮
.apply-btn {
  width: 100%;
  height: 28px;
  border-radius: 14px;
  background: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  
  &:active {
    background: darken(#007AFF, 10%);
  }
  
  &.disabled {
    background: #c7c7cc;
  }
  
  .btn-text {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
  }
}

// 分页控制器
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  gap: 16px;

  .page-btn {
    width: 36px;
    height: 36px;
    border-radius: 18px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    transition: all 0.2s;
    
    &:active {
      transform: scale(0.95);
      background: #f5f5f5;
    }
    
    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }
    
    .icon {
      font-size: 18px;
      color: #007AFF;
    }
  }
  
  .page-info {
    font-size: 15px;
    color: #8e8e93;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.buy-btn {
  background: #ff9900 !important;
}

// 条件标签
.condition-tags {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
  width: 100%;

  .tag {
    padding: 2rpx 8rpx;
    border-radius: 20rpx;
    font-size: 18rpx;
    color: #fff;
    display: flex;
    align-items: center;
    box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
    
    &.review {
      background: #ff6b6b;
    }
    
    &.currency {
      background: #ffd93d;
      color: #333;
    }
  }
}

// 预览弹窗
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.preview-content {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  width: 85%;
  max-width: 600rpx;
}

.preview-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 24rpx;
  text-align: center;
}

.preview-main {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.preview-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f5f5f7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-medal {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

.preview-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.preview-section {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.section-label {
  font-size: 24rpx;
  color: #86868b;
}

.section-content-wrapper {
  position: relative;
  cursor: pointer;
}

.section-content {
  font-size: 24rpx;
  color: #1d1d1f;
  line-height: 1.4;
  
  &.ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.view-more {
  font-size: 22rpx;
  color: #007AFF;
  margin-top: 4rpx;
}

.preview-requirements {
  margin-top: 16rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.requirement-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.requirement-label {
  font-size: 24rpx;
  color: #86868b;
}

.requirement-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.requirement-tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  
  &.review {
    background: #fef0f0;
    color: #ff3b30;
  }
  
  &.currency {
    background: #fff2d9;
    color: #ff9500;
  }
}

.requirement-value {
  font-size: 24rpx;
  color: #1d1d1f;
}

.preview-actions {
  margin-top: 30rpx;
  padding: 0 20rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border: none;
  
  &.cancel {
    background: #f5f5f5;
    color: #666;
    
    &:active {
      background: #e5e5e5;
    }
  }
  
  &.confirm {
    background: linear-gradient(135deg, #007AFF, #0056b3);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(0,122,255,0.3);
    
    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 6rpx rgba(0,122,255,0.2);
    }
  }
}

// 完整描述弹窗样式
.full-desc-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.full-desc-content {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  width: 80%;
  max-width: 560rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.full-desc-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 24rpx;
  text-align: center;
}

.full-desc-text {
  font-size: 28rpx;
  color: #1d1d1f;
  line-height: 1.6;
  padding: 0 16rpx;
  margin-bottom: 32rpx;
}

.full-desc-close {
  width: 160rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  border: none;
  
  &:active {
    background: #e5e5e5;
  }
}
</style>
