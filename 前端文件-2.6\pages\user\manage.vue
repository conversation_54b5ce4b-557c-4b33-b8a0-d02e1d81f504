<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px', 'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back" :style="{'color':isDark?'#ffffff':'#333333'}"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}, {'color':isDark?'#ffffff':'#333333'}]">
					管理员中心
				</view>
				<view class="action">
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="data-box" v-if="group=='administrator'||group=='editor'" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">全站数据</text>
				</view>
				<view class="action more">

				</view>
			</view>
			<view class="manage-data" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="manage-dataLoad" v-if="allData==null" :style="{'color':isDark?'#cccccc':'#666666'}">
					<image src="../../static/loading.gif" mode="widthFix"></image>
				</view>
				<view class="user-data grid col-4" v-if="allData!=null">
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allContents}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">文章总计</view>
					</view>
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allComments}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">文章评论</view>
					</view>
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allShop}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">商品总计</view>
					</view>
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allUsers}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">用户总计</view>
					</view>
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allSpace}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">动态总计</view>
					</view>
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allPost}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">帖子总计</view>
					</view>
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allForumComment}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">帖子评论</view>
					</view>
					<view class="user-data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.allAds}}</view>
						<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">广告总计</view>
					</view>
				</view>
			</view>

		</view>
		<view class="data-box" v-if="group=='administrator'||group=='editor'" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">待办事项</text>
				</view>
				<view class="action more">

				</view>
			</view>
			<view class="manage-data upcoming" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="manage-dataLoad" v-if="allData==null" :style="{'color':isDark?'#cccccc':'#666666'}">
					<image src="../../static/loading.gif" mode="widthFix"></image>
				</view>
				<view class="manage-dataLoad" v-if="allData!=null&&upcomingTotal==0" :style="{'color':isDark?'#cccccc':'#666666'}">
					你做得很好！暂无待办事项！
				</view>
				<view class="user-data grid col-3" v-if="allData!=null&&upcomingTotal>0">
					<view class="user-data-box" v-if="allData.upcomingContents!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/contents')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingContents}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">待审核文章</view>
						</view>
					</view>
					<view class="user-data-box" v-if="allData.upcomingComments!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/comments')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingComments}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">待审核评论</view>
						</view>
					</view>
					<view class="user-data-box" v-if="allData.upcomingShop!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/shop')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingShop}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">待审核商品</view>
						</view>
					</view>
					<view class="user-data-box" v-if="allData.upcomingPost!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/postReview')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingPost}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">待审核帖子</view>
						</view>
					</view>
					<view class="user-data-box" v-if="allData.upcomingSpace!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/space')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingSpace}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">待审核动态</view>
						</view>
					</view>
					<view class="user-data-box" v-if="allData.upcomingAds!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/ads')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingAds}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">待审核广告</view>
						</view>
					</view>
					<view class="user-data-box" v-if="allData.upcomingWithdraw!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/withdraw')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingWithdraw}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">待审核提现</view>
						</view>

					</view>
					<view class="user-data-box" v-if="allData.upcomingIdentifyConsumer!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/consumer')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingIdentifyConsumer}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">个人认证</view>
						</view>

					</view>
					<view class="user-data-box" v-if="allData.upcomingIdentifyCompany!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/company')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.upcomingIdentifyCompany}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">蓝V认证</view>
						</view>
					</view>
					<view class="user-data-box" v-if="allData.selfDelete!=0" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
						<view class="user-data-main" @tap="toLink('/pages/manage/selfDelete')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="user-data-value" :style="{'color':isDark?'#ffffff':'#ffffff'}">{{allData.selfDelete}}</view>
							<view class="user-data-title" :style="{'color':isDark?'#cccccc':'#666666'}">申请注销</view>
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="data-box" v-if="group=='administrator'||group=='editor'" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">全局模块</text>
				</view>
				<view class="action more">
				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/clean')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-file" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								常规清理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/company')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-profilefill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								蓝V审核
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/consumer')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-profilefill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								实名审核
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/chat')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-weixin" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								聊天管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/endException')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-paintfill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								解除异常
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/selfDelete')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-profilefill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								注销审核
							</view>
						</view>
					</waves>
				</view>
				<!-- 	#ifdef APP-PLUS || H5 -->
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toView2(stardmad)" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-file" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								Star后台
							</view>
						</view>
					</waves>
				</view>
				<!-- #endif -->
			</view>
		</view>


		<view class="data-box" v-if="group=='administrator'||group=='editor'" :style="{'background-color':isDark?'#333333':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">用户模块</text>
				</view>
				<view class="action more">

				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/users')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-friend" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								用户管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/senduser')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-light" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								推送消息
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/invitation')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-friendadd" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								邀请码管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/userClean')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-file" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								用户清理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/banuser')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-warnfill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								封禁用户
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/giftVIP')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-emojifill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								VIP赠送
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/grantBlueV')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-vipcard" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								蓝V授予
							</view>
						</view>
					</waves>
				</view>
			</view>
		</view>
		<view class="data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">圈子模块</text>
				</view>
				<view class="action more">

				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box" v-if="myPurview == 5">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/section')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-skin" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								圈子管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/postReview')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-text" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								帖子审核
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'||group=='editor'||myPurview == 5">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/postComment')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-commentfill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								评论管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'||group=='editor'||myPurview==5">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/moderator')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-selection" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								圈主管理
							</view>
						</view>
					</waves>
				</view>
			</view>
		</view>
		<view class="data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">文章模块</text>
				</view>
				<view class="action more">

				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/contents')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-text" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								文章管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/metas')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-skin" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								分类管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/comments')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-favorfill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								评论管理
							</view>
						</view>
					</waves>
				</view>

			</view>
		</view>
		<view class="data-box" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">更多模块</text>
				</view>
				<view class="action more">

				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/shop')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-shop" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								商品管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/space')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-new" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								动态管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/ads')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-read" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								广告管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/ads/home')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-skin" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}"	>
								广告开通
							</view>
						</view>
					</waves>
				</view>


			</view>
		</view>
		<view class="data-box" v-if="group=='administrator'">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">财务模块</text>
				</view>
				<view class="action more">

				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/finance')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-vipcard" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								财务中心
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/recharge')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-pay" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								快捷充扣
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/withdraw')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-vipcard" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								提现审核
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" v-if="group=='administrator'">
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/manage/tokenpay')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-ticket" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								卡密管理
							</view>
						</view>
					</waves>
				</view>

			</view>
		</view>
		<view class="data-box" v-if="group=='administrator'&&sy_gpt">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">AI模块</text>
				</view>
				<view class="action more">
				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box" >
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/plugins/sy_gpt/manage/gpt')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-vipcard" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								大模型管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" >
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/plugins/sy_gpt/manage/gptchat')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" style="background-color: #3c3c3c;" :style="{'background-color':isDark?'#333333':'#3c3c3c'}">
								<text class="cuIcon-vipcard" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								AI消息管理
							</view>
						</view>
					</waves>
				</view>
			</view>
		</view>
		<!-- 小祈愿AI模块 -->
		<view class="data-box" v-if="group=='administrator'&&xqy_gpt" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
			<view class="cu-bar" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action data-box-title">
					<text class="cuIcon-titles text-rule" :style="{'color':isDark?'#00BCD4':'#00BCD4'}"></text><text :style="{'color':isDark?'#ffffff':'#333333'}">小祈愿AI</text>
				</view>
				<view class="action more">
				</view>
			</view>
			<view class="index-sort grid col-4">
				<view class="index-sort-box" >
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/plugins/xqy_gpt/manage/gpt')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#4095ff'}">
								<text class="cuIcon-creativefill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								模型管理
							</view>
						</view>
					</waves>
				</view>
				<view class="index-sort-box" >
					<waves itemClass="butclass">
						<view class="index-sort-main" @tap="toLink('/pages/plugins/xqy_gpt/manage/gptchat')" :style="{'color':isDark?'#ffffff':'#ffffff'}">
							<view class="index-sort-i" :style="{'background-color':isDark?'#333333':'#36c1b0'}">
								<text class="cuIcon-messagefill" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							</view>
							<view class="index-sort-text" :style="{'color':isDark?'#ffffff':'#333333'}">
								聊天记录
							</view>
						</view>
					</waves>
				</view>
			</view>
		</view>
		
		<block v-if="group=='administrator'">
			<view class="data-box">
				<view class="cu-list menu">
					<view class="cu-item" @tap="toReward" :style="{'color':isDark?'#ffffff':'#ffffff'}">
						<view class="content">
							<text class="cuIcon-presentfill text-red" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							<text :style="{'color':isDark?'#ffffff':'#333333'}">加入交流群</text>
						</view>
					</view>
				</view>
			</view>
			<view class="data-box">
				<view class="cu-list menu">
					<view class="cu-item" @tap="toWord" :style="{'color':isDark?'#ffffff':'#ffffff'}">
						<view class="content">
							<text class="cuIcon-hotfill text-blue" :style="{'color':isDark?'#ffffff':'#ffffff'}"></text>
							<text :style="{'color':isDark?'#ffffff':'#333333'}">StarPro文档</text>
						</view>
					</view>
				</view>
			</view>
		</block>
	</view>
</template>

<script>
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import waves from '@/components/xxley-waves/waves.vue';
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		mixins: [darkModeMixin],
		data() {
				return {
					StatusBar: this.StatusBar,
					CustomBar: this.CustomBar,
					NavBar: this.StatusBar + this.CustomBar,
					AppStyle: this.$store.state.AppStyle,

					stardmad: this.$API.SPstardmAdlogin(),
					userInfo: null,
					token: "",
					allData: null,
					upcomingTotal: 0,
					ruleApiInfo: null,

					group: "",

					myPurviewList: [],
					isModerator: false,
					sy_gpt: false,
					xqy_gpt: false,
					myPurview: 0,
			}
		},
		onPullDownRefresh() {
			var that = this;

		},
		onShow() {
			var that = this;
			// #ifdef APP-PLUS

			//plus.navigator.setStatusBarStyle("dark")

			// #endif
			if (localStorage.getItem('userinfo')) {

				that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.userInfo.style = "background-image:url(" + that.userInfo.avatar + ");";
				that.group = that.userInfo.group;
			}
			if (localStorage.getItem('token')) {

				that.token = localStorage.getItem('token');
				that.getAllData();
			}

			that.userPurview();
		},
		onLoad() {
			var that = this;
			
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			if (localStorage.getItem('getPlugins')) {
				var cachedPlugins = localStorage.getItem('getPlugins');
				if (cachedPlugins) {
				  const pluginList = JSON.parse(cachedPlugins);
				  that.sy_gpt = pluginList.includes('sy_gpt');
				  that.xqy_gpt = pluginList.includes('xqy_gpt');
				}
			}
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			getAllData() {
				var that = this;
				that.$Net.request({

					url: that.$API.allData(),
					data: {
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.allData = res.data.data;
							var data = res.data.data;
							that.upcomingTotal = Number(data.upcomingContents) + Number(data
									.upcomingComments) + Number(data.upcomingShop) + Number(data
									.upcomingSpace) +
								Number(data.upcomingPost) + Number(data.upcomingAds) + Number(data
									.upcomingWithdraw);
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toView2(url) {
				var that = this;
				// #ifdef APP-PLUS
				plus.runtime.openURL(url + "&token=" + that.token);
				// #endif
				// #ifdef H5
				window.open(url + "&token=" + that.token)
				// #endif

			},
			toUrl(url) {
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			toReward() {
				var url =
					"http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=Ugxbp8mgVuunGh9ONjFKRYbNcu9ERMmo&authKey=7JAOX6RODw6EPDWrbGkHKuJb1%2BT4s%2F09NhveJthXAykhL6gMZr%2Bdje7vpbtgW6%2BT&noverify=0&group_code=570762080";
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			toWord() {
				var url = "https://www.yuque.com/senyun-ev0j3/starpro";
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			userPurview() {
				var that = this;
				var uid = 0;
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					uid = userInfo.uid;
				}
				var data = {
					"uid": uid,
				}
				that.$Net.request({
					url: that.$API.userPurview(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {


						if (res.data.code == 1) {
							that.myPurviewList = res.data.data;
							var list = res.data.data;
							for (var i in list) {
								//获取到大圈主权限
								that.myPurview = list[i].purview;

							}
							var myInfo = JSON.parse(localStorage.getItem('userinfo'));
							if (myInfo.group == 'administrator' || myInfo.group == 'editor') {
								that.myPurview = 5;
							}
							if (that.myPurviewList.length > 0) {
								that.isModerator = true;
							}
						}
					},
					fail: function(res) {

					}
				})

			},
		},
		components: {
			waves
		}
	}
</script>

<style>
	.index-sort-i {
		border-radius: 36upx;
	}
</style>