(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-chat-history~pages-contents-info~pages-edit-addPost~pages-edit-addshop~pages-edit-articlePost~~358c408f"],{"0782":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5c47"),n("0506"),n("c223");var a=i(n("51b8")),r=0;function s(t){this.vm=t,t._ids={}}s.prototype.onUpdate=function(t){if(this.vm.markdown)return(0,a.default)(t)},s.prototype.onParse=function(t,e){if(e.options.markdown){if(e.options.useAnchor&&t.attrs&&/[\u4e00-\u9fa5]/.test(t.attrs.id)){var n="t"+r++;this.vm._ids[t.attrs.id]=n,t.attrs.id=n}"p"!==t.name&&"table"!==t.name&&"tr"!==t.name&&"th"!==t.name&&"td"!==t.name&&"blockquote"!==t.name&&"pre"!==t.name&&"code"!==t.name||(t.attrs.class="md-".concat(t.name," ").concat(t.attrs.class||""))}};var o=s;e.default=o},"106b":function(t,e,n){"use strict";n.r(e);var i=n("8a69"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},1663:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("aa9c"),n("5c47"),n("a1c1"),n("c223"),n("f7a5"),n("dd2b"),n("e966"),n("20f3"),n("2c10"),n("4626"),n("5ac7"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("5ef2");var a=i(n("95c7")),r=i(n("cb97"));function s(t){var e;return e=t.touches[0].pageY,(e-t.currentTarget.offsetTop<150||e<600)&&(e=t.currentTarget.offsetTop),e<30&&(e+=70),e-30}var o={name:"node",options:{},data:function(){return{ctrl:{}}},props:{name:String,attrs:{type:Object,default:function(){return{}}},childs:Array,opts:Array},components:{myAudio:a.default,node:r.default},mounted:function(){var t=this;if(this.$nextTick((function(){for(t.root=t.$parent;"mp-html"!==t.root.$options.name;t.root=t.root.$parent);})),this.opts[0]){var e;for(e=this.childs.length;e--;)if("img"===this.childs[e].name)break;-1!==e&&(this.observer=uni.createIntersectionObserver(this).relativeToViewport({top:500,bottom:500}),this.observer.observe("._img",(function(e){e.intersectionRatio&&(t.$set(t.ctrl,"load",1),t.observer.disconnect())})))}},beforeDestroy:function(){this.root._edit===this&&(this.root._edit=void 0),this.observer&&this.observer.disconnect()},methods:{copyCode:function(t){uni.showActionSheet({itemList:["复制代码"],success:function(){return uni.setClipboardData({data:t.currentTarget.dataset.content})}})},editStart:function(t){var e=this;if(this.opts[5]){var n=t.currentTarget.dataset.i;this.ctrl["e"+n]?(this.root._mask.pop(),this.root._maskTap(),this.$set(this.ctrl,"e"+n,2),setTimeout((function(){e.$set(e.ctrl,"e"+n,3)}),50)):(this.$set(this.ctrl,"e"+n,1),setTimeout((function(){e.root._mask.push((function(){return e.$set(e.ctrl,"e"+n,0)}))}),50),this.root._edit=this,this.i=n,this.cursor=this.childs[n].text.length)}},editInput:function(t){var e=t.target.dataset.i,n=t.detail.value.replace(/ {2,}/,(function(t){for(var e=" ",n=1;n<t.length;n++)e+=" ";return e}));this.root._editVal("".concat(this.opts[7],".").concat(e,".text"),this.childs[e].text,n),this.cursor=t.detail.cursor},editEnd:function(t){var e=t.target.dataset.i;this.$set(this.ctrl,"e"+e,0),this.root._setData("".concat(this.opts[7],".").concat(e,".text"),t.detail.value.replace(/ {2}/g,"  ")),void 0!==t.detail.cursor&&(this.cursor=t.detail.cursor)},insert:function(t){var e=this;setTimeout((function(){var n=e.childs.slice(0);if(n[e.i])if(n[e.i].text){var i=n[e.i].text;if("text"===t.type)e.cursor?n[e.i].text=i.substring(0,e.cursor)+t.text+i.substring(e.cursor):n[e.i].text+=t.text;else{var a=[];e.cursor&&a.push({type:"text",text:i.substring(0,e.cursor)}),a.push(t),e.cursor<i.length&&a.push({type:"text",text:i.substring(e.cursor)}),n.splice.apply(n,[e.i,1].concat(a))}}else n.splice(parseInt(e.i)+1,0,t);else n.push(t);e.root._editVal(e.opts[7],e.childs,n,!0),e.i=parseInt(e.i)+1}),200)},remove:function(t){var e=this.childs.slice(0),n=e.splice(t,1)[0];if("img"===n.name||"video"===n.name||"audio"===n.name){var i=n.attrs.src;n.src&&(i=1===n.src.length?n.src[0]:n.src),this.root.$emit("remove",{type:n.name,src:i})}this.root._edit=void 0,this.root._maskTap(),this.root._editVal(this.opts[7],this.childs,e,!0)},nodeTap:function(t){var e=this;if(this.opts[5]){if(this.root._lock)return;if(this.root._lock=!0,setTimeout((function(){e.root._lock=!1}),50),3===this.ctrl["e"+this.i])return;this.root._maskTap(),this.root._edit=this;var n=this.opts[7].lastIndexOf("children.");-1!==n?n+=9:n=6;var i=parseInt(this.opts[7].substring(n,this.opts[7].lastIndexOf(".children"))),a=this.$parent;while(a&&"node"!==a.$options.name)a=a.$parent;if(!a||this.opts[7].length-a.opts[7].length>15)return;this.$set(this.ctrl,"root",1),this.root._mask.push((function(){return e.$set(e.ctrl,"root",0)})),1!==this.childs.length||"text"!==this.childs[0].type||this.ctrl.e0||(this.$set(this.ctrl,"e0",1),this.root._mask.push((function(){return e.$set(e.ctrl,"e0",0)})),this.i=0,this.cursor=this.childs[0].text.length);var r=this.root._getItem(a.childs[i],0!==i,i!==a.childs.length-1);this.root._tooltip({top:s(t),items:r,success:function(n){if("大小"===r[n]){var o=a.childs[i].attrs.style||"",l=o.match(/;font-size:([0-9]+)px/);l=l?parseInt(l[1]):16,e.root._slider({min:10,max:30,value:l,top:s(t),changing:function(e){Math.abs(e-l)>2&&(a.changeStyle("font-size",i,e+"px",l+"px"),l=t.detail.value)},change:function(t){t!==l&&a.changeStyle("font-size",i,t+"px",l+"px"),e.root._editVal("".concat(a.opts[7],".").concat(i,".attrs.style"),o,a.childs[i].attrs.style)}})}else if("上移"===r[n]||"下移"===r[n]){var c=a.childs.slice(0),u=c[i];"上移"===r[n]?(c[i]=c[i-1],c[i-1]=u):(c[i]=c[i+1],c[i+1]=u),e.root._editVal(a.opts[7],a.childs,c,!0)}else if("删除"===r[n])a.remove(i);else{var d,h,p=a.childs[i].attrs.style||"",f="",g=r[n];"斜体"===g?(d="font-style",h="italic"):"粗体"===g?(d="font-weight",h="bold"):"下划线"===g?(d="text-decoration",h="underline"):"居中"===g?(d="text-align",h="center"):"缩进"===g&&(d="text-indent",h="2em"),f=p.includes(d+":")?p.replace(new RegExp(d+":[^;]+"),""):p+";"+d+":"+h,e.root._editVal("".concat(a.opts[7],".").concat(i,".attrs.style"),p,f,!0)}}})}},mediaTap:function(t){var e=this;if(this.opts[5]){var n=t.target.dataset.i,i=this.childs[n],a=this.root._getItem(i);this.root._edit=this,this.i=n,this.root._tooltip({top:t.target.offsetTop-30,items:a,success:function(t){switch(a[t]){case"封面":e.root.getSrc("img",i.attrs.poster||"").then((function(t){e.root._editVal("".concat(e.opts[7],".").concat(n,".attrs.poster"),i.attrs.poster,t instanceof Array?t[0]:t,!0)})).catch((function(){}));break;case"删除":e.remove(n);break;case"循环":case"不循环":e.root._setData("".concat(e.opts[7],".").concat(n,".attrs.loop"),!i.attrs.loop),uni.showToast({title:"成功"});break;case"自动播放":case"不自动播放":e.root._setData("".concat(e.opts[7],".").concat(n,".attrs.autoplay"),!i.attrs.autoplay),uni.showToast({title:"成功"});break}}}),this.root._lock=!0,setTimeout((function(){e.root._lock=!1}),50)}},changeStyle:function(t,e,n,i){var a=this.childs[e].attrs.style||"";a.includes(";"+t+":"+i)?a=a.replace(";"+t+":"+i,";"+t+":"+n):a+=";"+t+":"+n,this.root._setData("".concat(this.opts[7],".").concat(e,".attrs.style"),a)},play:function(t){if(this.root.$emit("play"),this.root.pauseVideo){for(var e=!1,n=t.target.id,i=this.root._videos.length;i--;)this.root._videos[i].id===n?e=!0:this.root._videos[i].pause();if(!e){var a=uni.createVideoContext(n,this);a.id=n,this.root.playbackRate&&a.playbackRate(this.root.playbackRate),this.root._videos.push(a)}}},imgTap:function(t){var e=this;if(this.opts[5]){var n=t.currentTarget.dataset.i,i=this.childs[n],a=this.root._getItem(i);this.root._edit=this,this.i=n,this.root._maskTap(),this.$set(this.ctrl,"e"+n,1),this.root._mask.push((function(){return e.$set(e.ctrl,"e"+n,0)})),this.root._tooltip({top:s(t),items:a,success:function(r){if("换图"===a[r])e.root.getSrc("img",i.attrs.src||"").then((function(t){e.root._editVal(e.opts[7]+"."+n+".attrs.src",i.attrs.src,t instanceof Array?t[0]:t,!0)})).catch((function(){}));else if("宽度"===a[r]){var o=i.attrs.style||"",l=o.match(/max-width:([0-9]+)%/);l=l?parseInt(l[1]):100,e.root._slider({min:0,max:100,value:l,top:s(t),changing:function(t){Math.abs(t-l)>5&&(e.changeStyle("max-width",n,t+"%",l+"%"),l=t)},change:function(t){t!==l&&(e.changeStyle("max-width",n,t+"%",l+"%"),l=t),e.root._editVal(e.opts[7]+"."+n+".attrs.style",o,e.childs[n].attrs.style)}})}else"超链接"===a[r]?e.root.getSrc("link",i.a?i.a.href:"").then((function(t){if(i.a)e.root._editVal(e.opts[7]+"."+n+".a.href",i.a.href,t,!0);else{var a={name:"a",attrs:{href:t},children:[i]};i.a=a.attrs,e.root._editVal(e.opts[7]+"."+n,i,a,!0)}wx.showToast({title:"成功"})})).catch((function(){})):"预览图"===a[r]?e.root.getSrc("img",i.attrs["original-src"]||"").then((function(t){e.root._editVal(e.opts[7]+"."+n+".attrs.original-src",i.attrs["original-src"],t instanceof Array?t[0]:t,!0),uni.showToast({title:"成功"})})).catch((function(){})):"删除"===a[r]?e.remove(n):(e.root._setData(e.opts[7]+"."+n+".attrs.ignore",!i.attrs.ignore),uni.showToast({title:"成功"}))}}),this.root._lock=!0,setTimeout((function(){e.root._lock=!1}),50)}else{var r=this.childs[t.currentTarget.dataset.i];if(r.a)return void this.linkTap(r.a);if(r.attrs.ignore)return;r.attrs.src=r.attrs.src||r.attrs["data-src"],this.root.$emit("imgtap",r.attrs),this.root.previewImg&&uni.previewImage({current:parseInt(r.attrs.i),urls:this.root.imgList})}},imgLongTap:function(t){},imgLoad:function(t){var e=t.currentTarget.dataset.i;(this.opts[1]&&!this.ctrl[e]||-1===this.ctrl[e])&&this.$set(this.ctrl,e,1),this.checkReady()},checkReady:function(){var t=this;this.root.lazyLoad||(this.root._unloadimgs-=1,this.root._unloadimgs||setTimeout((function(){t.root.getRect().then((function(e){t.root.$emit("ready",e)})).catch((function(){t.root.$emit("ready",{})}))}),350))},linkTap:function(t){var e=this;if(this.opts[5]){var n=t.currentTarget.dataset.i,i=this.childs[n],a=this.root._getItem(i);this.root._tooltip({top:s(t),items:a,success:function(t){"更换链接"===a[t]?e.root.getSrc("link",i.attrs.href).then((function(t){e.root._editVal(e.opts[7]+"."+n+".attrs.href",i.attrs.href,t,!0),uni.showToast({title:"成功"})})).catch((function(){})):e.remove(n)}})}else{var r=t.currentTarget?this.childs[t.currentTarget.dataset.i]:{},o=r.attrs||t,l=o.href;if(this.root.$emit("linktap",Object.assign({innerText:this.root.getText(r.children||[])},o)),l)if("#"===l[0])this.root.navigateTo(l.substring(1)).catch((function(){}));else if(l.split("?")[0].includes("://")){var c=this.$API.GetLinkRule(),u=c.split("{cid}"),d=this.$API.GetshopStar(),h=d.split("{sid}"),p=this.$API.GetforumStar(),f=p.split("{id}"),g=this.$API.GetappStar(),m=g.split("{id}");if(-1!=l.indexOf(u[0])){var v=l;for(var b in u)v=v.replace(u[b],"");uni.navigateTo({url:"/pages/contents/info?cid="+v+"&title=starpro"})}else if(-1!=l.indexOf(h[0])){var y=l;for(var x in h)y=y.replace(h[x],"");uni.navigateTo({url:"/pages/shop/shopinfo?sid="+y+"&title=starpro"})}else if(-1!=l.indexOf(f[0])){var k=l;for(var _ in f)k=k.replace(f[_],"");uni.navigateTo({url:"/pages/forum/info?id="+k+"&title=starpro"})}else if(-1!=l.indexOf(m[0])){k=l;for(var x in m)k=k.replace(m[x],"");uni.navigateTo({url:"/pages/plugins/sy_appbox/info?id="+k+"&title=starpro"})}else this.root.copyLink&&window.open(l)}else uni.navigateTo({url:l,fail:function(){uni.switchTab({url:l,fail:function(){}})}})}},mediaError:function(t){var e=t.currentTarget.dataset.i,n=this.childs[e];if("video"===n.name||"audio"===n.name){var i=(this.ctrl[e]||0)+1;if(i>n.src.length&&(i=0),i<n.src.length)return void this.$set(this.ctrl,e,i)}else"img"===n.name&&(this.opts[2]&&this.$set(this.ctrl,e,-1),this.checkReady());this.root&&this.root.$emit("error",{source:n.name,attrs:n.attrs,errMsg:t.detail.errMsg})}}};e.default=o},"1fa6":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("2634")),r=i(n("2fdc"));n("fd3c"),n("8f71"),n("bf0f"),n("de6c"),n("9db6"),n("5c47"),n("dfcf"),n("2797");var s={name:"imgcache",prefix:"imgcache_"};var o=function(t){this.vm=t,this.i=0,t.imgCache={get list(){return uni.getStorageInfoSync().keys.filter((function(t){return t.startsWith(s.prefix)})).map((function(t){return t.split(s.prefix)[1]}))},get:function(t){return uni.getStorageSync(s.prefix+t)},delete:function(t){var e=uni.getStorageSync(s.prefix+t);return!!e&&(plus.io.resolveLocalFileSystemURL(e,(function(t){t.remove()})),uni.removeStorageSync(s.prefix+t),!0)},add:function(t){return(0,r.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,download(t);case 2:if(n=e.sent,!n){e.next=6;break}return uni.setStorageSync(s.prefix+t,n),e.abrupt("return","file://"+plus.io.convertLocalFileSystemURL(n));case 6:return e.abrupt("return",null);case 7:case"end":return e.stop()}}),e)})))()},clear:function(){uni.getStorageInfoSync().keys.filter((function(t){return t.startsWith(s.prefix)})).forEach((function(t){uni.removeStorageSync(t)})),plus.io.resolveLocalFileSystemURL("_doc/".concat(s.name,"/"),(function(t){t.removeRecursively((function(t){console.log("".concat(s.name,"缓存删除成功"),t)}),(function(t){console.log("".concat(s.name,"缓存删除失败"),t)}))}))}}};e.default=o},"2ffb":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("5c47"),n("a1c1"),n("aa9c");var a=i(n("b4d7")),r=i(n("beba")),s=i(n("ced8"));function o(t){this.vm=t}o.prototype.onParse=function(t,e){if("pre"===t.name){if(e.options.editable)return void(t.attrs.class=(t.attrs.class||"")+" hl-pre");var n;for(n=t.children.length;n--;)if("code"===t.children[n].name)break;if(-1===n)return;var i,o=t.children[n],l=o.attrs.class+" "+t.attrs.class;for(n=l.indexOf("language-"),-1===n?(n=l.indexOf("lang-"),-1===n?(l="language-javascript",n=9):n+=5):n+=9,i=n;i<l.length;i++)if(" "===l[i])break;var c=l.substring(n,i);if(o.children.length){var u=this.vm.getText(o.children).replace(/&amp;/g,"&");if(!u)return;if(t.c&&(t.c=void 0),a.default.languages[c]&&(o.children=new s.default(this.vm).parse("<pre>"+a.default.highlight(u,a.default.languages[c],c).replace(/token /g,"hl-")+"</pre>")[0].children),t.attrs.class="hl-pre",o.attrs.class="hl-code",r.default.showLanguageName&&t.children.push({name:"div",attrs:{class:"hl-language",style:"user-select:none"},children:[{type:"text",text:c}]}),r.default.copyByLongPress&&(t.attrs.style+=(t.attrs.style||"")+";user-select:none",t.attrs["data-content"]=u,e.expose()),r.default.showLineNumber){for(var d=u.split("\n").length,h=[],p=d;p--;)h.push({name:"span",attrs:{class:"span"}});t.children.push({name:"span",attrs:{class:"line-numbers-rows"},children:h})}}}};var l=o;e.default=l},"30ed":function(t,e,n){var i=n("917f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("561e25ff",i,!0,{sourceMap:!1,shadowMode:!1})},"343e":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 根节点样式 */._root[data-v-4e8fba57]{padding:1px 0;overflow-x:auto;overflow-y:hidden;-webkit-overflow-scrolling:touch}\n/* 长按复制 */._select[data-v-4e8fba57]{-webkit-user-select:text;user-select:text}\n\n/* 提示条 */._tooltip_contain[data-v-4e8fba57]{position:absolute;right:20px;left:20px;text-align:center}._tooltip[data-v-4e8fba57]{box-sizing:border-box;display:inline-block;width:auto;max-width:100%;height:30px;padding:0 3px;overflow:scroll;font-size:14px;line-height:30px;white-space:nowrap}._tooltip_item[data-v-4e8fba57]{display:inline-block;width:auto;padding:0 2vw;line-height:30px;background-color:#000;color:#fff}\n/* 图片宽度滚动条 */._slider[data-v-4e8fba57]{position:absolute;left:20px;width:220px}._tooltip[data-v-4e8fba57],\n._slider[data-v-4e8fba57]{background-color:#000;border-radius:3px;opacity:.75}",""]),t.exports=e},"4c64":function(t,e,n){"use strict";var i=n("30ed"),a=n.n(i);a.a},"51b8":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("6a54"),n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("f7a5"),n("08eb"),n("5c47"),n("0506"),n("7a76"),n("c9b5"),n("a1c1"),n("e966"),n("5ef2"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("dfcf"),n("dd2b"),n("aa9c"),n("0c26"),n("6e12"),n("2c10"),n("fd3c"),n("9db6"),n("9327"),n("dc8a"),n("4626"),n("5ac7"),n("20f3"),n("3efd");var i=
/*!
 * marked - a markdown parser
 * Copyright (c) 2011-2020, Christopher Jeffrey. (MIT Licensed)
 * https://github.com/markedjs/marked
 */
function(){function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function n(t,n){var i;if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator])return(i=t[Symbol.iterator]()).next.bind(i);if(Array.isArray(t)||(i=function(t,n){if(t){if("string"==typeof t)return e(t,n);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?e(t,n):void 0}}(t))||n&&t&&"number"==typeof t.length){i&&(t=i);var a=0;return function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(t){return u[t]}var a,r=(function(t){function e(){return{baseUrl:null,breaks:!1,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}t.exports={defaults:e(),getDefaults:e,changeDefaults:function(e){t.exports.defaults=e}}}(a={exports:{}}),a.exports),s=(r.defaults,r.getDefaults,r.changeDefaults,/[&<>"']/),o=/[&<>"']/g,l=/[<>"']|&(?!#?\w+;)/,c=/[<>"']|&(?!#?\w+;)/g,u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},d=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function h(t){return t.replace(d,(function(t,e){return"colon"===(e=e.toLowerCase())?":":"#"===e.charAt(0)?"x"===e.charAt(1)?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):""}))}var p=/(^|[^\[])\^/g,f=/[^\w:]/g,g=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i,m={},v=/^[^:]+:\/*[^/]*$/,b=/^([^:]+:)[\s\S]*$/,y=/^([^:]+:\/*[^/]*)[\s\S]*$/;function x(t,e,n){var i=t.length;if(0===i)return"";for(var a=0;a<i;){var r=t.charAt(i-a-1);if(r!==e||n){if(r===e||!n)break;a++}else a++}return t.substr(0,i-a)}var k=function(t,e){if(e){if(s.test(t))return t.replace(o,i)}else if(l.test(t))return t.replace(c,i);return t},_=h,w=function(t,e){t=t.source||t,e=e||"";var n={replace:function(e,i){return i=(i=i.source||i).replace(p,"$1"),t=t.replace(e,i),n},getRegex:function(){return new RegExp(t,e)}};return n},S={exec:function(){}},$=function(t){for(var e,n,i=1;i<arguments.length;i++)for(n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},T=function(t,e){var n=t.replace(/\|/g,(function(t,e,n){for(var i=!1,a=e;0<=--a&&"\\"===n[a];)i=!i;return i?"|":" |"})).split(/ \|/),i=0;if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;i<n.length;i++)n[i]=n[i].trim().replace(/\\\|/g,"|");return n},A=function(t,e){if(-1===t.indexOf(e[1]))return-1;for(var n=t.length,i=0,a=0;a<n;a++)if("\\"===t[a])a++;else if(t[a]===e[0])i++;else if(t[a]===e[1]&&--i<0)return a;return-1},z=r.defaults,O=x,I=T,F=k,C=A;function R(t,e,n){var i=e.href,a=e.title?F(e.title):null;e=t[1].replace(/\\([\[\]])/g,"$1");return"!"!==t[0].charAt(0)?{type:"link",raw:n,href:i,title:a,text:e}:{type:"image",raw:n,href:i,title:a,text:F(e)}}var E=function(){function t(t){this.options=t||z}var e=t.prototype;return e.space=function(t){if(t=this.rules.block.newline.exec(t),t)return 1<t[0].length?{type:"space",raw:t[0]}:{raw:"\n"}},e.code=function(t,e){if(t=this.rules.block.code.exec(t),t)return e=e[e.length-1],e&&"paragraph"===e.type?{raw:t[0],text:t[0].trimRight()}:(e=t[0].replace(/^ {4}/gm,""),{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:O(e,"\n")})},e.fences=function(t){var e=this.rules.block.fences.exec(t);if(e){var n=e[0];t=function(t,e){if(null===(t=t.match(/^(\s+)(?:```)/)))return e;var n=t[1];return e.split("\n").map((function(t){var e=t.match(/^\s+/);return null!==e&&e[0].length>=n.length?t.slice(n.length):t})).join("\n")}(n,e[3]||"");return{type:"code",raw:n,lang:e[2]&&e[2].trim(),text:t}}},e.heading=function(t){if(t=this.rules.block.heading.exec(t),t)return{type:"heading",raw:t[0],depth:t[1].length,text:t[2]}},e.nptable=function(t){if(t=this.rules.block.nptable.exec(t),t){var e={type:"table",header:I(t[1].replace(/^ *| *\| *$/g,"")),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:t[3]?t[3].replace(/\n$/,"").split("\n"):[],raw:t[0]};if(e.header.length===e.align.length){for(var n=e.align.length,i=0;i<n;i++)/^ *-+: *$/.test(e.align[i])?e.align[i]="right":/^ *:-+: *$/.test(e.align[i])?e.align[i]="center":/^ *:-+ *$/.test(e.align[i])?e.align[i]="left":e.align[i]=null;for(n=e.cells.length,i=0;i<n;i++)e.cells[i]=I(e.cells[i],e.header.length);return e}}},e.hr=function(t){if(t=this.rules.block.hr.exec(t),t)return{type:"hr",raw:t[0]}},e.blockquote=function(t){var e=this.rules.block.blockquote.exec(t);if(e)return t=e[0].replace(/^ *> ?/gm,""),{type:"blockquote",raw:e[0],text:t}},e.list=function(t){if(t=this.rules.block.list.exec(t),t){for(var e,n,i,a,r,s=t[0],o=t[2],l=1<o.length,c={type:"list",raw:s,ordered:l,start:l?+o.slice(0,-1):"",loose:!1,items:[]},u=t[0].match(this.rules.block.item),d=!1,h=u.length,p=this.rules.block.listItemStart.exec(u[0]),f=0;f<h;f++){if(s=e=u[f],f!==h-1){if((i=this.rules.block.listItemStart.exec(u[f+1]))[1].length>p[0].length||3<i[1].length){u.splice(f,2,u[f]+"\n"+u[f+1]),f--,h--;continue}(!this.options.pedantic||this.options.smartLists?i[2][i[2].length-1]!==o[o.length-1]:l==(1===i[2].length))&&(n=u.slice(f+1).join("\n"),c.raw=c.raw.substring(0,c.raw.length-n.length),f=h-1),p=i}i=e.length,~(e=e.replace(/^ *([*+-]|\d+[.)]) ?/,"")).indexOf("\n ")&&(i-=e.length,e=this.options.pedantic?e.replace(/^ {1,4}/gm,""):e.replace(new RegExp("^ {1,"+i+"}","gm"),"")),i=d||/\n\n(?!\s*$)/.test(e),f!==h-1&&(d="\n"===e.charAt(e.length-1),i=i||d),i&&(c.loose=!0),this.options.gfm&&(r=void 0,(a=/^\[[ xX]\] /.test(e))&&(r=" "!==e[1],e=e.replace(/^\[[ xX]\] +/,""))),c.items.push({type:"list_item",raw:s,task:a,checked:r,loose:i,text:e})}return c}},e.html=function(t){if(t=this.rules.block.html.exec(t),t)return{type:this.options.sanitize?"paragraph":"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):F(t[0]):t[0]}},e.def=function(t){if(t=this.rules.block.def.exec(t),t)return t[3]&&(t[3]=t[3].substring(1,t[3].length-1)),{tag:t[1].toLowerCase().replace(/\s+/g," "),raw:t[0],href:t[2],title:t[3]}},e.table=function(t){if(t=this.rules.block.table.exec(t),t){var e={type:"table",header:I(t[1].replace(/^ *| *\| *$/g,"")),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:t[3]?t[3].replace(/\n$/,"").split("\n"):[]};if(e.header.length===e.align.length){e.raw=t[0];for(var n=e.align.length,i=0;i<n;i++)/^ *-+: *$/.test(e.align[i])?e.align[i]="right":/^ *:-+: *$/.test(e.align[i])?e.align[i]="center":/^ *:-+ *$/.test(e.align[i])?e.align[i]="left":e.align[i]=null;for(n=e.cells.length,i=0;i<n;i++)e.cells[i]=I(e.cells[i].replace(/^ *\| *| *\| *$/g,""),e.header.length);return e}}},e.lheading=function(t){if(t=this.rules.block.lheading.exec(t),t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1]}},e.paragraph=function(t){if(t=this.rules.block.paragraph.exec(t),t)return{type:"paragraph",raw:t[0],text:"\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1]}},e.text=function(t,e){if(t=this.rules.block.text.exec(t),t)return e=e[e.length-1],e&&"text"===e.type?{raw:t[0],text:t[0]}:{type:"text",raw:t[0],text:t[0]}},e.escape=function(t){if(t=this.rules.inline.escape.exec(t),t)return{type:"escape",raw:t[0],text:F(t[1])}},e.tag=function(t,e,n){if(t=this.rules.inline.tag.exec(t),t)return!e&&/^<a /i.test(t[0])?e=!0:e&&/^<\/a>/i.test(t[0])&&(e=!1),!n&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?n=!0:n&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(n=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:e,inRawBlock:n,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):F(t[0]):t[0]}},e.link=function(t){var e=this.rules.inline.link.exec(t);if(e){t=C(e[2],"()"),-1<t&&(i=(0===e[0].indexOf("!")?5:4)+e[1].length+t,e[2]=e[2].substring(0,t),e[0]=e[0].substring(0,i).trim(),e[3]="");t=e[2];var n,i="";return i=this.options.pedantic?(n=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(t),n?(t=n[1],n[3]):""):e[3]?e[3].slice(1,-1):"",R(e,{href:(t=t.trim().replace(/^<([\s\S]*)>$/,"$1"))&&t.replace(this.rules.inline._escapes,"$1"),title:i&&i.replace(this.rules.inline._escapes,"$1")},e[0])}},e.reflink=function(t,e){if((n=this.rules.inline.reflink.exec(t))||(n=this.rules.inline.nolink.exec(t))){if(t=(n[2]||n[1]).replace(/\s+/g," "),(t=e[t.toLowerCase()])&&t.href)return R(n,t,n[0]);var n=n[0].charAt(0);return{type:"text",raw:n,text:n}}},e.strong=function(t,e,n){void 0===n&&(n="");var i=this.rules.inline.strong.start.exec(t);if(i&&(!i[1]||i[1]&&(""===n||this.rules.inline.punctuation.exec(n)))){e=e.slice(-1*t.length);var a,r="**"===i[0]?this.rules.inline.strong.endAst:this.rules.inline.strong.endUnd;for(r.lastIndex=0;null!=(i=r.exec(e));)if(a=this.rules.inline.strong.middle.exec(e.slice(0,i.index+3)))return{type:"strong",raw:t.slice(0,a[0].length),text:t.slice(2,a[0].length-2)}}},e.em=function(t,e,n){void 0===n&&(n="");var i=this.rules.inline.em.start.exec(t);if(i&&(!i[1]||i[1]&&(""===n||this.rules.inline.punctuation.exec(n)))){e=e.slice(-1*t.length);var a,r="*"===i[0]?this.rules.inline.em.endAst:this.rules.inline.em.endUnd;for(r.lastIndex=0;null!=(i=r.exec(e));)if(a=this.rules.inline.em.middle.exec(e.slice(0,i.index+2)))return{type:"em",raw:t.slice(0,a[0].length),text:t.slice(1,a[0].length-1)}}},e.codespan=function(t){var e=this.rules.inline.code.exec(t);if(e){var n=e[2].replace(/\n/g," "),i=/[^ ]/.test(n);t=n.startsWith(" ")&&n.endsWith(" ");return i&&t&&(n=n.substring(1,n.length-1)),n=F(n,!0),{type:"codespan",raw:e[0],text:n}}},e.br=function(t){if(t=this.rules.inline.br.exec(t),t)return{type:"br",raw:t[0]}},e.del=function(t){if(t=this.rules.inline.del.exec(t),t)return{type:"del",raw:t[0],text:t[2]}},e.autolink=function(t,e){if(t=this.rules.inline.autolink.exec(t),t){var n;e="@"===t[2]?"mailto:"+(n=F(this.options.mangle?e(t[1]):t[1])):n=F(t[1]);return{type:"link",raw:t[0],text:n,href:e,tokens:[{type:"text",raw:n,text:n}]}}},e.url=function(t,e){var n,i,a,r;if(n=this.rules.inline.url.exec(t)){if("@"===n[2])a="mailto:"+(i=F(this.options.mangle?e(n[0]):n[0]));else{for(;r=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0],r!==n[0];);i=F(n[0]),a="www."===n[1]?"http://"+i:i}return{type:"link",raw:n[0],text:i,href:a,tokens:[{type:"text",raw:i,text:i}]}}},e.inlineText=function(t,e,n){if(t=this.rules.inline.text.exec(t),t)return n=e?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):F(t[0]):t[0]:F(this.options.smartypants?n(t[0]):t[0]),{type:"text",raw:t[0],text:n}},t}();T=S,A=w,S=$,w={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?:\n+|$)|$)/,hr:/^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6}) +([^\n]*?)(?: +#+)? *(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3})(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?! {0,3}bull )\n*|\s*$)/,html:"^ {0,3}(?:<(script|pre|style)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:\\n{2,}|$)|<(?!script|pre|style)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$)|</(?!script|pre|style)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$))",def:/^ {0,3}\[(label)\]: *\n? *<?([^\s>]+)>?(?:(?: +\n? *| *\n *)(title))? *(?:\n+|$)/,nptable:T,table:T,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\[\[\]]|[^\[\]])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/},w.def=A(w.def).replace("label",w._label).replace("title",w._title).getRegex(),w.bullet=/(?:[*+-]|\d{1,9}[.)])/,w.item=/^( *)(bull) ?[^\n]*(?:\n(?! *bull ?)[^\n]*)*/,w.item=A(w.item,"gm").replace(/bull/g,w.bullet).getRegex(),w.listItemStart=A(/^( *)(bull)/).replace("bull",w.bullet).getRegex(),w.list=A(w.list).replace(/bull/g,w.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+w.def.source+")").getRegex(),w._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",w._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,w.html=A(w.html,"i").replace("comment",w._comment).replace("tag",w._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),w.paragraph=A(w._paragraph).replace("hr",w.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",w._tag).getRegex(),w.blockquote=A(w.blockquote).replace("paragraph",w.paragraph).getRegex(),w.normal=S({},w),w.gfm=S({},w.normal,{nptable:"^ *([^|\\n ].*\\|.*)\\n {0,3}([-:]+ *\\|[-| :]*)(?:\\n((?:(?!\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)",table:"^ *\\|(.+)\\n {0,3}\\|?( *[-:]+[-| :]*)(?:\\n *((?:(?!\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),w.gfm.nptable=A(w.gfm.nptable).replace("hr",w.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",w._tag).getRegex(),w.gfm.table=A(w.gfm.table).replace("hr",w.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",w._tag).getRegex(),w.pedantic=S({},w.normal,{html:A("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",w._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *(?:#+ *)?(?:\n+|$)/,fences:T,paragraph:A(w.normal._paragraph).replace("hr",w.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",w.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()}),T={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:T,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(?!\s*\])((?:\\[\[\]]?|[^\[\]\\])+)\]/,nolink:/^!?\[(?!\s*\])((?:\[[^\[\]]*\]|\\[\[\]]|[^\[\]])*)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",strong:{start:/^(?:(\*\*(?=[*punctuation]))|\*\*)(?![\s])|__/,middle:/^\*\*(?:(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)|\*(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)*?\*)+?\*\*$|^__(?![\s])((?:(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)|_(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)*?_)+?)__$/,endAst:/[^punctuation\s]\*\*(?!\*)|[punctuation]\*\*(?!\*)(?:(?=[punctuation_\s]|$))/,endUnd:/[^\s]__(?!_)(?:(?=[punctuation*\s])|$)/},em:{start:/^(?:(\*(?=[punctuation]))|\*)(?![*\s])|_/,middle:/^\*(?:(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)|\*(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)*?\*)+?\*$|^_(?![_\s])(?:(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)|_(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)*?_)+?_$/,endAst:/[^punctuation\s]\*(?!\*)|[punctuation]\*(?!\*)(?:(?=[punctuation_\s]|$))/,endUnd:/[^\s]_(?!_)(?:(?=[punctuation*\s])|$)/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:T,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\s*punctuation])/,_punctuation:"!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~"},T.punctuation=A(T.punctuation).replace(/punctuation/g,T._punctuation).getRegex(),T._blockSkip="\\[[^\\]]*?\\]\\([^\\)]*?\\)|`[^`]*?`|<[^>]*?>",T._overlapSkip="__[^_]*?__|\\*\\*\\[^\\*\\]*?\\*\\*",T._comment=A(w._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),T.em.start=A(T.em.start).replace(/punctuation/g,T._punctuation).getRegex(),T.em.middle=A(T.em.middle).replace(/punctuation/g,T._punctuation).replace(/overlapSkip/g,T._overlapSkip).getRegex(),T.em.endAst=A(T.em.endAst,"g").replace(/punctuation/g,T._punctuation).getRegex(),T.em.endUnd=A(T.em.endUnd,"g").replace(/punctuation/g,T._punctuation).getRegex(),T.strong.start=A(T.strong.start).replace(/punctuation/g,T._punctuation).getRegex(),T.strong.middle=A(T.strong.middle).replace(/punctuation/g,T._punctuation).replace(/overlapSkip/g,T._overlapSkip).getRegex(),T.strong.endAst=A(T.strong.endAst,"g").replace(/punctuation/g,T._punctuation).getRegex(),T.strong.endUnd=A(T.strong.endUnd,"g").replace(/punctuation/g,T._punctuation).getRegex(),T.blockSkip=A(T._blockSkip,"g").getRegex(),T.overlapSkip=A(T._overlapSkip,"g").getRegex(),T._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,T._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,T._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,T.autolink=A(T.autolink).replace("scheme",T._scheme).replace("email",T._email).getRegex(),T._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,T.tag=A(T.tag).replace("comment",T._comment).replace("attribute",T._attribute).getRegex(),T._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,T._href=/<(?:\\[<>]?|[^\s<>\\])*>|[^\s\x00-\x1f]*/,T._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,T.link=A(T.link).replace("label",T._label).replace("href",T._href).replace("title",T._title).getRegex(),T.reflink=A(T.reflink).replace("label",T._label).getRegex(),T.reflinkSearch=A(T.reflinkSearch,"g").replace("reflink",T.reflink).replace("nolink",T.nolink).getRegex(),T.normal=S({},T),T.pedantic=S({},T.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:A(/^!?\[(label)\]\((.*?)\)/).replace("label",T._label).getRegex(),reflink:A(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",T._label).getRegex()}),T.gfm=S({},T.normal,{escape:A(T.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*~]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))/}),T.gfm.url=A(T.gfm.url,"i").replace("email",T.gfm._extended_email).getRegex(),T.breaks=S({},T.gfm,{br:A(T.br).replace("{2,}","*").getRegex(),text:A(T.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()}),T={block:w,inline:T};var j=r.defaults,N=T.block,P=T.inline,L=function(t,e){if(e<1)return"";for(var n="";1<e;)1&e&&(n+=t),e>>=1,t+=t;return n+t};function M(t){return t.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function q(t){for(var e,n="",i=t.length,a=0;a<i;a++)e=t.charCodeAt(a),.5<Math.random()&&(e="x"+e.toString(16)),n+="&#"+e+";";return n}var D=function(){function e(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||j,this.options.tokenizer=this.options.tokenizer||new E,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,t={block:N.normal,inline:P.normal},this.options.pedantic?(t.block=N.pedantic,t.inline=P.pedantic):this.options.gfm&&(t.block=N.gfm,this.options.breaks?t.inline=P.breaks:t.inline=P.gfm),this.tokenizer.rules=t}e.lex=function(t,n){return new e(n).lex(t)},e.lexInline=function(t,n){return new e(n).inlineTokens(t)};var n,i,a=e.prototype;return a.lex=function(t){return t=t.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    "),this.blockTokens(t,this.tokens,!0),this.inline(this.tokens),this.tokens},a.blockTokens=function(t,e,n){var i,a,r,s;for(void 0===e&&(e=[]),void 0===n&&(n=!0),t=t.replace(/^ +$/gm,"");t;)if(i=this.tokenizer.space(t))t=t.substring(i.raw.length),i.type&&e.push(i);else if(i=this.tokenizer.code(t,e))t=t.substring(i.raw.length),i.type?e.push(i):((s=e[e.length-1]).raw+="\n"+i.raw,s.text+="\n"+i.text);else if(i=this.tokenizer.fences(t))t=t.substring(i.raw.length),e.push(i);else if(i=this.tokenizer.heading(t))t=t.substring(i.raw.length),e.push(i);else if(i=this.tokenizer.nptable(t))t=t.substring(i.raw.length),e.push(i);else if(i=this.tokenizer.hr(t))t=t.substring(i.raw.length),e.push(i);else if(i=this.tokenizer.blockquote(t))t=t.substring(i.raw.length),i.tokens=this.blockTokens(i.text,[],n),e.push(i);else if(i=this.tokenizer.list(t)){for(t=t.substring(i.raw.length),r=i.items.length,a=0;a<r;a++)i.items[a].tokens=this.blockTokens(i.items[a].text,[],!1);e.push(i)}else if(i=this.tokenizer.html(t))t=t.substring(i.raw.length),e.push(i);else if(n&&(i=this.tokenizer.def(t)))t=t.substring(i.raw.length),this.tokens.links[i.tag]||(this.tokens.links[i.tag]={href:i.href,title:i.title});else if(i=this.tokenizer.table(t))t=t.substring(i.raw.length),e.push(i);else if(i=this.tokenizer.lheading(t))t=t.substring(i.raw.length),e.push(i);else if(n&&(i=this.tokenizer.paragraph(t)))t=t.substring(i.raw.length),e.push(i);else if(i=this.tokenizer.text(t,e))t=t.substring(i.raw.length),i.type?e.push(i):((s=e[e.length-1]).raw+="\n"+i.raw,s.text+="\n"+i.text);else if(t){var o="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(o);break}throw new Error(o)}return e},a.inline=function(t){for(var e,n,i,a,r,s=t.length,o=0;o<s;o++)switch((r=t[o]).type){case"paragraph":case"text":case"heading":r.tokens=[],this.inlineTokens(r.text,r.tokens);break;case"table":for(r.tokens={header:[],cells:[]},i=r.header.length,e=0;e<i;e++)r.tokens.header[e]=[],this.inlineTokens(r.header[e],r.tokens.header[e]);for(i=r.cells.length,e=0;e<i;e++)for(a=r.cells[e],r.tokens.cells[e]=[],n=0;n<a.length;n++)r.tokens.cells[e][n]=[],this.inlineTokens(a[n],r.tokens.cells[e][n]);break;case"blockquote":this.inline(r.tokens);break;case"list":for(i=r.items.length,e=0;e<i;e++)this.inline(r.items[e].tokens)}return t},a.inlineTokens=function(t,e,n,i){var a;void 0===e&&(e=[]),void 0===n&&(n=!1),void 0===i&&(i=!1);var r,s,o,l=t;if(this.tokens.links){var c=Object.keys(this.tokens.links);if(0<c.length)for(;null!=(r=this.tokenizer.rules.inline.reflinkSearch.exec(l));)c.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(l=l.slice(0,r.index)+"["+L("a",r[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(r=this.tokenizer.rules.inline.blockSkip.exec(l));)l=l.slice(0,r.index)+"["+L("a",r[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;t;)if(s||(o=""),s=!1,a=this.tokenizer.escape(t))t=t.substring(a.raw.length),e.push(a);else if(a=this.tokenizer.tag(t,n,i))t=t.substring(a.raw.length),n=a.inLink,i=a.inRawBlock,e.push(a);else if(a=this.tokenizer.link(t))t=t.substring(a.raw.length),"link"===a.type&&(a.tokens=this.inlineTokens(a.text,[],!0,i)),e.push(a);else if(a=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(a.raw.length),"link"===a.type&&(a.tokens=this.inlineTokens(a.text,[],!0,i)),e.push(a);else if(a=this.tokenizer.strong(t,l,o))t=t.substring(a.raw.length),a.tokens=this.inlineTokens(a.text,[],n,i),e.push(a);else if(a=this.tokenizer.em(t,l,o))t=t.substring(a.raw.length),a.tokens=this.inlineTokens(a.text,[],n,i),e.push(a);else if(a=this.tokenizer.codespan(t))t=t.substring(a.raw.length),e.push(a);else if(a=this.tokenizer.br(t))t=t.substring(a.raw.length),e.push(a);else if(a=this.tokenizer.del(t))t=t.substring(a.raw.length),a.tokens=this.inlineTokens(a.text,[],n,i),e.push(a);else if(a=this.tokenizer.autolink(t,q))t=t.substring(a.raw.length),e.push(a);else if(n||!(a=this.tokenizer.url(t,q))){if(a=this.tokenizer.inlineText(t,i,M))t=t.substring(a.raw.length),o=a.raw.slice(-1),s=!0,e.push(a);else if(t){var u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}throw new Error(u)}}else t=t.substring(a.raw.length),e.push(a);return e},n=e,i=[{key:"rules",get:function(){return{block:N,inline:P}}}],(a=null)&&t(n.prototype,a),i&&t(n,i),e}(),U=r.defaults,Z=function(t,e,n){if(t){var i;try{i=decodeURIComponent(h(n)).replace(f,"").toLowerCase()}catch(t){return null}if(0===i.indexOf("javascript:")||0===i.indexOf("vbscript:")||0===i.indexOf("data:"))return null}e&&!g.test(n)&&(n=function(t,e){m[" "+t]||(v.test(t)?m[" "+t]=t+"/":m[" "+t]=x(t,"/",!0));var n=-1===(t=m[" "+t]).indexOf(":");return"//"===e.substring(0,2)?n?e:t.replace(b,"$1")+e:"/"===e.charAt(0)?n?e:t.replace(y,"$1")+e:t+e}(e,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(t){return null}return n},B=k,V=function(){function t(t){this.options=t||U}var e=t.prototype;return e.code=function(t,e,n){var i=(e||"").match(/\S*/)[0];return!this.options.highlight||null!=(e=this.options.highlight(t,i))&&e!==t&&(n=!0,t=e),i?'<pre><code class="'+this.options.langPrefix+B(i,!0)+'">'+(n?t:B(t,!0))+"</code></pre>\n":"<pre><code>"+(n?t:B(t,!0))+"</code></pre>\n"},e.blockquote=function(t){return"<blockquote>\n"+t+"</blockquote>\n"},e.html=function(t){return t},e.heading=function(t,e,n,i){return this.options.headerIds?"<h"+e+' id="'+this.options.headerPrefix+i.slug(n)+'">'+t+"</h"+e+">\n":"<h"+e+">"+t+"</h"+e+">\n"},e.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},e.list=function(t,e,n){var i=e?"ol":"ul";return"<"+i+(e&&1!==n?' start="'+n+'"':"")+">\n"+t+"</"+i+">\n"},e.listitem=function(t){return"<li>"+t+"</li>\n"},e.checkbox=function(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},e.paragraph=function(t){return"<p>"+t+"</p>\n"},e.table=function(t,e){return"<table>\n<thead>\n"+t+"</thead>\n"+(e=e&&"<tbody>"+e+"</tbody>")+"</table>\n"},e.tablerow=function(t){return"<tr>\n"+t+"</tr>\n"},e.tablecell=function(t,e){var n=e.header?"th":"td";return(e.align?"<"+n+' align="'+e.align+'">':"<"+n+">")+t+"</"+n+">\n"},e.strong=function(t){return"<strong>"+t+"</strong>"},e.em=function(t){return"<em>"+t+"</em>"},e.codespan=function(t){return"<code>"+t+"</code>"},e.br=function(){return this.options.xhtml?"<br/>":"<br>"},e.del=function(t){return"<del>"+t+"</del>"},e.link=function(t,e,n){return null===(t=Z(this.options.sanitize,this.options.baseUrl,t))?n:(t='<a href="'+B(t)+'"',e&&(t+=' title="'+e+'"'),t+">"+n+"</a>")},e.image=function(t,e,n){return null===(t=Z(this.options.sanitize,this.options.baseUrl,t))?n:(n='<img src="'+t+'" alt="'+n+'"',e&&(n+=' title="'+e+'"'),n+(this.options.xhtml?"/>":">"))},e.text=function(t){return t},t}(),H=function(){function t(){}var e=t.prototype;return e.strong=function(t){return t},e.em=function(t){return t},e.codespan=function(t){return t},e.del=function(t){return t},e.html=function(t){return t},e.text=function(t){return t},e.link=function(t,e,n){return""+n},e.image=function(t,e,n){return""+n},e.br=function(){return""},t}(),W=function(){function t(){this.seen={}}var e=t.prototype;return e.serialize=function(t){return t.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")},e.getNextSafeSlug=function(t,e){var n=t,i=0;if(this.seen.hasOwnProperty(n))for(i=this.seen[t];n=t+"-"+ ++i,this.seen.hasOwnProperty(n););return e||(this.seen[t]=i,this.seen[n]=0),n},e.slug=function(t,e){void 0===e&&(e={});var n=this.serialize(t);return this.getNextSafeSlug(n,e.dryrun)},t}(),G=r.defaults,J=_,X=function(){function t(t){this.options=t||G,this.options.renderer=this.options.renderer||new V,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new H,this.slugger=new W}t.parse=function(e,n){return new t(n).parse(e)},t.parseInline=function(e,n){return new t(n).parseInline(e)};var e=t.prototype;return e.parse=function(t,e){void 0===e&&(e=!0);for(var n,i,a,r,s,o,l,c,u,d,h,p,f,g,m,v="",b=t.length,y=0;y<b;y++)switch((c=t[y]).type){case"space":continue;case"hr":v+=this.renderer.hr();continue;case"heading":v+=this.renderer.heading(this.parseInline(c.tokens),c.depth,J(this.parseInline(c.tokens,this.textRenderer)),this.slugger);continue;case"code":v+=this.renderer.code(c.text,c.lang,c.escaped);continue;case"table":for(o=u="",a=c.header.length,n=0;n<a;n++)o+=this.renderer.tablecell(this.parseInline(c.tokens.header[n]),{header:!0,align:c.align[n]});for(u+=this.renderer.tablerow(o),l="",a=c.cells.length,n=0;n<a;n++){for(o="",r=(s=c.tokens.cells[n]).length,i=0;i<r;i++)o+=this.renderer.tablecell(this.parseInline(s[i]),{header:!1,align:c.align[i]});l+=this.renderer.tablerow(o)}v+=this.renderer.table(u,l);continue;case"blockquote":l=this.parse(c.tokens),v+=this.renderer.blockquote(l);continue;case"list":for(u=c.ordered,x=c.start,d=c.loose,a=c.items.length,l="",n=0;n<a;n++)f=(p=c.items[n]).checked,g=p.task,h="",p.task&&(m=this.renderer.checkbox(f),d?0<p.tokens.length&&"text"===p.tokens[0].type?(p.tokens[0].text=m+" "+p.tokens[0].text,p.tokens[0].tokens&&0<p.tokens[0].tokens.length&&"text"===p.tokens[0].tokens[0].type&&(p.tokens[0].tokens[0].text=m+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:m}):h+=m),h+=this.parse(p.tokens,d),l+=this.renderer.listitem(h,g,f);v+=this.renderer.list(l,u,x);continue;case"html":v+=this.renderer.html(c.text);continue;case"paragraph":v+=this.renderer.paragraph(this.parseInline(c.tokens));continue;case"text":for(l=c.tokens?this.parseInline(c.tokens):c.text;y+1<b&&"text"===t[y+1].type;)l+="\n"+((c=t[++y]).tokens?this.parseInline(c.tokens):c.text);v+=e?this.renderer.paragraph(l):l;continue;default:var x='Token with "'+c.type+'" type was not found.';if(this.options.silent)return void console.error(x);throw new Error(x)}return v},e.parseInline=function(t,e){e=e||this.renderer;for(var n,i="",a=t.length,r=0;r<a;r++)switch((n=t[r]).type){case"escape":i+=e.text(n.text);break;case"html":i+=e.html(n.text);break;case"link":i+=e.link(n.href,n.title,this.parseInline(n.tokens,e));break;case"image":i+=e.image(n.href,n.title,n.text);break;case"strong":i+=e.strong(this.parseInline(n.tokens,e));break;case"em":i+=e.em(this.parseInline(n.tokens,e));break;case"codespan":i+=e.codespan(n.text);break;case"br":i+=e.br();break;case"del":i+=e.del(this.parseInline(n.tokens,e));break;case"text":i+=e.text(n.text);break;default:var s='Token with "'+n.type+'" type was not found.';if(this.options.silent)return void console.error(s);throw new Error(s)}return i},t}(),Y=$,Q=function(t){t&&t.sanitize&&!t.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")},K=k,tt=(k=r.getDefaults,r.changeDefaults);function et(t,e,n){if(null==t)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof t)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected");if("function"==typeof e&&(n=e,e=null),e=Y({},et.defaults,e||{}),Q(e),n){var i,a=e.highlight;try{i=D.lex(t,e)}catch(t){return n(t)}var r=function(t){var r;if(!t)try{r=X.parse(i,e)}catch(r){t=r}return e.highlight=a,t?n(t):n(null,r)};if(!a||a.length<3)return r();if(delete e.highlight,!i.length)return r();var s=0;return et.walkTokens(i,(function(t){"code"===t.type&&(s++,setTimeout((function(){a(t.text,t.lang,(function(e,n){return e?r(e):(null!=n&&n!==t.text&&(t.text=n,t.escaped=!0),void(0===--s&&r()))}))}),0))})),void(0===s&&r())}try{var o=D.lex(t,e);return e.walkTokens&&et.walkTokens(o,e.walkTokens),X.parse(o,e)}catch(t){if(t.message+="\nPlease report this to https://github.com/markedjs/marked.",e.silent)return"<p>An error occurred:</p><pre>"+K(t.message+"",!0)+"</pre>";throw t}}return r=r.defaults,et.options=et.setOptions=function(t){return Y(et.defaults,t),tt(et.defaults),et},et.getDefaults=k,et.defaults=r,et.use=function(t){var e,n=Y({},t);t.renderer&&function(){var e,i=et.defaults.renderer||new V;for(e in t.renderer)!function(e){var n=i[e];i[e]=function(){for(var a=arguments.length,r=new Array(a),s=0;s<a;s++)r[s]=arguments[s];var o=t.renderer[e].apply(i,r);return!1===o&&(o=n.apply(i,r)),o}}(e);n.renderer=i}(),t.tokenizer&&function(){var e,i=et.defaults.tokenizer||new E;for(e in t.tokenizer)!function(e){var n=i[e];i[e]=function(){for(var a=arguments.length,r=new Array(a),s=0;s<a;s++)r[s]=arguments[s];var o=t.tokenizer[e].apply(i,r);return!1===o&&(o=n.apply(i,r)),o}}(e);n.tokenizer=i}(),t.walkTokens&&(e=et.defaults.walkTokens,n.walkTokens=function(n){t.walkTokens(n),e&&e(n)}),et.setOptions(n)},et.walkTokens=function(t,e){for(var i,a=n(t);!(i=a()).done;){var r=i.value;switch(e(r),r.type){case"table":for(var s=n(r.tokens.header);!(o=s()).done;){var o=o.value;et.walkTokens(o,e)}for(var l,c=n(r.tokens.cells);!(l=c()).done;)for(var u=n(l.value);!(d=u()).done;){var d=d.value;et.walkTokens(d,e)}break;case"list":et.walkTokens(r.items,e);break;default:r.tokens&&et.walkTokens(r.tokens,e)}}},et.parseInline=function(t,e){if(null==t)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!=typeof t)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected");e=Y({},et.defaults,e||{}),Q(e);try{var n=D.lexInline(t,e);return e.walkTokens&&et.walkTokens(n,e.walkTokens),X.parseInline(n,e)}catch(t){if(t.message+="\nPlease report this to https://github.com/markedjs/marked.",e.silent)return"<p>An error occurred:</p><pre>"+K(t.message+"",!0)+"</pre>";throw t}},et.Parser=X,et.parser=X.parse,et.Renderer=V,et.TextRenderer=H,et.Lexer=D,et.lexer=D.lex,et.Tokenizer=E,et.Slugger=W,et.parse=et}();e.default=i},"54e9":function(t,e,n){"use strict";var i=n("92d9"),a=n.n(i);a.a},"628f":function(t,e,n){"use strict";e["a"]=function(t){(t.options.wxs||(t.options.wxs={}))["handler"]=function(t){var e={abbr:!0,b:!0,big:!0,code:!0,del:!0,em:!0,i:!0,ins:!0,label:!0,q:!0,small:!0,span:!0,strong:!0,sub:!0,sup:!0};return t.exports={isInline:function(t,n){return e[t]||-1!==(n||"").indexOf("display:inline")}},t.exports}({exports:{}})}},"6e12":function(t,e,n){"use strict";n("73c2");var i=n("8bdb"),a=n("ab3f");i({target:"String",proto:!0,name:"trimEnd",forced:"".trimEnd!==a},{trimEnd:a})},"6fab":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("aa9c");var a=i(n("ace7")),r=0;function s(t){this.vm=t}s.prototype.onUpdate=function(){this.audios=[]},s.prototype.onParse=function(t){"audio"===t.name&&(t.attrs.id||(t.attrs.id="a"+r++),this.audios.push(t.attrs.id))},s.prototype.onLoad=function(){var t=this;setTimeout((function(){for(var e=0;e<t.audios.length;e++){var n=a.default.get(t.audios[e]);n.id=t.audios[e],t.vm._videos.push(n)}}),500)};var o=s;e.default=o},7118:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.controls?n("v-uni-view",{staticClass:"_contain"},[n("v-uni-view",{staticClass:"_poster",style:"background-image:url("+t.poster+")"},[n("v-uni-view",{staticClass:"_button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._buttonTap.apply(void 0,arguments)}}},[n("v-uni-view",{class:t.playing?"_pause":"_play"})],1)],1),n("v-uni-view",{staticClass:"_title"},[n("v-uni-view",{staticClass:"_name"},[t._v(t._s(t.name||"未知音频"))]),n("v-uni-view",{staticClass:"_author"},[t._v(t._s(t.author||"未知作者"))])],1),n("v-uni-slider",{staticClass:"_slider",attrs:{activeColor:"#585959","block-size":"12","handle-size":"12",disabled:t.error,value:t.value},on:{changing:function(e){arguments[0]=e=t.$handleEvent(e),t._seeking.apply(void 0,arguments)},change:function(e){arguments[0]=e=t.$handleEvent(e),t._seeked.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"_time"},[t._v(t._s(t.time||"00:00"))])],1):t._e()},a=[]},"73c2":function(t,e,n){"use strict";var i=n("8bdb"),a=n("ab3f");i({target:"String",proto:!0,name:"trimEnd",forced:"".trimRight!==a},{trimRight:a})},7569:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5c47"),n("af8f"),n("dfcf"),n("aa9c"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("0506"),n("4626"),n("5ac7"),n("bf0f");var i=function(t){t.search=function(e,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"background-color:yellow",a=[],r=[];return function s(o){for(var l=0;l<o.length;l++){var c=o[l];if("text"===c.type&&e){var u=c.text,d=u.split(e);if(d.length>1){c={name:"span",attrs:{},type:"node",c:1,s:1,children:[]},t.$set(o,l,c);for(var h=0;h<d.length;h++)d[h]&&c.children.push({type:"text",text:d[h]}),h!==d.length-1&&(c.children.push({name:"span",attrs:{id:n?"search"+(a.length+1):void 0,style:i},children:[{type:"text",text:e instanceof RegExp?e.exec(u)[0]:e}]}),a.push(c.children[c.children.length-1].attrs));if(e instanceof RegExp&&e.exec(u),n)for(var p=r.length;p--;){if(r[p].c)break;t.$set(r[p],"c",1)}}}else if(c.s){for(var f="",g=0;g<c.children.length;g++){var m=c.children[g];m.text?f+=m.text:f+=m.children[0].text}t.$set(o,l,{type:"text",text:f}),e&&(e instanceof RegExp?e.test(f):f.includes(e))&&l--}else c.children&&(r.push(c),s(c.children),r.pop())}}(t.nodes),new Promise((function(e){setTimeout((function(){e({num:a.length,highlight:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"background-color:#FF9632";t<1||t>a.length||(this.last&&(a[this.last-1].style=i),this.last=t,a[t-1].style=e)},jump:n?function(e,n){e>0&&e<=a.length&&t.navigateTo("search"+e,n)}:void 0})}),200)}))}};e.default=i},"8a69":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa"),n("aa9c"),n("bf0f"),n("7a76"),n("c9b5"),n("e966"),n("5c47"),n("a1c1"),n("c223");var a=i(n("cb97")),r=i(n("ced8")),s=i(n("0782")),o=i(n("6fab")),l=i(n("c605")),c=i(n("2ffb")),u=i(n("7569")),d=i(n("95cb")),h=i(n("1fa6")),p=i(n("eee6")),f=[s.default,o.default,l.default,c.default,u.default,d.default,h.default,p.default],g={name:"mp-html",data:function(){return{tooltip:null,slider:null,nodes:[]}},props:{editable:Boolean,placeholder:String,ImgCache:Boolean,markdown:Boolean,containerStyle:{type:String,default:""},content:{type:String,default:""},copyLink:{type:[Boolean,String],default:!0},domain:String,errorImg:{type:String,default:""},lazyLoad:{type:[Boolean,String],default:!1},loadingImg:{type:String,default:""},pauseVideo:{type:[Boolean,String],default:!0},previewImg:{type:[Boolean,String],default:!0},scrollTable:[Boolean,String],selectable:[Boolean,String],setTitle:{type:[Boolean,String],default:!0},showImgMenu:{type:[Boolean,String],default:!0},tagStyle:Object,useAnchor:[Boolean,Number]},components:{node:a.default},watch:{editable:function(t){this.setContent(t?this.content:this.getContent()),t||this._maskTap()},content:function(t){this.setContent(t)}},created:function(){this.plugins=[];for(var t=f.length;t--;)this.plugins.push(new f[t](this))},mounted:function(){!this.content&&!this.editable||this.nodes.length||this.setContent(this.content)},beforeDestroy:function(){this._hook("onDetached")},methods:{_containTap:function(){this._lock||this.slider||(this._edit=void 0,this._maskTap())},_tooltipTap:function(t){this._tooltipcb(t.currentTarget.dataset.i),this.$set(this,"tooltip",null)},_sliderChanging:function(t){this._slideringcb(t.detail.value)},_sliderChange:function(t){this._slidercb(t.detail.value)},in:function(t,e,n){t&&e&&n&&(this._in={page:t,selector:e,scrollTop:n})},navigateTo:function(t,e){var n=this;return t=this._ids[decodeURI(t)]||t,new Promise((function(i,a){if(n.useAnchor){e=e||parseInt(n.useAnchor)||0;var r=uni.createSelectorQuery().in(n._in?n._in.page:n).select((n._in?n._in.selector:"._root")+(t?"".concat(" ","#").concat(t):"")).boundingClientRect();n._in?r.select(n._in.selector).scrollOffset().select(n._in.selector).boundingClientRect():r.selectViewport().scrollOffset(),r.exec((function(t){if(t[0]){var r=t[1].scrollTop+t[0].top-(t[2]?t[2].top:0)+e;n._in?n._in.page[n._in.scrollTop]=r:uni.pageScrollTo({scrollTop:r,duration:300}),i()}else a(Error("Label not found"))}))}else a(Error("Anchor is disabled"))}))},getText:function(t){var e="";return function t(n){for(var i=0;i<n.length;i++){var a=n[i];if("text"===a.type)e+=a.text.replace(/&amp;/g,"&");else if("br"===a.name)e+="\n";else{var r="p"===a.name||"div"===a.name||"tr"===a.name||"li"===a.name||"h"===a.name[0]&&a.name[1]>"0"&&a.name[1]<"7";r&&e&&"\n"!==e[e.length-1]&&(e+="\n"),a.children&&t(a.children),r&&"\n"!==e[e.length-1]?e+="\n":"td"!==a.name&&"th"!==a.name||(e+="\t")}}}(t||this.nodes),e},getRect:function(){var t=this;return new Promise((function(e,n){uni.createSelectorQuery().in(t).select("#_root").boundingClientRect().exec((function(t){return t[0]?e(t[0]):n(Error("Root label not found"))}))}))},pauseMedia:function(){for(var t=(this._videos||[]).length;t--;)this._videos[t].pause()},setPlaybackRate:function(t){this.playbackRate=t;for(var e=(this._videos||[]).length;e--;)this._videos[e].playbackRate(t)},setContent:function(t,e){var n=this;e&&this.imgList||(this.imgList=[]);var i=new r.default(this).parse(t);if(this.$set(this,"nodes",e?(this.nodes||[]).concat(i):i),this._videos=[],this.$nextTick((function(){n._hook("onLoad"),n.$emit("load")})),this.lazyLoad||this.imgList._unloadimgs<this.imgList.length/2){var a=0,s=function t(e){e&&e.height||(e={}),e.height===a?n.$emit("ready",e):(a=e.height,setTimeout((function(){n.getRect().then(t).catch(t)}),350))};this.getRect().then(s).catch(s)}else this.imgList._unloadimgs||this.getRect().then((function(t){n.$emit("ready",t)})).catch((function(){n.$emit("ready",{})}))},_hook:function(t){for(var e=f.length;e--;)this.plugins[e][t]&&this.plugins[e][t]()}}};e.default=g},"8ae4":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={node:["大小","斜体","粗体","下划线","居中","缩进","上移","下移","删除"],img:["换图","宽度","超链接","预览图","禁用预览","上移","下移","删除"],link:["更换链接","上移","下移","删除"],media:["封面","循环","自动播放","上移","下移","删除"]}},"90ba":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("e966");var a=i(n("ace7")),r={data:function(){return{error:!1,playing:!1,time:"00:00",value:0}},props:{aid:String,name:String,author:String,poster:String,autoplay:[Boolean,String],controls:[Boolean,String],loop:[Boolean,String],src:String},watch:{src:function(t){this.setSrc(t)}},mounted:function(){var t=this;this._ctx=uni.createInnerAudioContext(),this._ctx.onError((function(e){t.error=!0,t.$emit("error",e)})),this._ctx.onTimeUpdate((function(){var e=t._ctx.currentTime,n=parseInt(e/60),i=Math.ceil(e%60);t.time=(n>9?n:"0"+n)+":"+(i>9?i:"0"+i),t.lastTime||(t.value=e/t._ctx.duration*100)})),this._ctx.onEnded((function(){t.loop||(t.playing=!1)})),a.default.set(this.aid,this),this.setSrc(this.src)},beforeDestroy:function(){this._ctx.destroy(),a.default.remove(this.aid)},onPageShow:function(){this.playing&&this._ctx.paused&&this._ctx.play()},methods:{setSrc:function(t){this._ctx.autoplay=this.autoplay,this._ctx.loop=this.loop,this._ctx.src=t,this.autoplay&&!this.playing&&(this.playing=!0)},play:function(){this._ctx.play(),this.playing=!0,this.$emit("play",{target:{id:this.aid}})},pause:function(){this._ctx.pause(),this.playing=!1,this.$emit("pause")},playbackRate:function(t){this._ctx.playbackRate=t},seek:function(t){this._ctx.seek(t)},_buttonTap:function(){this.playing?this.pause():this.play()},_seeking:function(t){if(!(t.timeStamp-this.lastTime<200)){var e=Math.round(t.detail.value/100*this._ctx.duration),n=parseInt(e/60),i=e%60;this.time=(n>9?n:"0"+n)+":"+(i>9?i:"0"+i),this.lastTime=t.timeStamp}},_seeked:function(t){this.seek(t.detail.value/100*this._ctx.duration),this.lastTime=void 0}}};e.default=r},"917f":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 顶层容器 */._contain[data-v-1973aa2a]{position:relative;display:inline-flex;width:290px;background-color:#fcfcfc;border:1px solid #e0e0e0;border-radius:2px}\n/* 播放、暂停按钮 */._button[data-v-1973aa2a]{display:flex;align-items:center;justify-content:center;width:20px;height:20px;overflow:hidden;background-color:rgba(0,0,0,.2);border:1px solid #fff;border-radius:50%;opacity:.9}._play[data-v-1973aa2a]{margin-left:2px;border-top:4px solid transparent;border-bottom:4px solid transparent;border-left:8px solid #fff}._pause[data-v-1973aa2a]{width:8px;height:8px;background-color:#fff}\n/* 海报 */._poster[data-v-1973aa2a]{display:flex;align-items:center;justify-content:center;width:70px;height:70px;background-color:#e6e6e6;background-size:contain}\n/* 标题栏 */._title[data-v-1973aa2a]{flex:1;margin:4px 0 0 14px;text-align:left}._author[data-v-1973aa2a]{width:45px;font-size:12px;color:#888}._name[data-v-1973aa2a]{width:140px;font-size:15px;line-height:39px}._author[data-v-1973aa2a],\n._name[data-v-1973aa2a]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\n/* 进度条 */._slider[data-v-1973aa2a]{position:absolute;right:16px;bottom:8px;width:140px;margin:0}\n/* 播放时间 */._time[data-v-1973aa2a]{margin:7px 14px 0 0;font-size:12px;color:#888}\n/* 响应式布局，大屏幕用更大的尺寸 */@media (min-width:400px){._contain[data-v-1973aa2a]{width:380px}._button[data-v-1973aa2a]{width:26px;height:26px}._poster[data-v-1973aa2a]{width:90px;height:90px}._author[data-v-1973aa2a]{width:60px;font-size:15px}._name[data-v-1973aa2a]{width:180px;font-size:19px;line-height:55px}._slider[data-v-1973aa2a]{right:20px;bottom:10px;width:180px}._time[data-v-1973aa2a]{font-size:15px}}",""]),t.exports=e},"92d9":function(t,e,n){var i=n("343e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("6fbea3c2",i,!0,{sourceMap:!1,shadowMode:!1})},9327:function(t,e,n){"use strict";var i=n("8bdb"),a=n("9f69"),r=n("1ded").f,s=n("c435"),o=n("9e70"),l=n("b6a1"),c=n("862c"),u=n("0931"),d=n("a734"),h=a("".slice),p=Math.min,f=u("endsWith"),g=!d&&!f&&!!function(){var t=r(String.prototype,"endsWith");return t&&!t.writable}();i({target:"String",proto:!0,forced:!g&&!f},{endsWith:function(t){var e=o(c(this));l(t);var n=arguments.length>1?arguments[1]:void 0,i=e.length,a=void 0===n?i:p(s(n),i),r=o(t);return h(e,a-r.length,a)===r}})},"951e":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4626"),n("5ac7"),n("aa9c"),n("c223"),n("5ef2"),n("6e12");var i={" ":!0,"\n":!0,"\t":!0,"\r":!0,"\f":!0};function a(){this.styles=[],this.selectors=[]}function r(t){this.selector="",this.style="",this.handler=t}a.prototype.parse=function(t){return new r(this).parse(t),this.styles},a.prototype.onSelector=function(t){if(!(t.includes("[")||t.includes("*")||t.includes("@"))){var e={};if(t.includes(":")){var n=t.split(":"),i=n.pop();if("before"!==i&&"after"!==i)return;e.pseudo=i,t=n[0]}if(t.includes(" ")){e.list=[];for(var a=t.split(" "),r=0;r<a.length;r++)if(a[r].length)for(var s=a[r].split(">"),o=0;o<s.length;o++)e.list.push(l(s[o])),o<s.length-1&&e.list.push(">")}else e.key=l(t);this.selectors.push(e)}function l(t){var e,n,i=[];for(e=1,n=0;e<t.length;e++)"."!==t[e]&&"#"!==t[e]||(i.push(t.substring(n,e)),n=e);return i.length?(i.push(t.substring(n,e)),i):t}},a.prototype.onContent=function(t){for(var e=0;e<this.selectors.length;e++)this.selectors[e].style=t;this.styles=this.styles.concat(this.selectors),this.selectors=[]},r.prototype.parse=function(t){this.i=0,this.content=t,this.state=this.blank;for(var e=t.length;this.i<e;this.i++)this.state(t[this.i])},r.prototype.comment=function(){this.i=this.content.indexOf("*/",this.i)+1,this.i||(this.i=this.content.length)},r.prototype.blank=function(t){if(!i[t]){if("/"===t&&"*"===this.content[this.i+1])return void this.comment();this.selector+=t,this.state=this.name}},r.prototype.name=function(t){if("/"!==t||"*"!==this.content[this.i+1])if("{"===t||","===t||";"===t){if(this.handler.onSelector(this.selector.trimEnd()),this.selector="","{"!==t)while(i[this.content[++this.i]]);"{"===this.content[this.i]?(this.floor=1,this.state=this.val):this.selector+=this.content[this.i]}else this.selector+=i[t]?" ":t;else this.comment()},r.prototype.val=function(t){if("/"!==t||"*"!==this.content[this.i+1]){if("{"===t)this.floor++;else if("}"===t&&(this.floor--,!this.floor))return this.handler.onContent(this.style),this.style="",void(this.state=this.blank);this.style+=t}else this.comment()};var s=a;e.default=s},9561:function(t,e,n){var i=n("af3b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("4a9e3e56",i,!0,{sourceMap:!1,shadowMode:!1})},"95c7":function(t,e,n){"use strict";n.r(e);var i=n("7118"),a=n("b817");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("4c64");var s=n("828b"),o=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"1973aa2a",null,!1,i["a"],void 0);e["default"]=o.exports},"95cb":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("c223"),n("5c47"),n("a1c1"),n("0c26"),n("e966"),n("3efd"),n("aa9c");var a=i(n("951e"));function r(){this.styles=[]}function s(t,e){function n(e){if("#"===e[0]){if(t.attrs.id&&t.attrs.id.trim()===e.substr(1))return 3}else if("."===e[0]){e=e.substr(1);for(var n=(t.attrs.class||"").split(" "),i=0;i<n.length;i++)if(n[i].trim()===e)return 2}else if(t.name===e)return 1;return 0}if(e instanceof Array){for(var i=0,a=0;a<e.length;a++){var r=n(e[a]);if(!r)return 0;r>i&&(i=r)}return i}return n(e)}r.prototype.onParse=function(t,e){if("style"===t.name&&t.children.length&&"text"===t.children[0].type)this.styles=this.styles.concat((new a.default).parse(t.children[0].text));else if(t.name){for(var n=["","","",""],i=0,r=this.styles.length;i<r;i++){var o=this.styles[i],l=s(t,o.key||o.list[o.list.length-1]),c=void 0;if(l){if(!o.key){c=o.list.length-2;for(var u=e.stack.length;c>=0&&u--;)if(">"===o.list[c]){if(c<1||c>o.list.length-2)break;s(e.stack[u],o.list[c-1])?c-=2:c++}else s(e.stack[u],o.list[c])&&c--;l=4}if(o.key||c<0)if(o.pseudo&&t.children){var d=void 0;o.style=o.style.replace(/content:([^;]+)/,(function(e,n){return d=n.replace(/['"]/g,"").replace(/attr\((.+?)\)/,(function(e,n){return t.attrs[n.trim()]||""})).replace(/\\(\w{4})/,(function(t,e){return String.fromCharCode(parseInt(e,16))})),""}));var h={name:"span",attrs:{style:o.style},children:[{type:"text",text:d}]};"before"===o.pseudo?t.children.unshift(h):t.children.push(h)}else n[l-1]+=o.style+(";"===o.style[o.style.length-1]?"":";")}}n=n.join(""),n.length>2&&(t.attrs.style=n+(t.attrs.style||""))}};var o=r;e.default=o},ab3f:function(t,e,n){"use strict";var i=n("ee98").end,a=n("8b27");t.exports=a("trimEnd")?function(){return i(this)}:"".trimEnd},ace7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={},a={get:function(t){return i[t]},set:function(t,e){i[t]=e},remove:function(t){i[t]=void 0}};e.default=a},af3b:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,"[data-v-96e6b98c] .hl-code,[data-v-96e6b98c] .hl-pre,[data-v-96e6b98c] .ql-syntax{color:#ccc;background:0 0;font-family:Consolas,Monaco,Andale Mono,Ubuntu Mono,monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}[data-v-96e6b98c] .hl-pre,[data-v-96e6b98c] .ql-syntax{padding:1em;margin:.5em 0;overflow:auto}[data-v-96e6b98c] .hl-pre,[data-v-96e6b98c] .ql-syntax{background:#2d2d2d}[data-v-96e6b98c] .hl-block-comment,[data-v-96e6b98c] .hl-cdata,[data-v-96e6b98c] .hl-comment,[data-v-96e6b98c] .hl-doctype,[data-v-96e6b98c] .hl-prolog{color:#999}[data-v-96e6b98c] .hl-punctuation{color:#ccc}[data-v-96e6b98c] .hl-attr-name,[data-v-96e6b98c] .hl-deleted,[data-v-96e6b98c] .hl-namespace,[data-v-96e6b98c] .hl-tag{color:#e2777a}[data-v-96e6b98c] .hl-function-name{color:#6196cc}[data-v-96e6b98c] .hl-boolean,[data-v-96e6b98c] .hl-function,[data-v-96e6b98c] .hl-number{color:#f08d49}[data-v-96e6b98c] .hl-class-name,[data-v-96e6b98c] .hl-constant,[data-v-96e6b98c] .hl-property,[data-v-96e6b98c] .hl-symbol{color:#f8c555}[data-v-96e6b98c] .hl-atrule,[data-v-96e6b98c] .hl-builtin,[data-v-96e6b98c] .hl-important,[data-v-96e6b98c] .hl-keyword,[data-v-96e6b98c] .hl-selector{color:#cc99cd}[data-v-96e6b98c] .hl-attr-value,[data-v-96e6b98c] .hl-char,[data-v-96e6b98c] .hl-regex,[data-v-96e6b98c] .hl-string,[data-v-96e6b98c] .hl-variable{color:#7ec699}[data-v-96e6b98c] .hl-entity,[data-v-96e6b98c] .hl-operator,[data-v-96e6b98c] .hl-url{color:#67cdcc}[data-v-96e6b98c] .hl-bold,[data-v-96e6b98c] .hl-important{font-weight:700}[data-v-96e6b98c] .hl-italic{font-style:italic}[data-v-96e6b98c] .hl-entity{cursor:help}[data-v-96e6b98c] .hl-inserted{color:green}[data-v-96e6b98c] .md-p{-webkit-margin-before:1em;margin-block-start:1em;-webkit-margin-after:1em;margin-block-end:1em}[data-v-96e6b98c] .md-table,[data-v-96e6b98c] .md-blockquote{margin-bottom:16px}[data-v-96e6b98c] .md-table{box-sizing:border-box;width:100%;overflow:auto;border-spacing:0;border-collapse:collapse}[data-v-96e6b98c] .md-tr{background-color:#fff;border-top:1px solid #c6cbd1}.md-table .md-tr[data-v-96e6b98c]:nth-child(2n){background-color:#f6f8fa}[data-v-96e6b98c] .md-th,[data-v-96e6b98c] .md-td{padding:6px 13px!important;border:1px solid #dfe2e5}[data-v-96e6b98c] .md-th{font-weight:600}[data-v-96e6b98c] .md-blockquote{padding:0 1em;color:#6a737d;border-left:.25em solid #dfe2e5}[data-v-96e6b98c] .md-code{padding:.2em .4em;font-family:SFMono-Regular,Consolas,Liberation Mono,Menlo,monospace;font-size:85%;background-color:rgba(27,31,35,.05);border-radius:3px}[data-v-96e6b98c] .md-pre .md-code{padding:0;font-size:100%;background:transparent;border:0}\n[data-v-96e6b98c] ._video{width:300px;height:225px;display:inline-block;background-color:#000}/* a 标签默认效果 */._a[data-v-96e6b98c]{padding:1.5px 0 1.5px 0;color:#3cc9a4;word-break:break-all;text-decoration:underline}/* a 标签点击态效果 */._hover[data-v-96e6b98c]{opacity:.7}p[data-v-96e6b98c]{word-wrap:break-word}/* 图片默认效果 */._img[data-v-96e6b98c]{width:100%;-webkit-touch-callout:none;border-radius:5px;margin-top:10px}/* 内部样式 */._block[data-v-96e6b98c]{display:block}._b[data-v-96e6b98c],\n._strong[data-v-96e6b98c]{font-weight:700}._code[data-v-96e6b98c]{font-family:monospace}._del[data-v-96e6b98c]{text-decoration:line-through}._em[data-v-96e6b98c],\n._i[data-v-96e6b98c]{font-style:italic}._h1[data-v-96e6b98c]{font-size:2em}._h2[data-v-96e6b98c]{font-size:1.5em}._h3[data-v-96e6b98c]{font-size:1.17em}._h5[data-v-96e6b98c]{font-size:.83em}._h6[data-v-96e6b98c]{font-size:.67em}._h1[data-v-96e6b98c],\n._h2[data-v-96e6b98c],\n._h3[data-v-96e6b98c],\n._h4[data-v-96e6b98c],\n._h5[data-v-96e6b98c],\n._h6[data-v-96e6b98c]{display:block;font-weight:700}._image[data-v-96e6b98c]{height:1px}h1[data-v-96e6b98c],h2[data-v-96e6b98c],h3[data-v-96e6b98c],h4[data-v-96e6b98c],h5[data-v-96e6b98c],h6[data-v-96e6b98c]{margin:10px 0}p[data-v-96e6b98c]{margin:5px 0!important}.tImg[data-v-96e6b98c]{width:25px;height:25px}._ins[data-v-96e6b98c]{text-decoration:underline}._li[data-v-96e6b98c]{display:list-item}._ol[data-v-96e6b98c]{list-style-type:decimal}._ol[data-v-96e6b98c],\n._ul[data-v-96e6b98c]{display:block;padding-left:40px;margin:1em 0}._q[data-v-96e6b98c]::before{content:'\"'}._q[data-v-96e6b98c]::after{content:'\"'}._sub[data-v-96e6b98c]{font-size:smaller;vertical-align:sub}._sup[data-v-96e6b98c]{font-size:smaller;vertical-align:super}._thead[data-v-96e6b98c],\n._tbody[data-v-96e6b98c],\n._tfoot[data-v-96e6b98c]{display:table-row-group}._tr[data-v-96e6b98c]{display:table-row}._td[data-v-96e6b98c],\n._th[data-v-96e6b98c]{display:table-cell;vertical-align:middle}._th[data-v-96e6b98c]{font-weight:700;text-align:center}._ul[data-v-96e6b98c]{list-style-type:disc}._ul ._ul[data-v-96e6b98c]{margin:0;list-style-type:circle}._ul ._ul ._ul[data-v-96e6b98c]{list-style-type:square}._abbr[data-v-96e6b98c],\n._b[data-v-96e6b98c],\n._code[data-v-96e6b98c],\n._del[data-v-96e6b98c],\n._em[data-v-96e6b98c],\n._i[data-v-96e6b98c],\n._ins[data-v-96e6b98c],\n._label[data-v-96e6b98c],\n._q[data-v-96e6b98c],\n._span[data-v-96e6b98c],\n._strong[data-v-96e6b98c],\n._sub[data-v-96e6b98c],\n._sup[data-v-96e6b98c]{display:inline}",""]),t.exports=e},b4d7:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("fd3c"),n("5c47"),n("a1c1"),n("f7a5"),n("bf0f"),n("6a54"),n("2797"),n("0506"),n("2c10"),n("7a76"),n("c9b5"),n("d4b5"),n("ab80"),n("23f4"),n("7d2f"),n("9c4e"),n("aa9c");
/*! PrismJS 1.22.0
https://prismjs.com/download.html#themes=prism-tomorrow&languages=markup+css+clike+javascript */
var i="undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{},a=function(t){var e=/\blang(?:uage)?-([\w-]+)\b/i,n=0,i={manual:t.Prism&&t.Prism.manual,disableWorkerMessageHandler:t.Prism&&t.Prism.disableWorkerMessageHandler,util:{encode:function t(e){return e instanceof a?new a(e.type,t(e.content),e.alias):Array.isArray(e)?e.map(t):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(t){return Object.prototype.toString.call(t).slice(8,-1)},objId:function(t){return t.__id||Object.defineProperty(t,"__id",{value:++n}),t.__id},clone:function t(e,n){var a,r;switch(n=n||{},i.util.type(e)){case"Object":if(r=i.util.objId(e),n[r])return n[r];for(var s in a={},n[r]=a,e)e.hasOwnProperty(s)&&(a[s]=t(e[s],n));return a;case"Array":return r=i.util.objId(e),n[r]?n[r]:(a=[],n[r]=a,e.forEach((function(e,i){a[i]=t(e,n)})),a);default:return e}},getLanguage:function(t){for(;t&&!e.test(t.className);)t=t.parentElement;return t?(t.className.match(e)||[,"none"])[1].toLowerCase():"none"},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(l){var t=(/at [^(\r\n]*\((.*):.+:.+\)$/i.exec(l.stack)||[])[1];if(t){var e=document.getElementsByTagName("script");for(var n in e)if(e[n].src==t)return e[n]}return null}},isActive:function(t,e,n){for(var i="no-"+e;t;){var a=t.classList;if(a.contains(e))return!0;if(a.contains(i))return!1;t=t.parentElement}return!!n}},languages:{extend:function(t,e){var n=i.util.clone(i.languages[t]);for(var a in e)n[a]=e[a];return n},insertBefore:function(t,e,n,a){var r=(a=a||i.languages)[t],s={};for(var o in r)if(r.hasOwnProperty(o)){if(o==e)for(var l in n)n.hasOwnProperty(l)&&(s[l]=n[l]);n.hasOwnProperty(o)||(s[o]=r[o])}var c=a[t];return a[t]=s,i.languages.DFS(i.languages,(function(e,n){n===c&&e!=t&&(this[e]=s)})),s},DFS:function t(e,n,a,r){r=r||{};var s=i.util.objId;for(var o in e)if(e.hasOwnProperty(o)){n.call(e,o,e[o],a||o);var l=e[o],c=i.util.type(l);"Object"!==c||r[s(l)]?"Array"!==c||r[s(l)]||(r[s(l)]=!0,t(l,n,o,r)):(r[s(l)]=!0,t(l,n,null,r))}}},plugins:{},highlightAll:function(t,e){i.highlightAllUnder(document,t,e)},highlightAllUnder:function(t,e,n){var a={callback:n,container:t,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};i.hooks.run("before-highlightall",a),a.elements=Array.prototype.slice.apply(a.container.querySelectorAll(a.selector)),i.hooks.run("before-all-elements-highlight",a);for(var r,s=0;r=a.elements[s++];)i.highlightElement(r,!0===e,a.callback)},highlightElement:function(n,a,r){var s=i.util.getLanguage(n),o=i.languages[s];n.className=n.className.replace(e,"").replace(/\s+/g," ")+" language-"+s;var l=n.parentElement;l&&"pre"===l.nodeName.toLowerCase()&&(l.className=l.className.replace(e,"").replace(/\s+/g," ")+" language-"+s);var c={element:n,language:s,grammar:o,code:n.textContent};function u(t){c.highlightedCode=t,i.hooks.run("before-insert",c),c.element.innerHTML=c.highlightedCode,i.hooks.run("after-highlight",c),i.hooks.run("complete",c),r&&r.call(c.element)}if(i.hooks.run("before-sanity-check",c),!c.code)return i.hooks.run("complete",c),void(r&&r.call(c.element));if(i.hooks.run("before-highlight",c),c.grammar)if(a&&t.Worker){var d=new Worker(i.filename);d.onmessage=function(t){u(t.data)},d.postMessage(JSON.stringify({language:c.language,code:c.code,immediateClose:!0}))}else u(i.highlight(c.code,c.grammar,c.language));else u(i.util.encode(c.code))},highlight:function(t,e,n){var r={code:t,grammar:e,language:n};return i.hooks.run("before-tokenize",r),r.tokens=i.tokenize(r.code,r.grammar),i.hooks.run("after-tokenize",r),a.stringify(i.util.encode(r.tokens),r.language)},tokenize:function(t,e){var n=e.rest;if(n){for(var l in n)e[l]=n[l];delete e.rest}var c=new r;return s(c,c.head,t),function t(e,n,r,l,c,u){for(var d in r)if(r.hasOwnProperty(d)&&r[d]){var h=r[d];h=Array.isArray(h)?h:[h];for(var p=0;p<h.length;++p){if(u&&u.cause==d+","+p)return;var f=h[p],g=f.inside,m=!!f.lookbehind,v=!!f.greedy,b=0,y=f.alias;if(v&&!f.pattern.global){var x=f.pattern.toString().match(/[imsuy]*$/)[0];f.pattern=RegExp(f.pattern.source,x+"g")}for(var k=f.pattern||f,_=l.next,w=c;_!==n.tail&&!(u&&w>=u.reach);w+=_.value.length,_=_.next){var S=_.value;if(n.length>e.length)return;if(!(S instanceof a)){var $=1;if(v&&_!=n.tail.prev){k.lastIndex=w;var T=k.exec(e);if(!T)break;var A=T.index+(m&&T[1]?T[1].length:0),z=T.index+T[0].length,O=w;for(O+=_.value.length;O<=A;)_=_.next,O+=_.value.length;if(O-=_.value.length,w=O,_.value instanceof a)continue;for(var I=_;I!==n.tail&&(O<z||"string"==typeof I.value);I=I.next)$++,O+=I.value.length;$--,S=e.slice(w,O),T.index-=w}else{k.lastIndex=0;T=k.exec(S)}if(T){m&&(b=T[1]?T[1].length:0);A=T.index+b;var F=T[0].slice(b),C=(z=A+F.length,S.slice(0,A)),R=S.slice(z),E=w+S.length;u&&E>u.reach&&(u.reach=E);var j=_.prev;C&&(j=s(n,j,C),w+=C.length),o(n,j,$);var N=new a(d,g?i.tokenize(F,g):F,y,F);_=s(n,j,N),R&&s(n,_,R),1<$&&t(e,n,r,_.prev,w,{cause:d+","+p,reach:E})}}}}}}(t,c,e,c.head,0),function(t){for(var e=[],n=t.head.next;n!==t.tail;)e.push(n.value),n=n.next;return e}(c)},hooks:{all:{},add:function(t,e){var n=i.hooks.all;n[t]=n[t]||[],n[t].push(e)},run:function(t,e){var n=i.hooks.all[t];if(n&&n.length)for(var a,r=0;a=n[r++];)a(e)}},Token:a};function a(t,e,n,i){this.type=t,this.content=e,this.alias=n,this.length=0|(i||"").length}function r(){var t={value:null,prev:null,next:null},e={value:null,prev:t,next:null};t.next=e,this.head=t,this.tail=e,this.length=0}function s(t,e,n){var i=e.next,a={value:n,prev:e,next:i};return e.next=a,i.prev=a,t.length++,a}function o(t,e,n){for(var i=e.next,a=0;a<n&&i!==t.tail;a++)i=i.next;(e.next=i).prev=e,t.length-=a}if(t.Prism=i,a.stringify=function t(e,n){if("string"==typeof e)return e;if(Array.isArray(e)){var a="";return e.forEach((function(e){a+=t(e,n)})),a}var r={type:e.type,content:t(e.content,n),tag:"span",classes:["token",e.type],attributes:{},language:n},s=e.alias;s&&(Array.isArray(s)?Array.prototype.push.apply(r.classes,s):r.classes.push(s)),i.hooks.run("wrap",r);var o="";for(var l in r.attributes)o+=" "+l+'="'+(r.attributes[l]||"").replace(/"/g,"&quot;")+'"';return"<"+r.tag+' class="'+r.classes.join(" ")+'"'+o+">"+r.content+"</"+r.tag+">"},!t.document)return t.addEventListener&&(i.disableWorkerMessageHandler||t.addEventListener("message",(function(e){var n=JSON.parse(e.data),a=n.language,r=n.code,s=n.immediateClose;t.postMessage(i.highlight(r,i.languages[a],a)),s&&t.close()}),!1)),i;var l=i.util.currentScript();function c(){i.manual||i.highlightAll()}if(l&&(i.filename=l.src,l.hasAttribute("data-manual")&&(i.manual=!0)),!i.manual){var u=document.readyState;"loading"===u||"interactive"===u&&l&&l.defer?document.addEventListener("DOMContentLoaded",c):window.requestAnimationFrame?window.requestAnimationFrame(c):window.setTimeout(c,16)}return i}(i),r=a;e.default=r,"undefined"!=typeof t&&(t.Prism=a),a.languages.markup={comment:/<!--[\s\S]*?-->/,prolog:/<\?[\s\S]+?\?>/,doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/,name:/[^\s<>'"]+/}},cdata:/<!\[CDATA\[[\s\S]*?]]>/i,tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},a.languages.markup.tag.inside["attr-value"].inside.entity=a.languages.markup.entity,a.languages.markup.doctype.inside["internal-subset"].inside=a.languages.markup,a.hooks.add("wrap",(function(t){"entity"===t.type&&(t.attributes.title=t.content.replace(/&amp;/,"&"))})),Object.defineProperty(a.languages.markup.tag,"addInlined",{value:function(t,e){var n={};n["language-"+e]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:a.languages[e]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;var i={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};i["language-"+e]={pattern:/[\s\S]+/,inside:a.languages[e]};var r={};r[t]={pattern:RegExp("(<__[^]*?>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[^])*?(?=</__>)".replace(/__/g,(function(){return t})),"i"),lookbehind:!0,greedy:!0,inside:i},a.languages.insertBefore("markup","cdata",r)}}),a.languages.html=a.languages.markup,a.languages.mathml=a.languages.markup,a.languages.svg=a.languages.markup,a.languages.xml=a.languages.extend("markup",{}),a.languages.ssml=a.languages.xml,a.languages.atom=a.languages.xml,a.languages.rss=a.languages.xml,function(t){var e=/("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/;t.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-]+[\s\S]*?(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\((?!\s*\))\s*)(?:[^()]|\((?:[^()]|\([^()]*\))*\))+?(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+e.source+"|(?:[^\\\\\r\n()\"']|\\\\[^])*)\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+e.source+"$"),alias:"url"}}},selector:RegExp("[^{}\\s](?:[^{};\"']|"+e.source+")*?(?=\\s*\\{)"),string:{pattern:e,greedy:!0},property:/[-_a-z\xA0-\uFFFF][-\w\xA0-\uFFFF]*(?=\s*:)/i,important:/!important\b/i,function:/[-a-z0-9]+(?=\()/i,punctuation:/[(){};:,]/},t.languages.css.atrule.inside.rest=t.languages.css;var n=t.languages.markup;n&&(n.tag.addInlined("style","css"),t.languages.insertBefore("inside","attr-value",{"style-attr":{pattern:/(^|["'\s])style\s*=\s*(?:"[^"]*"|'[^']*')/i,lookbehind:!0,inside:{"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{style:{pattern:/(["'])[\s\S]+(?=["']$)/,lookbehind:!0,alias:"language-css",inside:t.languages.css},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},"attr-name":/^style/i}}},n.tag))}(a),a.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|interface|extends|implements|trait|instanceof|new)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},a.languages.javascript=a.languages.extend("clike",{"class-name":[a.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|(?:get|set)(?=\s*[\[$\w\xA0-\uFFFF])|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/#?[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),a.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,a.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)\/(?:\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/,lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:a.languages.regex},"regex-flags":/[a-z]+$/,"regex-delimiter":/^\/|\/$/}},"function-variable":{pattern:/#?[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:a.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:a.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:a.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:a.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),a.languages.insertBefore("javascript","string",{"template-string":{pattern:/`(?:\\[\s\S]|\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}|(?!\${)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\${|}$/,alias:"punctuation"},rest:a.languages.javascript}},string:/[\s\S]+/}}}),a.languages.markup&&a.languages.markup.tag.addInlined("script","javascript"),a.languages.js=a.languages.javascript}).call(this,n("0ee4"))},b539:function(t,e,n){"use strict";var i=n("9561"),a=n.n(i);a.a},b817:function(t,e,n){"use strict";n.r(e);var i=n("90ba"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},beba:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={copyByLongPress:!0,showLanguageName:!1,showLineNumber:!1}},c605:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5c47"),n("a1c1"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80");var i=/\[(\S+?)\]/g,a={"笑脸":"😄","生病":"😷","破涕为笑":"😂","吐舌":"😝","脸红":"😳","恐惧":"😱","失望":"😔","无语":"😒","眨眼":"😉","酷":"😎","哭":"😭","痴迷":"😍","吻":"😘","思考":"🤔","困惑":"😕","颠倒":"🙃","钱":"🤑","惊讶":"😲","白眼":"🙄","叹气":"😤","睡觉":"😴","书呆子":"🤓","愤怒":"😡","面无表情":"😑","张嘴":"😮","量体温":"🤒","呕吐":"🤮","光环":"😇","幽灵":"👻","外星人":"👽","机器人":"🤖","捂眼镜":"🙈","捂耳朵":"🙉","捂嘴":"🙊","婴儿":"👶","男孩":"👦","女孩":"👧","男人":"👨","女人":"👩","老人":"👴","老妇人":"👵","警察":"👮","王子":"🤴","公主":"🤴","举手":"🙋","跑步":"🏃","家庭":"👪","眼睛":"👀","鼻子":"👃","耳朵":"👂","舌头":"👅","嘴":"👄","心":"❤️","心碎":"💔","雪人":"☃️","情书":"💌","大便":"💩","闹钟":"⏰","眼镜":"👓","雨伞":"☂️","音乐":"🎵","话筒":"🎤","游戏机":"🎮","喇叭":"📢","耳机":"🎧","礼物":"🎁","电话":"📞","电脑":"💻","打印机":"🖨️","手电筒":"🔦","灯泡":"💡","书本":"📖","信封":"✉️","药丸":"💊","口红":"💄","手机":"📱","相机":"📷","电视":"📺","中":"🀄","垃圾桶":"🚮","厕所":"🚾","感叹号":"❗","禁":"🈲","可":"🉑","彩虹":"🌈","旋风":"🌀","雷电":"⚡","雪花":"❄️","星星":"⭐","水滴":"💧","玫瑰":"🌹","加油":"💪","左":"👈","右":"👉","上":"👆","下":"👇","手掌":"🖐️","好的":"👌","好":"👍","差":"👎","胜利":"✌","拳头":"👊","挥手":"👋","鼓掌":"👏","猴子":"🐒","狗":"🐶","狼":"🐺","猫":"🐱","老虎":"🐯","马":"🐎","独角兽":"🦄","斑马":"🦓","鹿":"🦌","牛":"🐮","猪":"🐷","羊":"🐏","长颈鹿":"🦒","大象":"🐘","老鼠":"🐭","蝙蝠":"🦇","刺猬":"🦔","熊猫":"🐼","鸽子":"🕊️","鸭子":"🦆","兔子":"🐇","老鹰":"🦅","青蛙":"🐸","蛇":"🐍","龙":"🐉","鲸鱼":"🐳","海豚":"🐬","足球":"⚽","棒球":"⚾","篮球":"🏀","排球":"🏐","橄榄球":"🏉","网球":"🎾","骰子":"🎲","鸡腿":"🍗","蛋糕":"🎂","啤酒":"🍺","饺子":"🥟","汉堡":"🍔","薯条":"🍟","意大利面":"🍝","干杯":"🥂","筷子":"🥢","糖果":"🍬","奶瓶":"🍼","爆米花":"🍿","邮局":"🏤","医院":"🏥","银行":"🏦","酒店":"🏨","学校":"🏫","城堡":"🏰","火车":"🚂","高铁":"🚄","地铁":"🚇","公交":"🚌","救护车":"🚑","消防车":"🚒","警车":"🚓","出租车":"🚕","汽车":"🚗","货车":"🚛","自行车":"🚲","摩托":"🛵","红绿灯":"🚥","帆船":"⛵","游轮":"🛳️","轮船":"⛴️","飞机":"✈️","直升机":"🚁","缆车":"🚠","警告":"⚠️","禁止":"⛔"};function r(){}r.prototype.onUpdate=function(t){return t.replace(i,(function(t,e){return a[e]?a[e]:t}))},r.prototype.onGetContent=function(t){for(var e in a)t=t.replace(new RegExp(a[e],"g"),"["+e+"]");return t};var s=r;e.default=s},cb97:function(t,e,n){"use strict";n.r(e);var i=n("e228"),a=n("d3f7");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("b539");var s=n("828b"),o=n("628f"),l=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"96e6b98c",null,!1,i["a"],void 0);"function"===typeof o["a"]&&Object(o["a"])(l),e["default"]=l.exports},ced8:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5ef2"),n("e966"),n("4626"),n("5ac7"),n("dd2b"),n("f7a5"),n("c223"),n("e838"),n("0c26"),n("20f3"),n("5c47"),n("a1c1"),n("aa9c"),n("c9b5"),n("bf0f"),n("ab80");var i={trustTags:c("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,ruby,rt,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),blockTags:c("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:c("area,base,canvas,embed,frame,head,iframe,input,link,map,meta,param,rp,script,source,style,textarea,title,track,wbr"),voidTags:c("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),entities:{lt:"<",gt:">",quot:'"',apos:"'",ensp:" ",emsp:" ",nbsp:" ",semi:";",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…",larr:"←",uarr:"↑",rarr:"→",darr:"↓"},tagStyle:{address:"font-style:italic",big:"display:inline;font-size:1.2em",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",strike:"text-decoration:line-through",u:"text-decoration:underline"},svgDict:{animatetransform:"animateTransform",lineargradient:"linearGradient",viewbox:"viewBox",attributename:"attributeName",repeatcount:"repeatCount",repeatdur:"repeatDur"}},a={},r=uni.getSystemInfoSync(),s=r.windowWidth,o=c(" ,\r,\n,\t,\f"),l=0;function c(t){for(var e=Object.create(null),n=t.split(","),i=n.length;i--;)e[n[i]]=!0;return e}function u(t,e){var n=t.indexOf("&");while(-1!==n){var a=t.indexOf(";",n+3),r=void 0;if(-1===a)break;"#"===t[n+1]?(r=parseInt(("x"===t[n+2]?"0":"")+t.substring(n+2,a)),isNaN(r)||(t=t.substr(0,n)+String.fromCharCode(r)+t.substr(a+1))):(r=t.substring(n+1,a),(i.entities[r]||"amp"===r&&e)&&(t=t.substr(0,n)+(i.entities[r]||"&")+t.substr(a+1))),n=t.indexOf("&",n+1)}return t}function d(t){for(var e=t.length-1,n=e;n>=-1;n--)(-1===n||t[n].c||!t[n].name||"div"!==t[n].name&&"p"!==t[n].name&&"h"!==t[n].name[0]||(t[n].attrs.style||"").includes("inline"))&&(e-n>=5&&t.splice(n+1,e-n,{name:"div",attrs:{},children:t.slice(n+1,e+1)}),e=n-1)}function h(t){this.options=t||{},this.tagStyle=Object.assign({},i.tagStyle,this.options.tagStyle),this.imgList=t.imgList||[],this.imgList._unloadimgs=0,this.plugins=t.plugins||[],this.attrs=Object.create(null),this.stack=[],this.nodes=[],this.pre=(this.options.containerStyle||"").includes("white-space")&&this.options.containerStyle.includes("pre")?2:0}function p(t){this.handler=t}i.ignoreTags.iframe=void 0,i.trustTags.iframe=!0,i.ignoreTags.embed=void 0,i.trustTags.embed=!0,h.prototype.parse=function(t){for(var e=this.plugins.length;e--;)this.plugins[e].onUpdate&&(t=this.plugins[e].onUpdate(t,i)||t);new p(this).parse(t);while(this.stack.length)this.popNode();return this.nodes.length>50&&d(this.nodes),this.nodes},h.prototype.expose=function(){for(var t=this.stack.length;t--;){var e=this.stack[t];if(e.c||"a"===e.name||"video"===e.name||"audio"===e.name)return;e.c=1}},h.prototype.hook=function(t){for(var e=this.plugins.length;e--;)if(this.plugins[e].onParse&&!1===this.plugins[e].onParse(t,this))return!1;return!0},h.prototype.getUrl=function(t){var e=this.options.domain;return"/"===t[0]?"/"===t[1]?t=(e?e.split("://")[0]:"http")+":"+t:e&&(t=e+t):t.includes("data:")||t.includes("://")||e&&(t=e+"/"+t),t},h.prototype.parseStyle=function(t){var e=t.attrs,n=(this.tagStyle[t.name]||"").split(";").concat((e.style||"").split(";")),i={},a="";e.id&&!this.xml&&(this.options.useAnchor?this.expose():"img"!==t.name&&"a"!==t.name&&"video"!==t.name&&"audio"!==t.name&&(e.id=void 0)),e.width&&(i.width=parseFloat(e.width)+(e.width.includes("%")?"%":"px"),e.width=void 0),e.height&&(i.height=parseFloat(e.height)+(e.height.includes("%")?"%":"px"),e.height=void 0);for(var r=0,l=n.length;r<l;r++){var c=n[r].split(":");if(!(c.length<2)){var u=c.shift().trim().toLowerCase(),d=c.join(":").trim();if("-"===d[0]&&d.lastIndexOf("-")>0||d.includes("safe"))a+=";".concat(u,":").concat(d);else if(!i[u]||d.includes("import")||!i[u].includes("import")){if(d.includes("url")){var h=d.indexOf("(")+1;if(h){while('"'===d[h]||"'"===d[h]||o[d[h]])h++;d=d.substr(0,h)+this.getUrl(d.substr(h))}}else d.includes("rpx")&&(d=d.replace(/[0-9.]+\s*rpx/g,(function(t){return parseFloat(t)*s/750+"px"})));i[u]=d}}}return t.attrs.style=a,i},h.prototype.onTagName=function(t){this.tagName=this.xml?t:t.toLowerCase(),"svg"===this.tagName&&(this.xml=(this.xml||0)+1)},h.prototype.onAttrName=function(t){t=this.xml?t:t.toLowerCase(),"data-"===t.substr(0,5)?"data-src"!==t||this.attrs.src?"img"===this.tagName||"a"===this.tagName?this.attrName=t:this.attrName=void 0:this.attrName="src":(this.attrName=t,this.attrs[t]="T")},h.prototype.onAttrVal=function(t){var e=this.attrName||"";"style"===e||"href"===e?this.attrs[e]=u(t,!0):e.includes("src")?this.attrs[e]=this.getUrl(u(t,!0)):e&&(this.attrs[e]=t)},h.prototype.onOpenTag=function(t){var e=Object.create(null);e.name=this.tagName,e.attrs=this.attrs,this.options.nodes.length&&(e.type="node"),this.attrs=Object.create(null);var n=e.attrs,r=this.stack[this.stack.length-1],o=r?r.children:this.nodes,c=this.xml?t:i.voidTags[e.name];if(a[e.name]&&(n.class=a[e.name]+(n.class?" "+n.class:"")),"embed"===e.name&&this.expose(),"video"!==e.name&&"audio"!==e.name||("video"!==e.name||n.id||(n.id="v"+l++),n.controls||n.autoplay||(n.controls="T"),e.src=[],n.src&&(e.src.push(n.src),n.src=void 0),this.expose()),c){if(!this.hook(e)||i.ignoreTags[e.name])return void("base"!==e.name||this.options.domain?"source"===e.name&&r&&("video"===r.name||"audio"===r.name)&&n.src&&r.src.push(n.src):this.options.domain=n.href);var u=this.parseStyle(e);if("img"===e.name){if(n.src&&(n.src.includes("webp")&&(e.webp="T"),n.src.includes("data:")&&!n["original-src"]&&(n.ignore="T"),!n.ignore||e.webp||n.src.includes("cloud://"))){for(var d=this.stack.length;d--;){var h=this.stack[d];"a"===h.name&&(e.a=h.attrs),"table"!==h.name||e.webp||n.src.includes("cloud://")||(!u.display||u.display.includes("inline")?e.t="inline-block":e.t=u.display,u.display=void 0),h.c=1}n.i=this.imgList.length.toString();var p=n["original-src"]||n.src;this.imgList.push(p),e.t||(this.imgList._unloadimgs+=1),this.options.lazyLoad&&(n["data-src"]=n.src,n.src=void 0)}"inline"===u.display&&(u.display=""),n.ignore&&(u["max-width"]=u["max-width"]||"100%",n.style+=";-webkit-touch-callout:none"),parseInt(u.width)>s&&(u.height=void 0),isNaN(parseInt(u.width))||(e.w="T"),!isNaN(parseInt(u.height))&&(!u.height.includes("%")||r&&(r.attrs.style||"").includes("height"))&&(e.h="T")}else if("svg"===e.name)return o.push(e),this.stack.push(e),void this.popNode();for(var f in u)u[f]&&(n.style+=";".concat(f,":").concat(u[f].replace(" !important","")));n.style=n.style.substr(1)||void 0}else("pre"===e.name||(n.style||"").includes("white-space")&&n.style.includes("pre"))&&2!==this.pre&&(this.pre=e.pre=1),e.children=[],this.stack.push(e);o.push(e)},h.prototype.onCloseTag=function(t){var e;for(t=this.xml?t:t.toLowerCase(),e=this.stack.length;e--;)if(this.stack[e].name===t)break;if(-1!==e)while(this.stack.length>e)this.popNode();else if("p"===t||"br"===t){var n=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;n.push({name:t,attrs:{class:a[t]||"",style:this.tagStyle[t]||""}})}},h.prototype.popNode=function(){var t=this.options.editable,e=this.stack.pop(),n=e.attrs,a=e.children,r=this.stack[this.stack.length-1],o=r?r.children:this.nodes;if(!this.hook(e)||i.ignoreTags[e.name])return"title"===e.name&&a.length&&"text"===a[0].type&&this.options.setTitle&&uni.setNavigationBarTitle({title:a[0].text}),void o.pop();if(e.pre&&2!==this.pre){this.pre=e.pre=void 0;for(var l=this.stack.length;l--;)this.stack[l].pre&&(this.pre=1)}var c={};if("svg"===e.name){if(this.xml>1)return void this.xml--;var u="",h=n.style;return n.style="",n.xmlns="http://www.w3.org/2000/svg",function t(e){if("text"!==e.type){var n=i.svgDict[e.name]||e.name;for(var a in u+="<"+n,e.attrs){var r=e.attrs[a];r&&(u+=" ".concat(i.svgDict[a]||a,'="').concat(r,'"'))}if(e.children){u+=">";for(var s=0;s<e.children.length;s++)t(e.children[s]);u+="</"+n+">"}else u+="/>"}else u+=e.text}(e),e.name="img",e.attrs={src:"data:image/svg+xml;utf8,"+u.replace(/#/g,"%23"),style:h,ignore:"T"},e.children=void 0,void(this.xml=!1)}if(n.align&&("table"===e.name?"center"===n.align?c["margin-inline-start"]=c["margin-inline-end"]="auto":c.float=n.align:c["text-align"]=n.align,n.align=void 0),n.dir&&(c.direction=n.dir,n.dir=void 0),"font"===e.name&&(n.color&&(c.color=n.color,n.color=void 0),n.face&&(c["font-family"]=n.face,n.face=void 0),n.size)){var p=parseInt(n.size);isNaN(p)||(p<1?p=1:p>7&&(p=7),c["font-size"]=["x-small","small","medium","large","x-large","xx-large","xxx-large"][p-1]),n.size=void 0}if((n.class||"").includes("align-center")&&(c["text-align"]="center"),Object.assign(c,this.parseStyle(e)),"table"!==e.name&&parseInt(c.width)>s&&(c["max-width"]="100%",c["box-sizing"]="border-box"),i.blockTags[e.name]?t||(e.name="div"):i.trustTags[e.name]||this.xml||(e.name="span"),"a"===e.name||"ad"===e.name||"iframe"===e.name)this.expose();else if("video"===e.name)(c.height||"").includes("auto")&&(c.height=void 0);else if("ul"!==e.name&&"ol"!==e.name||!e.c&&!t){if("table"===e.name){var f=parseFloat(n.cellpadding),g=parseFloat(n.cellspacing),m=parseFloat(n.border),v=c["border-color"],b=c["border-style"];if((e.c||t)&&(isNaN(f)&&(f=2),isNaN(g)&&(g=2)),m&&(n.style+=";border:".concat(m,"px ").concat(b||"solid"," ").concat(v||"gray")),e.flag&&(e.c||t)){c.display="grid",g?(c["grid-gap"]=g+"px",c.padding=g+"px"):m&&(n.style+=";border-left:0;border-top:0");var y=[],x=[],k=[],_={};(function t(e){for(var n=0;n<e.length;n++)"tr"===e[n].name?x.push(e[n]):t(e[n].children||[])})(a);for(var w=1;w<=x.length;w++){for(var S=1,$=0;$<x[w-1].children.length;$++){var T=x[w-1].children[$];if("td"===T.name||"th"===T.name){while(_[w+"."+S])S++;t&&(T.r=w);var A=T.attrs.style||"",z=A.indexOf("width")?A.indexOf(";width"):0;if(-1!==z){var O=A.indexOf(";",z+6);-1===O&&(O=A.length),T.attrs.colspan||(y[S]=A.substring(z?z+7:6,O)),A=A.substr(0,z)+A.substr(O)}if(A+=";display:flex",z=A.indexOf("vertical-align"),-1!==z){var I=A.substr(z+15,10);I.includes("middle")?A+=";align-items:center":I.includes("bottom")&&(A+=";align-items:flex-end")}else A+=";align-items:center";if(z=A.indexOf("text-align"),-1!==z){var F=A.substr(z+11,10);F.includes("center")?A+=";justify-content: center":F.includes("right")&&(A+=";justify-content: right")}if(A=(m?";border:".concat(m,"px ").concat(b||"solid"," ").concat(v||"gray")+(g?"":";border-right:0;border-bottom:0"):"")+(f?";padding:".concat(f,"px"):"")+";"+A,T.attrs.colspan&&(A+=";grid-column-start:".concat(S,";grid-column-end:").concat(S+parseInt(T.attrs.colspan)),T.attrs.rowspan||(A+=";grid-row-start:".concat(w,";grid-row-end:").concat(w+1)),S+=parseInt(T.attrs.colspan)-1),T.attrs.rowspan){A+=";grid-row-start:".concat(w,";grid-row-end:").concat(w+parseInt(T.attrs.rowspan)),T.attrs.colspan||(A+=";grid-column-start:".concat(S,";grid-column-end:").concat(S+1));for(var C=1;C<T.attrs.rowspan;C++)for(var R=0;R<(T.attrs.colspan||1);R++)_[w+C+"."+(S-R)]=1}A&&(T.attrs.style=A),k.push(T),S++}}if(1===w){for(var E="",j=1;j<S;j++)E+=(y[j]?y[j]:"auto")+" ";c["grid-template-columns"]=E}}e.children=k}else(e.c||t)&&(c.display="table"),isNaN(g)||(c["border-spacing"]=g+"px"),(m||f)&&function t(e){for(var n=0;n<e.length;n++){var i=e[n];"th"===i.name||"td"===i.name?(m&&(i.attrs.style="border:".concat(m,"px ").concat(b||"solid"," ").concat(v||"gray",";").concat(i.attrs.style||"")),f&&(i.attrs.style="padding:".concat(f,"px;").concat(i.attrs.style||""))):i.children&&t(i.children)}}(a);if(this.options.scrollTable&&!(n.style||"").includes("inline")){var N=Object.assign({},e);e.name="div",e.attrs={style:"overflow:auto"},e.children=[N],n=N.attrs}}else if("td"!==e.name&&"th"!==e.name||!n.colspan&&!n.rowspan)if("ruby"===e.name){e.name="span";for(var P=0;P<a.length-1;P++)"text"===a[P].type&&"rt"===a[P+1].name&&(a[P]={name:"div",attrs:{style:"display:inline-block;text-align:center"},children:[{name:"div",attrs:{style:"font-size:50%;"+(a[P+1].attrs.style||"")},children:a[P+1].children},a[P]]},a.splice(P+1,1))}else!t&&e.c&&function(t){t.c=2;for(var e=t.children.length;e--;){var n=t.children[e];n.c&&"table"!==n.name||(t.c=1)}}(e);else for(var L=this.stack.length;L--;)if("table"===this.stack[L].name){this.stack[L].flag=1;break}}else{var M={a:"lower-alpha",A:"upper-alpha",i:"lower-roman",I:"upper-roman"};M[n.type]&&(n.style+=";list-style-type:"+M[n.type],n.type=void 0);for(var q=a.length;q--;)"li"===a[q].name&&(a[q].c=1)}if((c.display||"").includes("flex")&&!e.c&&!t)for(var D=a.length;D--;){var U=a[D];U.f&&(U.attrs.style=(U.attrs.style||"")+U.f,U.f=void 0)}var Z=r&&((r.attrs.style||"").includes("flex")||(r.attrs.style||"").includes("grid"))&&!e.c;for(var B in Z&&(e.f=";max-width:100%"),a.length>=50&&(e.c||t)&&!(c.display||"").includes("flex")&&d(a),c)if(c[B]){var V=";".concat(B,":").concat(c[B].replace(" !important",""));Z&&(B.includes("flex")&&"flex-direction"!==B||"align-self"===B||B.includes("grid")||"-"===c[B][0]||B.includes("width")&&V.includes("%"))?(e.f+=V,"width"===B&&(n.style+=";width:100%")):n.style+=V}n.style=n.style.substr(1)||void 0},h.prototype.onText=function(t){if(!this.pre){for(var e,n="",i=0,a=t.length;i<a;i++)o[t[i]]?(" "!==n[n.length-1]&&(n+=" "),"\n"!==t[i]||e||(e=!0)):n+=t[i];if(" "===n&&e)return;t=n}var r=Object.create(null);if(r.type="text",r.text=u(t),this.hook(r)){var s=this.stack.length?this.stack[this.stack.length-1].children:this.nodes;s.push(r)}},p.prototype.parse=function(t){this.content=t||"",this.i=0,this.start=0,this.state=this.text;for(var e=this.content.length;-1!==this.i&&this.i<e;)this.state()},p.prototype.checkClose=function(t){var e="/"===this.content[this.i];return!!(">"===this.content[this.i]||e&&">"===this.content[this.i+1])&&(t&&this.handler[t](this.content.substring(this.start,this.i)),this.i+=e?2:1,this.start=this.i,this.handler.onOpenTag(e),"script"===this.handler.tagName?(this.i=this.content.indexOf("</",this.i),-1!==this.i&&(this.i+=2,this.start=this.i),this.state=this.endTag):this.state=this.text,!0)},p.prototype.text=function(){if(this.i=this.content.indexOf("<",this.i),-1!==this.i){var t=this.content[this.i+1];if(t>="a"&&t<="z"||t>="A"&&t<="Z")this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i)),this.start=++this.i,this.state=this.tagName;else if("/"===t||"!"===t||"?"===t){this.start!==this.i&&this.handler.onText(this.content.substring(this.start,this.i));var e=this.content[this.i+2];if("/"===t&&(e>="a"&&e<="z"||e>="A"&&e<="Z"))return this.i+=2,this.start=this.i,void(this.state=this.endTag);var n="--\x3e";"!"===t&&"-"===this.content[this.i+2]&&"-"===this.content[this.i+3]||(n=">"),this.i=this.content.indexOf(n,this.i),-1!==this.i&&(this.i+=n.length,this.start=this.i)}else this.i++}else this.start<this.content.length&&this.handler.onText(this.content.substring(this.start,this.content.length))},p.prototype.tagName=function(){if(o[this.content[this.i]]){this.handler.onTagName(this.content.substring(this.start,this.i));while(o[this.content[++this.i]]);this.i<this.content.length&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)}else this.checkClose("onTagName")||this.i++},p.prototype.attrName=function(){var t=this.content[this.i];if(o[t]||"="===t){this.handler.onAttrName(this.content.substring(this.start,this.i));var e="="===t,n=this.content.length;while(++this.i<n)if(t=this.content[this.i],!o[t]){if(this.checkClose())return;if(e)return this.start=this.i,void(this.state=this.attrVal);if("="!==this.content[this.i])return this.start=this.i,void(this.state=this.attrName);e=!0}}else this.checkClose("onAttrName")||this.i++},p.prototype.attrVal=function(){var t=this.content[this.i],e=this.content.length;if('"'===t||"'"===t){if(this.start=++this.i,this.i=this.content.indexOf(t,this.i),-1===this.i)return;this.handler.onAttrVal(this.content.substring(this.start,this.i))}else for(;this.i<e;this.i++){if(o[this.content[this.i]]){this.handler.onAttrVal(this.content.substring(this.start,this.i));break}if(this.checkClose("onAttrVal"))return}while(o[this.content[++this.i]]);this.i<e&&!this.checkClose()&&(this.start=this.i,this.state=this.attrName)},p.prototype.endTag=function(){var t=this.content[this.i];if(o[t]||">"===t||"/"===t){if(this.handler.onCloseTag(this.content.substring(this.start,this.i)),">"!==t&&(this.i=this.content.indexOf(">",this.i),-1===this.i))return;this.start=++this.i,this.state=this.text}else this.i++};var f=h;e.default=f},d3f7:function(t,e,n){"use strict";n.r(e);var i=n("1663"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},d9e8:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{class:(t.selectable?"_select ":"")+"_root",style:(t.editable?"min-height:200px;":"")+t.containerStyle,attrs:{id:"_root"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._containTap.apply(void 0,arguments)}}},[t.nodes[0]?n("node",{attrs:{childs:t.nodes,opts:[t.lazyLoad,t.loadingImg,t.errorImg,t.showImgMenu,t.selectable,t.editable,t.placeholder,"nodes"],name:"span"}}):t._t("default"),t.tooltip?n("v-uni-view",{staticClass:"_tooltip_contain",style:"top:"+t.tooltip.top+"px"},[n("v-uni-view",{staticClass:"_tooltip"},t._l(t.tooltip.items,(function(e,i){return n("v-uni-view",{key:i,staticClass:"_tooltip_item",attrs:{"data-i":i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._tooltipTap.apply(void 0,arguments)}}},[t._v(t._s(e))])})),1)],1):t._e(),t.slider?n("v-uni-view",{staticClass:"_slider",style:"top:"+t.slider.top+"px"},[n("v-uni-slider",{staticStyle:{padding:"3px"},attrs:{value:t.slider.value,min:t.slider.min,max:t.slider.max,"handle-size":"14","block-size":"14","show-value":!0,activeColor:"white"},on:{changing:function(e){arguments[0]=e=t.$handleEvent(e),t._sliderChanging.apply(void 0,arguments)},change:function(e){arguments[0]=e=t.$handleEvent(e),t._sliderChange.apply(void 0,arguments)}}})],1):t._e()],2)},a=[]},e228:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{class:"_block _"+t.name+" "+t.attrs.class,style:(t.ctrl.root?"border:1px solid black;padding:5px;display:block;":"")+t.attrs.style,attrs:{id:t.attrs.id},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.nodeTap.apply(void 0,arguments)}}},[t._l(t.childs,(function(e,i){return["img"===e.name&&!e.t&&(t.opts[1]&&!t.ctrl[i]||t.ctrl[i]<0)?n("v-uni-image",{key:i+"_0",staticClass:"_img",style:e.attrs.style,attrs:{src:t.ctrl[i]<0?t.opts[2]:t.opts[1],mode:"widthFix"}}):t._e(),"img"===e.name?n("img",{key:i+"_1",class:"_img "+e.attrs.class,style:(t.ctrl["e"+i]?"border:1px dashed black;padding:3px;":"")+(-1===t.ctrl[i]?"display:none;":"")+e.attrs.style,attrs:{id:e.attrs.id,src:e.attrs.src||(t.ctrl.load?e.attrs["data-src"]:""),"data-i":i},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.imgLoad.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaError.apply(void 0,arguments)},longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.imgLongTap.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.imgTap.apply(void 0,arguments)}}}):"text"!==e.type||t.ctrl["e"+i]?"text"===e.type&&1===t.ctrl["e"+i]?n("v-uni-text",{staticStyle:{border:"1px dashed black","min-width":"50px",width:"auto",padding:"5px",display:"block"},attrs:{"data-i":i},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.editStart.apply(void 0,arguments)}}},[t._v(t._s(e.text)),e.text?t._e():n("v-uni-text",{staticStyle:{color:"gray"}},[t._v(t._s(t.opts[6]||"请输入"))])],1):"text"===e.type?n("v-uni-textarea",{staticStyle:{border:"1px dashed black","min-width":"50px",width:"auto",padding:"5px"},attrs:{"auto-height":!0,maxlength:"-1",focus:3===t.ctrl["e"+i],value:e.text,"data-i":i},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.editInput.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.editEnd.apply(void 0,arguments)}}}):"br"===e.name?n("v-uni-text",[t._v("\\n")]):"a"===e.name?n("v-uni-view",{class:(e.attrs.href?"_a ":"")+e.attrs.class,style:"display:inline;"+e.attrs.style,attrs:{id:e.attrs.id,"hover-class":"_hover","data-i":i},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.linkTap.apply(void 0,arguments)}}},[n("node",{staticStyle:{display:"inherit"},attrs:{name:"span",childs:e.children,opts:[t.opts[0],t.opts[1],t.opts[2],t.opts[3],t.opts[4],t.opts[5],t.opts[6],t.opts[7]+"."+i+".children"]}})],1):"video"===e.name?n("v-uni-video",{class:e.attrs.class,style:e.attrs.style,attrs:{"show-center-play-btn":!t.opts[5],id:e.attrs.id,autoplay:e.attrs.autoplay,controls:e.attrs.controls,loop:e.attrs.loop,muted:e.attrs.muted,"object-fit":e.attrs["object-fit"],poster:e.attrs.poster,src:e.src[t.ctrl[i]||0],"data-i":i},on:{play:function(e){arguments[0]=e=t.$handleEvent(e),t.play.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaError.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaTap.apply(void 0,arguments)}}}):"iframe"===e.name?n("iframe",{style:e.attrs.style,attrs:{allowfullscreen:e.attrs.allowfullscreen,frameborder:e.attrs.frameborder,src:e.attrs.src}}):"embed"===e.name?n("embed",{style:e.attrs.style,attrs:{src:e.attrs.src}}):"table"===e.name&&(e.c||t.opts[5])||"li"===e.name?n("v-uni-view",{class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style,attrs:{id:e.attrs.id}},["li"===e.name?n("node",{attrs:{childs:e.children,opts:[t.opts[0],t.opts[1],t.opts[2],t.opts[3],t.opts[4],t.opts[5],t.opts[6],t.opts[7]+"."+i+".children"]}}):t._l(e.children,(function(e,a){return n("v-uni-view",{key:a,class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},["td"===e.name||"th"===e.name?n("node",{attrs:{childs:e.children,opts:[t.opts[0],t.opts[1],t.opts[2],t.opts[3],t.opts[4],t.opts[5],t.opts[6],t.opts[7]+"."+i+".children."+a+".children"]}}):t._l(e.children,(function(e,r){return["td"===e.name||"th"===e.name?n("v-uni-view",{key:r+"_0",class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},[n("node",{attrs:{childs:e.children,opts:[t.opts[0],t.opts[1],t.opts[2],t.opts[3],t.opts[4],t.opts[5],t.opts[6],t.opts[7]+"."+i+".children."+a+".children."+r+".children"]}})],1):n("v-uni-view",{class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},t._l(e.children,(function(e,s){return n("v-uni-view",{key:s,class:"_"+e.name+" "+e.attrs.class,style:e.attrs.style},[n("node",{attrs:{childs:e.children,opts:[t.opts[0],t.opts[1],t.opts[2],t.opts[3],t.opts[4],t.opts[5],t.opts[6],t.opts[7]+"."+i+".children."+a+".children."+r+".children."+s+".children"]}})],1)})),1)]}))],2)}))],2):"audio"==e.name?n("my-audio",{class:e.attrs.class,style:e.attrs.style,attrs:{aid:e.attrs.id,author:e.attrs.author,controls:e.attrs.controls,autoplay:e.attrs.autoplay,loop:e.attrs.loop,name:e.attrs.name,poster:e.attrs.poster,src:e.src[t.ctrl[i]||0],"data-i":i,"data-source":"audio"},on:{play:function(e){arguments[0]=e=t.$handleEvent(e),t.play.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaError.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.mediaTap.apply(void 0,arguments)}}}):e.attrs&&e.attrs["data-content"]?n("v-uni-rich-text",{attrs:{nodes:[e],"data-content":e.attrs["data-content"],"data-lang":e.attrs["data-lang"]},on:{longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.copyCode.apply(void 0,arguments)}}}):t.opts[5]||e.c||t.handler.isInline(e.name,e.attrs.style)?2===e.c?n("v-uni-view",{class:"_block _"+e.name+" "+e.attrs.class,style:e.f+";"+e.attrs.style,attrs:{id:e.attrs.id}},t._l(e.children,(function(e,a){return n("node",{key:a,style:e.f,attrs:{name:e.name,attrs:e.attrs,childs:e.children,opts:[t.opts[0],t.opts[1],t.opts[2],t.opts[3],t.opts[4],t.opts[5],t.opts[6],t.opts[7]+"."+i+".children."+a+".children"]}})})),1):n("node",{style:e.f,attrs:{name:e.name,attrs:e.attrs,childs:e.children,opts:[t.opts[0],t.opts[1],t.opts[2],t.opts[3],t.opts[4],t.opts[5],t.opts[6],t.opts[7]+"."+i+".children"]}}):n("v-uni-rich-text",{style:e.f,attrs:{id:e.attrs.id,"user-select":t.opts[4],nodes:[e]}}):n("v-uni-text",{attrs:{"data-i":i,"user-select":t.opts[4],decode:!t.opts[5]},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editStart.apply(void 0,arguments)}}},[t._v(t._s(e.text)),e.text?t._e():n("v-uni-text",{staticStyle:{color:"gray"}},[t._v(t._s(t.opts[6]||"请输入"))])],1)]}))],2)},a=[]},eee6:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("aa9c"),n("f7a5"),n("5ef2"),n("dd2b"),n("5c47"),n("a1c1"),n("4626"),n("5ac7"),n("d4b5");var a=i(n("8ae4")),r=i(n("ced8"));function s(t){var e=this;this.vm=t,this.editHistory=[],this.editI=-1,t._mask=[],t._setData=function(e,n){for(var i=e.split("."),a=t,r=0;r<i.length-1;r++)a=a[i[r]];t.$set(a,i.pop(),n)};var n=function(n){setTimeout((function(){var i=e.editHistory[e.editI+n];i&&(e.editI+=n,t._setData(i.key,i.value))}),200)};function i(e){if(t._edit)t._edit.insert(e);else{var n=t.nodes.slice(0);n.push(e),t._editVal("nodes",t.nodes,n,!0)}}function s(e){"string"===typeof e.src&&(e.src=[e.src]);for(var n=new r.default(t),a=0;a<e.src.length;a++)e.src[a]=n.getUrl(e.src[a]);i({name:"div",attrs:{style:"text-align:center"},children:[e]})}t.undo=function(){return n(-1)},t.redo=function(){return n(1)},t._editVal=function(n,i,a,r){while(e.editI<e.editHistory.length-1)e.editHistory.pop();while(e.editHistory.length>30)e.editHistory.pop(),e.editI--;var s=e.editHistory[e.editHistory.length-1];s&&s.key===n||(s&&(e.editHistory.pop(),e.editI--),e.editHistory.push({key:n,value:i}),e.editI++),e.editHistory.push({key:n,value:a}),e.editI++,r&&t._setData(n,a)},t._getItem=function(e,n,i){var r,s;return"img"===e.name?(r=a.default.img.slice(0),t.getSrc||(s=r.indexOf("换图"),-1!==s&&r.splice(s,1),s=r.indexOf("超链接"),-1!==s&&r.splice(s,1),s=r.indexOf("预览图"),-1!==s&&r.splice(s,1)),s=r.indexOf("禁用预览"),-1!==s&&e.attrs.ignore&&(r[s]="启用预览")):"a"===e.name?(r=a.default.link.slice(0),t.getSrc||(s=r.indexOf("更换链接"),-1!==s&&r.splice(s,1))):"video"===e.name||"audio"===e.name?(r=a.default.media.slice(0),s=r.indexOf("封面"),t.getSrc||-1===s||r.splice(s,1),s=r.indexOf("循环"),e.attrs.loop&&-1!==s&&(r[s]="不循环"),s=r.indexOf("自动播放"),e.attrs.autoplay&&-1!==s&&(r[s]="不自动播放")):r=a.default.node.slice(0),n||(s=r.indexOf("上移"),-1!==s&&r.splice(s,1)),i||(s=r.indexOf("下移"),-1!==s&&r.splice(s,1)),r},t._tooltip=function(e){t.$set(t,"tooltip",{top:e.top,items:e.items}),t._tooltipcb=e.success},t._slider=function(e){t.$set(t,"slider",{min:e.min,max:e.max,value:e.value,top:e.top}),t._slideringcb=e.changing,t._slidercb=e.change},t._maskTap=function(){while(t._mask.length)t._mask.pop()();t.tooltip&&t.$set(t,"tooltip",null),t.slider&&t.$set(t,"slider",null)},t.insertHtml=function(n){e.inserting=!0;var a=new r.default(t).parse(n);e.inserting=void 0;for(var s=0;s<a.length;s++)i(a[s])},t.insertImg=function(){t.getSrc&&t.getSrc("img").then((function(e){"string"===typeof e&&(e=[e]);for(var n=new r.default(t),a=0;a<e.length;a++)i({name:"img",attrs:{src:n.getUrl(e[a])}})})).catch((function(){}))},t.insertLink=function(){t.getSrc&&t.getSrc("link").then((function(t){i({name:"a",attrs:{href:t},children:[{type:"text",text:t}]})})).catch((function(){}))},t.insertTable=function(t,e){for(var n={name:"table",attrs:{style:"display:table;width:100%;margin:10px 0;text-align:center;border-spacing:0;border-collapse:collapse;border:1px solid gray"},children:[]},a=0;a<t;a++){for(var r={name:"tr",attrs:{},children:[]},s=0;s<e;s++)r.children.push({name:"td",attrs:{style:"padding:2px;border:1px solid gray"},children:[{type:"text",text:""}]});n.children.push(r)}i(n)},t.insertVideo=function(){t.getSrc&&t.getSrc("video").then((function(t){s({name:"video",attrs:{controls:"T"},children:[],src:t})})).catch((function(){}))},t.insertAudio=function(){t.getSrc&&t.getSrc("audio").then((function(t){var e;t.src?(e=t.src,t.src=void 0):(e=t,t={}),t.controls="T",s({name:"audio",attrs:t,children:[],src:e})})).catch((function(){}))},t.insertText=function(){i({name:"p",attrs:{},children:[{type:"text",text:""}]})},t.clear=function(){t._maskTap(),t._edit=void 0,t.$set(t,"nodes",[{name:"p",attrs:{},children:[{type:"text",text:""}]}])},t.getContent=function(){var e="";(function t(n,i){for(var a=0;a<n.length;a++){var r=n[a];if("text"===r.type)e+=r.text.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\n/g,"<br>").replace(/\xa0/g,"&nbsp;");else{if("img"===r.name){if(r.attrs.i="",(r.attrs.src||"").includes("data:image/svg+xml;utf8,")){e+=r.attrs.src.substr(24).replace(/%23/g,"#").replace("<svg",'<svg style="'+(r.attrs.style||"")+'"');continue}}else if("video"===r.name||"audio"===r.name)if(r=JSON.parse(JSON.stringify(r)),r.src.length>1){r.children=[];for(var s=0;s<r.src.length;s++)r.children.push({name:"source",attrs:{src:r.src[s]}})}else r.attrs.src=r.src[0];else"div"===r.name&&(r.attrs.style||"").includes("overflow:auto")&&"table"===(r.children[0]||{}).name&&(r=r.children[0]);if("table"===r.name&&(r=JSON.parse(JSON.stringify(r)),i=r.attrs,(r.attrs.style||"").includes("display:grid"))){r.attrs.style=r.attrs.style.split("display:grid")[0];for(var o=[{name:"tr",attrs:{},children:[]}],l=0;l<r.children.length;l++)r.children[l].attrs.style=r.children[l].attrs.style.replace(/grid-[^;]+;*/g,""),r.children[l].r!==o.length?o.push({name:"tr",attrs:{},children:[r.children[l]]}):o[o.length-1].children.push(r.children[l]);r.children=o}for(var c in e+="<"+r.name,r.attrs){var u=r.attrs[c];u&&("T"!==u&&!0!==u?"t"===r.name[0]&&"style"===c&&i&&(u=u.replace(/;*display:table[^;]*/,""),i.border&&(u=u.replace(/border[^;]+;*/g,(function(t){return t.includes("collapse")?t:""}))),i.cellpadding&&(u=u.replace(/padding[^;]+;*/g,"")),!u)||(e+=" "+c+'="'+u.replace(/"/g,"&quot;")+'"'):e+=" "+c)}e+=">",r.children&&(t(r.children,i),e+="</"+r.name+">")}}})(t.nodes);for(var n=t.plugins.length;n--;)t.plugins[n].onGetContent&&(e=t.plugins[n].onGetContent(e)||e);return e}}s.prototype.onUpdate=function(t,e){var n=this;this.vm.editable&&(this.vm._maskTap(),e.entities.amp="&",this.inserting||(this.vm._edit=void 0,t||setTimeout((function(){n.vm.$set(n.vm,"nodes",[{name:"p",attrs:{},children:[{type:"text",text:""}]}])}),0)))},s.prototype.onParse=function(t){!this.vm.editable||"td"!==t.name&&"th"!==t.name||this.vm.getText(t.children)||t.children.push({type:"text",text:""})};var o=s;e.default=o},efec:function(t,e,n){"use strict";n.r(e);var i=n("d9e8"),a=n("106b");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("54e9");var s=n("828b"),o=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"4e8fba57",null,!1,i["a"],void 0);e["default"]=o.exports}}]);