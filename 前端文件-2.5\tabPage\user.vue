<template>
	<view :class="$store.state.AppStyle" style="background-color: #f6f6f6;height: 100vh;">
		<view :style="[{height:CustomBar + 'px'}]">
			<!-- #ifdef APP-PLUS || MP  -->
			<view class="cu-bar" style="background-color: #f6f6f6;"
				:style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
			<!-- #endif -->
				<!-- #ifdef H5 -->
				<view class="cu-bar" style="background-color: #f6f6f6;"
					:style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<!-- #endif -->
					<view class="action" @tap="toset">
						<text class="tn-icon-set" style="font-size: 40upx;font-weight: bold;"></text>
					</view>
					<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">

					</view>
					<view class="action" @tap="toScan">
						<!-- #ifdef APP-PLUS -->
						<text class="tn-icon-scan" style="font-size: 40upx;font-weight: bold;"></text>
						<!-- #endif -->
					</view>
				</view>
			</view>
			<view class="about__wrap" v-if="tabCurTab==isLogin">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-margin-bottom" style="margin-top: 50rpx;"
					v-if="token==''&&tabCurTab==isLogin" @tap="toLogin">
					<view class="justify-content-item">
						<view class="tn-flex tn-flex-col-center tn-flex-row-left">
							<view class="logo-pic tn-shadow">
								<view class="logo-image">
									<view class="tn-shadow-blur"
										style="width: 110rpx;height: 110rpx;background-size: cover;background-image: url('https://cravatar.cn/wp-content/themes/cravatar/assets/img/img1.png#');"
										>
									</view>
								</view>
							</view>
							<view class="tn-padding-right">
								<view class="tn-padding-right tn-padding-left-sm">
									<text class="tn-color-cat tn-text-xl tn-text-bold">立即登录</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-margin-bottom" style="margin-top: 50rpx;"
					v-else>
					<view class="justify-content-item" @tap="toUserContents()">
						<view class="tn-flex tn-flex-col-center tn-flex-row-left">
							<view class="logo-pic tn-shadow">
								<view class="tn-shadow-blur logo-image">
									<view class="tn-shadow-blur cu-avatar round lg"
										style="width: 110rpx;height: 110rpx;background-size: cover;"
										:style="userInfo.style">
									</view>
								</view>
							</view>
							<view class="tn-padding-right">
								<view class="tn-padding-right tn-padding-left-sm">
									<text class="tn-color-cat tn-text-xl tn-text-bold"
										:class="isvip==1?'name-vip':''">{{name}}</text>
								</view>
								<view class="tn-padding-right tn-padding-top-xs tn-padding-left-sm tn-text-ellipsis">
									<view class="tn-color-gray">{{subText(userInfo.introduce, 10)}}</view>
								</view>
							</view>
						</view>
					</view>
					<view class="justify-content-item" @tap="toSignin()">
						<view class="cu-btn bg-gradual-orange"
							style="font-size: 26upx;height: 55upx;border-radius: 100upx;">签到</view>
					</view>
				</view>
				<!-- #ifdef APP-PLUS || H5  -->
				<block v-if="token!=''&&tabCurTab==isLogin">
					<view v-if="isvip" class="open-vip" @tap="toLink('/pages/user/buyvip')">
						<image src="/static/user/vip01.png"></image>
						<text class="text" style="font-weight: bold;">已开通尊贵VIP</text>
						<image src="/static/user/vip03.png" style="width: 55px; height: 20px;"></image>

					</view>
					<view v-else class="open-vip" @tap="toLink('/pages/user/buyvip')">
						<image src="/static/user/vip01.png"></image>
						<text class="text" style="font-weight: bold;">开通VIP享受十余项尊贵特权</text>
						<image src="/static/user/vip03.png" style="width: 55px; height: 20px;"></image>
					</view>
				</block>
				<!-- #endif -->
				<view class="tn-margin-top-sm" v-if="userInfo!=null">
					<view class="button-vip tn-flex tn-flex-row-between tn-flex-col-center tn-shadow-blur"
						style="background: linear-gradient(-120deg, #3E445A, #31374A, #2B3042, #262B3C);">

						<view class="tn-margin-left">
							<view class='title' style="color: #F1C68E;" @tap="toLink('/pages/user/assets')">
								<text class="tn-text-bold tn-text-xl" style="font-family: 'D-DIN';font-size:40rpx">{{userData.assets}} </text><text class="tn-text-bold" style="font-size:25rpx;margin-left: 10rpx;"> {{currencyName}}</text>
								<!-- <text class="tn-icon-vip-text tn-text-center" style="position: absolute;margin: -22rpx 0 0 0;font-size: 92rpx;"></text> -->
							</view>
							<view class='tn-color-white tn-text-sm tn-padding-top-sm'>邀请好友赚取更多{{currencyName}}</view>
						</view>
						<view class="tn-margin-right" @tap="toLink('/pages/user/assets')">
							<tn-button shape="round" backgroundColor="#F1C68E" fontColor="#634738" padding="10rpx 0"
								width="160rpx" shadow>
								<!-- <text class="tn-icon-vip tn-padding-right-sm tn-text-lg"></text> -->
								<text class="tn-text-bold">钱包</text>
							</tn-button>

						</view>
					</view>
				</view>
				<view class="about-shadow tn-margin-top-sm tn-padding-top-sm tn-padding-bottom-sm tn-bg-white">
					<view class="tn-flex tn-flex-row-center tn-radius tn-padding-top">
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="wzof==1&&modOrder==2">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/userpost')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-image-text tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的文章</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="tzof==1">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/forum/myPost')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-inventory tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的帖子</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="wzof==1&&modOrder==1">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/userpost')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-image-text tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的文章</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="tzof==2||wzof==2">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/followList?uid='+uid)">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-my-add tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的关注</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="wzof==2">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/usermark')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-star tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的收藏</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/space/mySpace')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-topics tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的动态</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="wzof==1">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/usermark')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-star tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的收藏</text>
								</view>
							</view>
						</view>
					</view>
					<view class="tn-flex tn-flex-row-center tn-radius tn-padding-top">
						<view class="tn-padding-sm tn-margin-xs tn-radius">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toRebate">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-my-add tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的邀请</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="shopof==2">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/order')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-order tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的订单</text>
								</view>
							</view>
						</view>
						<!--  #ifdef H5 || APP-PLUS -->
						<view class="tn-padding-sm tn-margin-xs tn-radius" v-if="shopof==1">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/myshop')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-shop tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">我的店铺</text>
								</view>
							</view>
						</view>
						<!--  #endif -->
						<view class="tn-padding-sm tn-margin-xs tn-radius">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/identifyblue')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-trusty tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">蓝V申请</text>
								</view>
							</view>
						</view>
						<view class="tn-padding-sm tn-margin-xs tn-radius">
							<view class="tn-flex tn-flex-direction-column tn-flex-row-center tn-flex-col-center"
								@tap="toLink('/pages/user/task')">
								<view
									class="icon12__item--icon tn-flex tn-flex-row-center tn-flex-col-center tn-bg-grey--light">
									<view class="tn-icon-calendar tn-color-cat"></view>
								</view>
								<view class="tn-text-center">
									<text class="tn-text-ellipsis">每日任务</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="tn-margin-top-sm tn-padding-bottom-sm tn-padding-top-sm bg-white"
					style="border-radius: 20upx;box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.07);">
					<!--  #ifdef APP-PLUS || H5 -->
					<block v-if="group=='administrator'||group=='editor'">
						<view class="user-table" hover-class="tn-hover" @tap="toManage()">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-dashboard"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">管理中心</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<block v-if="myPurview!=0&&group!='administrator'&&group!='editor'">
						<view class="user-table" hover-class="tn-hover" @tap="toManage()">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-dashboard"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">圈子管理中心</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<!--  #endif -->
					<block v-if="FansteyAvatarFrame">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/plugins/Fanstey_AvatarFrame/index')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-order"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">头像框中心</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<block v-if="fansteymedal">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/plugins/Fanstey_medal/medalShop')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-order"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">勋章中心</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<!-- 小祈愿勋章插件 -->
					<block v-if="xqy_medal">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/plugins/xqy_medal/home')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-star"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">勋章中心</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<!-- 小祈愿头像框插件 -->
					<block v-if="xqy_avatar_frame">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/plugins/xqy_avatar_frame/home')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-image"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">头像框中心</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<!-- 小祈愿靓号插件 -->
					<block v-if="dor_pretty">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/plugins/dor_pretty/home')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-image"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">靓号中心</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<!-- 小祈愿视频插件 -->
					<block v-if="xqy_video">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/plugins/xqy_video/myVideos')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-video"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">我的视频</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<block v-if="shopof==1">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/user/order')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-order"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">我的订单</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<view class="user-table" hover-class="tn-hover"
						@tap="toLink('/pages/user/useredit?type=setInfo')">
						<view class="tn-flex tn-flex-col-center">
							<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
								style="color: #7C8191;">
								<view class="tn-icon-edit-form"></view>
							</view>
							<view class="tn-margin-left-sm tn-flex-1">个人资料</view>
							<view class="tn-color-gray tn-icon-right"></view>
						</view>
					</view>
					<view class="user-table" hover-class="tn-hover"
						@tap="toLink('/pages/user/identify')">
						<view class="tn-flex tn-flex-col-center">
							<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
								style="color: #7C8191;">
								<view class="tn-icon-identity"></view>
							</view>
							<view class="tn-margin-left-sm tn-flex-1">实名认证</view>
							<view class="tn-color-gray tn-icon-right"></view>
						</view>
					</view>
					<block v-if="shopof==2">
						<view class="user-table" hover-class="tn-hover"
							@tap="toLink('/pages/user/useredit?backif=1&type=setSafe')">
							<view class="tn-flex tn-flex-col-center">
								<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
									style="color: #7C8191;">
									<view class="tn-icon-safe"></view>
								</view>
								<view class="tn-margin-left-sm tn-flex-1">账号安全</view>
								<view class="tn-color-gray tn-icon-right"></view>
							</view>
						</view>
					</block>
					<view class="user-table" hover-class="tn-hover" @tap="toset()">
						<view class="tn-flex tn-flex-col-center">
							<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center"
								style="color: #7C8191;">
								<view class="tn-icon-install"></view>
							</view>
							<view class="tn-margin-left-sm tn-flex-1">系统设置</view>
							<view class="tn-color-gray tn-icon-right"></view>
						</view>
					</view>

				</view>
				<view class="about-tips text-center" v-html='replaceSpecialChar(settext)'>
				</view>

				<view class="loading" v-if="!isLoginding">
					<view class="loading-main">
						<image src="../../static/loading.gif"></image>
					</view>
				</view>
				<view class="cu-modal userLoginstatus" :class="isLoginShow?'show':''">
					<view class="cu-dialog">

						<view class="padding-sm">
							<view class="padding flex flex-direction">
								<view class="userLoginstatus-i bg-red">
									<text class="cuIcon-close"></text>
								</view>
								<view class="text-bold">登录状态已失效</view>

								<button class="cu-btn bg-blue margin-top" @tap="isLoginShow=false">确定</button>
							</view>
						</view>

					</view>
				</view>
			</view>
			<view style="width: 100%; height: 100upx;background-color: #f6f6f6"></view>
		</view>
</template>

<script>
	import waves from '@/components/xxley-waves/waves.vue';
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		props: {
			curPage: {
				type: Number,
				default: 0
			}
		},
		name: "user",
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				userInfo: null,
				name: "",
				uid: 0,
				isLogin: 1,
				swiperStyle: 0,
				tabCurTab: 1,
				swiperOf: 0,
				isLoginding: false,
				isvip: 0,
				token: "",
				isHuaWei: this.$API.isHuaWei(),
				isTy: false,
				userData: {},
				isClock: 0,
				group: "",
				avatar: "",
				assets: 0,
				userlvStyle: "",
				lvStyle: "",
				wzof: 0,
				FansteyAvatarFrame: false,
				tzof: 0,
				shopof: 0,
				settext: "  ",
				modOrder: 0,
				myPurview: 0,
				purview: 0,
				isLoginShow: false,

				noticeSum: 0,

				isModerator: false,
				fansteymedal: false,
				currencyName: "",

				modalName: null,

				curFullStyle: "",
				xqy_medal: false,
				xqy_avatar_frame: false,
				dor_pretty: false,
				xqy_video: false,
				isLoading: 0
			}
		},
		created() {
			try {
				const cachedPlugins = uni.getStorageSync('getPlugins')
				if (cachedPlugins) {
					const pluginList = JSON.parse(cachedPlugins)
					this.xqy_medal = pluginList.includes('xqy_medal')
					this.xqy_avatar_frame = pluginList.includes('xqy_avatar_frame')
					this.dor_pretty = pluginList.includes('dor_pretty')
					this.xqy_video = pluginList.includes('xqy_video')
				}
			} catch (error) {
				console.error('检查插件状态失败:', error)
			}
		},
		mounted() {
			var that = this;
			uni.$on('onShow', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}



				that.getSet()
				that.getleiji()
				console.log("触发Tab-" + data + "||页面下标" + that.curPage);
				if (localStorage.getItem('userinfo')) {
					that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.userInfo.style = "background-image:url(" + that.userInfo.avatar + ");"
					that.avatar = that.userInfo.avatar;
					that.isvip = that.userInfo.isvip;
					that.uid = that.userInfo.uid;
					that.group = that.userInfo.group;
					if (that.userInfo.screenName) {
						that.name = that.userInfo.screenName;
					} else {
						that.name = that.userInfo.name;
					}
				} else {
					that.userInfo = null;
				}
				if (localStorage.getItem('tabCurTab')) {
					that.tabCurTab = localStorage.getItem('tabCurTab');
				}
				if (localStorage.getItem('token')) {

					that.token = localStorage.getItem('token');
				} else {
					that.token = "";
				}
				// 获取已开启的插件列表
				var cachedPlugins = localStorage.getItem('getPlugins');
				if (cachedPlugins) {
					const pluginList = JSON.parse(cachedPlugins);
					// 检查插件是否存在于插件列表中
					that.FansteyAvatarFrame = pluginList.includes('Fanstey_AvatarFrame'); // 假设插件A的文件名是'sy_example'
					that.fansteymedal = pluginList.includes('Fanstey_medal'); // 假设插件A的文件名是'sy_example'
					that.xqy_medal = pluginList.includes('xqy_medal');
					that.xqy_video = pluginList.includes('xqy_video');
				}
				that.getUserData();
				that.userStatus();
				that.unreadNum();
				that.userPurview();
				if (localStorage.getItem('curFullStyle')) {
					that.curFullStyle = localStorage.getItem('curFullStyle');
				}

			});

			uni.$on('onReachBottom', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				console.log("触发触底刷新");

			});

			uni.$on('onPullDownRefresh', function(data) {
				if (Number(data) != Number(that.curPage)) {
					return false;
				}
				console.log("触发下拉刷新");
				that.getUserData();
				that.userStatus();
				that.unreadNum();
				that.userPurview();
			});
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
		},
		beforeDestroy() {
			uni.$off('onReachBottom')
			uni.$off('onShow')
			uni.$off('onPullDownRefresh')
		},
		methods: {
			getSet() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.wzof = res.data.wzof;
						that.tzof = res.data.tzof;
						that.shopof = res.data.shopof;
						if (res.data.settext == null) {
							that.settext = "  ";
						} else {
							that.settext = res.data.settext;
						}
						that.modOrder = res.data.modOrder;
						that.swiperStyle = res.data.swiperStyle;
						that.tabCurTab = res.data.swiperinfo;
						that.swiperOf = res.data.swiperOf;
						localStorage.setItem('tabCurTab', that.tabCurTab);
						that.isLoginding = true;
					},
					fail(error) {}
				})

			},
			getleiji() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.currencyName = res.data.assetsname;
					},
					fail(error) {
						console.log(error);
					}

				})
			},
			toLogin() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/login'
				});

			},
			replaceSpecialChar(text) {
				text = text.replace(/“/g, '');
				text = text.replace(/”/g, '');
				// text = text.replace(/&lt;/g, '<');
				// text = text.replace(/&gt;/g, '>');
				// text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			// #ifdef APP-PLUS
			//权限检测
			showTC() {
				var that = this;
				var tc1 = false;
				let _permissionID = 'android.permission.WRITE_EXTERNAL_STORAGE';
				let _permissionID2 = 'android.permission.CAMERA';
				plus.android.checkPermission(_permissionID2,
					granted => {
						if (granted.checkResult == -1) {
							console.log('无相机权限');
							uni.showModal({
								title: '权限申请说明',
								content: '我们将申请获取相机权限和访问相册权限，便于您使用拍照上传您的照片/视频及用于更换头像、意见反馈、保存相册、发布动态等场景。',
								cancelText: "取消",
								confirmText: "同意",
								showCancel: true,
								confirmColor: '#000',
								cancelColor: '#666',
								success: (res) => {
									if (res.confirm) {
										console.log('弹窗同意');
										tc1 = true;
										that.requestPermissions();
									} else {
										console.log('弹窗取消');
										that.isTy = false
										tc1 = true;
									}
								}
							})
							//还未授权当前查询的权限，打开权限申请目的自定义弹框
							that.$nextTick(() => {
								setTimeout(() => {
									that.ani = 'uni-' + that.type
								}, 30)
							})
						}
					},
					error => {
						console.log(error.message);
					}
				);
				plus.android.checkPermission(_permissionID,
					granted => {
						if (granted.checkResult == -1) {
							console.log('无相册权限');
							if (!tc1) {
								uni.showModal({
									title: '权限申请说明',
									content: '我们将申请获取相机权限和访问相册权限，便于您使用拍照上传您的照片/视频及用于更换头像、意见反馈、保存相册、发布动态等场景。',
									cancelText: "取消",
									confirmText: "同意",
									showCancel: true,
									confirmColor: '#000',
									cancelColor: '#666',
									success: (res) => {
										if (res.confirm) {
											console.log('弹窗同意');
											return that.requestPermissions();
										} else {
											console.log('弹窗取消');
											that.isTy = false
										}
									}
								})
							}

							//还未授权当前查询的权限，打开权限申请目的自定义弹框
							that.$nextTick(() => {
								setTimeout(() => {
									that.ani = 'uni-' + that.type
								}, 30)
							})
						}
					},
					error => {
						console.log(error.message);
					}
				);

			},

			requestPermissions() {
				let _this = this;

				let _permissionID = 'android.permission.WRITE_EXTERNAL_STORAGE';
				let _permissionID2 = 'android.permission.CAMERA';

				plus.android.checkPermission(_permissionID2,
					granted => {
						if (granted.checkResult == -1) {
							//还未授权当前查询的权限，打开权限申请目的自定义弹框

							_this.$nextTick(() => {
								setTimeout(() => {
									_this.ani = 'uni-' + _this.type
								}, 30)
							})
						}
					},
					error => {
						console.log(error.message);
					}
				);
				plus.android.requestPermissions([_permissionID2],
					(e) => {
						//关闭权限申请目的自定义弹框
						_this.ani = '';
						_this.$nextTick(() => {

						})
						console.log(e, 'kkkkk')
						if (e.granted.length > 0) {
							//当前查询权限已授权
							console.log('1已同意');
							plus.android.checkPermission(_permissionID,
								granted => {
									if (granted.checkResult == -1) {
										//还未授权当前查询的权限，打开权限申请目的自定义弹框

										_this.$nextTick(() => {
											setTimeout(() => {
												_this.ani = 'uni-' + _this.type
											}, 30)
										})
									}
								},
								error => {
									console.log(error.message);
								}
							);
							plus.android.requestPermissions([_permissionID],
								(e) => {
									//关闭权限申请目的自定义弹框
									_this.ani = '';
									_this.$nextTick(() => {

									})
									console.log(e, 'kkkkk')
									if (e.granted.length > 0) {
										//当前查询权限已授权
										console.log('2已同意');
										_this.isTy = true
									}
									if (e.deniedAlways.length > 0) {
										//当前查询权限已被永久禁用，此时需要引导用户跳转手机系统设置去开启
										uni.showModal({
											title: '温馨提示',
											content: '您没有给予访问相册权限，不给予权限将无法使用该功能，立即去设置开启？',
											cancelText: "取消",
											confirmText: "去设置",
											showCancel: true,
											confirmColor: '#000',
											cancelColor: '#666',
											success: (res) => {
												if (res.confirm) {
													_this.goSetting();
												} else {
													_this.isTy = false
												}
											}
										})
									}
								})
						}
						if (e.deniedAlways.length > 0) {
							//当前查询权限已被永久禁用，此时需要引导用户跳转手机系统设置去开启
							uni.showModal({
								title: '温馨提示',
								content: '您没有给予使用相机权限，不给予权限将无法使用该功能，立即去设置开启？',
								cancelText: "取消",
								confirmText: "去设置",
								showCancel: true,
								confirmColor: '#000',
								cancelColor: '#666',
								success: (res) => {
									if (res.confirm) {
										_this.goSetting();
									} else {
										_this.isTy = false
									}
								}
							})
						}
					})

			},
			//跳转手机系统设置
			goSetting() {
				var Intent = plus.android.importClass("android.content.Intent");
				var Settings = plus.android.importClass("android.provider.Settings");
				var Uri = plus.android.importClass("android.net.Uri");
				var mainActivity = plus.android.runtimeMainActivity();
				var intent = new Intent();
				intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
				var uri = Uri.fromParts("package", mainActivity.getPackageName(), null);
				intent.setData(uri);
				mainActivity.startActivity(intent);
			},
			// #endif

			hideModal(e) {
				this.modalName = null
			},

			getUserLv(i) {
				var that = this;
				var rankList = that.$API.GetRankList();
				var rankStyle = that.$API.GetRankStyle();
				that.userlvStyle = "color:#fff;background-color: " + rankStyle[i];
				return rankList[i];
			},
			getLv(i) {
				var that = this;
				var lv = that.$API.getLever(i);
				var leverList = that.$API.GetLeverList();
				var rankStyle = that.$API.GetRankStyle();
				that.lvStyle = "color:#fff;background-color: " + rankStyle[lv];
				return leverList[lv];
			},
			toset() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/setup'
				});
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			toPage(title, cid) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/info?cid=' + cid + "&title=" + title
				});
			},
			userStatus() {
				var that = this;
				that.$Net.request({

					url: that.$API.userStatus(),
					data: {
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 0) {

							if (that.userInfo != null) {
								that.isLoginShow = true;
							}
							localStorage.removeItem('userinfo');
							localStorage.removeItem('token');

							that.userInfo = null;
						} else {

							if (localStorage.getItem('userinfo')) {

								var userInfo = JSON.parse(localStorage.getItem('userinfo'));
								if (userInfo.screenName) {
									that.name = userInfo.screenName;
								} else {
									that.name = userInfo.name;
								}
								if (res.data.data.customize) {
									userInfo.customize = res.data.data.customize;
								}
								if (res.data.data.lv) {
									userInfo.lv = res.data.data.lv;
								}
								if (res.data.data.isvip) {
									userInfo.isvip = res.data.data.isvip;
									that.isvip = res.data.data.isvip;
								}
								if (res.data.data.vip) {
									userInfo.vip = res.data.data.vip;
								}
								if (res.data.data.experience) {
									userInfo.experience = res.data.data.experience;
								}
								userInfo.introduce = res.data.data.introduce;
								that.userInfo.style = "background-image:url(" + res.data.data.avatar + ");"
								localStorage.setItem('userinfo', JSON.stringify(userInfo));
								// if(res.data.data.avatar){
								// 	that.userInfo = res.data.data.avatar;
								// }

							}

						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toScan() {
				var that = this;
				if (that.isTy == false && that.isHuaWei == 1) {
					that.showTC()
				}
				uni.scanCode({
					onlyFromCamera: false,
					scanType: ['barCode', 'qrCode'],
					success: function(res) {
						var text = res.result;
						var strUrl = "^((https|http|ftp|rtsp|mms)?://)" +
							"?(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?" +
							"(([0-9]{1,3}\.){3}[0-9]{1,3}" +
							"|" +
							"([0-9a-z_!~*'()-]+\.)*" +
							"([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\." +
							"[a-z]{2,6})" +
							"(:[0-9]{1,4})?" +
							"((/?)|" +
							"(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$";
						var urlDemo = new RegExp(strUrl);
						if (urlDemo.test(text)) {
							var linkRule = that.$API.GetLinkRule();
							var linkRuleArr = linkRule.split("{cid}");
							if (text.indexOf(linkRuleArr[0]) != -1) {
								//是本站链接
								var cid = text;
								for (var i in linkRuleArr) {
									cid = cid.replace(linkRuleArr[i], "");
								}
								uni.navigateTo({
									url: '/pages/contents/info?cid=' + cid
								});
							} else {
								// #ifdef MP
								uni.setClipboardData({
									data: href,
									success: () =>
										uni.showToast({
											title: '链接已复制'
										})
								})
								// #endif
								// #ifdef APP-PLUS
								plus.runtime.openWeb(href)
								// #endif
							}
						} else {
							that.scanLogin(text);
						}
					}
				});
			},
			toGroup() {
				var url = that.$API.GetGroupUrl();
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			getUserData() {
				var that = this;
				that.$Net.request({

					url: that.$API.getUserData(),
					data: {
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res));
						if (res.data.code == 1) {
							that.userData = res.data.data;
							that.isClock = res.data.data.isClock;
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			formatNumber(num) {
				if (num == 0 || !num) {
					return 0;
				}
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},
			subText(text, num) {
				if (text) {
					if (text.length > num) {
						text = text.substring(0, num);
						return text + "…";
					} else {
						return text;
					}
				} else {
					return "还没有个人介绍哦"
				}
			},

			toSearch() {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/search'
				});
			},
			toMedia() {
				uni.navigateTo({
					url: '/pages/user/media'
				});
			},
			toSetUp() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/setup'
				});
			},
			toRebate() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/rebate'
				});
			},
			toManage() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/manage'
				});
			},
			callme() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/media'
				});
			},
			scanLogin(text) {
				var that = this;

				if (that.isJSON(text)) {
					text = JSON.parse(text);
				} else {
					uni.showToast({
						title: "无法解析的内容！",
						icon: 'none'
					})
					return false;
				}
				if (text.type) {
					if (text.type == "Scan") {
						if (that.token == "") {
							uni.showToast({
								title: "请先登录",
								icon: 'none'
							})
							return false;
						}
						uni.navigateTo({
							url: '/pages/user/scan?text=' + text.data
						});
						return false;
					} else if (text.type == "Invite") {
						uni.navigateTo({
							url: '/pages/user/register?inviteCode=' + text.code
						});
						return false;
					} else {
						uni.showToast({
							title: "无法解析的内容！",
							icon: 'none'
						})
						return false;
					}
				} else {
					uni.showToast({
						title: "无法解析的内容！",
						icon: 'none'
					})
					return false;
				}


			},
			toPage(title, cid) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/info?cid=' + cid + "&title=" + title
				});
			},
			goStyle() {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/clothes'
				});
			},
			toAbout() {
				var that = this;

				uni.navigateTo({
					url: '/pages/home/<USER>'
				});
			},
			isJSON(str) {

				if (typeof str == 'string') {
					try {
						var obj = JSON.parse(str);
						if (typeof obj == 'object' && obj) {
							return true;
						} else {
							return false;
						}
					} catch (e) {
						console.log('error：' + str + '!!!' + e);
						return false;
					}
				}
			},
			unreadNum() {
				var that = this;
				if (localStorage.getItem('noticeSum')) {
					that.noticeSum = Number(localStorage.getItem('noticeSum'));
				}
			},
			goFanList(uid) {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/fanList?uid=' + uid
				});
			},
			toUserContents() {
				var that = this;
				var name = that.name;
				var title = that.name + "的信息";
				var id = that.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(that.avatar)
				});
			},
			userPurview() {
				var that = this;
				var uid = 0;
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					uid = userInfo.uid;
				}
				var data = {
					"uid": uid,
				}
				that.$Net.request({
					url: that.$API.userPurview(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {


						if (res.data.code == 1) {
							var list = []
							list = res.data.data
							if (list && list.length > 0 && list[0].hasOwnProperty('purview')) {
								that.myPurview = list[0].purview;
							} else {
								console.log('List[0] does not have a purview property');
							}
							if (localStorage.getItem('userinfo')) {
								var myInfo = JSON.parse(localStorage.getItem('userinfo'));
								if (myInfo.group == 'administrator' || myInfo.group == 'editor') {
									that.myPurview = 5;
								}

							}


						}
					},
					fail: function(res) {

					}
				})

			},
			toSignin() {
				var that = this;
				if (that.token == "") {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/user/signin'
				});
			},
			changeStyle() {
				var that = this;
				if (that.modalName == null) {
					that.modalName = "changeStyle";
					return false;
				}
				if (that.curFullStyle == "index") {
					uni.redirectTo({
						url: '/pages/home/<USER>'
					})
				} else {
					uni.redirectTo({
						url: '/pages/home/<USER>'
					})
				}

			}
		},
		components: {
			waves
		},
	}
</script>

<style lang="scss" scoped>
	@font-face {
		font-family: 'D-DIN';
		src: url('../../tuniao-ui/static/D-DIN.ttf');
	}
	.open-vip {
		width: 100%;
		height: 72rpx;
		background: linear-gradient(180deg, #F7E5B4 0%, #FFE6AF 2%, #EBC075 100%);
		border-radius: 49rpx;
		display: flex;
		align-items: center;
		margin-top: 36rpx;
		padding: 0 24rpx 0 34rpx;
		box-sizing: border-box;

		>image {
			width: 48rpx;
			height: 48rpx;
		}

		.text {
			flex: 1;
			font-size: 24rpx;
			line-height: 34rpx;
			margin-left: 14rpx;
		}

		.button {
			width: 128rpx;
			height: 42rpx;
			background: linear-gradient(90deg, #4D4D4D 0%, #151515 100%);
			border-radius: 22rpx;
			font-size: 22rpx;
			color: #FFDFA9;
			line-height: 42rpx;
			text-align: center;
		}
	}

	.mine {
		max-height: 100vh;
	}

	/* 底部安全边距 start*/
	.tn-tabbar-height {
		min-height: 120rpx;
		height: calc(140rpx + env(safe-area-inset-bottom) / 2);
		height: calc(140rpx + constant(safe-area-inset-bottom));
	}

	.tn-color-cat {
		color: #1D2541;
	}

	.tn-bg-cat {
		background-color: #1D2541;
	}


	/* 自定义导航栏内容 start */
	.custom-nav {
		height: 100%;

		&__back {
			margin: auto 5rpx;
			font-size: 40rpx;
			margin-right: 10rpx;
			flex-basis: 5%;
			width: 100rpx;
			position: absolute;
		}
	}

	/* 自定义导航栏内容 end */


	/* 顶部背景图 end */


	/* 用户头像 start */
	.logo-image {
		width: 110rpx;
		height: 110rpx;
		position: relative;
		overflow: hidden;
		border-radius: 50%;
	}

	.logo-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		box-shadow: 0rpx 0rpx 60rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 50%;
		overflow: hidden;
		// background-color: #FFFFFF;
	}

	/* 页面 start*/
	.about-shadow {
		border-radius: 15rpx;
		box-shadow: 0rpx 0rpx 50rpx 0rpx rgba(0, 0, 0, 0.07);
	}

	.about {

		&__wrap {
			position: relative;
			z-index: 1;
			margin: 20rpx 30rpx;
		}
	}

	/* 页面 end*/

	/* 图标容器15 start */
	.icon15 {
		&__item {
			width: 30%;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;

			&--icon {
				width: 100rpx;
				height: 100rpx;
				font-size: 60rpx;
				border-radius: 50%;
				margin-bottom: 18rpx;
				position: relative;
				z-index: 1;

				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;


				}
			}
		}
	}

	/* 图标容器12 start */
	.tn-three {
		position: absolute;
		top: 50%;
		right: 50%;
		bottom: 50%;
		left: 50%;
		transform: translate(-38rpx, -16rpx) rotateX(30deg) rotateY(20deg) rotateZ(-30deg);
		text-shadow: -1rpx 2rpx 0 #f0f0f0, -2rpx 4rpx 0 #f0f0f0, -10rpx 20rpx 30rpx rgba(0, 0, 0, 0.2);
	}

	.icon20 {
		&__item {
			width: 30%;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;

			&--icon {
				width: 100rpx;
				height: 100rpx;
				font-size: 60rpx;
				border-radius: 50%;
				margin-bottom: 18rpx;
				position: relative;
				z-index: 1;

				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;
				}
			}
		}
	}



	.button-vip {
		width: 100%;
		height: 150rpx;
		border-radius: 15rpx;
		position: relative;
		z-index: 1;

		&::after {
			content: " ";
			position: absolute;
			z-index: -1;
			width: 100%;
			height: 100%;
			left: 0;
			bottom: 0;
			border-radius: inherit;
			opacity: 1;
			transform: scale(1, 1);
			background-size: 100% 100%;
		}
	}


	/* 图标容器12 start */
	.icon12 {
		&__item {
			width: 30%;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;

			&--icon {
				width: 15rpx;
				height: 15rpx;
				font-size: 50rpx;
				border-radius: 50%;
				margin-bottom: 38rpx;
				position: relative;
				z-index: 1;

				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;

				}
			}
		}
	}

	/* 图标容器1 start */
	.icon1 {
		&__item {
			// width: 30%;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			padding: 30rpx;
			margin: 20rpx 10rpx;
			transform: scale(1);
			transition: transform 0.3s linear;
			transform-origin: center center;

			&--icon {
				width: 40rpx;
				height: 40rpx;
				font-size: 40rpx;
				border-radius: 50%;
				position: relative;
				z-index: 1;

				&::after {
					content: " ";
					position: absolute;
					z-index: -1;
					width: 100%;
					height: 100%;
					left: 0;
					bottom: 0;
					border-radius: inherit;
					opacity: 1;
					transform: scale(1, 1);
					background-size: 100% 100%;

				}
			}
		}
	}

	/* 图标容器1 end */


	/* 顶部背景图 start */
	.top-backgroup {
		height: 450rpx;
		z-index: -1;

		.backgroud-image {
			width: 100%;
			height: 450rpx;
			// z-index: -1;
		}
	}

	/* 顶部背景图 end */
	.text-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.name-vip {
		color: #ff6c3e;
	}
	.user-table{
		position: relative;
		width: 100%;
		box-sizing: border-box;
		background-color: #FFFFFF;
		color: #080808;
		font-size: 30rpx;
		padding: 26rpx 30rpx;
	}
</style>