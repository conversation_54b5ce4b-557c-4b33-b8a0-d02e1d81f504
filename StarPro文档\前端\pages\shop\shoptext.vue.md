<template>
	<view class="user" :class="$store.state.AppStyle" style="background-color: #f6f6f6;">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					商品内容
				</view>
				<view class="action">
					
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="info" style="margin-top:10upx;">
			<view class="info-content">
				<!-- <joMarkdown :nodes="markdownData"></joMarkdown> -->
				
				
				<block v-if="shopIsMd==1">
					<mp-html :content="html" :selectable="true" :show-img-menu="true" :lazy-load="true" :ImgCache="true" :markdown="true"/>
				</block>
				<block v-if="shopIsMd==0">
					<mp-html :content="html" :selectable="true" :show-img-menu="true" :lazy-load="true" :ImgCache="true" :markdown="false"/>
				</block>
			</view>
		</view>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
	</view>
</template>

<script>
	import mpHtml from '@/components/mp-html/mp-html'
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
			AppStyle:this.$store.state.AppStyle,
				
				sid:0,
				title:"",
				html:"",
				markdownData: {},
				price:"",
				num:"",
				imgurl:"",
				shopIsMd:-1,
				
				isLoading:0,
				
				
			}
		},
		components: {
		  mpHtml,
		},
		onReachBottom() {
		    //触底后执行的方法，比如无限加载之类的
			var that = this;
		},
		
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			
			
		},
		onPullDownRefresh(){
			var that = this;
		},
		onLoad(res) {
			var that = this;
			
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			
			if(res.sid){
				that.sid = res.sid;
				that.getInfo(that.sid);
			}
			
		},
		methods:{
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			getInfo(sid){
				var that = this;
				var token = "";
				if(localStorage.getItem('token')){
					token=localStorage.getItem('token');
				}
				var data = {
					"key":that.sid,
					"token":token
				}
				that.$Net.request({
					url: that.$API.shopInfo(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						
						uni.stopPullDownRefresh();
						if(res.data.value){
							that.shopIsMd = res.data.isMd;
							var html = res.data.value;
							if(res.data.isMd==1){
								html =that.markHtml(res.data.value);
							}else{
								html =that.quillHtml(res.data.value);
							}
							that.html = html;
						}
						

						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.stopPullDownRefresh();
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			quillHtml(text){
				var that = this;
				text = that.replaceAll(text,"hljs","hl");
				text = that.replaceAll(text,"ql-syntax","hl-pre");
				
				text = that.markExpand(text);
				return text;
			},
			replaceAll(string, search, replace) {
			  return string.split(search).join(replace);
			},
			markExpand(text) {
				var that = this;
				//视频
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|type=video"[^>]*?>/g,
					(match, src, poster) => {
						return `<div style="border-radius:10px"><video src="${src}" poster="${poster}" object-fit width="100%" style="border-radius:10px" /></div>`;
					}
				);
				//超链接
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|text=([^"]+)\|type=url"[^>]*?>/g,
					(match, src, poster, text) => {
						return `<div><a href="${src}">${text}</a></div>`;
					}
				);
				//音乐
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|name=([^"]+)\|author=([^"]+)\|type=audio"[^>]*?>/g,
					(match, src, poster, name, author) => {
						return `<div><audio src="${src}" poster="${poster}" name="${name}" author="${author}" loop width="100%"></audio></div>`;
					}
				);
				//附件
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|pw=([^"]+)\|name=([^"]+)\|type=file"[^>]*?>/g,
					(match, src, poster, pw, name) => {
						var tqm = ''
						if(pw=='无'){
							tqm = ''
						}else{
							tqm = '提取码：'+pw
						}
						return `
						<div style='background: #f0f0f0;width:100%;padding:15px 15px;color:#666666;border:solid 1px black;box-sizing: border-box;border-radius: 20px;word-break:break-all;'/>
							<div style="display: flex;justify-content: space-between;align-items: center;">
								<div>
									<div style="font-size: 16px;font-weight: bold;color: black;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">${name}</div>
									<div style="font-size: 15px;">${tqm}</div>
								</div>
								<a href='${src}' style="color:#000000">
								<div>
									<span style='font-size:30px' class='tn-icon-download-simple' />
								</div>
								</a>
							</div>
						</div>
						`;
					}
				);
				
				//链接自动识别
				text = text.replace(/(?![^<>]*>)(src=['"]|poster=['"]|https?:\/\/[^\s'"<>]+)/gi, function(match, group1) {
					if (group1 && !/(src=['"]|poster=['"])/i.test(group1)) {
						var lastChar = match.charAt(match.length - 1);
						if (!/['"<>]/.test(match.charAt(4)) && !/[^a-zA-Z0-9\/]/.test(lastChar)) {
							return '<a href="' + match + '" target="_blank">' + match + '</a>';
						} else {
							return match;
						}
					} else {
						return match;
					}
				});
			
				return text;
			},
			markHtml(text){
				var that = this;
				//下面奇怪的代码是为了解决可执行代码区域问题
				text = that.replaceAll(text,"@!!!","@@@@");
				
				text = that.replaceAll(text,"!!!","");
				text = that.replaceAll(text,"@@@@","@!!!");
				text = that.markExpand(text);
				//text = text.replace(/(?<!\r)\n(?!\r)/g, "\n\n");
				//兼容垃圾的Safari浏览器
				text = text.replace(/([^\r])\n([^\r])/g, "$1\n\n$2");
				text = that.replaceAll(text,"||rn||","\n\n");
				return text;
				
			},
		}
	}
</script>

<style>
</style>
