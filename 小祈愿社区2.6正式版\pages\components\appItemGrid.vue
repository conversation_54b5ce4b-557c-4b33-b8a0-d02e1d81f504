<template>
	<view class="app-grid-container" :class="isDark?'dark':''">
		<view class="app-grid">
			<view class="app-grid-item" v-for="(item, index) in items" :key="index" @tap="toAppInfo(item.id)">
				<view class="app-icon-container">
					<view class="app-icon tn-shadow-blur" :style="'background-image: url(' + item.logo + ')'"></view>
				</view>
				<view class="app-name tn-color-black tn-text-center">
					<text class="tn-text-ellipsis">{{item.name}}</text>
				</view>
				<view class="app-download tn-color-black tn-text-center tn-margin-top-xs" v-if="showDownloadBtn">
					<text class="down-button">立即下载</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import darkModeMixin from '@/utils/darkModeMixin.js'
	export default {
		mixins: [darkModeMixin],
		props: {
			items: {
				type: Array,
				default: () => []
			},
			showDownloadBtn: {
				type: Boolean,
				default: true
			}
		},
		name: "appItemGrid",
		methods: {
			toAppInfo(id) {
				uni.navigateTo({
					url: '/pages/plugins/sy_appbox/info?id=' + id
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	@import "@/static/styles/home.scss";
	
	.app-grid-container {
		width: 100%;
		padding: 10rpx 0rpx;
	}
	
	.app-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
	}
	
	.app-grid-item {
		width: 25%;
		box-sizing: border-box;
		padding: 15rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.app-icon-container {
		width: 100%;
		display: flex;
		justify-content: center;
	}
	
	.app-icon {
		width: 90rpx;
		height: 90rpx;
		border-radius: 20rpx;
		background-size: 100% 100%;
		background-position: center;
		background-repeat: no-repeat;
	}
	
	.app-name {
		width: 100%;
		margin-top: 10rpx;
	}
	
	.tn-text-ellipsis {
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: block;
		font-size: 24rpx;
		text-align: center;
	}
	
	.app-download {
		margin-top: 8rpx;
	}
	
	.down-button {
		border-radius: 40rpx;
		background-color: #3cc9a4;
		padding: 6rpx 16rpx;
		color: white;
		font-size: 22rpx;
	}
</style> 