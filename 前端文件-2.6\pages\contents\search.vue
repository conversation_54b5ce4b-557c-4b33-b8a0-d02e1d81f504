<template>
	<view class="userpost" :class="[isDark?'dark':'', AppStyle]" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :class="isDark?'dark':'bg-white'" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px','background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<!--  #ifdef MP -->
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					搜索
				</view>
				<!--  #endif -->
				<!--  #ifdef H5 || APP-PLUS -->
				<view class="search-form radius" :style="[{top:StatusBar + 'px'}]">
					<text class="cuIcon-search"></text>
					<input v-model="searchText" :adjust-position="false" type="text" placeholder="搜索一下" confirm-type="search" @input="onSearchInput" @confirm="searchTag"></input>
					<view class="search-close" v-if="searchText!=''" @tap="searchClose()"><text class="cuIcon-close"></text></view>
				</view>
				<view class="action">
					<view class="cu-btn" @tap="searchTag()" style="font-size: 26upx;height: 62upx;border-radius: 100upx;padding: 0 24upx;color: #42c9a4;border: 1px solid #42c9a4;background-color: #fff;">搜索</view>
				</view>
				<!--  #endif -->
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="all-box">
			<!--  #ifdef MP -->
			<view class="cu-bar search" :class="isDark?'dark':'bg-white'" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}">
				<view class="search-form round">
					<text class="cuIcon-search"></text>
					<input type="text" :placeholder="sousuok" v-model="searchText" @input="onSearchInput" @confirm="searchTag"></input>
					<view class="search-close" v-if="searchText!=''" @tap="searchClose()"><text class="cuIcon-close"></text></view>
				</view>
			</view>
			<!--  #endif -->
			
			<!-- 搜索记录和推荐搜索 -->
			<view class="search-history-box" :class="isDark?'dark':'bg-white'" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}" v-if="showSearchHistory">
				<!-- 搜索记录 -->
				<view class="search-history">
					<view class="search-history-header">
						<view class="search-history-title">
							<text class="tn-icon-time text-grey"></text>
							<text class="text-bold margin-left-sm" :style="{'color':isDark?'#fff':'#333'}">搜索记录</text>
						</view>
						<view class="search-history-clear" @tap="clearSearchHistory" v-if="searchHistory.length > 0">
							<text class="tn-icon-delete text-grey"></text>
						</view>
						
					</view>
					<view class="search-history-content" v-if="searchHistory.length > 0">
						<view class="search-history-item" :style="{'background-color':isDark?'#2c2c2c':'#f8f9fa'}" v-for="(item, index) in searchHistory" :key="index" @tap="selectSearchHistory(item)">
							<text class="search-history-text" :style="{'color':isDark?'#ccc':'#666'}">{{item}}</text>
							<text class="tn-icon-close search-history-delete" :style="{'color':isDark?'#999':'#999'}" @tap.stop="deleteSearchHistory(index)"></text>
						</view>
					</view>
					<view class="text-center text-grey margin-lg" v-else>暂无搜索记录</view>
				</view>
				
				<!-- 推荐搜索 -->
				<view class="search-recommend" v-if="recommendList.length > 0">
					<view class="search-recommend-header">
						<text class="tn-icon-fire text-orange"></text>
						<text class="text-bold margin-left-sm" :style="{'color':isDark?'#fff':'#333'}">推荐搜索</text>
					</view>
					<view class="search-recommend-content">
						<view class="search-recommend-item" :style="{'background-color':isDark?'#2c2c2c':'#e4fff8','border-color':isDark?'#42c9a4':'#42c9a4'}" v-for="(item, index) in recommendList" :key="index" @tap="selectRecommend(item)">
							<text class="search-recommend-text" :style="{'color':isDark?'#42c9a4':'#42c9a4'}">{{item}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="search-type grid" :class="sy_appbox?'col-5':'col-4'" v-if="!isuser && !showSearchHistory">
				<view class="search-type-box" v-if="sy_appbox&&appModOrder==1" @tap="toType(5)" :class="type==5?'active':''">
					<text>应用</text>
				</view>
				<view class="search-type-box" v-if="wzof==1&&modOrder==2" @tap="toType(0)" :class="type==0?'active':''">
					<text>文章</text>
				</view>
				<view class="search-type-box" v-if="tzof==1"  @tap="toType(4)" :class="type==4?'active':''">
					<text>帖子</text>
				</view>
				<view class="search-type-box" v-if="wzof==1&&modOrder==1" @tap="toType(0)" :class="type==0?'active':''">
					<text>文章</text>
				</view>
				<view class="search-type-box" v-if="sy_appbox&&appModOrder==0" @tap="toType(5)" :class="type==5?'active':''">
					<text>应用</text>
				</view>
				<view class="search-type-box" @tap="toType(3)" :class="type==3?'active':''">
					<text>动态</text>
				</view>
				<view class="search-type-box" @tap="toType(2)" :class="type==2?'active':''">
					<text>用户</text>
				</view>
			</view>
			<view class="cu-card article no-card tn-padding-right-sm tn-padding-left-sm" v-if="type==0 && !showSearchHistory">
				<block v-for="(item,index) in contentsList" :key="index" v-if="type==0&&actStyle==1">
					<articleItemA :item="item"></articleItemA>
				</block>
				<block v-for="(item,index) in contentsList" :key="index" v-if="type==0&&actStyle==2">
					<articleItemB :item="item"></articleItemB>
				</block>
				
				<view class="load-more" @tap="loadMore" v-if="contentsList.length>0">
					<text>{{moreText}}</text>
				</view>
				<view class="no-data" v-if="contentsList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有数据
				</view>

			</view>
			
			<!--评论-->
			<!-- <view class="cu-list menu-avatar" v-if="type==1">
				<view class="no-data" v-if="commentsList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有评论
				</view>
				<view class="cu-card dynamic no-card" style="margin-top: 20upx;">
					<block  v-for="(item,index) in commentsList" :key="index" v-if="commentsList.length>0">
						<commentItem :item="item"></commentItem>
					</block>
				</view>
				
				<view class="load-more" @tap="loadMore" v-if="commentsList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view> -->
			<!--评论结束-->
			<!--动态-->
			<view class="search-space" v-if="type==3 && !showSearchHistory">
				<view class="no-data" v-if="spaceList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有动态
				</view>
				<spaceItem :spaceList="spaceList" :isDark="isDark"></spaceItem>
				<view class="load-more" @tap="loadMore" v-if="spaceList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
			
			<!--动态结束-->
			<!--论坛帖子-->
			<view class="forum-list-main" v-if="type==4 && !showSearchHistory">
				<view class="no-data" v-if="postList.length==0">
					<text class="cuIcon-text"></text>暂时没有数据
				</view>
				<block v-for="(item,index) in postList" :key="index">
					<forumItem :item="item" :myPurview="0"></forumItem>
				</block>
				<view class="load-more" @tap="loadMore" v-if="postList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
			<!--论坛帖子结束-->
			<view class="cu-list menu-avatar userList" style="margin-top: 4upx;" v-if="(type==2 && !showSearchHistory)||isuser">
				<view class="no-data" v-if="userList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有数据
				</view>
				<view class="cu-item" v-for="(item,index) in userList" :key="index" @tap="toUserContents(item)">
					<!-- 保留原有头像显示 -->
					<view class="cu-avatar round lg" :style="item.style">
						<!-- 添加头像框 -->
						<image v-if="xqy_avatarframe && item.AvatarItem" style="width:110rpx;height:110rpx;border-radius:0px;z-index: 90;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%) scale(1.1);max-width: initial;" :src="item.AvatarItem" mode="aspectFit"></image>
					</view>
					<view class="content">
						<view class="text-grey" v-if="item.screenName">{{item.screenName}}
							<!-- 添加勋章显示 -->
							<medalItem v-if="item.uid" :uid="item.uid"></medalItem>
						</view>
						<view class="text-grey" v-else>{{item.name}}
							 
						</view>
						<view class="text-gray text-sm flex">
							<view class="text-cut">
								{{subText(item.introduce,100)}}
							</view> </view>
					</view>
					<view class="action goUserIndex">
						<view class="cu-btn bg-gradual-orange" style="font-size: 26upx;height: 55upx;border-radius: 100upx;">主页</view>
					</view>
				</view>
				<view class="load-more" @tap="loadMore">
					<text>{{moreText}}</text>
				</view>
			
			</view>
			<!--用户结束-->
			<!-- 添加应用列表部分 -->
			<view :class="isDark?'dark':'bg-white'" :style="{'background-color':isDark?'#1c1c1c':'#ffffff'}" v-if="type==5 && !showSearchHistory">
				<view class="no-data" v-if="appList.length==0">
					<text class="cuIcon-text"></text>
					暂时没有应用
				</view>
				<block v-else>
					<view class="app-box" :style="{'padding': '20rpx','border-bottom-color':isDark?'#2c2c2c':'#f5f5f5'}" v-for="(item, index) in appList" :key="index">
						<view class="app-box-body" @tap="toAppInfo(item.id)">
							<view class="app-box-logo">
								<u-image :src="item.logo" width="110rpx" height="110rpx" mode="aspectFill" 
									:lazy-load="true" :fade="true" duration="450" border-radius="28rpx">
									<u-loading slot="loading"></u-loading>
								</u-image>
							</view>
							<view class="app-box-content">
								<view class="app-box-title text-cut" :style="{'color':isDark?'#fff':'#333'}">{{item.name}}</view>
								<view class="app-box-info" :style="{'color':isDark?'#ccc':'#666'}">
									<text :style="{color: item.tagInfo.color}" 
										:class="item.score>=3?'tn-icon-star-fill':'tn-icon-star'"></text>
									<text :style="{color: item.tagInfo.color}">{{item.score}}</text>
									<text>{{item.size}}</text>
									<text>v{{item.version}}</text>
									<text :class="item.system=='ios'?'tn-icon-iphone':''"></text>
								</view>
								<view class="app-box-tags">
									<text class="app-tag" 
										:style="{backgroundColor: item.tagInfo.color}">{{item.tagInfo.text}}</text>
									<text v-for="(category, idx) in item.sortJson" :key="idx" 
										class="app-category-tag" :style="{'background-color':isDark?'#2c2c2c':'#f5f5f5','color':isDark?'#ccc':'#666666'}">{{category.name}}</text>
								</view>
							</view>
						</view>
						<view class="app-box-down" @tap="toAppInfo(item.id)">下载</view>
					</view>
				</block>
				<view class="load-more" @tap="loadMore" v-if="appList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
		</view>
		
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0||changeLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
		<view class="full-noLogin" v-if="isuserlogin">
			<view class="full-noLogin-main">
				<view class="full-noLogin-text">
					您需要登录后才可以查看内容哦！
				</view>
				<view class="full-noLogin-btn">
					<view class="cu-btn bg-blue" @tap="goLogin()">
						立即登录
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import avatarItem from '../components/avatarItem.vue'
	import medalItem from '../components/medalItem.vue'
	export default {
		mixins: [darkModeMixin],
		components: {
			avatarItem,
			medalItem
		},
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
			AppStyle:this.$store.state.AppStyle,
				
				contentsList:[],
				submitStatus1:false,
				submitStatus2:false,
				commentsList:[],
				isuserlogin:false,
				sousuok:"",
				userList:[],
				
				spaceList:[],
				actStyle: 0,
				postList:[],
				searchText:"",
				wzof:0,
				tzof:0,
				modOrder:0,
				appModOrder:0,
				type:4,
				isuser:false,
				sy_appbox:false,
				xqy_avatarframe:false, // 头像框插件状态
				page:1,
				moreText:"加载更多",
				
				isLoad:0,
				
				isLoading:0,
				
				changeLoading:1,
				appList: [], // 应用列表数据
				tagMap: {
					1: {
						text: '搬运',
						color: '#7c72ff'
					},
					2: {
						text: '原创',
						color: '#19be6b'
					},
					3: {
						text: '金标',
						color: '#ff6600'
					},
					4: {
						text: '官方',
						color: '#2979ff'
					}
				},
				// 搜索记录相关
				searchHistory: [],
				recommendList: [],
				showSearchHistory: true,
				hasSearched: false
			}
		},
		onPullDownRefresh(){
			var that = this;
			that.page=1;
			that.moreText="加载更多";
			that.isLoad=0;
			if(that.type==0){
				that.getContentsList(false);
			}else if(that.type==1){
				that.getCommentsList(false)
			}else if(that.type==2){
				that.getUserList(false)
			}else if(that.type==4){
				that.getPostList(false)
			}else if(that.type==5&&that.sy_appbox){
				that.getAppList(false)
			}else {
				that.getSpaceList(false)
			}
			var timer = setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000)
			
		},
		onShow(){
			var that = this;
			
			
			
		},
		onReachBottom() {
		    //触底后执行的方法，比如无限加载之类的
			var that = this;
			if(that.isLoad==0){
				that.loadMore();
			}
			
		},
		onLoad(res) {
			var that = this;
			that.type = res.type;
			
			// 初始化搜索记录
			that.initSearchHistory();
			
			that.getGongg();
			var cachedPlugins = localStorage.getItem('getPlugins');
			if (cachedPlugins) {
				var pluginList = JSON.parse(cachedPlugins);
				// 检查插件是否存在于插件列表中
				// #ifdef APP-PLUS || H5
				that.sy_appbox = pluginList.includes('sy_appbox');
				// #endif
				// 检查头像框插件是否启用
				that.xqy_avatarframe = pluginList.includes('xqy_avatar_frame');
			}
			if(res.type==2){
				that.isuser = true
				that.type = 2;
				that.getUserList(false)
			}else{
				if(that.sy_appbox){
					that.getAppBoxInfo();
				}else{
					that.getSetCC()
				}
			}

			
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			

			that.page=1;
			that.moreText="加载更多";
			that.isLoad=0;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			
			// 如果没有传入type参数，默认显示搜索记录
			if(!res.type) {
				that.showSearchHistory = true;
				that.hasSearched = false;
			} else {
				// 如果有type参数，说明是从其他页面跳转过来的，直接显示搜索结果
				that.showSearchHistory = false;
				that.hasSearched = true;
				that.getContentsList(false);
				that.getPostList(false);
			}
			
		},
		methods: {
			toType(i){
				var that = this;
				that.type=i;
				that.page=1;
				that.moreText="加载更多";
				that.isLoad=0;
				if(i==0){
					that.getContentsList(false);
				}else if(i==1){
					that.getCommentsList(false)
				}else if(i==2){
					that.getUserList(false)
				}else if(i==4){
					that.getPostList(false)
				}else if(i==5&&that.sy_appbox){
					that.getAppList(false)
				}else {
					that.getSpaceList(false)
				}
			},
			getSetCC() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.wzof = res.data.wzof;
						that.tzof = res.data.tzof;
						that.actStyle = res.data.topStyle;
						that.modOrder = res.data.modOrder;
						if (that.sy_appbox&&that.appModOrder==1) {
							that.type = 5;
							that.toType(that.type);
							
						}else if (that.tzof==1&&that.wzof==1&&that.modOrder==2) {
							that.type = 0;
							that.toType(that.type);
							
						}else if (that.tzof==1) {
							that.type = 4;
							that.toType(that.type);
						}else {
							that.type = 0;
							that.toType(that.type);
						}
					},
					fail(error) {}
				})
			
			},
			getGongg() {
				var that = this;
				uni.request({
					url: that.$API.SPgonggao(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.sousuok = res.data.sousuok;
						// 处理推荐搜索
						that.processRecommendSearch();
					},
					fail(error) {
					}
				})
			
			},
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			loadMore(){
				var that = this;
				that.moreText="正在加载中...";
				that.isLoad=1;
				if(that.type==0){
					that.getContentsList(true);
				}else if(that.type==1){
					that.getCommentsList(true)
				}else if(that.type==2){
					that.getUserList(true)
				}else if(that.type==4){
					that.getPostList(true)
				}else if(that.type==5&&that.sy_appbox){
					that.getAppList(true)
				}else{
					that.getSpaceList(true)
				}
				
			},
			reload(){
				var that = this;
				if(that.type==0){
					that.getContentsList(false);
				}else if(that.type==1){
					that.getCommentsList(false)
				}else if(that.type==2){
					that.getUserList(false)
				}else if(that.type==4){
					that.getPostList(false)
				}else if(that.type==5&&that.sy_appbox){
					that.getAppList(false)
				}else{
					that.getSpaceList(false)
				}
				
			},
			searchTag(){
				var that = this;
				if(!that.searchText.trim()) {
					uni.showToast({
						title: '请输入搜索内容',
						icon: 'none'
					});
					return;
				}
				
				// 保存搜索记录
				that.saveSearchHistory(that.searchText);
				that.showSearchHistory = false;
				that.hasSearched = true;
				
				that.changeLoading = 0;
				var searchText = that.searchText;
				that.page=1;
				if(that.type==0){
					that.getContentsList(false);
				}else if(that.type==1){
					that.getCommentsList(false)
				}else if(that.type==2){
					that.getUserList(false)
				}else if(that.type==4){
					that.getPostList(false)
				}else if(that.type==5&&that.sy_appbox){
					that.getAppList(false)
				}else{
					that.getSpaceList(false)
				}

			},
			searchClose(){
				var that = this;
				that.searchText = "";
				that.showSearchHistory = true;
				that.hasSearched = false;
				that.page=1;
				// 清空搜索结果
				that.contentsList = [];
				that.commentsList = [];
				that.userList = [];
				that.postList = [];
				that.spaceList = [];
				that.appList = [];
			},
			getContentsList(isPage){
				var that = this;
				var data = {
					"type":"post",
				}
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				
				}
				var page = that.page;
				if(isPage){
					page++;
				}
				that.$Net.request({
					url: that.$API.getContentsList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":8,
						"page":page,
						"searchKey":that.searchText,
						"order":"created",
						"token":token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.changeLoading = 1;
						that.isLoad=0;
						that.moreText="加载更多";
						if(res.data.code==1){
							var list = res.data.data;
							if(list.length>0){
								//that.contentsList = list;
								if(isPage){
									that.page++;
									that.contentsList = that.contentsList.concat(list);
								}else{
									that.contentsList = list;
								}
								
								
							}else{
								if(isPage){
									that.moreText="没有更多数据了";
								}else{
									that.contentsList = [];
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						that.changeLoading = 1;
						that.moreText="加载更多";
						that.isLoad=0;
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			loadUserFrame(userList) {
				var that = this;
				// 如果插件未开启，则不加载
				if (!that.xqy_avatarframe) return;
				// 提取所有用户ID
				const uids = userList.map(user => user.uid);
				if (uids.length === 0) return;
				// 请求API
				uni.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						action: 'get_user_avatar_frames',
						uids: JSON.stringify(uids)
					},
					method: 'POST',
					dataType: 'json',
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					success(res) {
						if (res.data.code === 200) {
							const frameData = res.data.data;
							// 将获取到的头像框数据更新到userList中
							that.userList.forEach(user => {
								if (frameData[user.uid]) {
									that.$set(user, 'AvatarItem', frameData[user.uid]);
								}
							});
						} else {
							console.error('获取头像框失败:', res.data.msg);
						}
					},
					fail(err) {
						console.error('请求头像框API失败:', err);
					}
				});
			},
			getPostList(isPage,isLogin){
				var that = this;
				if(that.submitStatus1){
					return false;
				}
				that.submitStatus1 = true;
				var page = that.page;
				var token = "";
				if(!isLogin){
					localStorage.setItem('isbug','1');
				}
				
				if(isPage){
					page++;
				}
				var data = {
					"status":"1",
				}
				that.$Net.request({
					url: that.$API.postList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":10,
						"page":page,
						"searchKey":that.searchText,
						"order":"created",
						"token":token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if(!isLogin){
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						that.changeLoading = 1;
						that.isLoad=0;
						if(res.data.code==1){
							var list = res.data.data;
							if(list.length>0){
								var postList = list;
								for(var i in postList){
									postList[i].isAds = 0;
								}
								if(isPage){
									that.page++;
									that.postList = that.postList.concat(postList);
								}else{
									that.postList = postList;
								}
							}else{
								if(isPage){
									that.moreText="没有更多数据了";
								}else{
									that.postList = [];
								}
								
							}
						} else {
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.isuserlogin = true
								}else{
									that.getPostList(isPage, true);
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						if(!isLogin){
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						that.changeLoading = 1;
						that.moreText="加载更多";
						that.isLoad=0;
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			goLogin() {
				uni.navigateTo({
					url: '/pages/user/login'
				});
			},
			getCommentsList(isPage){
				var that = this;
				var data = {
					"type":"comment",
				}
				var page = that.page;
				if(isPage){
					page++;
				}
				that.$Net.request({
					url: that.$API.getCommentsList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":5,
						"page":page,
						"searchKey":that.searchText,
						"order":"created"
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						that.changeLoading = 1;
						that.isLoad=0;
						if(res.data.code==1){
							var list = res.data.data;
							if(list.length>0){
								var commentsList = [];
								for(var i in list){
									var arr = list[i];
									arr.style = "background-image:url("+list[i].avatar+");"
									commentsList.push(arr);
								}
								if(isPage){
									that.page++;
									that.commentsList = that.commentsList.concat(commentsList);
								}else{
									that.commentsList = commentsList;
								}
							}else{
								that.moreText="没有更多数据了";
							}
							
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						that.changeLoading = 1;
						that.isLoad=0;
						that.moreText="加载更多";
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getUserList(isPage,isLogin){
				var that = this;
				if(that.submitStatus2){
					return false;
				}
				that.submitStatus2 = true;
				var page = that.page;
				if(isPage){
					page++;
				}
				var token = "";
				if(!isLogin){
					localStorage.setItem('isbug','1');
				}
				
				that.$Net.request({
					url: that.$API.getUserList(),
					data:{
						"searchParams":"",
						"limit":10,
						"page":page,
						"searchKey":that.searchText,
						"order":"created",
						"token":token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if(!isLogin){
							localStorage.removeItem('isbug');
						}
						that.submitStatus2 = false;
						that.changeLoading = 1;
						that.isLoad=0;
						if(res.data.code==1){
							var list = res.data.data;
							if(list.length>0){
								
								var userList = [];
								for(var i in list){
									var arr = list[i];
									arr.style = "background-image:url("+list[i].avatar+");"
									arr.AvatarItem = ''; // 初始化为空
									userList.push(arr);
								}
								// 加载用户头像框
								that.loadUserFrame(userList);
								if(isPage){
									that.page++;
									that.userList = that.userList.concat(userList);
								}else{
									that.userList = userList;
								}
							}else{
								that.moreText="没有更多数据了";
							}
						}else{
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.isuserlogin = true
								}else{
									that.getUserList(isPage, true);
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						if(!isLogin){
							localStorage.removeItem('isbug');
						}
						that.submitStatus2 = false;
						that.changeLoading = 1;
						that.isLoad=0;
						that.moreText="加载更多";
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			getSpaceList(isPage){
				var that = this;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"status":1
				}
				var page = that.page;
				if(isPage){
					page++;
				}
				that.$Net.request({
					url: that.$API.spaceList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":10,
						"page":page,
						"order":"created",
						"searchKey":that.searchText,
						"token":token
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						that.changeLoading = 1;
						that.isLoad=0;
						that.moreText="加载更多";
						if(!isPage){
							that.dataLoad = true;
						}
						if(res.data.code==1){
							var list = res.data.data;
							var spaceList = [];
							for(var i in list){
								if(list[i].type==0){
									if(list[i].pic){
										var pic = list[i].pic;
										list[i].picList = pic.split("||");
									}else{
										list[i].picList = [];
									}
									
								}
								if(list[i].type==2){
									if(list[i].forwardJson.pic){
										var pic = list[i].forwardJson.pic;
										list[i].forwardJson.picList = pic.split("||");
									}else{
										list[i].forwardJson.picList = [];
									}
									
								}
							}
							spaceList = list;
							if(list.length>0){
								if(isPage){
									that.page++;
									that.spaceList = that.spaceList.concat(spaceList);
								}else{
									that.spaceList = spaceList;
								}
								
							}else{
								that.moreText="没有更多动态了";
							}
						}
					},
					fail: function(res) {
						
						that.changeLoading = 1;
						that.isLoad=0;
						that.moreText="加载更多";
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			toUserContents(data){
				var that = this;
				var name = data.name;
				var title = data.name+"的信息";
				if(data.screenName){
					title = data.screenName+" 的信息";
					name = data.screenName
				}
				var id= data.uid;
				var type="user";
				uni.navigateTo({
				    url: '/pages/contents/userinfo?title='+title+"&name="+name+"&uid="+id+"&avatar="+encodeURIComponent(data.avatar)
				});
			},
			commentsAdd(title,coid,reply){
				var that = this;
				var cid = that.cid;
				uni.navigateTo({
				    url: '/pages/contents/commentsadd?cid='+cid+"&coid="+coid+"&title="+title+"&isreply="+reply
				});
			},
			subText(text,num){
				if(text.length < null){
					return text.substring(0,num)+"……"
				}else{
					return text;
				}
				
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				// 获取年月日时分秒值  slice(-2)过滤掉大于10日期前面的0
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				//second = ("0" + date.getSeconds()).slice(-2);
				// 拼接
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				// 返回
				return result;
			},
			toInfo(data){
				var that = this;
				
				uni.navigateTo({
				    url: '/pages/contents/info?cid='+data.cid+"&title="+data.title
				});
			},
			toInfoComment(cid,title){
				var that = this;
				
				uni.navigateTo({
				    url: '/pages/contents/info?cid='+cid+"&title="+title
				});
			},
			ToCopy(text) {
				var that = this;
				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: text,
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: "复制成功"
						})
					}
				});
				// #endif
				// #ifdef H5 
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select();
				textarea.setSelectionRange(0, text.length) ;
				uni.showToast({ //提示
					title: "复制成功"
				})
				var result = document.execCommand("copy") 
				textarea.remove();
				
			// #endif
			},
			formatNumber(num) {
			    return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' : num
			},
			replaceSpecialChar(text) {
			  text = text.replace(/&quot;/g, '"');
			  text = text.replace(/&amp;/g, '&');
			  text = text.replace(/&lt;/g, '<');
			  text = text.replace(/&gt;/g, '>');
			  text = text.replace(/&nbsp;/g, ' ');
			  return text;
			},
			subText(text,num){
				if(text){
					if(text.length>num){
						text = text.substring(0,num);
						return text+"……";
					}else{
						return text;
					}
				}else{
					return "Ta还没有个人介绍哦"
				}
			},
			getAppBoxInfo(){
				var that = this;
				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getConfig"
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if(res.data.code == 200) {
							that.appModOrder = res.data.data.appModOrder;
							// that.appTopOf = res.data.data.appTopOf;
							// that.homeModApp = res.data.data.homeModApp;
							// that.appAudit = res.data.data.appAudit;
							// that.scoreAudit = res.data.data.scoreAudit;
						} else {
							console.log(res.data.msg)
						}
						that.getSetCC();
					},
					fail(error) {
						that.apploading = false;
						console.log(error);
						uni.showToast({
							title: "网络开小差了",
							icon: 'none'
						});
					}
				});
			},
			getAppList(isPage) {
				const that = this;
				if(that.submitStatus1){
					return false;
				}
				that.submitStatus1 = true;
				let page = that.page;
				if(isPage){
					page++;
				}
				
				uni.request({
					url: that.$API.PluginLoad('sy_appbox'),
					data: {
						"action": "getAppList",
						"getapp_page": page,
						"getapp_limit": 10,
						"getapp_order": "created",
						"getapp_searchKey": that.searchText
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						that.submitStatus1 = false;
						that.changeLoading = 1;
						that.isLoad = 0;
						if(res.data.code == 200){
							const data = res.data.data || [];
							// 处理返回数据,添加标签信息
							const list = data.map(item => {
								return {
									...item,
									tagInfo: that.tagMap[item.type] || {
										text: '未知',
										color: '#999'
									},
									size: that.formatSize(item.size)
								};
							});
							
							if(list && list.length > 0){
								if(isPage){
									that.page++;
									that.appList = that.appList.concat(list);
								}else{
									that.appList = list;
								}
							}else{
								if(isPage){
									that.moreText = "没有更多应用了";
								}else{
									that.appList = [];
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail(error) {
						that.submitStatus1 = false;
						that.changeLoading = 1;
						that.moreText = "加载更多";
						that.isLoad = 0;
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				});
			},
			toAppInfo(id) {
				uni.navigateTo({
					url: '/pages/plugins/sy_appbox/info?id=' + id
				});
			},
			// 添加文件大小格式化方法
			formatSize(size) {
				if (!size) return '未知大小';
				if (size >= 1024 * 1024) {
					return (size / (1024 * 1024)).toFixed(1) + 'Gb';
				} else if (size >= 1024) {
					return (size / 1024).toFixed(1) + 'Mb';
				} else {
					return size.toFixed(1) + 'Kb';
				}
			},
			// 搜索记录相关方法
			initSearchHistory() {
				var that = this;
				var history = localStorage.getItem('searchHistory');
				if (history) {
					that.searchHistory = JSON.parse(history);
				}
			},
			saveSearchHistory(keyword) {
				var that = this;
				if (!keyword || !keyword.trim()) return;
				
				keyword = keyword.trim();
				// 移除已存在的记录
				var index = that.searchHistory.indexOf(keyword);
				if (index > -1) {
					that.searchHistory.splice(index, 1);
				}
				
				// 添加到开头
				that.searchHistory.unshift(keyword);
				
				// 最多保存10条记录
				if (that.searchHistory.length > 10) {
					that.searchHistory = that.searchHistory.slice(0, 10);
				}
				
				// 保存到本地存储
				localStorage.setItem('searchHistory', JSON.stringify(that.searchHistory));
			},
			selectSearchHistory(keyword) {
				var that = this;
				that.searchText = keyword;
				that.searchTag();
			},
			deleteSearchHistory(index) {
				var that = this;
				that.searchHistory.splice(index, 1);
				localStorage.setItem('searchHistory', JSON.stringify(that.searchHistory));
			},
			clearSearchHistory() {
				var that = this;
				uni.showModal({
					title: '提示',
					content: '确定要清空所有搜索记录吗？',
					success: function(res) {
						if (res.confirm) {
							that.searchHistory = [];
							localStorage.removeItem('searchHistory');
						}
					}
				});
			},
			processRecommendSearch() {
				var that = this;
				if (!that.sousuok || that.sousuok.trim() === '') {
					that.recommendList = [];
					return;
				}
				
				// 处理推荐搜索词
				if (that.sousuok.includes('|')) {
					// 多个推荐词，用|分割
					that.recommendList = that.sousuok.split('|').filter(item => item.trim() !== '');
				} else {
					// 单个推荐词
					that.recommendList = [that.sousuok.trim()];
				}
			},
			selectRecommend(keyword) {
				var that = this;
				that.searchText = keyword;
				that.searchTag();
			},
			onSearchInput() {
				var that = this;
				// 当有输入内容时隐藏搜索记录，没有内容时显示
				if (that.searchText.trim() === '' && !that.hasSearched) {
					that.showSearchHistory = true;
				} else {
					that.showSearchHistory = false;
				}
			}
		}
	}
</script>

<style>
	.app-box {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.app-box-body {
		flex: 1;
		display: flex;
		margin-right: 20rpx;
		min-width: 0;
		align-items: center;
	}

	.app-box-logo {
		width: 110rpx;
		height: 110rpx;
		flex-shrink: 0;
	}

	.app-box-content {
		flex: 1;
		margin-left: 20rpx;
		min-width: 0;
	}

	.app-box-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		width: 400rpx;
	}

	.app-box-info {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;
	}

	.app-box-info text {
		margin-right: 10rpx;
	}

	.app-box-down {
		background-color: #3cc9a4;
		color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 100rpx;
		white-space: nowrap;
	}

	.app-box-tags {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		font-size: 28rpx;
	}

	.app-tag {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		color: #ffffff;
		margin-right: 12rpx;
		font-size: 24rpx;
	}

	.app-category-tag {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		background-color: #f5f5f5;
		color: #666666;
		margin-right: 12rpx;
		font-size: 24rpx;
	}

	.text-cut {
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}
	.dark input,
	.dark textarea {
		color: #c7cddb !important;
		background-color: #2c2c2c !important;
		border-color: #303644 !important;
	}
	/* 搜索记录样式 */
	.search-history-box {
		margin: 20rpx;
		border-radius: 16rpx;
		padding: 30rpx;
	}

	.search-history {
		margin-bottom: 40rpx;
	}

	.search-history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.search-history-title {
		display: flex;
		align-items: center;
		font-size: 32rpx;
	}

	.search-history-clear {
		padding: 10rpx;
	}

	.search-history-content {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.search-history-item {
		display: flex;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 20rpx;
		padding: 12rpx 20rpx;
		max-width: 200rpx;
	}

	.search-history-text {
		font-size: 28rpx;
		color: #666;
		margin-right: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		flex: 1;
	}

	.search-history-delete {
		font-size: 24rpx;
		color: #999;
		padding: 5rpx;
	}

	.search-recommend {
		
	}

	.search-recommend-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 32rpx;
	}

	.search-recommend-content {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.search-recommend-item {
		background-color: #e4fff8;
		border: 1px solid #42c9a4;
		border-radius: 20rpx;
		padding: 12rpx 20rpx;
	}

	.search-recommend-text {
		font-size: 28rpx;
		color: #42c9a4;
	}
</style>
