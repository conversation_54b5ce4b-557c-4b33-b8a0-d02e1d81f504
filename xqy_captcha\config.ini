[plugin]
tokenKey = "123456"
name = "小祈愿-行为验证"
filename = "xqy_captcha"
author = "小祈愿"
version = "100"
enabled = "false"
installed = "false"
isUseMysql = "true"
isUseRedis = "false"
isHaveAdmin = "true"
isH5Debug = "true"
isFree = "true"
[initialize]
request_type = "ALL"
description = "【必要】初始化"
[install]
request_type = "ALL"
description = "【必要】插件安装"
[uninstall]
request_type = "ALL"
description = "【必要】插件卸载"
[getConfig]
request_type = "ALL"
description = "获取验证配置"
[setConfig]
request_type = "ALL"
description = "设置验证配置"
[checkCaptcha]
request_type = "ALL"
description = "验证行为验证码"
[getCaptchaConfig]
request_type = "ALL"
description = "获取前端验证配置"
[getPluginStatus]
request_type = "ALL"
description = "获取插件启用状态"
