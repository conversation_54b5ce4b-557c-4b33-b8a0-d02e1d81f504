<?php
initializeActions();

if ($installed == "false") {
    send_json(402, "未安装无需卸载");
} else {
    $msg = "";
    //【以上勿动】 
    // -------------------已安装，开始执行卸载---------------------

    // 定义需要删除的表
    $tables = [
        'Sy_Plugin_xqy_pretty_number_config',
        'Sy_Plugin_xqy_pretty_numbers',
        'Sy_Plugin_xqy_pretty_number_cooldowns',
        'Sy_Plugin_xqy_pretty_number_prices',
        'Sy_Plugin_xqy_pretty_number_applications',
    ];

    foreach ($tables as $table) {
        $sql_check = "SHOW TABLES LIKE '$table'";
        $result = mysqli_query($connect, $sql_check);

        if (mysqli_num_rows($result) != 0) {
            // 表存在，尝试删除
            $sql_drop = "DROP TABLE `$table`";
            if (mysqli_query($connect, $sql_drop)) {
                $msg .= "删除 $table 表成功。";
            } else {
                $msg .= "删除 $table 表失败：" . mysqli_error($connect) . "。";
            }
        } else {
            $msg .= "$table 表不存在。";
        }
    }

    // -------------------卸载完成，以下勿动---------------------
    
    //【勿动】 将插件安装状态设置为未安装
  $currentDir = __DIR__;
  $configFile = str_replace('Actions', '', $currentDir) . 'config.ini';
  if (file_exists($configFile)) {
    $configData = parse_ini_file($configFile, true);
    $configData['plugin']['installed'] = "false";
    $newConfig = "";
    foreach ($configData as $section => $values) {
      $newConfig .= "[$section]\n";
      foreach ($values as $key => $value) {
        $newConfig .= "$key = \"$value\"\n";
      }
    }
    if (file_put_contents($configFile, $newConfig) !== false) {
      $msg .= "更新配置文件成功。";
    } else {
      $msg .= "更新配置文件失败。";
    }
  } else {
    $msg .= "配置文件不存在。";
  }
  $data['log'] = $msg;
  send_json(200, "插件卸载完成", $data);
}
?>