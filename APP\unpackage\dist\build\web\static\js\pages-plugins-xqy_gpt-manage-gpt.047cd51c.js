(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-plugins-xqy_gpt-manage-gpt"],{"41dd":function(t,e,i){"use strict";i.r(e);var a=i("fb28"),n=i("c9c1");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);var o=i("828b"),c=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"58c916a6",null,!1,a["a"],void 0);e["default"]=c.exports},c3a5:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966"),i("f7a5"),i("aa9c"),i("c223");var a=i("9254"),n={data:function(){return{StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,gptList:[],searchText:"",type:0,page:1,limit:10,moreText:"加载更多",isLoad:0,isLoading:0}},onPullDownRefresh:function(){this.page=1,this.getUserList(!1),setTimeout((function(){uni.stopPullDownRefresh()}),1e3)},onReachBottom:function(){0==this.isLoad&&this.loadMore()},onShow:function(){this.page=1},onLoad:function(){this.getGptList(!1)},methods:{allCache:function(){},back:function(){uni.navigateBack({delta:1})},searchTag:function(){this.searchText;this.page=1,this.getGptList()},setType:function(t){this.type=t,this.page=1,this.getGptList()},searchClose:function(){this.searchText="",this.page=1,this.getGptList()},formatDate:function(t){t=new Date(parseInt(1e3*t));var e=t.getFullYear(),i=("0"+(t.getMonth()+1)).slice(-2),a=("0"+t.getDate()).slice(-2),n=("0"+t.getHours()).slice(-2),s=("0"+t.getMinutes()).slice(-2),o=e+"-"+i+"-"+a+" "+n+":"+s;return o},loadMore:function(){this.moreText="正在加载中...",this.isLoad=1,this.getGptList(!0)},getGptList:function(t){var e=this,i=e.page;t&&i++;var n="";if(a.localStorage.getItem("userinfo")){var s=JSON.parse(a.localStorage.getItem("userinfo"));n=s.token}e.type;e.$Net.request({url:e.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"models",type:e.type,limit:e.limit,search_key:e.searchText,page:i,token:n},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(i){if(e.isLoad=0,i.data&&200==i.data.code){var a=i.data.data&&i.data.data.list?i.data.data.list:[];if(a.length>0){var n=[];for(var s in a){var o=a[s];o.avatar?o.style="background-image:url("+o.avatar+");":o.style="background-image:url(/static/admin/images/ai.png);",n.push(o)}t?(e.page++,e.gptList=e.gptList.concat(n)):e.gptList=n}else t?e.moreText="没有更多数据了":e.gptList=[]}else e.gptList=[],i.data&&i.data.msg&&uni.showToast({title:i.data.msg,icon:"none"});setTimeout((function(){e.isLoading=1,clearTimeout("timer")}),300)},fail:function(t){e.isLoad=0,e.moreText="加载更多";setTimeout((function(){e.isLoading=1,clearTimeout("timer")}),300)}})},toLink:function(t){if(!a.localStorage.getItem("token")||""==a.localStorage.getItem("token"))return uni.showToast({title:"请先登录哦",icon:"none"}),!1;uni.navigateTo({url:t})},subText:function(t,e){return t?t.length>e?(t=t.substring(0,e),t+"……"):t:"管理员暂未设置介绍"},toEdit:function(t){uni.navigateTo({url:"/pages/plugins/xqy_gpt/manage/gptAdd?type=edit&id="+t})},deleteGpt:function(t){var e=this,i="";if(a.localStorage.getItem("userinfo")){var n=JSON.parse(a.localStorage.getItem("userinfo"));i=n.token}uni.showModal({title:"删除将清空该模型所有聊天室和消息",success:function(a){a.confirm?(uni.showLoading({title:"加载中"}),e.$Net.request({url:e.$API.PluginLoad("xqy_gpt"),data:{plugin:"xqy_gpt",action:"deleteModel",id:t,token:i},header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(t){setTimeout((function(){uni.hideLoading()}),1e3),t.data&&t.data.msg?uni.showToast({title:t.data.msg,icon:"none"}):uni.showToast({title:"操作完成",icon:"none"}),t.data&&200==t.data.code&&(e.page=1,e.getGptList())},fail:function(t){setTimeout((function(){uni.hideLoading()}),1e3),uni.showToast({title:"网络开小差了哦",icon:"none"})}})):a.cancel}})}}};e.default=n},c9c1:function(t,e,i){"use strict";i.r(e);var a=i("c3a5"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},fb28:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"user",class:t.$store.state.AppStyle},[a("v-uni-view",{staticClass:"header",style:[{height:t.CustomBar+"px"}]},[a("v-uni-view",{staticClass:"cu-bar bg-white",style:{height:t.CustomBar+"px","padding-top":t.StatusBar+"px"}},[a("v-uni-view",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"cuIcon-back"})],1),a("v-uni-view",{staticClass:"content text-bold",style:[{top:t.StatusBar+"px"}]},[t._v("大模型管理")]),a("v-uni-view",{staticClass:"action"},[a("v-uni-button",{staticClass:"cu-btn round bg-blue",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toLink("/pages/plugins/xqy_gpt/manage/gptAdd")}}},[t._v("新增")])],1)],1)],1),a("v-uni-view",{style:[{padding:t.NavBar+"px 10px 0px 10px"}]}),a("v-uni-view",{staticClass:"cu-list menu-avatar userList",staticStyle:{"margin-top":"20upx"}},[a("v-uni-view",{staticClass:"cu-bar bg-white search"},[a("v-uni-view",{staticClass:"search-form round"},[a("v-uni-text",{staticClass:"cuIcon-search"}),a("v-uni-input",{attrs:{type:"text",placeholder:"输入搜索关键字"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.searchTag.apply(void 0,arguments)}},model:{value:t.searchText,callback:function(e){t.searchText=e},expression:"searchText"}}),""!=t.searchText?a("v-uni-view",{staticClass:"search-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.searchClose()}}},[a("v-uni-text",{staticClass:"cuIcon-close"})],1):t._e()],1)],1),a("v-uni-view",{staticClass:"search-type grid col-2"},[a("v-uni-view",{staticClass:"search-type-box",class:0==t.type?"active":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setType(0)}}},[a("v-uni-text",[t._v("聊天GPT")])],1),a("v-uni-view",{staticClass:"search-type-box",class:1==t.type?"active":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setType(1)}}},[a("v-uni-text",[t._v("AI应用")])],1)],1),0==t.gptList.length?a("v-uni-view",{staticClass:"no-data"},[a("v-uni-text",{staticClass:"cuIcon-text"}),t._v("暂时没有数据")],1):t._e(),t._l(t.gptList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"cu-item"},[a("v-uni-view",{staticClass:"cu-avatar round lg",style:e.style}),a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"text-black"},[t._v(t._s(e.name))]),a("v-uni-view",{staticClass:"text-gray text-sm flex"},[a("v-uni-view",{staticClass:"text-cut"},[t._v(t._s(t.subText(e.intro,100)))])],1)],1),a("v-uni-view",{staticClass:"action user-list-btn"},[a("v-uni-view",{staticClass:"cu-btn text-red radius",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.deleteGpt(e.id)}}},[a("v-uni-text",{staticClass:"cuIcon-deletefill"})],1),a("v-uni-view",{staticClass:"cu-btn text-blue radius",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toEdit(e.id)}}},[a("v-uni-text",{staticClass:"cuIcon-post"})],1)],1)],1)})),t.gptList.length>=t.limit?a("v-uni-view",{staticClass:"load-more",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[a("v-uni-text",[t._v(t._s(t.moreText))])],1):t._e()],2),0==t.isLoading?a("v-uni-view",{staticClass:"loading"},[a("v-uni-view",{staticClass:"loading-main"},[a("v-uni-image",{attrs:{src:i("0b62")}})],1)],1):t._e()],1)},n=[]}}]);