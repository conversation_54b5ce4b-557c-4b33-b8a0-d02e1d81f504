<template>
	<view class="user" :class="$store.state.AppStyle">
		<!-- 顶部导航栏 -->
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back text-black"></text>
				</view>
				<view class="content text-black" :style="[{top:StatusBar + 'px'}]">
					蓝V认证
				</view>
			</view>
		</view>

		<view class="main-content" :style="[{paddingTop: NavBar+5 + 'px'}]">
			<!-- 认证状态卡片 -->
			<view class="auth-card">
				<view class="auth-header">
					<view class="auth-title">
						<text class="title-text">蓝V认证</text>
						<view class="status-tag" :class="{
							'verified': identifyCompany==1,
							'pending': identifyCompany==-1,
							'unverified': identifyCompany==0
						}">
							<text v-if="identifyCompany==1">已认证</text>
							<text v-else-if="identifyCompany==-1">审核中</text>
							<text v-else @tap="toLink('/pages/identify/company')">立即申请</text>
						</view>
					</view>
					<view class="auth-desc">
						在认证完成后，您将获得蓝V标识。
					</view>
				</view>
			</view>

			<!-- 认证要求区域 -->
			<view class="requirement-section">
				<view class="requirement-title">认证要求</view>
				<view class="requirement-content" v-html="lvtext"></view>
			</view>
		</view>

		<!-- 加载动画 -->
		<view class="loading-mask" v-if="isLoading==0">
			<view class="loading-wrapper">
				<image src="../../static/loading.gif" mode="aspectFit"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				
				uid:0,
				
				identifyCompany:0,
				identifyConsumer:0,
				lvtext:"",
				userInfo:"",
				token:'',
				isLoading:0,
				
				
			}
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			if(localStorage.getItem('token')){
				
				that.token = localStorage.getItem('token');
			}
			that.getCacheInfo();
			that.identifyStatus();
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
		},
		mounted() {
			this.getset()
		},
		methods: {
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			getset() {
			  var that = this;
			      uni.request({
			        url:that.$API.SPset(),
			        method:'GET',
			        data:{
			          id:1
			        },
			        dataType:"json",
			        success(res) {
					    that.lvtext = res.data.lvtext;
			        },
			        fail(error) {
			          console.log(error);
			        }
			      })
			},
			getCacheInfo(){
				var that = this;
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.uid=userInfo.uid;
					that.userInfo = userInfo;
				}
			},
			toLink(text){
				var that = this;
				
				if(!localStorage.getItem('token')||localStorage.getItem('token')==""){
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			identifyStatus() {
				var that = this;
				that.$Net.request({
					
					url: that.$API.identifyStatus(),
					data:{
						"uid":that.uid
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						
						if(res.data.code==1){
							
							that.identifyCompany = res.data.data.identifyCompany;
							that.identifyConsumer = res.data.data.identifyConsumer;
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
		}
	}
</script>

<style>
.main-content {
	padding: 0 24rpx;
	background-color: #f8f9fa;
	min-height: 100vh;
}

.auth-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}

.auth-header {
	position: relative;
}

.auth-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.status-tag {
	padding: 8rpx 24rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	transition: all 0.3s ease;
}

.status-tag.verified {
	background: rgba(52, 199, 89, 0.1);
	color: #34c759;
}

.status-tag.pending {
	background: rgba(255, 149, 0, 0.1);
	color: #ff9500;
}

.status-tag.unverified {
	background: rgba(0, 122, 255, 0.1);
	color: #007aff;
}

.status-tag.unverified text {
	position: relative;
}

.status-tag.unverified text:active {
	opacity: 0.8;
}

.auth-desc {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
	margin-top: 8rpx;
}

.requirement-section {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}

.requirement-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 20rpx;
	position: relative;
	padding-left: 16rpx;
}

.requirement-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4rpx;
	height: 24rpx;
	background: #007aff;
	border-radius: 2rpx;
}

.requirement-content {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.8;
}

.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.loading-wrapper {
	width: 120rpx;
	height: 120rpx;
}

.loading-wrapper image {
	width: 100%;
	height: 100%;
}

/* 顶部导航栏 */
.header {
	background: #ffffff;
	position: fixed;
	width: 100%;
	z-index: 100;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}

.cu-bar {
	background: transparent;
}

.cu-bar .content {
	font-size: 32rpx;
	font-weight: 500;
}

.cuIcon-back {
	font-size: 36rpx;
}
</style>
