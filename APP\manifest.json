{
    "name" : "小祈愿",
    "appid" : "__UNI__64F04FD",
    "description" : "在忙碌的生活中寻找一片宁静之地。小祈愿社区是一个温暖的在线平台，专为那些希望用简单而美好的方式表达心愿的人们设计。无论您是想为亲人朋友送上祝福，还是为自己许下心愿，这里都是最佳选择。通过个性化设置，您可以定制属于自己的祈愿场景，如温馨的生日祝福、励志的学习加油等。此外，我们还鼓励用户之间相互支持和鼓励，共同构建一个充满爱与希望的社区。加入我们，让每一天都有新的美好开始。",
    "versionName" : "1.3.9",
    "versionCode" : 139,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Barcode" : {},
            "Camera" : {},
            "VideoPlayer" : {},
            "OAuth" : {},
            "Push" : {},
            "Share" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "payment" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "push" : {
                    "unipush" : {
                        "icons" : {
                            "small" : {
                                "xxhdpi" : "C:/Users/<USER>/Pictures/logo/小祈愿/tw1dj-zo5pi.png"
                            }
                        }
                    }
                },
                "share" : {
                    "qq" : {
                        "appid" : "102111918",
                        "UniversalLinks" : ""
                    }
                },
                "ad" : {},
                "oauth" : {
                    "univerify" : {},
                    "qq" : {
                        "appid" : "102111918",
                        "UniversalLinks" : ""
                    }
                },
                "statics" : {}
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "android" : {
                    "hdpi" : "D:/桌面/祈愿社区/android/mipmap-hdpi/ic_launcher.png",
                    "xhdpi" : "D:/桌面/祈愿社区/android/mipmap-xhdpi/ic_launcher.png",
                    "xxhdpi" : "C:/Users/<USER>/Pictures/logo/小祈云/新/广告启动图.png"
                },
                "useOriginalMsgbox" : true
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "uniStatistics" : {
            "enable" : false
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxa6108b73d2c1880d",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "uniStatistics" : {
        "enable" : false,
        "version" : "2"
    },
    "vueVersion" : "2",
    "h5" : {
        "title" : "小祈愿",
        "router" : {
            "base" : "/h5/",
            "mode" : "hash"
        },
        "uniStatistics" : {
            "enable" : false
        },
        "unipush" : {
            "enable" : false
        }
    },
    "fallbackLocale" : "zh-Hans",
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : false
        }
    }
}
