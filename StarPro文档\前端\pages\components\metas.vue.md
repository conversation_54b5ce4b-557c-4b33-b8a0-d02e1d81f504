# metas.vue 前端分析
## 文件信息
- **文件路径**：`APP前端部分/pages/components/metas.vue.md`
- **组件说明**：推测此组件用于展示和处理分类（Category）或标签（Tag）相关的数据和交互。

---

<template>
	<view class="userpost" :class="AppStyle">
		<!-- 用户榜 -->
		<block v-if="isshow">
		<view class="tn-margin-top-sm"></view>
		<block v-if="userlist_of==1">
		<view @tap="toUserList">
		  <view class="tn-flex tn-flex-row-between tn-round tn-padding-xs tn-margin" style="background: #71e0b7;color: #fff;align-items: center;">
		    <view class="justify-content-item tn-text-center tn-flex" style="background-color: transparent;">
				<tn-avatar-group :lists="latestUserAvatar" :txNum="4" size="sm"></tn-avatar-group> 
		      <text class="tn-padding-xs">共{{getNumber(usercount+userxn)}}人</text>
		    </view>
		   <view class="justify-content-item tn-text-center tn-flex" style="background-color: transparent;">
		     <text class="tn-padding-xs tn-text-bold">全部用户</text>
		   </view>
		    <view class="justify-content-item tn-text-center tn-flex" style="background-color: transparent;">
		       <text class="tn-icon-right"></text>
		    </view>
		  </view>
		</view>
		</block>
		<block v-if="quanzi_style==1">
		<view class="container" style="padding: 5px;">
		    <view class="ghj234">
		      <block v-for="(item, index) in topic" :key="index">
		        <view class="klm098" @tap="toCategoryContents(item.name,item.mid,item.imgurl)">
		          <view class="poi321">
		            <view class="image-picbook" v-if="item.imgurl" :style="'background-image:url(' + item.imgurl + ')'"></view>
					<view class="image-picbook" v-else :style="'background-image:url(' + 'https://starimg.illlt.com/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png' + ')'"></view>
		            <view class="tn-blogger-content__label">
		              <text class="tn-blogger-content__label__desc clamp-text-1">{{ item.name }}</text>
					  <text class="tn-color-gray clamp-text-1" v-if="item.description">{{ item.description}}</text>
					   <text class="tn-color-gray clamp-text-1" v-else>暂无描述</text>
		            </view>
		          </view>
		        </view>
		      </block>
		  </view>
		   </view>
		   </block>
		<block v-if="quanzi_style==2">
		<view class="tn-flex tn-flex-wrap tn-margin-sm">
				  <block v-for="(item, index) in topic" :key="index">
				    <view class="" style="width: 50%;"  @tap="toCategoryContents(item.name,item.mid,item.imgurl)">
				      <view class="tn-blogger-content__wrap" style="background-color: rgba(255,255,255,0.6);">
				        <view class="image-picbook" v-if="item.imgurl" :style="'background-image:url(' + item.imgurl + ')'">
				          <view class="image-book">
				          </view>
				        </view> 
				        <view class="image-picbook" v-else :style="'background-image:url(' + 'https://starimg.illlt.com/upload/2024/1/26/d48af2f1-5933-46a8-89d9-714e9322aa43.png' + ')'">
				          <view class="image-book">
				          </view>
				        </view> 
				        <view class="tn-blogger-content__label tn-text-justify" style="padding: 10px 10px 0px 10px;">
				          <text class="tn-blogger-content__label__desc clamp-text-1">{{ item.name }}</text>  
				        </view>
						
						<view class="tn-blogger-content__label tn-text-justify" style="padding: 0px 10px 10px 10px;">
						 <text class="tn-text-sm tn-blogger-content__label__desc tn-color-gray clamp-text-1" v-if="item.description">{{ item.description }}</text>
						  <text class="tn-text-sm tn-blogger-content__label__desc tn-color-gray clamp-text-1" v-else>暂时还没有设置分类介绍哦~</text> 
						</view>
				      </view>
				    </view>
				  </block>
				</view>
		</block>
		</block>
		<block v-else>
			<view class="loading">
				<view class="loading-main">
					<image src="../../static/loading.gif"></image>
				</view>
			</view>
		</block>
	</view>
	
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		props: {
			topic: {
				type: Array,
				default:() => []
			},
			moreText: {
				type: String,
				default: ""
			},
			isLoading: {
				type: Number,
				default: 1
			},
			name: "metas",
		},
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				// moreText:"加载更多",
				page: 1,
				quanzi_style: 1,
				userlist_of: 1,
				userlist_all: 0,
				mid: 0,
				isshow: false,
				userxn: 0,
				islogin: false,
				usercount: "0",
				sy_article: false,
				latestUserAvatar: [],
			}
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onShow(){
			var that = this;
			that.$emit('loadMore')
			
			// #ifdef APP-PLUS
			
			plus.navigator.setStatusBarStyle("dark")
			// #endif
			
		},
		onLoad(){
			var that = this;
			
		},
		onReachBottom() {
		    //触底后执行的方法，比如无限加载之类的
			var that = this;
			
			that.loadMore();
		},
		mounted() {
			var that = this;
			this.$emit('loadMore')
			if (localStorage.getItem('getPlugins')) {
				var cachedPlugins = localStorage.getItem('getPlugins');
				if (cachedPlugins) {
					const pluginList = JSON.parse(cachedPlugins);
					that.sy_article = pluginList.includes('sy_article');
				}
			}
			if(that.sy_article){
				that.getPluginConfig();
			}else{
				that.getContinuous()
			}
			that.getLatestUsers();
			// #ifdef APP-PLUS || MP || H5
			that.NavBar = this.CustomBar;
			// #endif
			// that.getTopPic(false);
		},
		methods: {
			toUserList() {
				uni.navigateTo({
					url: '/pages/user/userlist'
				})
			},
			
			getLatestUsers() {
				const that = this;
				that.$Net.request({
					url: that.$API.getUserList(),
					data: {
						"searchParams": "",
						"limit": 4,
						"page": 1,
						"searchKey": "",
						"order": "created"
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if(res.data.code == 1) {
							const users = res.data.data;
							that.latestUserAvatar = users.map(user => ({
								userJson: {
									avatar: user.avatar
								}
							}));
						}
					}
				})
			},
			
			getContinuous() {
				var that = this;
				uni.request({
					url: that.$API.SPuser(),
					method:'GET',
					dataType:"json",
					success(res) {
						that.usercount = res.data.usercount;
						that.isshow = true;
					},
					fail(error) {
						console.log(error);
						that.isshow = true;
					}
				})
			},
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			getNumber(){
				let total = this.userxn + parseInt(this.usercount);
				if(total >= 10000){
					return (total/10000).toFixed(2) + 'w';
				}else if(total >= 1000){
					return (total/1000).toFixed(2) + 'k'; 
				}
				return total;
			},
			loadMore(){
				var that = this;
				// that.moreText="加载中...";
				// if(that.isLoad==0){
				// 	that.getTopPic(true);
				// }
				this.$emit('loadMore')
			},
			getTopPic(isPage){
				var that = this;
				var data = {
					"isrecommend":"1"
				}
				var page = that.page;
				if(isPage){
					page++;
				}
				that.$Net.request({
					url: that.$API.getMetasList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":20,
						"order":"order",
						"page":page,
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isLoading=1;
						that.isLoad=0;
						if(res.data.code==1){
							var list = res.data.data;
							if(list.length>0){
								var Topic = list;
								if(isPage){
									that.page++;
									that.Topic = that.Topic.concat(Topic);
								}else{
									that.Topic = Topic;
								}
							}else{
								that.moreText="没有更多数据了";
							}
							
						}
					},
					fail: function(res) {
						that.isLoading=1;
						that.isLoad=0;
						that.moreText="加载更多";
					}
				})
			},
			replaceSpecialChar(text) {
			  text = text.replace(/&quot;/g, '"');
			  text = text.replace(/&amp;/g, '&');
			  text = text.replace(/&lt;/g, '<');
			  text = text.replace(/&gt;/g, '>');
			  text = text.replace(/&nbsp;/g, ' ');
			  return text;
			},
			toCategoryContents(title,id){
				var that = this;
				var type="meta";
				uni.navigateTo({
				    url: '/pages/contents/contentlist?title='+title+"&type="+type+"&id="+id
				});
			},
			getPluginConfig() {
				const that = this;
				uni.request({
					url: that.$API.PluginLoad('sy_article'),
					data: {
						"action": "getConfig"
					},
					method: 'GET',
					dataType: "json",
					success(res) {
						if(res.data.code == 200) {
							that.quanzi_style = res.data.data.quanzi_style || 1;
							that.userlist_of = res.data.data.userlist_of || 1;
							that.userlist_all = res.data.data.userlist_all || 0;
							that.userxn = res.data.data.userxn || 0;
							that.getContinuous();
							
						}
					},
					fail(error) {
						console.log(error);
						that.getContinuous();
					}
				});
			},
		}
	}
</script>

<style scoped>
	.margin-top-sm{
		/* margin-top: 0 ; */
		/* margin-bottom: 10px; */
	}
	.image-book{
	  padding: 150rpx 0rpx;
	  font-size: 16rpx;
	  font-weight: 300;
	  position: relative;
	}
	.image-picbook{
	  background-size: cover;
	  background-repeat:no-repeat;
	  background-position:top;
	  border-radius: 15rpx 15rpx 0 0;
	}
	.tn-blogger-content__wrap{
		box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.07);
		border-radius: 10px;
		margin: 7px;
	}
	.data-box{
		background: #f6f6f6;
	}
	.tn-flex{
		margin: 10px;
	}
	/* 为class属性添加偏僻词 */

	.container {
	margin-left: 14px;
	  padding: 1px;
	  border-radius: 10px;
	}
	/* 用偏僻词替换class属性 */
	.ghj234 {
	  display: flex;
	  flex-wrap: wrap;
	}
	
	/* 文字截取*/
	.clamp-text-1 {
		-webkit-line-clamp: 1;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}
	
	.clamp-text-2 {
		-webkit-line-clamp: 2;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}
	.ghj234 .klm098 {
	  width: 46%;
	  box-sizing: border-box;
	  padding: 10px;
	  background-color: #fff;
	  border-radius: 10px;
	  box-shadow: 0px 5px 5px rgb(207 207 207 / 30%);
	  margin-bottom: 15px;
	  margin-right: 8px;
	}
	
	
	
	.ghj234 .klm098 .poi321 {
	  display: flex;
	  align-items: center;
	}
	
	.ghj234 .klm098 .poi321 .image-picbook {
	  width: 50px;
	  height: 50px;
	  background-size: cover;
	  border-radius: 50%;
	  margin-right: 10px;
	}
	
	.ghj234 .klm098 .poi321 .tn-blogger-content__label {
	  display: flex;
	  flex-direction: column;
	  justify-content: center;
	}
	
	.ghj234 .klm098 .poi321 .tn-blogger-content__label .tn-blogger-content__label__desc {
	  font-size: 15px;
	  font-weight: bold;
	  margin-bottom: 5px;
	}
	
	.ghj234 .klm098 .poi321 .tn-blogger-content__label .tn-color-gray {
	  font-size: 10px;
	}
	
	/* 避免描述过长排版会乱 */
	.ghj234 .klm098 .poi321 .tn-color-gray {
 white-space: nowrap; /* 不换行 */
     overflow: hidden; /* 超出隐藏 */
     text-overflow: ellipsis; /* 显示省略号 */
     display: inline-block; /* 显示为行内块元素 */
  max-width: 8em; 
	}
</style>
