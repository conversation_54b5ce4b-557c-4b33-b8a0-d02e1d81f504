# postReview.vue 文件分析

## 文件信息
- **文件路径**：`StarPro文档/前端/pages/manage/postReview.vue.md` (实际代码中路径为 `APP前端部分/pages/manage/postReview.vue.md`)
- **页面说明**：此页面用于管理员（包括不同等级的圈主/版主）审核用户发布的帖子（论坛帖子）。支持按状态（待审核、已锁定、已发布）筛选，并根据管理员权限对帖子进行审核、锁定/解锁、移动等操作。

---

## 概述

`postReview.vue` 是一个帖子审核管理页面。管理员登录后，页面会首先获取该管理员在各个圈子的权限等级 (`myPurview`)。

-   **圈子管理员 (`myPurview >= 5` 或系统管理员/编辑)**: 可以查看所有圈子的帖子，并按状态筛选。
-   **普通圈主/版主 (`myPurview < 5`)**: 只能查看其负责的圈子的帖子，页面会显示一个"选择圈子"的表单项，允许切换管理的圈子。

页面顶部提供状态筛选（待审核、已锁定、已发布）和关键词搜索功能。帖子列表由 `forumItem` 子组件渲染，展示帖子的基本信息。每个帖子下方会根据其状态和管理员权限显示不同的操作按钮：

-   **待审核 (`item.status==0`)**: 显示"通过"和"不通过"按钮，调用 `toReview()`。
-   **已发布 (`item.status==1`)**: 显示"锁定"和"移动"按钮，分别调用 `toLock()` 和 `move()`。
-   **已锁定 (`item.status==2`)**: 显示"解除锁定"按钮，调用 `toLock()`。

审核通过帖子时，还会额外调用一个API (`$API.SPgetjifen()`)，可能是为了给发帖用户增加积分。

页面支持下拉刷新和上拉加载更多。

## 主要组成部分分析

### 1. 模板 (`<template>`)
   - **自定义导航栏 (`cu-bar`)**: 返回按钮 (`back()`)，标题 "帖子审核"。
   - **圈子选择 (`form` if `myPurview<5`)**: 
     - 仅对非最高权限管理员显示。
     - 点击调用 `toSection()` 跳转到圈子选择页面。
     - 显示当前选中的圈子名称 (`curSection.name`)。
   - **搜索框 (`cu-bar search`)**: 绑定 `searchText`，处理搜索逻辑。
   - **状态筛选 (`search-type grid col-3`)**: "待审核" (`toType(0)`), "已锁定" (`toType(2)`), "已发布" (`toType(1)`)。
   - **帖子列表区域 (`forum-list-main postReview`)**: 
     - `v-for` 遍历 `postList`。
     - 每个帖子由 `<forumItem :item="item" :myPurview="myPurview"></forumItem>` 渲染。
     - **操作按钮区域 (`forum-list-operate`)**: 根据 `item.status` 显示不同的操作按钮 (通过/不通过/锁定/移动/解除锁定)。
     - **加载更多 (`load-more`)**.

### 2. 脚本 (`<script>`)
   - **组件依赖**: `localStorage`。
   - **`data`**: 
     - UI相关: `StatusBar`, `CustomBar`, `NavBar`, `AppStyle`, `isLoading`, `moreText`, `dataLoad`。
     - 核心数据: `postList`, `topList` (未使用), `token`, `curSection` (当前圈子), `status` (筛选状态)。
     - 权限相关: `group` (未使用), `myPurview` (当前管理员权限等级), `myPurviewList` (存储从API获取的权限列表)。
     - 分页: `page`, `isLoad`。
     - 搜索: `searchText`。
     - `curId`: 用于帖子移动操作，临时存储帖子ID。
   - **生命周期**: 
     - `onPullDownRefresh`, `onReachBottom`: 处理列表刷新和加载。
     - `onHide()`: 移除 `localStorage` 中的 `getuid` (可能为遗留)。
     - `onShow()`: 处理圈子选择返回 (`localStorage.getItem('curSection')`)，如果 `curId` 不为0，则执行移动操作 (`goMove`)。
     - `onLoad(res)`: 获取 `token`，调用 `userPurview()` 获取管理员权限。
   - **`methods`**: 
     - `back()`: 返回。
     - `toType(type)`: 切换状态筛选，调用 `getPostList()`。
     - `toSection()`: 跳转到圈子选择页 (`/pages/forum/section?type=1`)。
     - `move(id)`: 准备移动帖子，记录帖子ID (`curId`)，跳转到圈子选择页。
     - `goMove(id, sectionid)`: (圈子选择返回后调用) 执行移动帖子操作，调用 `$API.postTransfer()`，成功后刷新列表。
     - `searchClose()`/`searchTag()`: 处理搜索，调用 `getPostList()`。
     - `loadMore()`: 加载更多，调用 `getPostList(true)`。
     - `getPostList(isPage)`: 
       - **核心帖子列表获取**。
       - 构建请求参数 `data`，包含 `status`。
       - 如果 `myPurview < 5`，则加入 `section` (圈子ID) 参数。
       - 调用 `$API.postList()` 获取数据。
       - 更新 `postList` 和分页状态。
     - `toLock(id, type, index)`: 锁定/解锁帖子，调用 `$API.postLock()`。
     - `formatDate()`: 格式化日期 (未使用)。
     - `toReview(id, type, index)`: 
       - **审核帖子核心逻辑** (0=不通过, 1=通过)。
       - 调用 `$API.postReview()`。
       - 如果审核通过 (`type==1`)，额外调用 `$API.SPgetjifen()` (给用户加分)。
       - 成功后刷新列表。
     - `goInfo(id)`: 跳转到帖子详情页 (未使用)。
     - `userPurview()`: 
       - 调用 `$API.userPurview()` 获取当前登录管理员在各个圈子的权限等级。
       - 根据返回结果和用户自身的系统角色 (`administrator`/`editor`)，设置 `that.myPurview` (取最高权限等级，系统管理员/编辑视为5)。
       - 如果是最高权限管理员 (`myPurview > 4`)，则调用 `getPostList()` 加载所有帖子。

## 总结与注意事项

-   页面实现了分权限的帖子审核管理功能。
-   **权限逻辑**: `myPurview` 是核心，决定了管理员能看到的圈子范围和操作。
-   **子组件依赖**: 帖子的具体展示依赖 `forumItem` 组件。
-   **API依赖**: `$API.userPurview`, `$API.postList`, `$API.postLock`, `$API.postReview`, `$API.postTransfer`, `$API.SPgetjifen`。
-   **圈子选择与移动**: 通过跳转页面和 `localStorage` 实现。
-   **审核加分**: 审核通过时有额外的加分操作。

## 后续分析建议

-   **`forumItem` 组件分析**: 需要分析 `forumItem.vue` 以了解帖子内容的具体展示方式。
-   **API确认**: 
    - `$API.userPurview()`: 确认返回的权限数据结构和 `purview` 等级的具体含义。
    - `$API.postList()`: 确认 `searchParams` 中的 `status`, `section`, `order` 等参数的作用。
    - `$API.postReview()`, `$API.postLock()`, `$API.postTransfer()`, `$API.SPgetjifen()`: 确认请求参数和响应。
-   **权限细化**: `myPurview` 的判断逻辑（系统管理员/编辑直接视为5）是否覆盖所有场景？普通版主是否只能审核自己圈子的帖子？
-   **代码整洁性**: `formatDate` 和 `goInfo` 方法未使用。`topList` 在 `data` 中定义但未使用。
-   **用户体验**: 帖子移动操作需要跳转两次页面（一次选圈子，一次返回），流程可以优化。审核/锁定操作后列表刷新及时。 