<template>
	<view :class="isDark?'dark':''">
		<view class="cu-card dynamic no-card square-list">
			<block  v-for="(item,index) in spaceList" :key="index" v-if="spaceList.length>0">
				<view class="cu-item" style="border-radius: 0px;margin: 0;">
					<view class="user-rz-qz">
					<view class="forum-list-user" style="padding: 10px 10px 0  10px;">
						<view class="forum-avatar" @tap="toUserContents(item.userJson)" style="position: relative; width: 90upx; height: 90upx;">
							<tn-lazy-load :image="item.userJson.avatar" borderRadius="50%" height="90"
								mode="aspectFill">
							</tn-lazy-load>
							<avatarItem :uid="item.userJson.uid" :lvrz="0" style="position: absolute; top: -12px; left: -12.5px; width: 110%; height: 110%; transform: scale(0.4); z-index: 1;"></avatarItem>
						</view>
						<image class="user-rz-icon-qz" :src="rzImg" v-if="item.userJson.lvrz==1" mode="aspectFill" style="z-index: 2;"></image>
						
						<view class="forum-userinfo">
							<view class="forum-userinfo-name" :class="item.userJson.isvip>0?'name-vip':''">
								{{item.userJson.name}}
								<image v-if="item.userJson.isvip>0" :src="vipImg" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;" mode="widthFix"></image>
								<image :src="lvImg+getLv(item.userJson.experience)+'.png'" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 44upx;height: 22upx;" mode="widthFix"></image>
								<medalItem :uid="item.userJson.uid" style="display: inline-block; margin-left: 6upx; vertical-align: middle;"></medalItem>
							</view>
								
							<view class="forum-userinfo-date">
								{{formatDate(item.created)}} <text class="margin-left-sm" v-if="$API.localOf()">{{getLocal(item.userJson.local)}}</text>
							</view>
							<!-- <block v-if="item.isAds==0">
								<view class="cu-btn forum-follow-btn" v-if="item.isFollow==0"
									@tap="follow(1,item.userJson.uid)">
									<text class="cuIcon-add"></text>关注
								</view>
								<view class="cu-btn text-red forum-follow-btn isFollow" v-if="item.isFollow==1"
									@tap="follow(0,item.userJson.uid)">
									已关注
								</view>
							</block> -->
						</view>
					</view>
					</view>
					<view class="text-content break-all" @tap="toInfo(item.id)">
						<text class="text-orange text-sm" v-if="item.onlyMe==1">[私密]</text><rich-text :nodes="markHtml(item.text)"></rich-text>
					</view>

					<block v-if="item.type==0">
						<view class="image-container" v-if="item.picList.length>0">
							<!-- 1张图片：等宽长方形 -->
							<view class="image-layout-1" v-if="item.picList.length === 1">
								<view class="image-item large-rect" 
									:style="'background-image:url('+item.picList[0]+');'"
									@tap="previewImage(item.picList,item.picList[0])">
							</view>
						</view>
							
							<!-- 2张图片：左右等分 -->
							<view class="image-layout-2" v-if="item.picList.length === 2">
								<view class="image-item half-width" 
									v-for="(img, i) in item.picList" :key="i"
									:style="'background-image:url('+img+');'"
									@tap="previewImage(item.picList,img)">
										</view>
										</view>
							
							<!-- 3张图片：6宫格布局 -->
							<view class="image-layout-3" v-if="item.picList.length === 3">
								<view class="image-grid-3">
									<!-- 第一张图占1,2,4,5位置 -->
									<view class="image-item grid-main" style="margin: 0;" 
										:style="'background-image:url('+item.picList[0]+');'"
										@tap="previewImage(item.picList,item.picList[0])">
									</view>
									<!-- 第二张图占3位置 -->
									<view class="image-item grid-top-right" style="margin: 0;" 
										:style="'background-image:url('+item.picList[1]+');'"
										@tap="previewImage(item.picList,item.picList[1])">
								</view>
									<!-- 第三张图占6位置 -->
									<view class="image-item grid-bottom-right" style="margin: 0;" 
										:style="'background-image:url('+item.picList[2]+');'"
										@tap="previewImage(item.picList,item.picList[2])">
									</view>
										</view>
										</view>
							
							<!-- 4张图片：2x2宫格 -->
							<view class="image-layout-4" v-if="item.picList.length === 4">
								<view class="image-item quarter" 
									v-for="(img, i) in item.picList" :key="i"
									:style="'background-image:url('+img+');'"
									@tap="previewImage(item.picList,img)">
									</view>
								</view>
							
							<!-- 5张图片：上3下2布局 -->
							<view class="image-layout-5" v-if="item.picList.length === 5">
								<view class="image-row-top">
									<view class="image-item third-width" 
										v-for="(img, i) in item.picList.slice(0, 3)" :key="i"
										:style="'background-image:url('+img+');'"
										@tap="previewImage(item.picList,img)">
						</view>
										</view>
								<view class="image-row-bottom">
									<view class="image-item half-width-5" 
										v-for="(img, i) in item.picList.slice(3)" :key="i+3"
										:style="'background-image:url('+img+');'"
										@tap="previewImage(item.picList,img)">
										</view>
									</view>
								</view>
							
							<!-- 6张图片：9宫格布局 -->
							<view class="image-layout-6" v-if="item.picList.length === 6">
								<view class="image-grid-6">
									<!-- 第一张图占1,2,3,4位置 -->
									<view class="image-item grid-main-6" style="margin: 0;" 
										:style="'background-image:url('+item.picList[0]+');'"
										@tap="previewImage(item.picList,item.picList[0])">
									</view>
									<!-- 其他图片占5,6,7,8,9位置 -->
									<view class="image-item grid-small-6" style="margin: 0;" 
										v-for="(img, i) in item.picList.slice(1)" :key="i+1"
										:style="'background-image:url('+img+');'"
										@tap="previewImage(item.picList,img)">
										</view>
										</view>
									</view>
								</view>
							</block>
					<block  v-if="item.type==4">
						<view class="padding-lr spaceVideo">
							<!--  #ifdef H5 || MP-->
							<video :src="item.pic" @play="play(item.pic)" ></video>
							<!--  #endif -->
							<!--  #ifdef APP-PLUS -->
							<view class="paceVideo2">
								<view class="spaceVideo-play" :style="{ backgroundImage: 'url(' + curIMG + ')', backgroundSize: 'cover', backgroundRepeat: 'no-repeat', backgroundPosition: 'center center' }" @tap="goPlay(item.pic,item.text,item.userJson.name)">
									<text class="cuIcon-playfill"></text>
								</view>
							</view>
							<!--  #endif -->
							
							
						</view>
					</block>
					<block  v-if="item.type==2">
						<view class="grid flex-sub padding-lr">
							<block v-if="item.forwardJson.id==0">
								<view class="user-space-info">
									<view class="user-space-text">
										该动态已被删除！
									</view>
								</view>
							</block>
							<block v-else>
								<view class="user-space-info" @tap="toInfo(item.forwardJson.id)">
									<view class="user-space-text">
										<text class="text-blue">@{{item.forwardJson.username}}：</text><rich-text :nodes="markHtml(item.forwardJson.text)"></rich-text>
									</view>
									
									<view class="image-container forward-image-container" v-if="item.forwardJson.picList.length > 0">
										<!-- 1张图片：等宽长方形 -->
										<view class="image-layout-1 forward-layout-1" v-if="item.forwardJson.picList.length === 1">
											<view class="image-item large-rect forward-large-rect"
												:style="'background-image:url(' + item.forwardJson.picList[0] + ');'"
												@tap="previewImage(item.forwardJson.picList, item.forwardJson.picList[0])">
										</view>
									</view>

										<!-- 2张图片：左右等分 -->
										<view class="image-layout-2 forward-layout-2" v-if="item.forwardJson.picList.length === 2">
											<view class="image-item half-width forward-half-width"
												v-for="(img, i) in item.forwardJson.picList" :key="i"
												:style="'background-image:url(' + img + ');'"
												@tap="previewImage(item.forwardJson.picList, img)">
								</view>
										</view>

										<!-- 3张图片：6宫格布局 -->
										<view class="image-layout-3 forward-layout-3" v-if="item.forwardJson.picList.length === 3">
											<view class="image-grid-3">
												<!-- 第一张图占1,2,4,5位置 -->
												<view class="image-item grid-main" style="margin: 0;"
													:style="'background-image:url(' + item.forwardJson.picList[0] + ');'"
													@tap="previewImage(item.forwardJson.picList, item.forwardJson.picList[0])">
						</view>
												<!-- 第二张图占3位置 -->
												<view class="image-item grid-top-right" style="margin: 0;"
													:style="'background-image:url(' + item.forwardJson.picList[1] + ');'"
													@tap="previewImage(item.forwardJson.picList, item.forwardJson.picList[1])">
								</view>
												<!-- 第三张图占6位置 -->
												<view class="image-item grid-bottom-right" style="margin: 0;"
													:style="'background-image:url(' + item.forwardJson.picList[2] + ');'"
													@tap="previewImage(item.forwardJson.picList, item.forwardJson.picList[2])">
							</view>
						</view>
										</view>

										<!-- 4张图片：2x2宫格 -->
										<view class="image-layout-4 forward-layout-4" v-if="item.forwardJson.picList.length === 4">
											<view class="image-item quarter forward-quarter"
												v-for="(img, i) in item.forwardJson.picList" :key="i"
												:style="'background-image:url(' + img + ');'"
												@tap="previewImage(item.forwardJson.picList, img)">
										</view>
										</view>

										<!-- 5张图片：上3下2布局 -->
										<view class="image-layout-5 forward-layout-5" v-if="item.forwardJson.picList.length === 5">
											<view class="image-row-top">
												<view class="image-item third-width"
													v-for="(img, i) in item.forwardJson.picList.slice(0, 3)" :key="i"
													:style="'background-image:url(' + img + ');'"
													@tap="previewImage(item.forwardJson.picList, img)">
										</view>
									</view>
											<view class="image-row-bottom">
												<view class="image-item half-width-5"
													v-for="(img, i) in item.forwardJson.picList.slice(3)" :key="i + 3"
													:style="'background-image:url(' + img + ');'"
													@tap="previewImage(item.forwardJson.picList, img)">
								</view>
									</view>
										</view>

										<!-- 6张图片：9宫格布局 -->
										<view class="image-layout-6 forward-layout-6" v-if="item.forwardJson.picList.length === 6">
											<view class="image-grid-6">
												<!-- 第一张图占1,2,3,4位置 -->
												<view class="image-item grid-main-6" style="margin: 0;"
													:style="'background-image:url(' + item.forwardJson.picList[0] + ');'"
													@tap="previewImage(item.forwardJson.picList, item.forwardJson.picList[0])">
										</view>
												<!-- 其他图片占5,6,7,8,9位置 -->
												<view class="image-item grid-small-6" style="margin: 0;"
													v-for="(img, i) in item.forwardJson.picList.slice(1)" :key="i + 1"
													:style="'background-image:url(' + img + ');'"
													@tap="previewImage(item.forwardJson.picList, img)">
												</view>
											</view>
										</view>
									</view>
								</view>
							</block>
							
						</view>
					</block>
					
					<view class="text-center grid col-3 padding-xs">
						<view class="square-post-btn" @tap="forward(item.id)">
							<text class="cuIcon-forward"></text>
							<block v-if="item.forward>0">
								{{formatNumber(item.forward)}}
							</block>
							<block v-else>
								转发
							</block>
						</view>
						<view class="square-post-btn"  @tap="toInfo(item.id)">
							<text class="cuIcon-community"></text>
							<block v-if="item.reply>0">
								{{formatNumber(item.reply)}}
							</block>
							<block v-else>
								评论
							</block>
						</view>
						<view class="square-post-btn" @tap="toLike(item.id,index)">
							<text class="cuIcon-appreciate" :class="item.isLikes==1?'text-red':''"></text>
							<block v-if="item.likes>0">
								{{formatNumber(item.likes)}}
							</block>
							<block v-else>
								点赞
							</block>
						</view>
					</view>
				</view>
			</block>
		</view>
		<view class="videoPlay" v-if="isPlay">
			<view class="videoPlay-bg" @tap="isPlay=false">
				<view class="videoPlay-close" @tap="isPlay=true">
					<i class="cuIcon-close"></i>
				</view>
			</view>
			<video :src="curVideo" http-cache="true" autoplay :title="mp4title"></video>
		</view>
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	// #ifdef APP-PLUS
	import owo from '../../static/app-plus/owo/OwO.js'
	// #endif
	// #ifdef H5
	import owo from '../../static/h5/owo/OwO.js'
	// #endif
	// #ifdef MP
	var owo = [];
	// #endif
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import avatarItem from './avatarItem.vue';
	import medalItem from './medalItem.vue';
	export default {
		components: { avatarItem, medalItem },
	    props: {
	        spaceList: {
			  type: Array,
			  default: () => []
			},
			isHead: {
			  type: Boolean,
			  default: true
			},
			curIMG: {
			  type: String,
			  default: ''
			},
			isDark: {
			  type: Boolean,
			  default: false
			}
	    },
		mixins: [darkModeMixin],
		name: "spaceItem",
		data() {
			return {
				owo:owo,
				owoList:[],
				vipDiscount:0,
				currencyName:"",
				group:"",
				vipImg: this.$API.SPvip(),
				lvImg: this.$API.SPLv(),
				rzImg: this.$API.SPRz(),
				
				uid:0,
				mp4bt:"",
				mp4name:"",
				mp4title:"视频动态",
				isPlay:false,
				curVideo:"",
			};
		},
		created(){
			var that = this;
			if(localStorage.getItem('userinfo')){
							
				var userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.group = userInfo.group;
				that.uid = userInfo.uid;
			}
			// #ifdef APP-PLUS || H5
			var owo = that.owo.data;
			var owoList=[];
			for(var i in owo){
				owoList = owoList.concat(owo[i].container);
			}
			that.owoList = owoList;
			// #endif
		},
		mounted() {
			var that = this;
			that.getleiji();
			
		},
		
		methods: {

			getLv(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var lv  = that.$API.getLever(i);
				return lv;
			},
			
			getleiji() {
			  var that = this;
			  		uni.request({
			  			url:that.$API.SPset(),
			  			method:'GET',
			  			dataType:"json",
			  			success(res) {
							that.currencyName = res.data.assetsname;
			  			},
			  			fail(error) {
			  			  console.log(error);
			  			}
			  			
			  		})
			},
			previewImage(imageList,image) {
				//预览图片
				uni.previewImage({
					urls: imageList,
					current: image
				});
			},
			subText(text,num){
				if(text.length < null){
					return text.substring(0,num)+"……"
				}else{
					return text;
				}
				
			},
			replaceSpecialChar(text) {
			  text = text.replace(/&quot;/g, '"');
			  text = text.replace(/&amp;/g, '&');
			  text = text.replace(/&lt;/g, '<');
			  text = text.replace(/&gt;/g, '>');
			  text = text.replace(/&nbsp;/g, ' ');
			  return text;
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var now = new Date();
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
			
				var timeDiff = now - datetime;
				var seconds = Math.floor(timeDiff / 1000);
				var minutes = Math.floor(timeDiff / (1000 * 60));
				var hours = Math.floor(timeDiff / (1000 * 60 * 60));
				var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
				var months = Math.floor(days / 30);
			
				var result = "";
			
				if (seconds < 60) {
					result = seconds + "秒前";
				} else if (minutes < 60) {
					result = minutes + "分钟前";
				} else if (hours < 24) {
					result = hours + "小时前";
				} else if (days < 7) {
					result = days + "天前";
				} else if (year === now.getFullYear()) {
					result = month + "月" + date + "日";
				} else {
					result = year + "年" + month + "月" + date + "日";
				}
			
				return result;
			},
			formatNumber(num) {
			    return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' : num
			},
			toInfo(id){
				var that = this;
				
				uni.navigateTo({
				    url: '/pages/space/info?id='+id
				});
			},
			goContentInfo(data){
				var that = this;
				if(data.status!="publish"){
					uni.showToast({
						title:"文章正在审核中，请稍后再试！",
						icon:'none',
						duration: 1000,
						position:'bottom',
					});
					return false;
				}
				uni.navigateTo({
				    url: '/pages/contents/info?cid='+data.cid+"&title="+data.title
				});
			},
			goAds(data){
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if(type==1){
					plus.runtime.openURL(url);
				}
				if(type==0){
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			formatNumber(num) {
			    return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' : num
			},
			getUserLv(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			getUserLvStyle(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle ="color:#fff;background-color: "+rankStyle[i];
				return userlvStyle;
			},
			markHtml(text){
				var that = this;
				text = that.replaceAll(text,"<","&lt;");
				text = that.replaceAll(text,">","&gt;");
				var owoList=that.owoList;
				for(var i in owoList){
				
					if(that.replaceSpecialChar(text).indexOf(owoList[i].data) != -1){
						text = that.replaceAll(that.replaceSpecialChar(text),owoList[i].data,"<img src='/"+owoList[i].icon+"' class='tImg' />")
						
					}
				}
				text = that.replaceAll(text,"/r/n","<br>");
				text = that.TransferString(text);
				return text;
			},
			TransferString(content)
			{  
			    var string = content;  
			    try{  
			        string=string.replace(/\r\n/g,"<br>")  
			        string=string.replace(/\n/g,"<br>");  
			    }catch(e) {  
			        return content;
			    }  
			    return string;  
			},
			replaceAll(string, search, replace) {
			  return string.split(search).join(replace);
			},
			getUserLv(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			
			getUserLvStyle(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle ="color:#fff;background-color: "+rankStyle[i];
				return userlvStyle;
			},
			getLvStyle(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var lv  = that.$API.getLever(i);
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle ="color:#fff;background-color: "+rankStyle[lv];
				return userlvStyle;
			},
			follow(type,uid,index){
				var that = this;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}else{
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				that.spaceList[index].isFollow = type;
				var data = {
					token:token,
					touid:uid,
					type:type,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.follow(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==0){
							that.spaceList[index].isFollow = 0;
						}else{
							var spaceList = that.spaceList;
							for(var i in spaceList){
								if(spaceList[i].userJson.uid==uid){
									spaceList[i].isFollow = type;
								}
							}
							that.spaceList = spaceList;
						}
						
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						
					}
				})
			},
			toLike(id,index){
				var that = this;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}else{
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				if(that.spaceList[index].isLikes==1){
					uni.showToast({
						title: "你已经点赞过了",
						icon: 'none'
					});
					return false;
				}else{
					that.spaceList[index].isLikes = 1;
				}
				
				that.spaceList[index].likes += 1;
				var data = {
					token:token,
					id:id,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.spaceLikes(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==0){
							that.spaceList[index].isLikes = 0;
						}
						
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						
					}
				})
			},
			edit(id){
				var that = this;
				uni.navigateTo({
				    url: '/pages/space/post?postType=edit&id='+id
				});
			},
			forward(id){
				var that = this;
				uni.navigateTo({
				    url: '/pages/space/post?type=2&id='+id
				});
			},
			
			goShopInfo(data){
				var that = this;
				var sid = data.id;
				var status = data.status;
				if(status!=1){
					uni.showToast({
						title: "商品未通过审核",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
				    url: '/pages/shop/shopinfo?sid='+sid
				});
			},
			goPostInfo(id){
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/info?id='+id
				});
			},
			toUserContents(data){
				var that = this;
				var name = data.name;
				var title = data.name+"的信息";
				var id= data.uid;
				var type="user";
				uni.navigateTo({
				    url: '/pages/contents/userinfo?title='+title+"&name="+name+"&uid="+id+"&avatar="+encodeURIComponent(data.avatar)
				});
			},
			toBan(uid){
				if(uid==0){
					uni.showToast({
						title: "该用户不存在",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/manage/banuser?uid='+uid
				});
			},
			toDelete(id){
				var that = this;
				var token = "";
				
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"id":id,
					"token":token
				}
				uni.showModal({
					title: '确定要删除该动态吗',
					success: function (res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							
							that.$Net.request({
								url: that.$API.spaceDelete(),
								data:data,
								header:{
									'Content-Type':'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function () {
										uni.hideLoading();
									}, 1000);
									if(res.data.code==1){
										// uni.request({
										// 	url:that.$API.SPdongtairemove(),
										// 	method:'GET',
										// 	data:{
										// 		id:id,
										// 	},
										// 	dataType:"json",
										// 	success(res) {
										// 	},
										// 	fail() {
										// 		setTimeout(function () {
										// 			uni.hideLoading();
										// 		}, 1000);
										// 		uni.showToast({
										// 			title: "网络不太好哦",
										// 			icon: 'none'
										// 		})
										// 	}
											
											
										// })
										uni.showToast({
											title: res.data.msg+"，刷新数据后生效",
											icon: 'none'
										})
									}else{
										uni.showToast({
											title: res.data.msg,
											icon: 'none'
										})
									}
									
									
								},
								fail: function(res) {
									setTimeout(function () {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			goPlay(url,title,name){
				var that = this;
				that.curVideo = url;
				that.mp4bt = title;
				that.mp4name = name;
				that.mp4title = that.mp4bt + ' - ' + that.mp4name + ' | ' + that.$API.GetAppName();
				that.isPlay=true;
			},
			getLocal(local){
				var that = this;
				if(local&&local!=''){
					var local_arr = local.split("|");
					if(!local_arr[3]||local_arr[3]==0){
						return local_arr[2];
					}else{
						return local_arr[3];
					}
					
				}else{
					return "未知"
				}
				
			},
		}
	}
</script>

<style>
	.cu-card.no-card>.cu-item {
	    margin: 20upx;
	    border-radius: 20upx;
	}
	.grid.col-3.grid-square>view {
	    border-radius: 20upx;
	}
	.square-text {
	    width: calc(100% - 120upx);
	    padding: 0 20upx;
	    float: left;
	    margin: 0 20upx;
	}
	
	.user-rz-qz{
		position: relative;
	}
	.user-rz-icon-qz{
		position: absolute;
		left: 80upx;
		bottom: -6upx;
		width: 36upx;
		height: 36upx;
	}
	
	/* 图片容器样式 */
	.image-container {
		padding: 0 20upx;
		margin-top: 20upx;
	}
	
	/* 基础图片样式 */
	.image-item {
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		border-radius: 20upx;
		margin: 8upx;
	}
	
	/* 1张图片：等宽长方形 */
	.image-layout-1 {
		display: flex;
		width: 100%;
	}
	
	.large-rect {
		width: calc(100% - 20upx);
		height: 400upx;
	}
	
	/* 2张图片：左右等分 */
	.image-layout-2 {
		display: flex;
		width: 100%;
	}
	
	.half-width {
		width: calc(50% - 20upx);
		height: 340upx;
	}
	
	/* 3张图片：6宫格布局 */
	.image-layout-3 {
		display: flex;
		width: 100%;
		height: 370upx;
	}
	
	.image-grid-3 {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		grid-template-rows: 1fr 1fr;
		width: 100%;
		height: 100%;
		gap: 15upx;
		padding: 0 10upx;
	}
	
	.grid-main {
		grid-column: 1 / 3;
		grid-row: 1 / 3;
	}
	
	.grid-top-right {
		grid-column: 3 / 4;
		grid-row: 1 / 2;
	}
	
	.grid-bottom-right {
		grid-column: 3 / 4;
		grid-row: 2 / 3;
	}
	
	/* 4张图片：2x2宫格 */
	.image-layout-4 {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		height: 480upx;
	}
	
	.quarter {
		width: calc(50% - 20upx);
		height: calc(50% - 20upx);
	}
	
	/* 5张图片：上3下2布局 */
	.image-layout-5 {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 480upx;
	}
	
	.image-row-top {
		display: flex;
		width: 100%;
		height: 50%;
	}
	
	.image-row-bottom {
		display: flex;
		width: 100%;
		height: 50%;
	}
	
	.third-width {
		width: calc(33.33% - 20upx);
		height: calc(100% - 20upx);
	}
	
	.half-width-5 {
		width: calc(50% - 20upx);
		height: calc(100% - 20upx);
	}
	
	/* 6张图片：9宫格布局 */
	.image-layout-6 {
		display: flex;
		width: 100%;
		height: 480upx;
	}
	
	.image-grid-6 {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		grid-template-rows: 1fr 1fr 1fr;
		width: 100%;
		height: 100%;
		gap: 15upx;
		padding: 0 10upx;
	}
	
	.grid-main-6 {
		grid-column: 1 / 3;
		grid-row: 1 / 3;
	}
	
	.grid-small-6 {
		width: 100%;
		height: 100%;
	}
	
	.grid-small-6:nth-child(2) {
		grid-column: 3 / 4;
		grid-row: 1 / 2;
	}
	
	.grid-small-6:nth-child(3) {
		grid-column: 3 / 4;
		grid-row: 2 / 3;
	}
	
	.grid-small-6:nth-child(4) {
		grid-column: 1 / 2;
		grid-row: 3 / 4;
	}
	
	.grid-small-6:nth-child(5) {
		grid-column: 2 / 3;
		grid-row: 3 / 4;
	}
	
	.grid-small-6:nth-child(6) {
		grid-column: 3 / 4;
		grid-row: 3 / 4;
	}
	
	/* 转发动态图片样式 - 降低高度 */
	.forward-image-container {
		padding: 0 20upx;
		margin-top: 15upx;
	}
	
	/* 转发动态：1张图片 - 高度降低 */
	.forward-large-rect {
		width: calc(100% - 20upx);
		height: 280upx; /* 从400upx降低到280upx */
	}
	
	/* 转发动态：2张图片 - 高度降低 */
	.forward-half-width {
		width: calc(50% - 20upx);
		height: 240upx; /* 从340upx降低到240upx */
	}
	
	/* 转发动态：3张图片 - 高度降低 */
	.forward-layout-3 {
		display: flex;
		width: 100%;
		height: 260upx; /* 从370upx降低到260upx */
	}
	
	/* 转发动态：4张图片 - 高度降低 */
	.forward-layout-4 {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		height: 340upx; /* 从480upx降低到340upx */
	}
	
	.forward-quarter {
		width: calc(50% - 20upx);
		height: calc(50% - 20upx);
	}
	
	/* 转发动态：5张图片 - 高度降低 */
	.forward-layout-5 {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 340upx; /* 从480upx降低到340upx */
	}
	
	/* 转发动态：6张图片 - 高度降低 */
	.forward-layout-6 {
		display: flex;
		width: 100%;
		height: 340upx; /* 从480upx降低到340upx */
	}
</style>