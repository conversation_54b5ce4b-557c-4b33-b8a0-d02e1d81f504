# company.vue 文件分析

## 文件信息
- **文件路径**：`APP前端部分/pages/manage/company.vue.md`
- **页面说明**：此页面用于管理员管理企业认证申请。

---

<template>
	<view class="user" :class="$store.state.AppStyle" style="background-color: #f6f6f6;">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					蓝V认证申请
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar+5 + 'px 10px 0px 10px'}]"></view>
		<view class="data-box">
			<view class="cu-bar bg-white search">
				<view class="search-form round">
					<text class="cuIcon-search"></text>
					<input type="text" placeholder="输入用户UID" v-model="searchText"  @input="searchTag"></input>
					<view class="search-close" v-if="searchText!=''" @tap="searchClose()"><text class="cuIcon-close"></text></view>
				</view>
			</view>
			<view class="search-type grid col-2">
				<view class="search-type-box" @tap="toType(0)" :class="type==0?'active':''">
					<text>待审核</text>
				</view>
				<view class="search-type-box" @tap="toType(1)" :class="type==1?'active':''">
					<text>已通过</text>
				</view>
			</view>
			<view class="identify-list">
				<view class="no-data" v-if="identifyList.length==0">
					<text class="cuIcon-text"></text>暂时没有数据
				</view>
				<view class="identify-item-box" v-for="(item,index) in identifyList" :key="index">
					<view class="identify-box-concent">
						<view class="identify-box-value">
							申请理由：<text>{{item.entname}}</text>
						</view>
						<!-- <view class="identify-box-value">
							注册号：<text>{{item.regno}}</text>
						</view>
						<view class="identify-box-value">
							姓名：<text>{{item.name}}</text>
						</view>
						<view class="identify-box-value">
							身份证：<text>{{item.idcard}}</text>
						</view> -->
					</view>
					<view class="identify-box-user">
						<text class="text-blue">
							<text class="cuIcon-people">{{item.userJson.name}}</text>
						</text>
						<text class="identify-box-btn">
							<block v-if="item.identifyStatus==0">
								<!-- <text class="text-green margin-right-sm" @tap="autoReview(item.uid)">自动审核</text> -->
								<text class="text-blue margin-right-sm" @tap="systemReview(item.uid)">通过</text>
								<text class="text-red" @tap="toDelete(item.uid,0)">不通过</text>
							</block>
							<block v-else>
								
								<text class="text-red" @tap="toDelete(item.uid,1)">撤销认证</text>
							</block>
							
						</text>
					</view>
				</view>
				<view class="load-more" @tap="loadMore" v-if="identifyList.length>0">
					<text>{{moreText}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				
				userInfo:"",
				token:'',
				moreText:"加载更多",
				searchText:'',
				type:0,
				
				identifyList:[],
				
				
			}
		},
		onPullDownRefresh(){
			var that = this;
			that.page=1;
			that.getIdentifyList(false);
			var timer = setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000)
			
		},
		onReachBottom() {
		    //触底后执行的方法，比如无限加载之类的
			var that = this;
			if(that.isLoad==0){
				that.loadMore();
			}
		},
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			if(localStorage.getItem('token')){
				
				that.token = localStorage.getItem('token');
			}
			that.getCacheInfo();
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			
			if(localStorage.getItem('token')){
				that.token=localStorage.getItem('token');
				that.getIdentifyList(false);
			}
		},
		methods: {
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			getCacheInfo(){
				var that = this;
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.uid=userInfo.uid;
					that.userInfo = userInfo;
				}
			},
			toType(i){
				var that = this;
				that.type=i;
				that.page=1;
				that.moreText="加载更多";
				that.isLoad=0;
				that.identifyList = [];
				that.getIdentifyList(false);
			},
			loadMore(){
				var that = this;
				that.moreText="正在加载中...";
				that.isLoad=1;
				that.getIdentifyList(true);
			},
			searchTag(){
				var that = this;
				var searchText = that.searchText;
				that.page=1;
				that.getIdentifyList();
			
			},
			getIdentifyList(isPage){
				var that = this;
				var data = {
					"identifyStatus":that.type,
				}
				if(that.searchText!=""){
					data.uid = that.searchText;
				}
				var page = that.page;
				if(isPage){
					page++;
				}
				that.$Net.request({
					url: that.$API.companyList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":8,
						"page":page,
						"token":that.token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh()
						that.isLoad=0;
						//console.log(JSON.stringify(res))
						that.moreText="加载更多";
						if(res.data.code==1){
							var list = res.data.data;
							if(list.length>0){
								if(isPage){
									that.page++;
									that.identifyList = that.identifyList.concat(list);
								}else{
									that.identifyList = list;
								}
								
							}else{
								that.moreText="没有更多内容了";
								if(!isPage){
									that.identifyList = list;
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.stopPullDownRefresh()
						that.moreText="加载更多";
						that.isLoad=0;
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			systemReview(uid){
				var that = this;
				var token = "";
				
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"uid":uid,
					"token":token
				}
				uni.showModal({
				    title: '确定要进行该操作吗',
				    success: function (res) {
				        if (res.confirm) {
				            uni.showLoading({
				            	title: "加载中"
				            });
				            
				            that.$Net.request({
				            	url: that.$API.systemIdentifyCompany(),
				            	data:data,
				            	header:{
				            		'Content-Type':'application/x-www-form-urlencoded'
				            	},
				            	method: "get",
				            	dataType: 'json',
				            	success: function(res) {
				            		setTimeout(function () {
				            			uni.hideLoading();
				            		}, 1000);
				            		uni.showToast({
				            			title: res.data.msg,
				            			icon: 'none'
				            		})
				            		if(res.data.code==1){
										if(localStorage.getItem('userinfo')){
											var userInfo = JSON.parse(localStorage.getItem('userinfo'));
											var uid=userInfo.uid;
										}
										uni.request({
											url:that.$API.SPgetcompany(),
											method:'GET',
											data:{
												uid:uid,
											},
											dataType:"json",
											success(res) {
											},
											fail() {
												setTimeout(function () {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}
											
											
										})
				            			that.page=1;
				            			that.moreText="加载更多";
				            			that.isLoad=0;
				            			that.getIdentifyList(false);
				            			
				            		}
				            		
				            	},
				            	fail: function(res) {
				            		setTimeout(function () {
				            			uni.hideLoading();
				            		}, 1000);
				            		uni.showToast({
				            			title: "网络开小差了哦",
				            			icon: 'none'
				            		})
				            	}
				            })
				        } else if (res.cancel) {
				            console.log('用户点击取消');
				        }
				    }
				});
			},
			autoReview(uid){
				var that = this;
				var token = "";
				
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"uid":uid,
					"token":token
				}
				uni.showModal({
				    title: '确定要进行该操作吗',
				    success: function (res) {
				        if (res.confirm) {
				            uni.showLoading({
				            	title: "加载中"
				            });
				            
				            that.$Net.request({
				            	url: that.$API.identifyCompany(),
				            	data:data,
				            	header:{
				            		'Content-Type':'application/x-www-form-urlencoded'
				            	},
				            	method: "get",
				            	dataType: 'json',
				            	success: function(res) {
				            		setTimeout(function () {
				            			uni.hideLoading();
				            		}, 1000);
				            		uni.showToast({
				            			title: res.data.msg,
				            			icon: 'none'
				            		})
				            		if(res.data.code==1){
				            			that.page=1;
				            			that.moreText="加载更多";
				            			that.isLoad=0;
				            			that.getIdentifyList(false);
				            			
				            		}
				            		
				            	},
				            	fail: function(res) {
				            		setTimeout(function () {
				            			uni.hideLoading();
				            		}, 1000);
				            		uni.showToast({
				            			title: "网络开小差了哦",
				            			icon: 'none'
				            		})
				            	}
				            })
				        } else if (res.cancel) {
				            console.log('用户点击取消');
				        }
				    }
				});
			},
			toDelete(uid,type){
				var that = this;
				var token = "";
				
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"uid":uid,
					"token":token
				}
				uni.showModal({
				    title: '确定要进行该操作吗',
				    success: function (res) {
				        if (res.confirm) {
				            uni.showLoading({
				            	title: "加载中"
				            });
				            
				            that.$Net.request({
				            	url: that.$API.removeCompany(),
				            	data:data,
				            	header:{
				            		'Content-Type':'application/x-www-form-urlencoded'
				            	},
				            	method: "get",
				            	dataType: 'json',
				            	success: function(res) {
				            		setTimeout(function () {
				            			uni.hideLoading();
				            		}, 1000);
				            		uni.showToast({
				            			title: res.data.msg,
				            			icon: 'none'
				            		})
				            		if(res.data.code==1){
										if(type==1){
										uni.request({
											url:that.$API.SPgetuncompany(),
											method:'GET',
											data:{
												uid:uid,
											},
											dataType:"json",
											success(res) {
											},
											fail() {
												setTimeout(function () {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}
											
											
										})
										}
				            			that.page=1;
				            			that.moreText="加载更多";
				            			that.isLoad=0;
				            			that.getIdentifyList(false);
				            			
				            		}
				            		
				            	},
				            	fail: function(res) {
				            		setTimeout(function () {
				            			uni.hideLoading();
				            		}, 1000);
				            		uni.showToast({
				            			title: "网络开小差了哦",
				            			icon: 'none'
				            		})
				            	}
				            })
				        } else if (res.cancel) {
				            console.log('用户点击取消');
				        }
				    }
				});
			},
		}
	}
</script>

<style>
</style>
