<template>
	<view class="grade">
		<view class="header" style="background-color: rgba(255, 255, 255, 0.0);" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" style="background-color: rgba(255, 255, 255, 0.0);" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="goBack">
					<text class="tn-icon-left text-bold text-white"></text>
				</view>
				<view class="content text-bold text-white" style="color: white;" :style="[{top:StatusBar + 'px'}]">
					每日任务
				</view>
				<view class="action text-white" style="color: white;" @tap="showModal" data-target="Modal">
					<text class="tn-icon-tip text-bold"></text>
				</view>
			</view>
		</view>
		<view class="top">
			<view class="num">
				<view>
				</view>
			</view>
			<view class="numerical">
				<view class="experience">
				</view>
			</view>
		</view>
		<view class="cont">
			
			<view class="task">
				
				<!--<text class="title" style="font-size:60rpx;text-align: center;color: #5f5f5f;">{{userInfo.assets}}<img src="../../static/user/ld2.png" style="margin-left: 6px;" width="10rpx" height="10rpx"></text>
			--><br>
				<text class="title" style="margin: 0px 0px 3px 0px;">今日任务 </text>
					
				<text class="text" @tap="showModal" data-target="Modal">点我查看任务说明</text> 
				
				
				
				<br>
				<view class="invitation">
					<view class="item">
						<view class="icon">
							<text class="tn-icon-calendar-fill"></text>
						</view>
						
						<view class="cent">
							<text>连续签到7天（{{leiji}}/7）</text>
							<text>
							<text style="margin-right: 10px;" v-if="qd7_exp!==0">经验+{{qd7_exp}} </text><text v-if="qd7_jf!==0">{{assetsname}}+{{qd7_jf}}</text>
							</text>
						</view>
						<view v-if="leiji<7" class="complete" style="background: #bbbbbb;" @tap="toQiandao">
							<text>未完成</text>
						</view>
						<view v-else class="complete" @tap="toQiandao">
							<text>已完成</text>
						</view>
					</view>
					<view class="item" v-if="qqbang_to==1">
						<view class="icon">
							<text class="tn-icon-qq"></text>
						</view>
						
						<view class="cent">
							<text>绑定第三方账号（{{qqbang}}/1）</text>
							<text>
							<text style="margin-right: 10px;" v-if="qqbang_exp!==0">经验+{{qqbang_exp}} </text><text v-if="qqbang_jf!==0">{{assetsname}}+{{qqbang_jf}}</text>
							</text>
						</view>
						<view v-if="qqbang==0&&wxbang==0&&wbbang==0" class="complete" style="background: #bbbbbb;" @tap="touserbind">
							<text>去绑定</text>
						</view>
						<view v-else class="complete">
							<text>已绑定</text>
						</view>
					</view>
					<view class="item">
						<view class="icon">
							<text class="tn-icon-trusty-fill"></text>
						</view>
						
						<view class="cent">
							<text>通过蓝V申请（{{company}}/1）</text>
							<text>
							<text style="margin-right: 10px;" v-if="rz_exp!==0">经验+{{rz_exp}} </text><text v-if="rz_jf!==0">{{assetsname}}+{{rz_jf}}</text>
							</text>
						</view>
						<view v-if="company<1" class="complete" style="background: #bbbbbb;" @tap="toRz">
							<text>去申请</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					<view class="item">
						<view class="icon">
							<text class="tn-icon-identity-fill"></text>
						</view>
						
						<view class="cent">
							<text>完成实名认证（{{continuous}}/1）</text>
							<text>
							<text style="margin-right: 10px;" v-if="sm_exp!==0">经验+{{sm_exp}} </text><text v-if="sm_jf!==0">{{assetsname}}+{{sm_jf}}</text>
							</text>
						</view>
						<view v-if="continuous<1" class="complete" style="background: #bbbbbb;" @tap="toSm">
							<text>去完成</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					<view class="item" v-if="post_to==1">
						<view class="icon">
							<text class="tn-icon-inventory-fill"></text>
						</view>
						
						<view class="cent" v-if="post_to==1">
							<text>发布1条贴子（{{post_num}}/{{post_max}}）</text>
							<text>
							<text style="margin-right: 10px;" v-if="post_exp!==0">经验+{{post_exp}} </text><text v-if="post_jf!==0">{{assetsname}}+{{post_jf}}</text>
							</text>
						</view>
						<view v-if="post_num<post_max" class="complete" style="background: #bbbbbb;">
							<text>未完成</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					<view class="item" v-if="wz_to==1">
						<view class="icon">
							<text class="tn-icon-order-fill"></text>
						</view>
						
						<view class="cent">
							<text>投稿1篇文章（{{wz_num}}/{{wz_max}}）</text>
							<text>
							<text style="margin-right: 10px;" v-if="wz_exp!==0">经验+{{wz_exp}} </text><text v-if="wz_jf!==0">{{assetsname}}+{{wz_jf}}</text>
							</text>
						</view>
						<view v-if="wz_num<wz_max" class="complete" style="background: #bbbbbb;">
							<text>未完成</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					<view class="item" v-if="shoucang_to==1">
						<view class="icon">
							<text class="tn-icon-live-stream-fill"></text>
						</view>
						<view class="cent">
							<text>发布1条动态（{{shoucang_num}}/{{shoucang_max}}）</text>
							<text>
							<text style="margin-right: 10px;" v-if="shoucang_exp!==0">经验+{{shoucang_exp}} </text><text v-if="shoucang_jf!==0">{{assetsname}}+{{shoucang_jf}}</text>
							</text>
						</view>
						<view v-if="shoucang_num<shoucang_max" class="complete" style="background: #bbbbbb;">
							<text>未完成</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					<view class="item" v-if="pinglun_to==1">
						<view class="icon">
							<text class="tn-icon-message-fill"></text>
						</view>
						<view class="cent">
							<text>评论1条贴子（{{pinglun_num}}/{{pinglun_max}}）</text>
							<text>
							<text style="margin-right: 10px;" v-if="pinglun_exp!==0">经验+{{pinglun_exp}} </text><text v-if="pinglun_jf!==0">{{assetsname}}+{{pinglun_jf}}</text>
							</text>
						</view>
						<view v-if="pinglun_num<pinglun_max" class="complete" style="background: #bbbbbb;">
							<text>未完成</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					
					<view class="item" v-if="dashang_to==1">
						<view class="icon">
							<text class="tn-icon-money-fill"></text>
						</view>
						<view class="cent">
							<text>打赏1条帖子（{{dashang_num}}/{{dashang_max}}）</text>
							<text>
							<text style="margin-right: 10px;" v-if="dashang_exp!==0">经验+{{dashang_exp}} </text><text v-if="dashang_jf!==0">{{assetsname}}+{{dashang_jf}}</text>
							</text>
						</view>
						<view v-if="dashang_num<dashang_max" class="complete" style="background: #bbbbbb;">
							<text>未完成</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					
					<view class="item" v-if="guanzhu_to==1">
						<view class="icon">
							<text class="tn-icon-add-fill"></text>
						</view>
						<view class="cent">
							<text>关注1个用户（{{guanzhu_num}}/{{guanzhu_max}}）</text>
							<text>
							<text style="margin-right: 10px;" v-if="guanzhu_exp!==0">经验+{{guanzhu_exp}} </text><text v-if="guanzhu_jf!==0">{{assetsname}}+{{guanzhu_jf}}</text>
							</text>
						</view>
						<view v-if="guanzhu_num<guanzhu_max" class="complete" style="background: #bbbbbb;">
							<text>未完成</text>
						</view>
						<view v-else class="complete">
							<text>已完成</text>
						</view>
					</view>
					
				</view>
			</view>
			<view class="cu-modal" :class="modalName=='Modal'?'show':''" style="padding: 15px 15px;">
				<view class="cu-dialog" style="border-radius: 15px">
					<view class="cu-bar bg-white justify-end" style="background: white;">
						<view class="content">任务说明</view>
						
				</view>
					<view class="padding text-left" style="background: white;" v-html="renwutext">
					
					</view>
					<view class="padding text-center" style="background: white;">
						<tn-button style="margin-left: 20rpx;" backgroundColor="#3cc9a4" fontColor="#fff" @tap="hideModal">好的</tn-button>
					</view>
					
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
		import waves from '@/components/xxley-waves/waves.vue';
		import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
export default {
	data() {
		return {
			StatusBar: this.StatusBar,
			CustomBar: this.CustomBar,
			NavBar:this.StatusBar +  this.CustomBar,
			userInfo:{
				name:''
			},
			uid: 0,
			qqbang_jf: 0,
			qqbang_exp: 0,
			modalName: null,
			qd7_jf: 0,
			qd7_exp: 0,
			leiji: 0,
			post_jf: 0,
			post_exp: 0,
			qqbang_to: 0,
			post_to: 0,
			wz_to: 0,
			dashang_to: 0,
			pinglun_to: 0,
			shoucang_to: 0,
			guanzhu_to: 0,
			pinglun_jf: 0,
			dashang_jf: 0,
			guanzhu_jf: 0,
			dashang_exp: 0,
			pinglun_exp: 0,
			shoucang_exp: 0,
			shoucang_jf: 0,
			guanzhu_exp: 0,
			qqlogin: 0,
			assetsname:"",
			renwutext:"",
			post_num: 0,
			shoucang_num: 0,
			dashang_num: 0,
			pinglun_num: 0,
			guanzhu_num: 0,
			qqbang: 0,
			sm_exp: 0,
			sm_jf: 0,
			rz_exp: 0,
			rz_jf: 0,
			wz_exp: 0,
			wz_jf: 0,
			wz_num: 0,
			wz_max: 0,
			post_max: 0,
			shoucang_max: 0,
			dashang_max: 0,
			pinglun_max: 0,
			wxbang: 0,
			wbbang: 0,
			continuous: 0,
			company: 0,
			guanzhu_max: 0,
		}
	},

	onShow(){
		
				var that = this;
				
				// #ifdef APP-PLUS
				uni.hideTabBar({
					animation: false
				})
				
				plus.navigator.setStatusBarStyle("light")
				// #endif
				if (localStorage.getItem('userinfo')) {
				
					that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.userInfo.style = "background-image:url(" + that.userInfo.avatar + ");"
					that.avatar = that.userInfo.avatar;
					that.uid = that.userInfo.uid;
					that.assets = that.userInfo.assets;
					that.group = that.userInfo.group;
					if (that.userInfo.screenName) {
						that.name = that.userInfo.screenName;
					} else {
						that.name = that.userInfo.name;
					}
				}
				
			},
	
			onLoad() {
				var that = this;
				// #ifdef APP-PLUS || MP
				that.NavBar = this.CustomBar;
				plus.navigator.setStatusBarStyle('light');
				// #endif
				
			},
	mounted() {
		this.userStatus();
	this.getset();
	this.getleiji()
	this.getjiangli()
	this.getrenwu()
	},
	
			methods: {
				
				userStatus() {
					var that = this;
					that.$Net.request({
				
						url: that.$API.userStatus(),
						data: {
							"token": that.token
						},
						header: {
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						method: "get",
						dataType: 'json',
						success: function(res) {
							if (res.data.code == 0) {
				
							} else {
				
								if (localStorage.getItem('userinfo')) {
				
									var userInfo = JSON.parse(localStorage.getItem('userinfo'));
									if (userInfo.screenName) {
										that.name = userInfo.screenName;
									} else {
										that.name = userInfo.name;
									}
									that.uid = res.data.data.uid;
									if (res.data.data.lv) {
										userInfo.lv = res.data.data.lv;
									}
									if (res.data.data.isvip) {
										userInfo.isvip = res.data.data.isvip;
									}
									if (res.data.data.vip) {
										userInfo.vip = res.data.data.vip;
									}
									if (res.data.data.experience) {
										userInfo.experience = res.data.data.experience;
									}
									localStorage.setItem('userinfo', JSON.stringify(userInfo));
									// if(res.data.data.avatar){
									// 	that.userInfo = res.data.data.avatar;
									// }
				
								}
				
							}
						},
						fail: function(res) {
							uni.showToast({
								title: "QAQ信号君失踪了~",
								icon: 'none'
							})
						}
					})
				},
				getset() {
				  var that = this;
				      uni.request({
				        url:that.$API.SPset(),
				        method:'GET',
				        dataType:"json",
				        success(res) {
	
					that.assetsname = res.data.assetsname;
					that.renwutext = res.data.renwutext;
						  
				          //console.log(res);
				        },
				        fail(error) {
				          console.log(error);
				        }
				      })
				},
		showModal(e) {
			this.modalName = e.currentTarget.dataset.target
		},
		hideModal(e) {
			this.modalName = null
		},
		toRz() {
			var that = this;
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
				uni.showToast({
					title: "请先登录哦",
					icon: 'none'
				})
				return false;
			}
			uni.navigateTo({
				url: '/pages/user/identifyblue'
			});
		},
		toSm() {
			var that = this;
				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
				uni.showToast({
					title: "请先登录哦",
					icon: 'none'
				})
				return false;
			}
			uni.navigateTo({
				url: '/pages/user/identify'
			});
		},
		getrenwu() {
		  var that = this;
		  		uni.request({
		  			url:that.$API.SPgetrenwu(),
		  			method:'GET',
		  			data:{
		  				uid:that.uid
		  			},
		  			dataType:"json",
		  			success(res) {
						that.sm_jf = res.data.sm_jf;
						that.sm_exp = res.data.sm_exp;
						that.rz_jf = res.data.rz_jf;
						that.rz_exp = res.data.rz_exp;
						that.wz_max = res.data.wz_max;
						that.wz_exp = res.data.wz_exp;
						that.wz_num = res.data.wz_num;
						that.wz_jf = res.data.wz_jf;
						
						that.post_jf = res.data.post_jf;
						that.post_exp = res.data.post_exp;
						that.dashang_exp = res.data.dashang_exp;
						that.pinglun_exp = res.data.pinglun_exp;
						that.pinglun_jf = res.data.pinglun_jf;
						that.dashang_jf = res.data.dashang_jf;
						that.guanzhu_jf = res.data.guanzhu_jf;
						that.shoucang_exp = res.data.shoucang_exp;
						that.shoucang_jf = res.data.shoucang_jf;
						that.guanzhu_exp = res.data.guanzhu_exp;
						that.qqbang_jf = res.data.qqbang_jf;
						that.qqbang_exp = res.data.qqbang_exp;
						that.post_num = res.data.post_num;
						that.shoucang_num = res.data.shoucang_num;
						that.dashang_num = res.data.dashang_num;
						that.pinglun_num = res.data.pinglun_num;
						that.guanzhu_num = res.data.guanzhu_num;
						that.post_max = res.data.post_max;
						that.shoucang_max = res.data.shoucang_max;
						that.dashang_max = res.data.dashang_max;
						that.guanzhu_max = res.data.guanzhu_max;
						that.pinglun_max = res.data.pinglun_max;
		  			  //console.log(res);
		  			},
		  			fail(error) {
		  			  console.log(error);
		  			}
		  			
		  		})
		  		
		},
		getjiangli(){
			var that = this;
			
			  		uni.request({
			  			url:that.$API.SPqiandaojl(),
			  			method:'GET',
			  			data:{
			  				uid:that.uid
			  			},
			  			dataType:"json",
			  			success(res) {
							that.qd7_exp = res.data.experience_7day;
							that.qd7_jf = res.data.assets_7day;
			  			  //console.log(res);
			  			},
			  			fail(error) {
			  			  console.log(error);
			  			}
			  			
			  		})
			},
		getleiji() {
		  var that = this;
		  		uni.request({
		  			url:that.$API.SPleiji(),
		  			method:'GET',
		  			data:{
		  				uid:that.uid
		  			},
		  			dataType:"json",
		  			success(res) {
						that.continuous = res.data.continuous;
						that.company = res.data.company;
						that.leiji = res.data.leiji;
						that.qqbang = res.data.qqbang;
						that.wxbang = res.data.wxbang;
						that.wbbang = res.data.wbbang;
						that.qqlogin = res.data.qqlogin;
						that.qqbang_to = res.data.qqbang_to;
						that.post_to = res.data.post_to;
						that.wz_to = res.data.wz_to;
						that.dashang_to = res.data.dashang_to;
						that.pinglun_to = res.data.pinglun_to;
						that.shoucang_to = res.data.shoucang_to;
						that.guanzhu_to = res.data.guanzhu_to;
		  			},
		  			fail(error) {
		  			  console.log(error);
		  			}
		  			
		  		})
		},
		toQiandao(){
			uni.navigateTo({
			    url: '/pages/user/signin'
			});
		},
		touserbind(){
			uni.navigateTo({
			    url: '/pages/user/userbind'
			});
		},
		goBack() {
						uni.navigateBack({
							delta: 1
						});
		 },
		

		loadData() {
		  var that = this;
		  that.getrenwu();
		}
	}
}
</script>

<style lang="scss" scoped>
	.grade{
		& text{
			font-family: PingFangSC-Semibold, PingFang SC;
		}
		.bar{
			.left image{
				width: 48rpx;
				height: 48rpx;
			}
			.center text{
				font-size: 32rpx;
				font-weight: 600;
				color: #FFFFFF;
				line-height: 44rpx;
			}
			.right{
				width: 148rpx;
				height: 44rpx;
				background: #3cc9a4;
				border-radius: 22rpx 0 0 22rpx;
				border: 2rpx solid #FFFFFF;
				text-align: center;
				margin-right: -34rpx;
				>text{
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					color: #FFFFFF;
					line-height: 44rpx;
				}
			}
		}
		.top{
			position: absolute;
			z-index: 1;
			width: 750rpx;
			height: 280rpx;
			padding: 0 50rpx;
			border-radius: 0 0 20% 20%/0 0 10% 10%;
			background: linear-gradient(360deg, #44e4ba 0%, #28866b 100%);
			.line{
				width: 100%;
				height: 142rpx;
				margin-top: 88rpx;
			}
			.num{
				display: flex;
				justify-content: center;
				margin-top: -84rpx;
				>view{
					
					width: 144rpx;
					height: 136rpx;
					justify-content: center;
					align-items: center;
					display: flex;
					
					& image{
						width: 144rpx;
						height: 136rpx;
					}
					& text{
						font-size: 40rpx;
						font-weight: 600;
						color: #999999;
						line-height: 90rpx;
						text-shadow: 0rpx 2rpx 1rpx rgba(0,0,0,0.0200);
						background: linear-gradient(to bottom right, #bababa, #727272, #595959);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						position: absolute;
					}
					
				}
			}
			.numerical{
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-top: 24rpx;
				
				.experience{
					
					display: flex;
					align-items: center;
					.pre{
						font-size: 28rpx;
						color: #FFFFFF;
						line-height: 40rpx;
					}
					>image{
						width: 36rpx;
						height: 30rpx;
						margin: 0 6rpx;
					}
					.expNum{
						font-size: 36rpx;
						font-weight: 600;
						color: #FFFFFF;
					}
				}
				.differ{
					>text{
						font-size: 20rpx;
						color: #999999;
						line-height: 42rpx;
					}
				}
			}
		}
		.cont{
			
			position: relative;
			 z-index: 2;
			padding: 155rpx 40rpx;
			
			.people{
				
				width: 100%;
				height: 116rpx;
				background: linear-gradient(90deg, #43e3b9 0%, #3cc9a4 51%, #39bf9b 100%);
				border-radius: 58rpx;
				margin-top: 12rpx;
				padding: 0 36rpx 0 18rpx;
				display: flex;
				align-items: center;
				.image{
					width: 84rpx;
					height: 84rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 20rpx;
					>image{
						width: 84rpx;
						height: 84rpx;
						border-radius: 50%;
					}
					>view{
						
						width: 92rpx;
						height: 92rpx;
						border-radius: 50%;
						border: 2rpx solid #FFFFFF;
						filter: blur(2rpx);
						position: absolute;
					}
				}
				.text{
					flex: 1;
					display: flex;
					align-items: center;
					>text:first-child{
						font-size: 36rpx;
						font-weight: 600;
						color: #FFFFFF;
						line-height: 50rpx;
						margin-right: 14rpx;
					}
					>text:last-child{
						height: 34rpx;
						background: #BDA3FF;
						border-radius: 17rpx;
						padding: 0 14rpx;
						font-size: 24rpx;
						color: #FFFFFF;
						line-height: 34rpx;
					}
				}
				.money{
					display: flex;
					flex-direction: column;
					>text:first-child{
						font-size: 36rpx;
						font-weight: 600;
						color: #FFFFFF;
						line-height: 50rpx;
					}
					>text:last-child{
						font-size: 24rpx;
						color: #DBCDFF;
						line-height: 34rpx;
					}
				}
			}
			.experience{
				
				width: 100%;
				height: 600rpx;
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 40rpx 18rpx;
				margin-top: 24rpx;
				.title{
					display: block;
					font-size: 32rpx;
					font-weight: 600;
					color: #333333;
					line-height: 44rpx;
					margin-bottom: 22rpx;
				}
				.number{
					display: flex;
					margin: 22rpx 0 56rpx 0;
					justify-content: space-between;
					>text{
						font-size: 28rpx;
						font-weight: 600;
						color: #000000;
						line-height: 40rpx;
						&:nth-child(2){
							font-size: 28rpx;
							font-weight: 400;
							color: #999999;
							line-height: 40rpx;
						}
					}
				}
				.gift{
					display: flex;
					.item{
						display: flex;
						flex-direction: column;
						align-items: center;
						margin-right: 22rpx;
						>view{
							width: 70rpx;
							height: 70rpx;
							background: #FFF0F0;
							border-radius: 20rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							>image{
								width: 36rpx;
								height: 40rpx;
							}
						}
						.special{
							background: #FFFAF0;
						}
						>text{
							font-size: 20rpx;
							color: #999999;
							line-height: 28rpx;
							margin-top: 4rpx;
						}
					}
				}
				.button{
					width: 368rpx;
					height: 100rpx;
					margin: auto;
					margin-top: 60rpx;
					background: linear-gradient(90deg, #44e9bd 0%, #3cc9a4 100%);
					border-radius: 58rpx;
					text-align: center;
					>text{
						font-size: 32rpx;
						font-weight: 600;
						color: #FFFFFF;
						line-height: 100rpx;
					}
				}
			}
			.experience2{
				width: 100%;
				height: 300rpx;
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 40rpx 18rpx;
				margin-top: 24rpx;
				.title{
					display: block;
					font-size: 32rpx;
					font-weight: 600;
					color: #333333;
					line-height: 44rpx;
					margin-bottom: 22rpx;
				}
				.number{
					display: flex;
					margin: 22rpx 0 56rpx 0;
					justify-content: space-between;
					>text{
						font-size: 28rpx;
						font-weight: 600;
						color: #000000;
						line-height: 40rpx;
						&:nth-child(2){
							font-size: 28rpx;
							font-weight: 400;
							color: #999999;
							line-height: 40rpx;
						}
					}
				}
			}
			.task{
				width: 100%;
				height: 1450rpx;
				background: #FFFFFF;
				border-radius: 30rpx;
				margin-top: 40rpx;
				padding: 30rpx 40rpx;
				display: flex;
				flex-direction: column;
				box-shadow: 0px 4px 15px #a9a9a9;
				.title{
					font-size: 32rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #333333;
					line-height: 44rpx;
				}
				.text{
					font-size: 24rpx;
					color: #999999;
					line-height: 34rpx;
					margin: 6rpx 0 22rpx 0;
				}
				.list{
					display: flex;
					justify-content: space-between;
					margin-bottom: 74rpx;
					>view{
						width: 92rpx;
						height: 116rpx;
						background: linear-gradient(90deg, #41dbb2 0%, #3cc9a4 100%);
						border-radius: 6rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						>image{
							width: 58rpx;
							height: 56rpx;
						}
						>text{
							font-size: 20rpx;
							color: #FFFFFF;
							line-height: 28rpx;
							margin-top: 6rpx;
						}
					}
				}
				.invitation{
					.item{
						display: flex;
						align-items: center;
						margin-bottom: 36rpx;
						.icon{
							width: 84rpx;
							height: 84rpx;
							border-radius: 50%;
							background: #eefdff;
							display: flex;
							justify-content: center;
							align-items: center;
							margin-right: 14rpx;
							>text{
								font-size: 60rpx;
								color: #00BCD4;
							}
						}
						.cent{
							flex: 1;
							display: flex;
							flex-direction: column;
							>text:first-child{
								font-size: 28rpx;
								color: #333333;
								line-height: 40rpx;
							}
							>text:last-child{
								font-size: 24rpx;
								color: #999999;
								line-height: 34rpx;
							}
						}
						.complete{
							width: 122rpx;
							height: 52rpx;
							background: linear-gradient(90deg, #43e2b8 0%, #3cc9a4 100%);
							border-radius: 58rpx;
							text-align: center;
							>text{
								font-size: 20rpx;
								color: #FFFFFF;
								line-height: 52rpx;
							}
						}
					}
				}
			}
		}
	}
</style>