<template>
	<view class="user-header" style="width: 180upx;">
		<view class="user-rz-container">
			<view class="user-rz" :style="{ backgroundImage: 'url(' + avatar + ')' }">
				<!-- 头像框图层，z-index设为90，确保在头像上方，蓝V下方 -->
				<image v-if="xqy_avatarframe && AvatarItem" style="width:220rpx;height:220rpx;border-radius:0px;z-index: 90;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%) scale(1.1);max-width: initial;" :src="AvatarItem" mode="aspectFit"></image>
			</view>
			<!-- 蓝V认证图标，z-index设为100，确保在最上层 -->
			<image v-if="lvrz==1" class="user-rz-icon" :src="rzImg" mode="aspectFill" style="z-index: 100;"></image>
		</view>
	</view>
</template>

<script>
import {
	localStorage
} from '../../js_sdk/mp-storage/mp-storage/index.js'
import darkModeMixin from '@/utils/darkModeMixin.js'
export default {
	mixins: [darkModeMixin],
    props: {
        avatar: {
		  type: String,
		  default: ''
		},
		lvrz: {
		  type: Number,
		  default: 0
		},
        uid: {
          type: [String, Number],
          default: ''
        }
    },
	watch: {
	    lvrz(newVal) {
	      console.log('lvrz updated to: ', newVal);
	    },
	    uid(newVal) {
	      // 当用户ID变化时，重新获取头像框
	      if(this.xqy_avatarframe && newVal) {
	        this.getAvatarFrameWithCache();
	      }
	    }
	  },
	name: "avatarItem",
	data() {
		return {
			AvatarItem:"",
			userinfo:null,
			xqy_avatarframe:false,
			rzImg: this.$API.SPRz(),
            // 添加缓存相关变量
            frameCache: {},
            cacheExpiration: 5 * 60 * 1000, // 5分钟缓存
            requestInProgress: false
		};
	},
	mounted() {
		const that = this;
		if (localStorage.getItem('userinfo')) {
			that.userinfo = JSON.parse(localStorage.getItem('userinfo'));
		} else {
			that.userinfo = null;
		}
		
		// 获取已开启的插件列表
		var cachedPlugins = localStorage.getItem('getPlugins');
		if (cachedPlugins) {
			const pluginList = JSON.parse(cachedPlugins);
			// 检查插件是否存在于插件列表中
			that.xqy_avatarframe = pluginList.includes('xqy_avatar_frame');
		}
		if(that.xqy_avatarframe){//如果开启了插件
			that.getAvatarFrameWithCache();
		}
	},
	methods: {
        // 带缓存的头像框获取方法
        getAvatarFrameWithCache() {
            const that = this;
            // 确定要查询的用户ID
            const queryUid = that.uid || (that.userinfo ? that.userinfo.uid : '');
            if (!queryUid) return;
            
            // 检查是否有缓存
            const cacheKey = `avatar_frame_${queryUid}`;
            const cachedData = localStorage.getItem(cacheKey);
            
            if (cachedData) {
                try {
                    const data = JSON.parse(cachedData);
                    // 检查缓存是否过期
                    if (data.timestamp && (Date.now() - data.timestamp < that.cacheExpiration)) {
                        that.AvatarItem = data.frameUrl || '';
                        return;
                    }
                } catch (e) {
                    // 缓存解析错误，继续获取新数据
                }
            }
            
            // 防止重复请求
            if (that.requestInProgress) return;
            that.requestInProgress = true;
            
            // 缓存不存在或已过期，发起请求
            that.getAvatarFrameByid();
        },
		
		getAvatarFrameByid(){
			var that = this;
			// 确定要查询的用户ID
			const queryUid = that.uid || (that.userinfo ? that.userinfo.uid : '');
			if (!queryUid) {
				that.requestInProgress = false;
				return;
			}
			that.$Net.request({
				url:that.$API.PluginLoad('xqy_avatar_frame'),
				data: {
					"action":"get_user_frames",
					"op": "list",
					"type": "view",
					"uid": queryUid
				},
				method: "post",
				dataType: 'json',
				success: function(res) {
					try {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame && wearingFrame.frame_url) {
								that.AvatarItem = wearingFrame.frame_url;
								// 缓存结果
								localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
									frameUrl: wearingFrame.frame_url,
									timestamp: Date.now()
								}));
							} else {
								// 缓存空结果
								localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
									frameUrl: '',
									timestamp: Date.now()
								}));
							}
						} else {
							// 尝试其他可能的响应格式
							if (res.data && res.data.data && res.data.data.frame_url) {
								that.AvatarItem = res.data.data.frame_url;
								// 缓存结果
								localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
									frameUrl: res.data.data.frame_url,
									timestamp: Date.now()
								}));
							} else {
								// 缓存空结果
								localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
									frameUrl: '',
									timestamp: Date.now()
								}));
							}
						}
					} catch (error) {
						console.error('处理头像框数据时出错:', error);
						// 缓存空结果
						localStorage.setItem(`avatar_frame_${queryUid}`, JSON.stringify({
							frameUrl: '',
							timestamp: Date.now()
						}));
					}
                that.requestInProgress = false;
				},
				fail: function(res) {
					console.error('获取头像框失败:', res);
                    that.requestInProgress = false;
				}
			})
		},
	}
}
</script>

<style>
	.text-content {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 10;
		-webkit-box-orient: vertical;
	}
	
	.text-shojo2 {
		color: #ff6c3e;
	}
	
	.user-info-data {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.xyy {
		margin-left: 0px;
	}
	
	.search-type {
		display: flex;
		position: relative;
		align-items: center;
		justify-content: space-around;
		border-bottom: solid 4upx #f3f3f3;
	}
	
	.search-type-box.active {
		border-bottom: solid 4upx #000000;
		color: #000000;
	}
	
	.user-info-data-box {
		flex-grow: 1;
		text-align: center;
	}
	
	.user-data-num {
		margin-right: 0px;
		font-size: 36upx;
	}
	
	.user-data-label {
		font-size: 28upx;
	}
	
	.sup-script {
		font-size: 28upx;
		font-weight: 400;
	}
	.user-rz-container image{
		background-color: initial!important;
		border: initial!important;
	}
	
	.cu-bar .action2 {
		background: #00000057;
		width: 80upx;
		height: 80upx;
		border-radius: 50%;
		/* text-align: center; */
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.grid2 {
		display: flex;
		flex-direction: row;
		justify-content: normal;
	}
	
	.cu-bar2 {
		margin-top: 10upx;
	}
	
	.userIndex .header .action {
		font-size: 36upx;
	}
	
	.user-rz-container {
		width: 200upx;
		height: 200upx;
		position: relative;
		display: inline-block;
		overflow: visible; /* 确保头像框不会被裁剪 */
	}
	
	.user-rz {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		border: 3px solid white;
		overflow: visible; /* 确保头像框不会被裁剪 */
		position: relative; /* 确保子元素可以相对于它定位 */
		z-index: 80; /* 头像层级设为80，低于头像框 */
	}
	
	.user-rz-icon {
		position: absolute;
		right: -5upx;
		bottom: -5upx;
		width: 60upx;
		height: 60upx;
		z-index: 100; /* 确保蓝V认证图标在最上层 */
		border-radius: 50%;
		background-color: white;
		padding: 2upx;
		box-sizing: border-box;
	}
	
	/*  #ifdef MP || APP-PLUS */
	.user-header image {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
	}
	
	/*  #endif */
	.user-info-main {
		/*  #ifdef H5 || APP-PLUS */
		height: 480rpx;
		/*  #endif */
		/*  #ifdef MP */
		height: 450rpx;
		/*  #endif */
	}
	
	.tn-margin-top-xxl {
		margin-top: 60upx;
	}
	
	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
</style>