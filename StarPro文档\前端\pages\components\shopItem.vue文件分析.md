# shopItem.vue 文件分析

## 概述

`shopItem.vue` 组件用于在列表中展示单个商品信息。它支持两种模式：普通用户视图和管理员视图。

-   **用户视图**: 显示商品图片、标题、价格（区分VIP价）、销量和库存。点击卡片跳转到商品详情页。
-   **管理员视图**: 显示商品图片、标题、禁用状态，并提供审核通过、编辑和删除的操作按钮。

组件会根据用户是否为VIP来显示不同的价格。

## 文件信息
- **文件路径**：`APP前端部分/pages/components/shopItem.vue.md`
- **主要功能**：以卡片形式展示商品信息，支持用户浏览和管理员操作。

## 主要组成部分分析

### 1. Props
   - **`item`**: `Object` (默认 `{}`) - 商品数据对象。
     - `id`: `String|Number` - 商品ID。
     - `imgurl`: `String` - 商品主图URL。
     - `title`: `String` - 商品标题。
     - `status`: `Number` - 商品状态 (0: 待审核, 1: 已上架, 2: 已禁用)。
     - `price`: `Number` - 商品价格。
     - `vipDiscount`: `Number` - VIP折扣率 (例如 0.8 表示8折)。
     - `sellNum`: `Number` - 销量。
     - `num`: `Number` - 库存 (-1 可能表示无限库存)。
     - (可能还包含 `integral` 积分抵扣相关字段，但模板中注释掉了)
   - **`isAdmin`**: `Boolean` (默认 `false`) - 是否为管理员模式。

### 2. 模板 (`<template>`)
   - **容器**: 区分 APP/H5 (宽度50%) 和 MP (宽度100%)。
   - **商品卡片 (`tn-blogger-content__wrap`)**: 点击事件 `@tap="shopInfo(item)"`。
   - **商品图片 (`image-picbook`)**: 背景图设置为 `item.imgurl`。
   - **商品标题 (`tn-blogger-content__label`)**: 
     - 管理员模式下，根据 `item.status` 显示状态前缀 (如 `[已禁用]`)。
     - 显示截断后的商品标题 (`item.title` 或 "无标题商品")。
   - **价格与库存区 (`v-if="!isAdmin"`)**: 
     - **价格**: 
       - 如果当前用户是VIP (`isvip == 1`)，显示折扣价 `parseInt(item.price * item.vipDiscount)`。
       - 否则，显示原价 `item.price`。
       - 显示货币名称 `currencyName`。
     - **销量**: 显示 `item.sellNum`。
     - **库存**: 如果 `item.num != -1`，显示 `item.num`。
   - **管理员操作区 (`v-else`)**: 
     - **审核通过**: `item.status == 0` 时显示，点击 `@tap="auditShop(item.id)"`。
     - **编辑**: 点击 `@tap="editShop(item)`。
     - **删除**: 点击 `@tap="deleteShop(item.id)"`。

### 3. 脚本 (`<script>`)
   - **依赖**: `localStorage`。
   - **`name`**: "forumItem" (错误，应为 "shopItem")。
   - **`props`**: 定义了 `item`, `isAdmin`。
   - **`data`**: 
     - `vipDiscount`: `Number` - VIP折扣率 (从API获取)。
     - `vipPrice`: `Number` - (已定义但未使用，可能是VIP开通价格)。
     - `scale`: `Number` - (已定义但未使用，可能是积分兑换比例)。
     - `isvip`: `Number` - 当前用户是否为VIP (从 `localStorage` 获取)。
     - `vip`: `Number` - (已定义但未使用)。
     - `currencyName`: `String` - 货币名称 (从API获取)。
     - `group`: `String` - 当前用户组 (从 `localStorage` 获取)。
   - **`created()`**: 获取本地存储的用户信息，设置 `group` 和 `isvip`。
   - **`mounted()`**: 调用 `getVipInfo()` 获取VIP配置，调用 `getleiji()` 获取货币名称。
   - **`methods`**: 
     - **`getleiji()`**: 调用 `$API.SPset()` 获取货币名称 (`assetsname`) 并存入 `currencyName`。
     - **`shopInfo(data)`**: 
       - 如果是管理员模式 (`that.isAdmin`)，则不执行跳转。
       - 否则，跳转到商品详情页 `/pages/shop/shopinfo?sid=`。
     - **`getVipInfo()`**: 调用 `$API.getVipInfo()` 获取VIP折扣率 (`vipDiscount`) 等信息。
     - **`getUserInfo(uid)`**: (已定义但未使用，可能是从其他组件复制过来的)。
     - **`toUserContents(data)`**: (已定义但未使用，可能是从其他组件复制过来的)。
     - **`deleteShop(sid)`**: 
       - 管理员操作。
       - 弹出确认框。
       - 调用 `$API.deleteShop()` 删除商品。
       - 成功后触发 `deletef` 事件通知父组件。
     - **`auditShop(sid)`**: 
       - 管理员操作。
       - 弹出确认框。
       - 调用 `$API.auditShop()` 审核通过商品。
       - 成功后触发 `auditf` 事件通知父组件。
     - **`editShop(item)`**: 
       - 管理员操作。
       - 跳转到商品编辑页 `/pages/shop/shopfabu?id=`，携带商品ID。

### 4. Emitted Events
   - **`deletef(sid)`**: 管理员删除商品成功后触发。
   - **`auditf(sid)`**: 管理员审核通过商品成功后触发。

## 总结与注意事项

-   `shopItem.vue` 组件清晰地分离了用户视图和管理员视图的展示与交互。
-   组件名称 (`name`) 在脚本中错误地写成了 `forumItem`，应修正为 `shopItem`。
-   VIP价格显示逻辑依赖于从API获取的 `vipDiscount` 和本地存储的用户 `isvip` 状态。
-   货币名称 (`currencyName`) 动态从API获取。
-   管理员操作（审核、编辑、删除）需要登录和相应权限，并通过事件通知父组件更新列表。
-   包含部分未使用或从其他组件遗留的方法 (`getUserInfo`, `toUserContents`) 和 data 属性 (`vipPrice`, `scale`, `vip`)。

## 后续分析建议

-   **组件命名**: 修改脚本中的 `name` 属性为 `shopItem`。
-   **API 依赖**: 查看 `$API.SPset()`, `$API.getVipInfo()`, `$API.deleteShop()`, `$API.auditShop()` 的实现。
-   **父组件交互**: 分析使用此组件的父组件如何处理 `deletef` 和 `auditf` 事件，以及如何传递 `item` 数据和 `isAdmin` 状态。
-   **未使用的代码**: 清理脚本中未使用的 data 属性和方法。
-   **导航**: 确认商品详情页 (`/pages/shop/shopinfo`) 和编辑页 (`/pages/shop/shopfabu`) 的路径和功能。 