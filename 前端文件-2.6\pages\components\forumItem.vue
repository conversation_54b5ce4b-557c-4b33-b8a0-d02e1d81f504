<template>
	<view class="forum-list-box" :class="isDark?'dark':''">
		<view class="forum-list-content forum-shadow">
			<view class="user-rz-qz">
				<view class="forum-list-user">
					<view class="forum-avatar-container" @tap="toUserContents(item.userJson)">
						<view class="forum-avatar" :style="{ backgroundImage: 'url(' + item.userJson.avatar + ')' }">
							<!-- <tn-lazy-load :image="item.userJson.avatar" borderRadius="50%" height="90" mode="aspectFill"> 
							</tn-lazy-load> -->
							<!-- 头像框 -->
							<image v-if="xqy_avatarframe" style="width:100%;height:100%;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%) scale(1.1);z-index:90;" :src="getAvatarFrameWithCache(item.userJson.uid)" mode="aspectFit"></image>
						</view>
						<image class="user-rz-icon-qz" :src="rzImg" v-if="item.userJson.lvrz==1" mode="aspectFill" style="z-index:100;"></image>
					</view>

					<view class="forum-userinfo">
						<view class="forum-userinfo-name" :class="item.userJson.isvip>0?'name-vip':''">
							{{item.userJson.name}}
							<view v-if="item.userJson.isGood>0" class="tag-view" style="background-color: #f23a3a;color: #fff;border: 1px solid #f23a3a;">
								靓
							</view>
							<image v-if="item.userJson.isvip>0" :src="vipImg"
								style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;"
								mode="widthFix"></image>
							<image :src="lvImg+getLv(item.userJson.experience)+'.png'"
								style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 44upx;height: 22upx;"
								mode="widthFix"></image>
							<!-- 勋章组件 -->
							<medal-item :uid="item.userJson.uid" @medal-loaded="onMedalLoaded"></medal-item>
						</view>

						<view class="forum-userinfo-date">
							{{formatDate(item.created)}} <text class="margin-left-sm" v-if="$API.localOf()">{{getLocal(item.userJson.local)}}</text>
						</view>
						<!-- <block v-if="item.isAds==0">
						<view class="cu-btn forum-follow-btn" v-if="item.isFollow==0"
							@tap="follow(1,item.userJson.uid)">
							<text class="cuIcon-add"></text>关注
						</view>
						<view class="cu-btn text-red forum-follow-btn isFollow" v-if="item.isFollow==1"
							@tap="follow(0,item.userJson.uid)">
							已关注
						</view>
					</block> -->
						<view class="forum-follow-btn" style="background-color: transparent;padding: 20upx 0;"
							v-if="item.isAds==0" @click="show = true">
							<span class="cuIcon-moreandroid"></span>
						</view>
						<tn-popup v-model="show" mode="bottom" :closeBtn="true" :height="myPurview>0?'50%':'20%'"
							:borderRadius="20">
							<view class="center-container tn-margin-top-xxl">
								<view class="">
									<block v-if="mySelf==false">
										<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
											v-if="item.isFollow==0" @tap="follow(1,item.userJson.uid)">
											<text class="tn-icon-my-add" style="margin-right: 5px;"></text>立即关注
										</view>
										<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
											v-if="item.isFollow==1" @tap="follow(0,item.userJson.uid)">
											<text class="tn-icon-my-reduce" style="margin-right: 5px;"></text>取消关注
										</view>
									</block>
									<!-- <view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
									@tap="toLink('../space/post?type=6&toid='+item.id)">
									<text class="tn-icon-send" style="margin-right: 5px;"></text>转发帖子
								</view> -->
									<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
										@tap="toLike(item.id)">
										<text :class="item.isLikes==1?'tn-icon-like-fill':'tn-icon-like-lack'"
											style="margin-right: 5px;"></text>
										<block v-if="item.isLikes!=1">
											点赞帖子
										</block>
										<block v-else>
											已赞该帖
										</block>
									</view>
									<block v-if="myPurview > 0">
										<block v-if="myPurview >= 3">
											<block v-if="item.isrecommend==0">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toRecommend(item.id,1)">
													<text class="tn-icon-fire" style="margin-right: 5px;"></text>帖子加精
												</view>
											</block>
											<block v-if="item.isrecommend==1">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toRecommend(item.id,0)">
													<text class="tn-icon-fire-fill"
														style="margin-right: 5px;"></text>取消加精
												</view>
											</block>
											<block v-if="item.isTop==0">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toTop(item.id,1)">
													<text class="tn-icon-pushpin" style="margin-right: 5px;"></text>添加置顶
												</view>
											</block>
											<block v-if="item.isTop==1">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toTop(item.id,0)">
													<text class="tn-icon-pushpin-fill"
														style="margin-right: 5px;"></text>取消置顶
												</view>
											</block>

											<block v-if="item.isswiper==0">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toSwiper(item.id,1)">
													<text class="tn-icon-task" style="margin-right: 5px;"></text>设为轮播
												</view>
											</block>
											<block v-if="item.isswiper==1">
												<view
													class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
													@tap="toSwiper(item.id,0)">
													<text class="tn-icon-task-fill"
														style="margin-right: 5px;"></text>取消轮播
												</view>
											</block>
										</block>
										<block v-if="myPurview >= 2">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toDelete(item.id)">
												<text class="tn-icon-delete" style="margin-right: 5px;"></text>删除帖子
											</view>
										</block>
										<block v-if="myPurview == 5">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toBan(item.userJson.uid)">
												<text class="tn-icon-prohibit" style="margin-right: 5px;"></text>封禁用户
											</view>
										</block>
										<block v-if="item.status==1">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toLock(item.id,2)">
												<text class="tn-icon-lock" style="margin-right: 5px;"></text>锁定帖子
											</view>
										</block>
										<block v-if="item.status==2">
											<view
												class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
												@tap="toLock(item.id,1)">
												<text class="tn-icon-unlock" style="margin-right: 5px;"></text>解除锁定
											</view>
										</block>
									</block>
								</view>
							</view>
						</tn-popup>

					</view>
				</view>
			</view>
			<block v-if="item.isAds!=1">
				<view class="forum-list-text" @tap="goInfo(item.id)">
					<view class="forum-list-title text-content-1">
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.isTop==2">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-pushpin tn-tag-content__item--prefix"
									style="color: #d40000;"></text>置顶</text>
						</view>
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.isrecommend==1&&item.sectionJson.slug != 'vip'&&item.isTop!=2">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-pushpin tn-tag-content__item--prefix"
									style="color: #00BCD4;"></text>精华</text>
						</view>
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.sectionJson.slug == 'vip'">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-vip-diamond tn-tag-content__item--prefix"
									style="color: #df9319;"></text>VIP专享</text>
						</view>
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.sectionJson.slug == 'lv4'">
							<text class="tn-tag-content__item--prefix tn-text-bold"
								style="padding-right: 0upx;font-size: 26upx;">
								<text class="tn-icon-vip tn-tag-content__item--prefix"
									style="color: #00b755;"></text>4级专享</text>
						</view>


						{{item.title}}
					</view>
					<view v-if="item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'" class="forum-list-intro text-content-2">
						{{subText(item.text)}}
					</view>
					<view v-if="item.sectionJson.slug == 'vip'" class="forum-list-intro text-content-2">
						该贴内容属于VIP专享！
					</view>
					<view v-if="item.sectionJson.slug == 'lv4'" class="forum-list-intro text-content-2">
						该贴内容属于等级专享！
					</view>
				</view>
				<block v-if="item.images.length==1&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="forum-media forum-one">
						<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[0]" mode="aspectFill" @tap="previewImage(item.images,item.images[0])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
				</block>
				<block v-if="item.images.length==2&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-2 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-qz">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[0]" mode="aspectFill" @tap="previewImage(item.images,item.images[0])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-pm">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[1]" mode="aspectFill" @tap="previewImage(item.images,item.images[1])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
				</block>
				<block v-if="item.images.length==3&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-3 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-qz">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[0]" mode="aspectFill" @tap="previewImage(item.images,item.images[0])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[1]" mode="aspectFill" @tap="previewImage(item.images,item.images[1])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-pm">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[2]" mode="aspectFill" @tap="previewImage(item.images,item.images[2])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
				</block>
				<block v-if="item.images.length==4&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-2 grid-square grid-square-1 grid-square-10">
						<view class="bg-img radius-q">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[0]" mode="aspectFill" @tap="previewImage(item.images,item.images[0])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-p">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[1]" mode="aspectFill" @tap="previewImage(item.images,item.images[1])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
							<!-- #endif -->
						</view>

					</view>
					<view class="grid flex-sub col-2 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-z">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[2]" mode="aspectFill" @tap="previewImage(item.images,item.images[2])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-m">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[3]" mode="aspectFill" @tap="previewImage(item.images,item.images[3])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[3]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[3])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
				</block>
				<block v-if="item.images.length==5&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-3 grid-square grid-square-1 grid-square-10">
						<view class="bg-img radius-q">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[0]" mode="aspectFill" @tap="previewImage(item.images,item.images[0])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[1]" mode="aspectFill" @tap="previewImage(item.images,item.images[1])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-p">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[2]" mode="aspectFill" @tap="previewImage(item.images,item.images[2])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
					<view class="grid flex-sub col-2 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-z">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[3]" mode="aspectFill" @tap="previewImage(item.images,item.images[0])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[3]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-m">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[4]" mode="aspectFill" @tap="previewImage(item.images,item.images[1])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[4]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
				</block>
				<block v-if="item.images.length>5&&item.videos.length!=1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="grid flex-sub col-3 grid-square grid-square-1 grid-square-10">
						<view class="bg-img radius-q">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[0]" mode="aspectFill" @tap="previewImage(item.images,item.images[0])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[0]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[0])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[1]" mode="aspectFill" @tap="previewImage(item.images,item.images[1])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[1]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[1])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-p">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[2]" mode="aspectFill" @tap="previewImage(item.images,item.images[2])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[2]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[2])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
					<view class="grid flex-sub col-3 grid-square grid-square-2 grid-square-10">
						<view class="bg-img radius-z">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[3]" mode="aspectFill" @tap="previewImage(item.images,item.images[3])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[3]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[3])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img">
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[4]" mode="aspectFill" @tap="previewImage(item.images,item.images[4])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[4]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[4])"></tn-lazy-load>
							<!-- #endif -->
						</view>
						<view class="bg-img radius-m">
							<view v-if="item.images.length > 6" class="extra-count">
								<view class="cuIcon-add center-add"> {{ item.images.length-6 }}</view>
							</view>
							<!-- #ifdef MP-WEIXIN -->
							<image :src="item.images[5]" mode="aspectFill" @tap="previewImage(item.images,item.images[5])"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load :image="item.images[5]" mode="aspectFill"
								@tap="previewImage(item.images,item.images[5])"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
				</block>
				<block v-if="item.videos.length==1&&item.sectionJson.slug != 'vip'&&item.sectionJson.slug != 'lv4'">
					<view class="spaceVideo">
						<!--  #ifdef H5 || MP-->
						<video :src="item.videos[0].src" :poster="item.videos[0].poster"></video>
						<!--  #endif -->
						<!--  #ifdef APP-PLUS -->
						<block v-if="isIos">
							<view class="paceVideo2">
								<video :src="item.videos[0].src" http-cache="true" @play="playVedio(item.id)"
									:poster="item.videos[0].poster" @fullscreenchange="screenchange" :id="item.id"
									:title="item.title + ' - ' + item.userJson.name + ' | ' + $API.GetAppName()"></video>
							</view>
						</block>
						<block v-else>
							<view class="paceVideo2">
								<view class="spaceVideo-play" :style="{ 
						            backgroundImage: 'url(' + item.videos[0].poster + ')', 
						            backgroundSize: 'cover', 
						            backgroundRepeat: 'no-repeat', 
						            backgroundPosition: 'center center' 
						         }" @tap="goPlay(item.videos[0].src,item.title,item.userJson.name,item.id)">
									<text class="cuIcon-playfill"></text>
								</view>
							</view>
						</block>
						<!--  #endif -->
					</view>
				</block>
				<block v-if="item.sectionJson.slug == 'vip'">
					<view class="forum-media forum-one">
						<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
							<!-- #ifdef MP-WEIXIN -->
							<image src="../../static/page/vip_img.png" @tap="goInfo(item.id)" mode="aspectFill"></image>
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<tn-lazy-load image="../../static/page/vip_img.png" @tap="goInfo(item.id)" mode="aspectFill"></tn-lazy-load>
							<!-- #endif -->
						</view>
					</view>
				</block>
				<block v-if="item.sectionJson.slug == 'lv4'">
					<view class="forum-media forum-one">
						<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
							<tn-lazy-load image="../../static/page/lv_img.png" @tap="goInfo(item.id)" mode="aspectFill"></tn-lazy-load>
						</view>
					</view>
				</block>
			</block>
			<block v-if="item.isAds==1">
				<view class="forum-list-text" @tap="goAds(item)">
					<view class="forum-list-title">
						<view class="justify-content-item tn-tag-content__item tn-margin-right tn-text"
							v-if="item.isAds==1">
							<text class="tn-tag-content__item--prefix tn-text-bold" style="padding-right: 0upx;">
								广告</text>
						</view>
						{{item.name}}
					</view>
					<view class="forum-list-intro">
						{{item.intro}}
					</view>
				</view>
				<view class="forum-media forum-one">
					<view class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm">
						<!-- #ifdef MP-WEIXIN -->
						<image :src="item.img" mode="aspectFill" @tap="previewImage(item.img,item.img)"></image>
						<!-- #endif -->
						<!-- #ifndef MP-WEIXIN -->
						<tn-lazy-load :image="item.img" mode="aspectFill"
							@tap="previewImage(item.img,item.img)"></tn-lazy-load>
						<!-- #endif -->
					</view>
				</view>
			</block>
			<block v-if="item.isAds!=1">
				<view
					class="article-content-btn article-list-btn flex justify-between tn-margin-top-sm tn-margin-right-lg">
					<view class="content-author flex align-center" style="margin-top: 0upx;line-height: 20upx;"
						@click="goSection(item.sectionJson.id)">
						<view class="custom-tag tn-text-bold section-tag" 
							:style="{ 
								padding: '0 40rpx',
								width: '100%'
							}">
							{{item.sectionJson.name}}
						</view>
					</view>
					<view class="flex align-center" style="color:#666">
						<view class="margin-left-lg" @tap="goInfo(item.id)">
							<span class="tn-icon-eye tn-color-gray" style="font-size: 40upx;"></span>
						</view>
						<text @tap="goInfo(item.id)"> {{item.views}} </text>
						<view class="margin-left-lg" @tap="goInfo(item.id)">
							<span class="tn-icon-comment tn-color-gray" style="font-size: 40upx;"></span>
						</view>
						<text @tap="goInfo(item.id)"> {{item.commentsNum}} </text>
						<view class="margin-left-lg" @tap="toLike(item.id)">
							<span class="tn-color-gray" :class="item.isLikes==1?'tn-icon-like-fill':'tn-icon-like-lack'"
								style="font-size: 40upx;"></span>
						</view>
						<text @tap="toLike(item.id)"> {{item.likes}}</text>
					</view>
				</view>
			</block>
		</view>
		<view class="videoPlay" v-if="isPlay">
			<view class="videoPlay-bg" @tap="isPlay=false">
				<view class="videoPlay-close" @tap="isPlay=true">
					<i class="cuIcon-close"></i>
				</view>
			</view>
			<video :src="curVideo" http-cache="true" @play="playVedio(videoid)" autoplay
				@fullscreenchange="screenchange" :id="videoid" :title="mp4title"></video>
		</view>
	</view>
</template>

<script>
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import { shouldEnableVideoLandscape } from '../../utils/pageConfig.js'
	//#ifdef MP-QQ
	import TnLazyLoad from 'tuniao-ui/components/tn-lazy-load/tn-lazy-load';
	// #endif
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	

import medalItem from './medalItem.vue';

	export default {
		components: {
			medalItem
		},
		mixins: [darkModeMixin],
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
			myPurview: {
				type: Number,
				default: 0
			},
			mySelf: {
				type: Boolean,
				default: false
			},
			sectionId: {
				type: [Number, String],
				default: null
			}
		},
		name: "forumItem",

		data() {
			return {
				// danmuList: [{
				// 		text: '第 1s 出现的弹幕',
				// 		time: 1
				// 	},
				// 	{
				// 		text: '第 3s 出现的弹幕',
				// 		time: 3
				// 	}
				// ],
				vipImg: this.$API.SPvip(),
				lvImg: this.$API.SPLv(),
				show: false,
				isProcessing: false,
				rzImg: this.$API.SPRz(),
				identifyCompany: 0,
				AvatarItem: "",
				mp4title: "帖子包含的视频",
				mp4bt: "",
				mp4name: "",
				videoid: 0,
				isPlay: false,
				avatar: "",
				curVideo: "",
				identifyConsumer: 0,
				isIos: false,
				Rz: false,
				xqy_avatarframe: false,
				avatarFrames: {},
				medals: [],
				medalCached: {}
			};
		},
		mounted() {
			const that = this;
			uni.getSystemInfo({
				success: (res) => {
					if (res.platform == 'ios') {
						that.isIos = true;
					}
				}
			});
			uni.$emit('tOnLazyLoadReachBottom');
			that.avatar = "background-image:url(" + that.item.userJson.avatar + ");"
			if (localStorage.getItem('userinfo')) {

				that.userinfo = JSON.parse(localStorage.getItem('userinfo'));
			} else {
				that.userinfo = null;
			}
			// 获取已开启的插件列表
			var cachedPlugins = localStorage.getItem('getPlugins');
			if (cachedPlugins) {
				const pluginList = JSON.parse(cachedPlugins);
				// 检查插件是否存在于插件列表中
				that.xqy_avatarframe = pluginList.includes('xqy_avatar_frame');
			}
		},
		methods: {
			async onMedalLoaded(medal) {
			    const medalId = medal.id;
			    if (this.medalCached[medalId]) {
			        medal.show = true;
			        return;
			    }
			    this.medalCached[medalId] = true;
			    medal.show = true;
			},
			
			getAvatarFrameWithCache(uid) {
				const that = this;
				if (that.avatarFrames[uid]) {
					return that.avatarFrames[uid];
				}

				that.$Net.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": uid
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame && wearingFrame.frame_url) {
								that.$set(that.avatarFrames, uid, wearingFrame.frame_url);
							}
						}
					},
					fail: function(res) {
						console.error('获取头像框失败:', res);
					}
				})
				return '';
			},
			
			
			goPlay(url, title, name, id) {
				var that = this;
				that.curVideo = url;
				that.mp4bt = title;
				that.mp4name = name;
				that.mp4title = that.mp4bt + ' - ' + that.mp4name + ' | ' + that.$API.GetAppName();
				that.videoid = id
				that.isPlay = true;

			},

			subText(text) {
				text = text.replace(/vip(.*?)\/vip/g, "(该内容仅会员可见)");
				text = text.replace(/audio(.*?)\/audio/g, "(该帖子包含音乐)");
				text = text.replace(/video(.*?)\/video/g, "(该帖子包含视频)");
				return text;
			},
			goSection(id) {
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/home?id=' + id
				});
			},
			playVedio(id) {
				const that = this;
				that.videoContext = uni.createVideoContext(String(id), that);
				
				// 获取圈子ID，优先使用传入的sectionId，否则从item中获取
				const currentSectionId = that.sectionId || (that.item && that.item.sectionJson && that.item.sectionJson.id);
				
				// 检查是否启用横屏播放
				const enableLandscape = shouldEnableVideoLandscape(currentSectionId);
				
				uni.getSystemInfo({
					success: (res) => {
						if (res.platform !== 'ios') {
							if (enableLandscape) {
								that.videoContext.requestFullScreen();
							}
							that.videoContext.play();
						} else {
							//ios避免自动重复执行
							if (that.isProcessing) {
								return;
							}

							that.isProcessing = true;
							if (enableLandscape) {
								that.videoContext.requestFullScreen();
							}
							setTimeout(() => {
								that.videoContext.play();
							}, 500);
							setTimeout(() => {
								that.isProcessing = false;
							}, 800);
							// #ifdef APP-PLUS
							if (enableLandscape) {
								plus.screen.lockOrientation('landscape-primary');
							}
							// #endif

						}
					}
				});

			},
			screenchange(e) {
				const that = this;
				if (!e.detail.fullScreen) {
					that.videoContext.stop()
					that.isPlay = false
					
					// 获取圈子ID，优先使用传入的sectionId，否则从item中获取
					const currentSectionId = that.sectionId || (that.item && that.item.sectionJson && that.item.sectionJson.id);
					
					// 检查是否启用横屏播放
					const enableLandscape = shouldEnableVideoLandscape(currentSectionId);
					
					setTimeout(() => {
						if (that.isIos && enableLandscape) {
							// #ifdef APP-PLUS
							plus.screen.lockOrientation('portrait-primary');
							// #endif
						}
					}, 500);

				}
			},
			getLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var lv = that.$API.getLever(i);
				return lv;
			},
			follow(type, uid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					that.show = false
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				that.item.isFollow = type;
				var data = {
					token: token,
					touid: uid,
					type: type,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.follow(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						that.show = false
						if (res.data.code == 1) {
							if (type == 1) {
								if (localStorage.getItem('userinfo')) {
									that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
									that.uid = that.userInfo.uid;

									uni.request({
										url: that.$API.SPguanzhu(),
										method: 'GET',
										data: {
											uid: that.uid,
										},
										dataType: "json",
										success(res) {},
										fail() {
											setTimeout(function() {
												uni.hideLoading();
											}, 1000);
											uni.showToast({
												title: "网络不太好哦",
												icon: 'none'
											})
										}


									})
								}
							} else {
								if (localStorage.getItem('userinfo')) {
									that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
									that.uid = that.userInfo.uid;

									uni.request({
										url: that.$API.SPquguan(),
										method: 'GET',
										data: {
											uid: that.uid,
										},
										dataType: "json",
										success(res) {},
										fail() {
											setTimeout(function() {
												uni.hideLoading();
											}, 1000);
											uni.showToast({
												title: "网络不太好哦",
												icon: 'none'
											})
										}


									})
								}
							}
							that.item.isFollow = type;
						} else {
							that.item.isFollow = 0;
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			toLike(id) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				if (that.item.isLikes == 1) {
					uni.showToast({
						title: "你已经点赞过了",
						icon: 'none'
					});
					return false;
				} else {
					that.item.isLikes = 1;
				}

				that.item.likes += 1;
				var data = {
					token: token,
					id: id,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.postLikes(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 0) {
							that.item.isLikes = 1;
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			toSwiper(id, type) {
				var that = this;

				var typeText = "确定要添加帖子轮播吗？";
				if (type == 0) {
					typeText = "确定要取消帖子轮播吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							that.item.isswiper = type;
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postSwiper(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 0) {
										that.item.isswiper = 0;
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toTop(id, type) {
				var that = this;

				var typeText = "确定要置顶帖子吗？";
				if (type == 0) {
					typeText = "确定要取消置顶帖子吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							that.item.isTop = type;
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postTop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									that.show = false
									if (res.data.code == 0) {
										that.item.isTop = 0;

									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toLock(id, type) {
				var that = this;

				var typeText = "确定要锁定帖子吗？";
				if (type == 1) {
					typeText = "确定要取消锁定帖子吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {

							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postLock(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 1) {
										that.item.status = type;
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toRecommend(id, type) {
				var that = this;

				var typeText = "确定要加精帖子吗？";
				if (type == 0) {
					typeText = "确定要取消帖子加精吗？";
				}
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"type": type,
					"token": token
				}
				uni.showModal({
					title: typeText,
					success: function(res) {
						if (res.confirm) {
							that.item.isrecommend = type;
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postRecommend(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "post",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 0) {
										that.item.isrecommend = 0;
										that.show = false
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toDelete(id) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"id": id,
					"token": token
				}
				uni.showModal({
					title: '确定要删除该文章吗',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.postDelete(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									that.show = false
									if (res.data.code == 1) {
										uni.request({
											url: that.$API.SPglyremove(),
											method: 'GET',
											data: {
												id: id,
											},
											dataType: "json",
											success(res) {},
											fail() {
												setTimeout(function() {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}


										})
										that.page = 1;
										that.moreText = "加载更多";
										that.isLoad = 0;
										that.getPostList();
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toBan(uid) {
				if (uid == 0) {
					uni.showToast({
						title: "该用户不存在",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/manage/banuser?uid=' + uid
				});
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var now = new Date();
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);

				var timeDiff = now - datetime;
				var seconds = Math.floor(timeDiff / 1000);
				var minutes = Math.floor(timeDiff / (1000 * 60));
				var hours = Math.floor(timeDiff / (1000 * 60 * 60));
				var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
				var months = Math.floor(days / 30);

				var result = "";

				if (seconds < 60) {
					result = seconds + "秒前";
				} else if (minutes < 60) {
					result = minutes + "分钟前";
				} else if (hours < 24) {
					result = hours + "小时前";
				} else if (days < 7) {
					result = days + "天前";
				} else if (year === now.getFullYear()) {
					result = month + "月" + date + "日";
				} else {
					result = year + "年" + month + "月" + date + "日";
				}

				return result;
			},

			getLocal(local) {
				var that = this;
				if (local && local != '') {
					var local_arr = local.split("|");
					if (!local_arr[3] || local_arr[3] == 0) {
						return local_arr[2];
					} else {
						return local_arr[3];
					}

				} else {
					return "未知"
				}

			},
			previewImage(imageList, image) {
				console.log(imageList);
				//预览图片
				uni.previewImage({
					urls: imageList,
					current: image
				});
			},
			goModerators(id) {
				var that = this;
				uni.redirectTo({
					url: '/pages/forum/moderators?id=' + id
				});
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			goInfo(id) {
				var that = this;
				uni.navigateTo({
					url: '/pages/forum/info?id=' + id
				});
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			toUserContents(data) {
				var that = this;
				var name = data.name;
				var title = data.name + "的信息";
				var id = data.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
		}
	}
</script>
<style lang="scss" scoped>
	@import "@/static/styles/home.scss";

	/* 自定义tag样式，替代tn-tag组件 */
	.custom-tag {
		vertical-align: middle;
		position: relative;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		font-family: Helvetica Neue, Helvetica, sans-serif;
		white-space: nowrap;
		height: 48rpx;
		font-size: 24rpx;
		border-radius: 1000rpx;  /* 对应shape="circle"属性 */
	}
	
	/* 版块标签默认样式（白天模式） */
	.section-tag {
		background-color: #ececec;
		color: #555;
	}
	
	/* 注意：夜间模式的样式需要在首页通过CSS选择器覆盖 */
	/* 首页可以使用 .dark .section-tag { background-color: #3b3b3b; } 来覆盖 */

	.forum-avatar-container {
		position: relative;
		width: 90upx;
		height: 90upx;
		float: left;
		margin-right: 12upx;
		display: inline-block;
	}

	.forum-avatar {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		border: 2px solid white;
		overflow: hidden;
	}

	.forum-avatar image {
		width: 100upx !important;
		height: 100upx;
	}

	/* 文章内容 start*/
	.blogger {
		&__item {
			padding: 30rpx;
		}

		&__author {
			&__btn {
				margin-right: -12rpx;
				padding: 0 20rpx;
			}
		}

		&__desc {
			line-height: 55rpx;

			&__label {
				padding: 0 20rpx;
				margin: 0rpx 18rpx 0 0;

				&--prefix {
					color: #00FFC8;
					padding-right: 10rpx;
				}
			}

			&__content {}
		}

		&__content {
			margin-top: 18rpx;
			padding-right: 18rpx;

			&__data {
				line-height: 46rpx;
				text-align: justify;
				overflow: hidden;
				transition: all 0.25s ease-in-out;

			}

			&__status {
				margin-top: 10rpx;
				font-size: 26rpx;
				color: #82B2FF;
			}
		}

		&__main-image {
			border-radius: 16rpx;

			&--1 {
				max-width: 100%;
				max-height: 360rpx;
			}

			&--2 {
				max-width: 260rpx;
				max-height: 260rpx;
			}

			&--3 {
				height: 212rpx;
				width: 100%;
			}
		}

		&__count-icon {
			font-size: 40rpx;
			padding-right: 5rpx;
		}

		&__ad {
			width: 100%;
			height: 500rpx;
			transform: translate3d(0px, 0px, 0px) !important;

			::v-deep .uni-swiper-slide-frame {
				transform: translate3d(0px, 0px, 0px) !important;
			}

			.uni-swiper-slide-frame {
				transform: translate3d(0px, 0px, 0px) !important;
			}

			&__item {
				position: absolute;
				width: 100%;
				height: 100%;
				transform-origin: left center;
				transform: translate3d(100%, 0px, 0px) scale(1) !important;
				transition: transform 0.25s ease-in-out;
				z-index: 1;

				&--0 {
					transform: translate3d(0%, 0px, 0px) scale(1) !important;
					z-index: 4;
				}

				&--1 {
					transform: translate3d(13%, 0px, 0px) scale(0.9) !important;
					z-index: 3;
				}

				&--2 {
					transform: translate3d(26%, 0px, 0px) scale(0.8) !important;
					z-index: 2;
				}
			}

			&__content {
				border-radius: 40rpx;
				width: 640rpx;
				height: 500rpx;
				overflow: hidden;
			}

			&__image {
				width: 100%;
				height: 100%;
			}
		}
	}

	/* 文章内容 end*/
	.tn-margin-top-xxl {
		margin-top: 60upx;
	}

	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}


	.text-content-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.text-content-2 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.extra-count {
		position: absolute;
		background-color: #0000005c;
		color: white;
		z-index: 100;
		font-size: 20px;
		font-weight: bold;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 微信小程序image标签样式 */
	/* #ifdef MP-WEIXIN */
	image {
		width: 100%;
		height: 100%;
		border-radius: inherit;
	}

	.blogger__main-image--1 image {
		width: 100%;
		max-height: 360rpx;
		border-radius: 16rpx;
	}

	.bg-img image {
		width: 100%;
		height: 100%;
		border-radius: inherit;
	}
	/* #endif */

	.user-rz-qz {
		position: relative;
	}

	.user-rz-icon-qz {
		position: absolute;
		right: -5upx;
		bottom: -5upx;
		width: 36upx;
		height: 36upx;
		z-index: 10;
		border-radius: 50%;
		background-color: white;
		padding: 2upx;
		box-sizing: border-box;
	}

	.forum-shadow {
		border-radius: 0rpx;
		border-bottom: 2rpx solid #ebebeb;
	}
	.forum-list-content{
		border-radius: 0rpx;
	}

	.sy-text-lg {
		font-size: 30upx;
	}

	.radius-q {
		border-radius: 40rpx 0 0 0;
	}

	.radius-p {
		border-radius: 0 40rpx 0 0;
	}

	.radius-m {
		border-radius: 0 0 40rpx 0;
	}

	.radius-z {
		border-radius: 0 0 0 40rpx;
	}

	.radius-qz {
		border-radius: 40rpx 0 0 40rpx;
	}

	.radius-pm {
		border-radius: 0 40rpx 40rpx 0;
	}

	.spaceVideo-play {
		position: relative;
		border-radius: 20px;
		z-index: 1;
	}

	.spaceVideo-play::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 20px;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1;
	}

	.cuIcon-playfill {
		position: relative;
		z-index: 2;
	}

	.avatar-img {
		position: absolute;
		top: -10rpx;
		left: -10rpx;
		width: 110rpx;
		height: 110rpx;
		z-index: 1;
	}

	.medal-container {
		display: inline-block;
		vertical-align: middle;
		margin-left: 10rpx;
	}

	.medal-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 6rpx;
	}

	.tag-view {
		display: inline-block;
		min-width: 20rpx;
		height: 30rpx;
		line-height: 30rpx;
		padding: 0 6rpx;
		font-size: 20rpx;
		text-align: center;
		border-radius: 15rpx;
		margin-left: 6rpx;
		vertical-align: middle;
	}
</style>