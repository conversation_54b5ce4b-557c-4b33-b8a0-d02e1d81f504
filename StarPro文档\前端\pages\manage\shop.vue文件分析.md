# shop.vue 文件分析

## 文件信息
- **文件路径**：`StarPro文档/前端/pages/manage/shop.vue.md` (实际代码中路径为 `APP前端部分/pages/manage/shop.vue.md`)
- **页面说明**：此页面用于管理员管理商品列表。支持按商品状态（待审核、已上架）和两级商品分类进行筛选，通过关键词搜索商品，并对商品执行操作（如审核）。商品列表项由子组件 `shopItem` 渲染。

---

## 概述

`shop.vue` 是一个商品管理后台页面。管理员可以在此页面查看商品列表，并进行筛选和管理操作。

**主要功能**: 
1.  **筛选**: 
    -   **按状态**: "待审核" (`status=0`) 和 "已上架" (`status=1`)。
    -   **按分类**: 支持两级分类筛选（大类 `sort` 和小类 `subtype`）。点击筛选条件会弹出下拉列表供选择。
2.  **搜索**: 按关键词 (`searchText`) 搜索商品。
3.  **商品列表**: 以卡片形式展示商品，每个商品项由 `shopItem` 子组件负责渲染和具体操作（如编辑、删除、上下架等，这些操作的实现在 `shopItem` 中，本文件通过 `@updateList` 事件刷新列表）。
4.  **审核操作**: `auditShop(sid)` 方法用于审核通过商品（但模板中未直接调用此方法，推测审核等操作由 `shopItem` 组件内部处理并通过事件通知父组件刷新）。
5.  **商品分类管理入口**: 如果管理员权限为 `administrator`，会显示"商品分类"按钮，点击 (`toSort`) 跳转到商品分类管理页面 (`shoptype.vue`)。

页面支持下拉刷新和上拉加载更多。

## 主要组成部分分析

### 1. 模板 (`<template>`)
   - **自定义导航栏 (`cu-bar`)**: 返回按钮 (`back()`)，标题 "商品管理"。
   - **"商品分类"按钮 (`fullpost-btn`)**: 仅当 `group=='administrator'` 时显示，点击调用 `toSort()`。
   - **搜索框 (`cu-bar search`)**: 输入框绑定 `searchText`，输入调用 `searchTag()`，清空按钮调用 `searchClose()`。
   - **分类筛选 (`shop-sort shop-filter`)**: 
     - **大类筛选**: 点击显示/隐藏大类列表 (`sortShow`)。显示当前选中大类 (`sortText` 或 "全部大类")。
     - **小类筛选**: 点击显示/隐藏小类列表 (`subtypeShow`)。显示当前选中子类 (`subtypeText` 或 "全部")。
     - **下拉列表 (`shop-sort-list`)**: 分别用于展示大类 (`sortList`) 和小类 (`subtypeList`) 供选择。点击选项调用 `setSort()` 或 `setSubtype()`。
     - 点击列表外的背景 (`shop-sort-list-bg`) 可以关闭下拉列表。
   - **状态筛选 (`search-type grid col-2`)**: 
     - "待审核" (`toType(0)`)。
     - "已上架" (`toType(1)`)。
     - (已注释掉 "已禁用" 选项)。
   - **商品列表区域 (`shop-list`)**: 
     - `v-for` 遍历 `shopList`。
     - 每个商品通过 `<shopItem :item="item" :isAdmin="true" @updateList="updateList"></shopItem>` 组件渲染。
       - `:isAdmin="true"` 表明 `shopItem` 会显示管理相关的操作按钮。
       - `@updateList="updateList"` 监听子组件发出的事件，用于刷新列表。
     - **空状态 (`no-data`)**: 当 `shopList` 为空时显示。
   - **加载更多 (`load-more`)**: 点击调用 `loadMore()`。
   - **加载遮罩 (`loading`)**.

### 2. 脚本 (`<script>`)
   - **组件依赖**: `waves` (自定义组件), `localStorage`。
   - **`data`**: 
     - UI相关: `StatusBar`, `CustomBar`, `NavBar`, `AppStyle`, `isLoading`, `moreText`, `sortShow`, `subtypeShow`。
     - 用户信息: `userInfo`, `uid`, `token`, `group`。
     - 商品列表: `shopList`。
     - 分页: `page`, `isLoad`。
     - 搜索与筛选: `searchText`, `status` (商品状态), `sort` (大类ID), `sortText`, `sortList`, `subtype` (小类ID), `subtypeText`, `subtypeList`。
     - `shopTypelist`: 存储原始的扁平化商品分类列表。
   - **生命周期**: `onPullDownRefresh`, `onReachBottom`, `onShow`, `onLoad` (主要用于数据初始化和列表加载)。
   - **`methods`**: 
     - `back()`: 返回。
     - `loadMore()`: 加载更多商品，调用 `getShopList(true)`。
     - `getShopTypeList()`: 
       - 调用 `$API.shopTypeList()` 获取所有商品分类。
       - 处理返回数据，分离出大类列表 `sortList`。
       - 如果当前已选大类 (`that.sort > 0`)，则尝试恢复小类列表的显示 (通过再次调用 `setSort` 但传入 `true` 避免重复加载商品列表)。
     - `setSort(data, noSub)`: 
       - 设置当前选中的大类 (`that.sort`, `that.sortText`)。
       - 根据选中的大类，从 `shopTypelist` 中筛选出对应的小类列表 `subtypeList`。
       - 清空小类选择。
       - 调用 `getShopList()` 刷新商品列表 (除非 `noSub` 为 `true`)。
     - `setSubtype(data)`: 设置当前选中的小类 (`that.subtype`, `that.subtypeText`)，并调用 `getShopList()`。
     - `getShopList(isPage)`: 
       - **核心商品列表获取逻辑**。
       - 检查登录状态。
       - 构建请求参数 `data`，包含 `status`, `type`(固定为1), `sort` (如果已选), `subtype` (如果已选)。
       - 调用 `$Net.request()` 向 `$API.shopList()` 请求商品数据，参数包括 `searchParams`, `limit`(6), `searchKey`, `page`, `order`("created")。
       - 更新 `shopList` 和分页状态。
     - `deleteShop(sid)`: 删除商品 (模板中未直接调用，应在 `shopItem` 中)。
     - `searchTag()`/`searchClose()`: 处理搜索，调用 `getShopList()`。
     - `toShop(sid)`/`editShop(data)`: 跳转到添加/编辑商品页面 (模板中未直接调用)。
     - `auditShop(sid)`: 审核商品 (模板中未直接调用)。
     - `toType(i)`: 切换商品状态筛选，调用 `getShopList()`。
     - `updateList()`: 刷新商品列表 (由 `shopItem` 子组件事件触发)。
     - `getUserInfo(uid)`/`toUserContents(data)`: 获取并跳转到用户信息页 (模板中未直接调用)。
     - `toSort()`: 跳转到商品分类管理页 `/pages/manage/shoptype`。

## 总结与注意事项

-   页面提供了多维度的商品筛选功能（状态、两级分类、关键词）。
-   商品列表的渲染和具体操作（编辑、删除、上下架、审核）依赖于子组件 `shopItem`。
-   **API依赖**: `$API.shopTypeList` (获取商品分类), `$API.shopList` (获取商品列表)。删除、审核等操作的API调用可能在 `shopItem` 组件中。
-   **权限控制**: "商品分类"按钮仅对 `administrator` 显示。
-   分页加载商品，每页6条。
-   方法如 `deleteShop`, `auditShop`, `editShop`, `toShop`, `getUserInfo` 在当前组件的模板中没有直接调用点，其功能可能已移至 `shopItem` 子组件或为遗留代码。

## 后续分析建议

-   **`shopItem` 组件分析**: 必须分析 `shopItem.vue` 组件才能完整理解商品管理的具体操作流程和API调用。
-   **API确认**: 
    - `$API.shopList()`: 确认 `searchParams` 中 `status`, `type`, `sort`, `subtype` 的作用。
    - `$API.shopTypeList()`: 确认返回的分类数据结构。
-   **`type:1` 的含义**: 在 `getShopList` 的 `data` 中，`type` 固定为1，需要明确其含义。
-   **代码整洁性**: 梳理并移除当前组件内未被调用的方法，以减少冗余。
-   **用户体验**: 分类筛选的下拉列表交互是否流畅，尤其是在分类层级较多时。 