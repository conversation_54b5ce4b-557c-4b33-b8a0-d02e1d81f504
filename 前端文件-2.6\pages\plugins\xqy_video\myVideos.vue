<template>
	<view class="container" :class="[AppStyle, isDark?'dark':'']" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<!-- 自定义导航栏 -->
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar" :class="isDark?'bg-black':'bg-white'" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					我的视频
				</view>
				<view class="action">
					<text class="cuIcon-upload text-primary" @tap="goUpload"></text>
				</view>
			</view>
		</view>
		<view :style="[{padding:(NavBar) + 'px 0 0'}]"></view>

		<!-- 页面内容 -->
		<view class="content-container">
			<!-- 状态切换 -->
			<view class="status-tabs">
				<view
					class="status-tab"
					:class="{'active': currentStatus === 1}"
					@tap="changeStatus(1)"
				>
					已发布
				</view>
				<view
					class="status-tab"
					:class="{'active': currentStatus === 0}"
					@tap="changeStatus(0)"
				>
					审核中
				</view>
				<view
					class="status-tab"
					:class="{'active': currentStatus === 2}"
					@tap="changeStatus(2)"
				>
					已拒绝
				</view>
			</view>

			<!-- 加载中 -->
			<view v-if="loading" class="loading-container">
				<view class="cu-load loading"></view>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 视频列表 -->
			<view v-else-if="videoList.length > 0" class="video-list">
				<view
					class="video-item"
					v-for="(item, index) in videoList"
					:key="index"
				>
					<view class="video-cover" @tap="goDetail(item.id)">
						<image :src="item.cover_url" mode="aspectFill"></image>
						<view class="video-duration">{{item.duration_text}}</view>
						<view class="video-status" :class="getStatusClass(item.status)">
							{{getStatusText(item.status)}}
						</view>
					</view>
					<view class="video-info">
						<view class="video-title text-cut" @tap="goDetail(item.id)">{{item.title}}</view>
						<view class="video-stats">
							<text class="stats-item">
								<text class="cuIcon-attentionfill"></text>
								<text>{{formatNumber(item.view_count)}}</text>
							</text>
							<text class="stats-item">
								<text class="cuIcon-appreciatefill"></text>
								<text>{{formatNumber(item.like_count)}}</text>
							</text>
							<text class="stats-item">
								<text class="cuIcon-commentfill"></text>
								<text>{{formatNumber(item.comment_count)}}</text>
							</text>
						</view>
						<view class="video-time">{{item.created_at}}</view>
						<view class="video-actions">
							<view class="action-btn" @tap="goDetail(item.id)">
								<text class="cuIcon-attention"></text>
								<text>查看</text>
							</view>
							<view class="action-btn" @tap="editVideo(item)" v-if="item.status == 0 || item.status == 1">
								<text class="cuIcon-edit"></text>
								<text>编辑</text>
							</view>
							<view class="action-btn" @tap="deleteVideo(item.id)">
								<text class="cuIcon-delete"></text>
								<text>删除</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 无数据 -->
			<view v-else class="empty-container">
				<image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无视频</text>
			</view>

			<!-- 加载更多 -->
			<view class="cu-load" :class="loadStatus"></view>
		</view>

		<!-- 编辑视频弹窗 -->
		<view class="edit-modal" v-if="showEditModal" @tap="closeEditModal">
			<view class="edit-content" @tap.stop="">
				<view class="edit-header">
					<text class="edit-title">编辑视频</text>
					<text class="cuIcon-close" @tap="closeEditModal"></text>
				</view>
				<view class="edit-body">
					<view class="form-item">
						<text class="form-label">封面</text>
						<view class="cover-upload">
							<view class="cover-preview" @tap="chooseCover">
								<image v-if="editForm.cover_url" :src="editForm.cover_url" mode="aspectFill"></image>
								<view v-else class="cover-placeholder">
									<text class="cuIcon-cameraadd"></text>
									<text class="placeholder-text">点击更换封面</text>
								</view>
								<view class="cover-mask">
									<text class="cuIcon-cameraadd"></text>
									<text class="mask-text">更换封面</text>
								</view>
							</view>
						</view>
					</view>
					<view class="form-item">
						<text class="form-label">标题</text>
						<input class="form-input" v-model="editForm.title" placeholder="请输入视频标题" maxlength="100" />
					</view>
					<view class="form-item">
						<text class="form-label">分区</text>
						<view class="form-select" @tap="showCategoryPicker">
							<text class="select-text" :class="{'placeholder': !editForm.category_name}">{{editForm.category_name || '请选择分区'}}</text>
							<text class="cuIcon-unfold"></text>
						</view>
					</view>
					<view class="form-item">
						<text class="form-label">描述</text>
						<textarea class="form-textarea" v-model="editForm.description" placeholder="请输入视频描述" maxlength="500"></textarea>
					</view>
					<view class="edit-notice">
						<text class="notice-icon cuIcon-infofill"></text>
						<text class="notice-text">编辑已发布的视频后需要重新审核</text>
					</view>
				</view>
				<view class="edit-footer">
					<view class="btn-cancel" @tap="closeEditModal">取消</view>
					<view class="btn-confirm" @tap="submitEdit" :class="{'disabled': editSubmitting}">{{editSubmitting ? '保存中...' : '保存'}}</view>
				</view>
			</view>
		</view>

		<!-- 分区选择弹窗 -->
		<view class="category-modal" v-if="showCategoryModal" @tap="closeCategoryPicker">
			<view class="category-content" @tap.stop="">
				<view class="category-header">
					<text class="category-title">选择分区</text>
					<text class="cuIcon-close" @tap="closeCategoryPicker"></text>
				</view>
				<view class="category-list">
					<view 
						class="category-item" 
						v-for="(category, index) in categoryList" 
						:key="index"
						@tap="selectCategory(category)"
						:class="{'selected': editForm.category_id == category.id}"
					>
						<text class="category-name">{{category.name}}</text>
						<text class="cuIcon-check" v-if="editForm.category_id == category.id"></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import darkModeMixin from '@/utils/darkModeMixin.js'

export default {
	mixins: [darkModeMixin],
	data() {
		return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				token: '',
				loading: true,
				videoList: [],
				currentStatus: 1, // 默认显示已发布的视频
				page: 1,
				limit: 10,
				hasMore: true,
				loadStatus: 'loading',
				isLogin: false,
				submitStatus: false,
				// 编辑相关
				showEditModal: false,
				showCategoryModal: false,
				editSubmitting: false,
				categoryList: [],
				editForm: {
					id: 0,
					title: '',
					description: '',
					category_id: 0,
					category_name: '',
					cover_url: ''
				}
			}
	},
	onLoad() {
		// 获取token
		let token = '';
		// #ifdef H5
		token = localStorage.getItem('token') || '';
		// #endif
		
		// #ifdef APP-PLUS || MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
		token = uni.getStorageSync('token') || '';
		// #endif
		
		this.token = token;
		this.isLogin = !!this.token;

		if (!this.isLogin) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});

			setTimeout(() => {
				uni.navigateBack({
					delta: 1
				});
			}, 1500);
			return;
		}

		// 获取视频列表
		this.getVideoList();
	},
	onPullDownRefresh() {
		this.page = 1;
		this.videoList = [];
		this.hasMore = true;
		this.loadStatus = 'loading';
		this.getVideoList();
	},
	onReachBottom() {
		if (this.hasMore) {
			this.page++;
			this.getVideoList();
		}
	},
	methods: {
		// 返回上一页
		back() {
			uni.navigateBack({
				delta: 1
			});
		},

		// 切换状态
		changeStatus(status) {
			if (this.currentStatus === status) return;

			this.currentStatus = status;
			this.page = 1;
			this.videoList = [];
			this.hasMore = true;
			this.loadStatus = 'loading';
			this.getVideoList();
		},

		// 获取视频列表
		getVideoList() {
			if (this.submitStatus) {
				return false;
			}

			if (!this.hasMore && this.page > 1) return;

			const that = this;
			that.loading = true;
			that.submitStatus = true;

			// 调试输出请求参数
			const requestData = {
				action: 'getVideoList',
				plugin: 'xqy_video',
				page: that.page,
				limit: that.limit,
				status: that.currentStatus,
				author_id: 'current', // 获取当前用户的视频
				token: that.token
			};

			uni.request({
				url: that.$API.PluginLoad('xqy_video'),
				data: requestData,
				method: 'POST',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				success: function(res) {
					that.loading = false;
					that.submitStatus = false;
					uni.stopPullDownRefresh();

					if (res.data.code === 200) {
						const videos = res.data.data.videos || [];

						// 检查每个视频的status值
						videos.forEach((video, index) => {
							//console.log(`视频${index+1} status:`, video.status, typeof video.status);
						});

						if (that.page === 1) {
							that.videoList = videos;
						} else {
							that.videoList = [...that.videoList, ...videos];
						}

						that.hasMore = videos.length >= that.limit;
						that.loadStatus = that.hasMore ? 'loading' : 'over';
					} else {
						uni.showToast({
							title: res.data.msg || '获取视频列表失败',
							icon: 'none'
						});
					}
				},
				fail: function(err) {
					that.loading = false;
					that.submitStatus = false;
					uni.stopPullDownRefresh();

					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				}
			});
		},

		// 跳转到视频详情页
		goDetail(id) {
			uni.navigateTo({
				url: `/pages/plugins/xqy_video/detail?id=${id}`
			});
		},

		// 跳转到视频上传页
		goUpload() {
			uni.navigateTo({
				url: '/pages/plugins/xqy_video/upload'
			});
		},

		// 删除视频
		deleteVideo(id) {
			const that = this;

			uni.showModal({
				title: '提示',
				content: '确定要删除这个视频吗？',
				success: function(res) {
					if (res.confirm) {
						// 调试输出
						const requestData = {
							action: 'manageVideo',
							plugin: 'xqy_video',
							op: 'delete',
							video_id: id,
							token: that.token
						};
						//console.log('删除视频请求参数:', requestData);

						uni.request({
							url: that.$API.PluginLoad('xqy_video'),
							data: requestData,
							method: 'POST',
							header: {
								'Content-Type': 'application/x-www-form-urlencoded'
							},
							success: function(res) {
								//console.log('删除视频响应:', res.data);

								if (res.data.code === 200) {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									});

									// 刷新列表
									that.page = 1;
									that.videoList = [];
									that.hasMore = true;
									that.loadStatus = 'loading';
									that.getVideoList();
								} else {
									uni.showToast({
										title: res.data.msg || '删除失败',
										icon: 'none'
									});
								}
							},
							fail: function(err) {
								//console.error('删除视频请求失败:', err);

								uni.showToast({
									title: '网络错误，请稍后重试',
									icon: 'none'
								});
							}
						});
					}
				}
			});
		},

		// 获取状态文本
		getStatusText(status) {
			//console.log('获取状态文本, status:', status, typeof status);

			// 将status转换为数字
			const statusNum = parseInt(status);
			//console.log('转换后的status:', statusNum, typeof statusNum);

			switch (statusNum) {
				case 0: return '审核中';
				case 1: return '已发布';
				case 2: return '已拒绝';
				default: return '未知(' + status + ')';
			}
		},

		// 获取状态样式类
		getStatusClass(status) {
			//将status转换为数字
			const statusNum = parseInt(status);

			switch (statusNum) {
				case 0: return 'status-pending';
				case 1: return 'status-published';
				case 2: return 'status-rejected';
				default: return 'status-unknown';
			}
		},

		// 格式化数字
		formatNumber(num) {
			num = parseInt(num) || 0;
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w';
			} else if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k';
			}
			return num;
		},

		// 编辑视频
		editVideo(video) {
			// 获取分区列表
			this.getCategoryList();
			
			// 设置编辑表单数据
			this.editForm = {
				id: video.id,
				title: video.title,
				description: video.description || '',
				category_id: video.category_id,
				category_name: video.category_name || '',
				cover_url: video.cover_url || ''
			};
			
			this.showEditModal = true;
		},

		// 关闭编辑弹窗
		closeEditModal() {
			this.showEditModal = false;
			this.editForm = {
				id: 0,
				title: '',
				description: '',
				category_id: 0,
				category_name: '',
				cover_url: ''
			};
		},

		// 获取分区列表
		getCategoryList() {
			const that = this;
			
			uni.request({
				url: that.$API.PluginLoad('xqy_video'),
				data: {
					action: 'getCategoryList',
					plugin: 'xqy_video'
				},
				method: 'POST',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				success: function(res) {
					if (res.data.code === 200) {
						that.categoryList = res.data.data || [];
					}
				},
				fail: function(err) {
					console.error('获取分区列表失败:', err);
				}
			});
		},

		// 显示分区选择器
		showCategoryPicker() {
			this.showCategoryModal = true;
		},

		// 关闭分区选择器
		closeCategoryPicker() {
			this.showCategoryModal = false;
		},

		// 选择分区
		selectCategory(category) {
			this.editForm.category_id = category.id;
			this.editForm.category_name = category.name;
			this.showCategoryModal = false;
		},

		// 选择封面
		chooseCover() {
			const that = this;
			
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: function(res) {
					const tempFilePath = res.tempFilePaths[0];
					
					// 显示上传进度
					uni.showLoading({
						title: '上传中...'
					});
					
					// 构建请求参数
					const formData = {
						action: 'editVideo',
						plugin: 'xqy_video',
						op: 'uploadCover',
						upload_type: 'file',
						token: that.token
					};
					

					
					// 上传封面
					uni.uploadFile({
						url: that.$API.PluginLoad('xqy_video'),
						filePath: tempFilePath,
						name: 'cover',
						formData: formData,
						success: function(uploadRes) {
							
							uni.hideLoading();
							
							try {
								const result = JSON.parse(uploadRes.data);
								
								if (result.code === 200) {
										that.editForm.cover_url = result.data.cover_url;
									uni.showToast({
										title: '封面上传成功',
										icon: 'success'
									});
								} else {
									uni.showToast({
										title: result.msg || '封面上传失败',
										icon: 'none'
									});
								}
							} catch (e) {
								uni.showToast({
									title: '封面上传失败',
									icon: 'none'
								});
							}
						},
						fail: function(err) {
							uni.hideLoading();
							uni.showToast({
								title: '封面上传失败',
								icon: 'none'
							});
						}
				});
			},
			fail: function(err) {}
			});
	},

		// 提交编辑
		submitEdit() {
			if (this.editSubmitting) return;
			
			if (!this.editForm.title.trim()) {
				uni.showToast({
					title: '请输入视频标题',
					icon: 'none'
				});
				return;
			}
			
			if (!this.editForm.category_id) {
				uni.showToast({
					title: '请选择分区',
					icon: 'none'
				});
				return;
			}
			
			const that = this;
			that.editSubmitting = true;
			
			uni.request({
				url: that.$API.PluginLoad('xqy_video'),
				data: {
					action: 'editVideo',
					plugin: 'xqy_video',
					op: 'updateVideo',
					video_id: that.editForm.id,
					title: that.editForm.title.trim(),
					description: that.editForm.description.trim(),
					category_id: that.editForm.category_id,
					cover_url: that.editForm.cover_url,
					token: that.token
				},
				method: 'POST',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				success: function(res) {
					that.editSubmitting = false;
					
					if (res.data.code === 200) {
						uni.showToast({
							title: res.data.msg || '编辑成功',
							icon: 'success'
						});
						
						// 关闭弹窗
						that.closeEditModal();
						
						// 刷新列表
						that.page = 1;
						that.videoList = [];
						that.hasMore = true;
						that.loadStatus = 'loading';
						that.getVideoList();
					} else {
						uni.showToast({
							title: res.data.msg || '编辑失败',
							icon: 'none'
						});
					}
				},
				fail: function(err) {
					that.editSubmitting = false;
					
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				}
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f6f6f6;

	.content-container {
		padding: 0;
	}

	.status-tabs {
		display: flex;
		background-color: #fff;
		border-radius: 0;
		margin-bottom: 0;
		overflow: hidden;

		.status-tab {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			position: relative;

			&.active {
				color: #0081ff;
				font-weight: bold;

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: #0081ff;
				}
		}
		
		// 封面编辑区域夜间模式
		.cover-upload {
			.cover-preview {
				border-color: #333;
				background-color: #2a2a2a;
				
				.cover-placeholder {
					color: #999;
					background-color: #2a2a2a;
					border-color: #333;
					
					.placeholder-text {
						color: #999;
					}
				}
				
				.cover-mask {
					background-color: rgba(0, 0, 0, 0.7);
					
					.mask-text {
						color: #fff;
					}
				}
			}
		}
	}
}

	&.dark {
		.status-tabs {
			background-color: #2c2c2c;
			
			.status-tab {
				color: #aaa;
				
				&.active {
					color: #0081ff;
				}
			}
		}
		
		.loading-text {
			color: #aaa;
		}
		
		.empty-text {
			color: #aaa;
		}
		
		.video-list {
			.video-item {
				background-color: #2c2c2c;
				
				.video-info {
					.video-title {
						color: #ddd;
					}
					
					.video-stats {
						.stats-item {
							color: #aaa;
						}
					}
					
					.video-time {
						color: #aaa;
					}
					
					.video-actions {
						.action-btn {
							background-color: #333;
							color: #aaa;
						}
					}
				}
			}
		}
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20rpx;

		.loading-text {
			margin-top: 20rpx;
			color: #999;
			font-size: 28rpx;
		}
	}

	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20rpx;

		.empty-image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 30rpx;
		}

		.empty-text {
			color: #999;
			font-size: 28rpx;
		}
	}

	.video-list {
		padding: 20rpx;
		.video-item {
			display: flex;
			background-color: #fff;
			border-radius: 12rpx;
			margin-bottom: 20rpx;
			overflow: hidden;

			.video-cover {
				width: 240rpx;
				height: 180rpx;
				position: relative;

				image {
					width: 100%;
					height: 100%;
				}

				.video-duration {
					position: absolute;
					right: 10rpx;
					bottom: 10rpx;
					background-color: rgba(0, 0, 0, 0.5);
					color: #fff;
					font-size: 20rpx;
					padding: 2rpx 8rpx;
					border-radius: 4rpx;
				}

				.video-status {
					position: absolute;
					left: 0;
					top: 0;
					padding: 4rpx 10rpx;
					font-size: 20rpx;
					color: #fff;

					&.status-pending {
						background-color: #f37b1d;
					}

					&.status-published {
						background-color: #0081ff;
					}

					&.status-rejected {
						background-color: #e54d42;
					}

					&.status-unknown {
						background-color: #8799a3;
					}
				}
			}

			.video-info {
				flex: 1;
				padding: 20rpx;
				display: flex;
				flex-direction: column;

				.video-title {
					font-size: 28rpx;
					font-weight: bold;
					line-height: 1.4;
					margin-bottom: 10rpx;
				}

				.video-stats {
					display: flex;
					margin-bottom: 10rpx;

					.stats-item {
						display: flex;
						align-items: center;
						margin-right: 20rpx;
						color: #999;
						font-size: 24rpx;

						text:first-child {
							margin-right: 4rpx;
						}
					}
				}

				.video-time {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 10rpx;
				}

				.video-actions {
					display: flex;
					margin-top: auto;

					.action-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						height: 60rpx;
						padding: 0 20rpx;
						background-color: #f6f6f6;
						border-radius: 30rpx;
						margin-right: 20rpx;
						font-size: 24rpx;
						color: #666;

			text:first-child {
								margin-right: 4rpx;
							}
						}
					}
				}
			}
		}
	}

	// 编辑弹窗样式
	.edit-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.edit-content {
			width: 90%;
			max-width: 600rpx;
			background-color: #fff;
			border-radius: 20rpx;
			overflow: hidden;

			.edit-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx;
				border-bottom: 1rpx solid #f0f0f0;

				.edit-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}

				.cuIcon-close {
					font-size: 40rpx;
					color: #999;
				}
			}

			.edit-body {
				padding: 30rpx;

				.form-item {
					margin-bottom: 30rpx;

					.form-label {
						display: block;
						font-size: 28rpx;
						color: #333;
						margin-bottom: 15rpx;
					}

					.form-input {
						width: 100%;
						height: 80rpx;
						padding: 0 20rpx;
						border: 1rpx solid #e0e0e0;
						border-radius: 10rpx;
						font-size: 28rpx;
						box-sizing: border-box;
					}

					.form-select {
						display: flex;
						align-items: center;
						justify-content: space-between;
						height: 80rpx;
						padding: 0 20rpx;
						border: 1rpx solid #e0e0e0;
						border-radius: 10rpx;
						box-sizing: border-box;

						.select-text {
							font-size: 28rpx;
							color: #333;

							&.placeholder {
								color: #999;
							}
						}

						.cuIcon-unfold {
							font-size: 24rpx;
							color: #999;
						}
					}

					.form-textarea {
						width: 100%;
						height: 120rpx;
						padding: 15rpx 20rpx;
						border: 1rpx solid #e0e0e0;
						border-radius: 10rpx;
						font-size: 28rpx;
						box-sizing: border-box;
						resize: none;
					}
				}

				.edit-notice {
					display: flex;
					align-items: center;
					padding: 20rpx;
					background-color: #fff3cd;
					border-radius: 10rpx;
					margin-top: 20rpx;

					.notice-icon {
						font-size: 28rpx;
						color: #856404;
						margin-right: 10rpx;
					}

					.notice-text {
						font-size: 24rpx;
						color: #856404;
					}
				}
			}

			.edit-footer {
				display: flex;
				padding: 30rpx;
				border-top: 1rpx solid #f0f0f0;

				.btn-cancel {
					flex: 1;
					height: 80rpx;
					line-height: 80rpx;
					text-align: center;
					font-size: 28rpx;
					color: #666;
					border: 1rpx solid #e0e0e0;
					border-radius: 10rpx;
					margin-right: 20rpx;
				}

				.btn-confirm {
					flex: 1;
					height: 80rpx;
					line-height: 80rpx;
					text-align: center;
					font-size: 28rpx;
					color: #fff;
					background-color: #0081ff;
					border-radius: 10rpx;

					&.disabled {
						background-color: #ccc;
					}
				}
			}
		}
	}

	// 分区选择弹窗样式
	.category-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10000;

		.category-content {
			width: 80%;
			max-width: 500rpx;
			max-height: 70%;
			background-color: #fff;
			border-radius: 20rpx;
			overflow: hidden;

			.category-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx;
				border-bottom: 1rpx solid #f0f0f0;

				.category-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}

				.cuIcon-close {
					font-size: 40rpx;
					color: #999;
				}
			}

			.category-list {
				max-height: 400rpx;
				overflow-y: auto;

				.category-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 25rpx 30rpx;
					border-bottom: 1rpx solid #f5f5f5;

					&:last-child {
						border-bottom: none;
					}

					&.selected {
						background-color: #f0f8ff;

						.category-name {
							color: #0081ff;
						}
					}

					.category-name {
						font-size: 28rpx;
						color: #333;
					}

					.cuIcon-check {
						font-size: 32rpx;
						color: #0081ff;
					}
				}
			}
		}
	}

.container.dark {
		// 状态切换标签
		.status-tabs {
			background-color: #1e1e1e;
			border-bottom-color: #333;

			.status-tab {
				color: #aaa;

				&.active {
					color: #0081ff;
					border-bottom-color: #0081ff;
				}
			}
		}

		// 加载状态
		.loading-container {
			.loading-text {
				color: #aaa;
			}
		}

		// 视频列表
		.video-list {
			.video-item {
				background-color: #1e1e1e;
				border-color: #333;

				.video-info {
					.video-title {
						color: #fff;
					}

					.video-stats {
						.stats-item {
							color: #aaa;
						}
					}

					.video-time {
						color: #888;
					}

					.video-actions {
						.action-btn {
							background-color: #2a2a2a;
							border-color: #444;
							color: #ccc;

							&:hover {
								background-color: #333;
							}
						}
					}
				}
			}
		}

		// 空状态
		.empty-container {
			.empty-text {
				color: #aaa;
			}
		}

		.edit-modal {
			.edit-content {
				background-color: #1e1e1e;
				border: 1rpx solid #333;

				.edit-header {
					border-bottom-color: #333;

					.edit-title {
						color: #fff;
						font-weight: bold;
					}

					.cuIcon-close {
						color: #ccc;
					}
				}

				.edit-body {
					.form-item {
						.form-label {
							color: #fff;
							font-weight: 500;
						}

						.form-input, .form-textarea {
							background-color: #2a2a2a;
							border-color: #444;
							color: #fff;

							&::placeholder {
								color: #888;
							}

							&:focus {
								border-color: #0081ff;
								box-shadow: 0 0 0 2rpx rgba(0, 129, 255, 0.2);
							}
						}

						.form-select {
							background-color: #2a2a2a;
							border-color: #444;

							.select-text {
								color: #fff;

								&.placeholder {
									color: #888;
								}
							}

							.cuIcon-unfold {
								color: #ccc;
							}
						}
					}

					.edit-notice {
						background-color: rgba(255, 193, 7, 0.1);
						border: 1rpx solid rgba(255, 193, 7, 0.3);

						.notice-icon, .notice-text {
							color: #ffc107;
						}
					}
				}

				.edit-footer {
					border-top-color: #333;

					.btn-cancel {
						background-color: #2a2a2a;
						border-color: #444;
						color: #ccc;

						&:hover {
							background-color: #333;
						}
					}

					.btn-confirm {
						&.disabled {
							background-color: #555;
							color: #888;
						}
					}
				}
			}
		}

		.category-modal {
			.category-content {
				background-color: #1e1e1e;
				border: 1rpx solid #333;

				.category-header {
					border-bottom-color: #333;

					.category-title {
						color: #fff;
						font-weight: bold;
					}

					.cuIcon-close {
						color: #ccc;

						&:hover {
							color: #fff;
						}
					}
				}

				.category-list {
					.category-item {
						border-bottom-color: #333;

						&:hover {
							background-color: #2a2a2a;
						}

						&.selected {
							background-color: rgba(0, 129, 255, 0.15);
							border-left: 4rpx solid #0081ff;

							.category-name {
								color: #0081ff;
								font-weight: 500;
							}
						}

						.category-name {
							color: #fff;
						}

						.cuIcon-check {
							color: #0081ff;
						}
					}
				}
			}
		}
	}
</style>
