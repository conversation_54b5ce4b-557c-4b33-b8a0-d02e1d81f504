# home.vue (ads) 文件分析

## 概述

`home.vue` (位于 `pages/ads/` 目录下) 是 StarPro 前端项目的**付费广告中心首页**。此页面向用户展示了当前平台提供的不同类型的付费广告位（文章推流、横幅、启动图），包括它们的描述、剩余坑位数、价格，并提供了"立即购买"的入口，引导用户去发布对应类型的广告。

## 文件信息
- **文件路径**：`APP前端部分/pages/ads/home.vue.md`
- **主要功能**：展示广告位信息，引导用户购买和发布广告。

## 主要组成部分分析

### 1. 模板 (`<template>`)
   - **导航栏**: 
     - 使用 `cu-bar` 构建自定义导航栏。
     - 标题固定为"付费广告位"。
     - 左侧为返回按钮 (`cuIcon-back`)。
     - 右侧提供"我的广告"链接 (`toLink('/pages/ads/myAds')`)，跳转到 `myAds.vue` 页面。
   - **广告位列表 (`ads-list`)**: 
     - 包含三个固定的广告位展示框 (`ads-box`)，分别对应"文章推流广告"、"横幅广告"和"启动图广告"。
     - **每个广告位展示框 (`ads-main`) 包含**: 
       - `ads-name`: 广告位名称。
       - `ads-text`: 广告位的详细描述，解释其展示方式、效果，并包含一些建议（如图片尺寸、格式）。
       - `ads-info`: 使用 `grid col-3` 布局展示关键信息：
         - `ads-num`: 显示该类型广告剩余的可用数量 (如 `pushAdsNum`)。
         - `ads-price`: 显示该类型广告的单价 (如 `pushAdsPrice`) 和货币名称 (`currencyName`)。
         - `ads-btn`: "立即购买"按钮，点击触发 `goAdsBuy` 方法，并传入对应的价格、剩余数量和广告类型ID(0, 1, 或 2)。
   - **加载遮罩**: 
     - 通过 `isLoading` 控制显示，用于初始加载数据时提供反馈。

### 2. 脚本 (`<script>`)
   - **依赖**: 引入 `localStorage`。
   - **`data` 属性**: 
     - `StatusBar`, `CustomBar`, `NavBar`, `AppStyle`: UI适配和样式。
     - `isLoading`: 加载状态标志。
     - `pushAdsNum`, `pushAdsPrice`: 文章推流广告的剩余数量和价格。
     - `bannerAdsNum`, `bannerAdsPrice`: 横幅广告的剩余数量和价格。
     - `startAdsNum`, `startAdsPrice`: 启动图广告的剩余数量和价格。
     - `token`: 用户登录凭证。
     - `userData`: 存储用户数据，特别是用户资产 (`assets`)，用于判断余额是否足够。
     - `currencyName`: 货币名称 (如 积分、金币等)。
   - **生命周期函数**: 
     - `onLoad()`: 
       - 获取 `token`。
       - 调用 `getAdsConfig()` 获取各类型广告的价格和剩余数量。
       - 调用 `getUserData()` 获取当前用户信息（主要是资产）。
     - `mounted()`: 调用 `getleiji()` 获取货币名称 (`currencyName`)。
     - `onShow()`: (空，可能用于后续逻辑)。
     - `onPullDownRefresh()`: (空，未实现下拉刷新)。
   - **`methods` 方法**: 
     - `getleiji()`: 调用 `$API.SPset()` 接口获取全局设置，主要是为了获取货币名称 (`assetsname`) 并赋值给 `currencyName`。
     - `back()`: 返回上一页。
     - `getUserData()`: 调用 `$API.getUserData()` 接口获取当前用户的详细信息，特别是 `assets` 字段。
     - `getAdsConfig()`: 调用 `$API.adsConfig()` 接口获取所有广告类型的配置信息（剩余数量 `Num` 和价格 `Price`）。
     - `toLink(text)`: 
       - 检查用户是否登录 (`token` 是否存在)。
       - 如果已登录，则跳转到指定的页面路径 (`text`)。
       - 如果未登录，提示"请先登录哦"。
     - `goAdsBuy(price, num, type)`: 
       - **购买逻辑入口**。
       - 检查用户是否登录。
       - 检查该类型广告是否还有剩余数量 (`num > 0`)。
       - 检查用户余额是否足够支付一天的广告费用 (`that.userData.assets >= price`)。
       - 如果以上条件都满足，则跳转到 `adsPost` 页面进行广告发布，并携带 `post=add` 和广告类型 `type` 参数。
       - 如果条件不满足，则根据具体情况弹出相应的提示（如"该广告位暂时无法购买"、"余额不足"）。

## 总结与注意事项

-   `ads/home.vue` 作为广告中心的入口页面，清晰地展示了可购买的广告类型及其价格和库存。
-   页面数据主要依赖 `$API.adsConfig()` 获取广告位信息和 `$API.getUserData()` 获取用户余额。
-   通过 `$API.SPset()` 获取货币名称。
-   点击"立即购买"按钮会进行登录状态、库存和余额检查，通过后才跳转到 `adsPost.vue` 页面。
-   页面同样使用了 `$Net.request` 进行网络请求。
-   UI风格与 `adsPost` 和 `myAds` 保持一致。

## 后续分析建议

-   **API 依赖**: 查看 `$API.adsConfig()`, `$API.getUserData()`, `$API.SPset()` 接口的后端实现。
-   **数据一致性**: 确认从不同接口获取的货币名称 (`currencyName` 来自 `SPset`, `price` 来自 `adsConfig`, `assets` 来自 `getUserData`) 是否基于同一种货币单位。
-   **用户体验**: 评估余额不足或库存不足时的提示信息是否清晰友好。
-   **网络请求封装一致性**: 再次关注 `$Net.request` 和 `$API.GetRequest` 的使用差异。
