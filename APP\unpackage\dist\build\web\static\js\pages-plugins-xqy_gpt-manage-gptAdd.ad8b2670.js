(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-plugins-xqy_gpt-manage-gptAdd"],{"01791":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("9254"),a=i("0f7c"),o={data:function(){return{StatusBar:this.StatusBar,CustomBar:this.CustomBar,NavBar:this.StatusBar+this.CustomBar,AppStyle:this.$store.state.AppStyle,token:"",id:0,name:"",avatar:"",isVip:0,switchVip:!1,picNew:"",price:0,intro:"",prompt:"",gptType:0,model_name:"",api_key:"",source:"喵小算",sourceText:"",type:"add",sourceList:[{id:"喵小算",name:"喵小算"}],modalName:null,submitStatus:!1}},onPullDownRefresh:function(){},onShow:function(){if(n.localStorage.getItem("toAvatar")){var t=JSON.parse(n.localStorage.getItem("toAvatar"));this.avatarUpload(t.dataUrl)}},onLoad:function(t){n.localStorage.getItem("token")&&(this.token=n.localStorage.getItem("token")),t.type&&(this.type=t.type,"edit"==this.type&&(t.id?(this.id=t.id,this.getGptInfo(this.id)):uni.showToast({title:"无参数访问",icon:"none"}))),this.sourceList.length>0&&this.setSource(this.sourceList[0])},methods:{back:function(){uni.navigateBack({delta:1})},SwitchVIP:function(t){this.switchVip=t.detail.value,this.switchVip?this.isVip=1:this.isVip=0},showModal:function(t){this.modalName=t.currentTarget.dataset.target},hideModal:function(t){this.modalName=null},RadioChange:function(t){this.radio=t.detail.value},setSource:function(t){this.source=t.id,this.sourceText=t.name,this.hideModal()},avatarUpload:function(t){var e=this;(0,a.base64ToPath)(t).then((function(t){var i=t;uni.uploadFile({url:e.$API.upload(),filePath:i,name:"file",formData:{token:e.token},success:function(t){setTimeout((function(){uni.hideLoading()}),1e3);var i=JSON.parse(t.data);1==i.code?(e.avatar=i.data.url,e.picNew=i.data.url,n.localStorage.removeItem("toAvatar")):uni.showToast({title:"图片上传失败，请检查接口",icon:"none"})},fail:function(){setTimeout((function(){uni.hideLoading()}),1e3)}})})).catch((function(t){console.error("失败"+t)}))},add:function(){var t=this;if(t.submitStatus)return!1;if(t.submitStatus=!0,0==t.parent)return uni.showToast({title:"未选择大类",icon:"none",duration:1e3,position:"bottom"}),t.submitStatus=!1,!1;if(""==t.name)return uni.showToast({title:"请输入名称",icon:"none",duration:1e3,position:"bottom"}),t.submitStatus=!1,!1;var e={plugin:"xqy_gpt",action:"saveModel",name:t.name,source:t.source,avatar:t.avatar,intro:t.intro,type:t.gptType,prompt:t.prompt,isVip:t.isVip,price:t.price,model_name:t.model_name,api_key:t.api_key,token:t.token};uni.showLoading({title:"加载中"}),t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:e,header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(e){if(t.submitStatus=!1,setTimeout((function(){uni.hideLoading()}),1e3),e.data&&e.data.msg?uni.showToast({title:e.data.msg,icon:"none"}):uni.showToast({title:"操作完成",icon:"none"}),e.data&&200==e.data.code)setTimeout((function(){t.back()}),1e3)},fail:function(e){t.submitStatus=!1,setTimeout((function(){uni.hideLoading()}),1e3),uni.showToast({title:"网络开小差了哦",icon:"none"}),uni.stopPullDownRefresh()}})},edit:function(){var t=this;if(0==t.parent)return uni.showToast({title:"未选择大类",icon:"none",duration:1e3,position:"bottom"}),!1;if(""==t.name)return uni.showToast({title:"请输入名称",icon:"none",duration:1e3,position:"bottom"}),!1;var e={plugin:"xqy_gpt",action:"saveModel",id:t.id,name:t.name,source:t.source,avatar:t.avatar,intro:t.intro,type:t.gptType,prompt:t.prompt,isVip:t.isVip,price:t.price,model_name:t.model_name,api_key:t.api_key,token:t.token};uni.showLoading({title:"加载中"}),t.$Net.request({url:t.$API.PluginLoad("xqy_gpt"),data:e,header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(e){if(setTimeout((function(){uni.hideLoading()}),1e3),e.data&&e.data.msg?uni.showToast({title:e.data.msg,icon:"none"}):uni.showToast({title:"操作完成",icon:"none"}),e.data&&200==e.data.code)setTimeout((function(){t.back()}),1e3)},fail:function(t){setTimeout((function(){uni.hideLoading()}),1e3),uni.showToast({title:"网络开小差了哦",icon:"none"}),uni.stopPullDownRefresh()}})},toAvatar:function(){uni.navigateTo({url:"/uni_modules/buuug7-img-cropper/pages/cropper",events:{imgCropped:function(t){}}})},getGptInfo:function(t){var e=this,i={plugin:"xqy_gpt",action:"models",id:t,token:e.token};e.$Net.request({url:e.$API.PluginLoad("xqy_gpt"),data:i,header:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",dataType:"json",success:function(t){if(t.data&&200==t.data.code&&t.data.data&&t.data.data.info){var i=t.data.data.info;e.name=i.name||"",e.source=i.source||"喵小算",e.isVip=i.isVip||0,1==e.isVip?e.switchVip=!0:e.switchVip=!1,e.price=i.price||0,e.avatar=i.avatar||"",e.intro=i.intro||"",e.model_name=i.model_name||"",e.api_key=i.api_key||"",e.gptType=i.type||0,e.prompt=i.prompt||""}else t.data&&t.data.msg&&uni.showToast({title:t.data.msg,icon:"none"})},fail:function(t){uni.showToast({title:"网络请求失败",icon:"none"})}})}}};e.default=o},"0f7c":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.base64ToPath=function(t){return new Promise((function(e,i){if("object"===("undefined"===typeof window?"undefined":(0,a.default)(window))&&"document"in window){t=t.split(",");var n=t[0].match(/:(.*?);/)[1],u=atob(t[1]),c=u.length,r=new Uint8Array(c);while(c--)r[c]=u.charCodeAt(c);return e((window.URL||window.webkitURL).createObjectURL(new Blob([r],{type:n})))}var l=t.split(",")[0].match(/data\:\S+\/(\S+);/);l?l=l[1]:i(new Error("base64 error"));var d=function(){return Date.now()+String(s++)}()+"."+l;if("object"!==("undefined"===typeof plus?"undefined":(0,a.default)(plus)))if("object"===("undefined"===typeof wx?"undefined":(0,a.default)(wx))&&wx.canIUse("getFileSystemManager")){p=wx.env.USER_DATA_PATH+"/"+d;wx.getFileSystemManager().writeFile({filePath:p,data:o(t),encoding:"base64",success:function(){e(p)},fail:function(t){i(t)}})}else i(new Error("not support"));else{var p="_doc/uniapp_temp/"+d;if(!function(t,e){for(var i=t.split("."),n=e.split("."),a=!1,o=0;o<n.length;o++){var s=i[o]-n[o];if(0!==s){a=s>0;break}}return a}("Android"===plus.os.name?"1.9.9.80627":"1.9.9.80472",plus.runtime.innerVersion))return void plus.io.resolveLocalFileSystemURL("_doc",(function(n){n.getDirectory("uniapp_temp",{create:!0,exclusive:!1},(function(n){n.getFile(d,{create:!0,exclusive:!1},(function(n){n.createWriter((function(n){n.onwrite=function(){e(p)},n.onerror=i,n.seek(0),n.writeAsBinary(o(t))}),i)}),i)}),i)}),i);var v=new plus.nativeObj.Bitmap(d);v.loadBase64Data(t,(function(){v.save(p,{},(function(){v.clear(),e(p)}),(function(t){v.clear(),i(t)}))}),(function(t){v.clear(),i(t)}))}}))},e.pathToBase64=function(t){return new Promise((function(e,i){if("object"===("undefined"===typeof window?"undefined":(0,a.default)(window))&&"document"in window){if("function"===typeof FileReader){var n=new XMLHttpRequest;return n.open("GET",t,!0),n.responseType="blob",n.onload=function(){if(200===this.status){var t=new FileReader;t.onload=function(t){e(t.target.result)},t.onerror=i,t.readAsDataURL(this.response)}},n.onerror=i,void n.send()}var o=document.createElement("canvas"),s=o.getContext("2d"),u=new Image;return u.onload=function(){o.width=u.width,o.height=u.height,s.drawImage(u,0,0),e(o.toDataURL()),o.height=o.width=0},u.onerror=i,void(u.src=t)}"object"!==("undefined"===typeof plus?"undefined":(0,a.default)(plus))?"object"===("undefined"===typeof wx?"undefined":(0,a.default)(wx))&&wx.canIUse("getFileSystemManager")?wx.getFileSystemManager().readFile({filePath:t,encoding:"base64",success:function(t){e("data:image/png;base64,"+t.data)},fail:function(t){i(t)}}):i(new Error("not support")):plus.io.resolveLocalFileSystemURL(function(t){if(0===t.indexOf("_www")||0===t.indexOf("_doc")||0===t.indexOf("_documents")||0===t.indexOf("_downloads"))return t;if(0===t.indexOf("file://"))return t;if(0===t.indexOf("/storage/emulated/0/"))return t;if(0===t.indexOf("/")){var e=plus.io.convertAbsoluteFileSystem(t);if(e!==t)return e;t=t.substr(1)}return"_www/"+t}(t),(function(t){t.file((function(t){var n=new plus.io.FileReader;n.onload=function(t){e(t.target.result)},n.onerror=function(t){i(t)},n.readAsDataURL(t)}),(function(t){i(t)}))}),(function(t){i(t)}))}))};var a=n(i("fcf3"));function o(t){var e=t.split(",");return e[e.length-1]}i("5ef2"),i("bf0f"),i("7a76"),i("c9b5"),i("5c47"),i("2c10"),i("15d1"),i("d5c6"),i("5a56"),i("f074"),i("4db2"),i("c976"),i("4d8f"),i("7b97"),i("668a"),i("c5b7"),i("8ff5"),i("2378"),i("641a"),i("64e0"),i("cce3"),i("efba"),i("d009"),i("bd7d"),i("7edd"),i("d798"),i("f547"),i("5e54"),i("b60a"),i("8c18"),i("12973"),i("f991"),i("198e"),i("8557"),i("63b1"),i("1954"),i("1cf1"),i("18f7"),i("de6c"),i("dc89"),i("2425");var s=0},"65e4":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"user",class:t.$store.state.AppStyle},[i("v-uni-view",{staticClass:"header",style:[{height:t.CustomBar+"px"}]},[i("v-uni-view",{staticClass:"cu-bar bg-white",style:{height:t.CustomBar+"px","padding-top":t.StatusBar+"px"}},[i("v-uni-view",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[i("v-uni-text",{staticClass:"cuIcon-back"})],1),i("v-uni-view",{staticClass:"content text-bold",style:[{top:t.StatusBar+"px"}]},["add"==t.type?[t._v("添加大模型")]:[t._v("大模型修改")]],2),"edit"==t.type?i("v-uni-view",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.edit.apply(void 0,arguments)}}},[i("v-uni-button",{staticClass:"cu-btn round bg-blue"},[t._v("保存")])],1):t._e(),"add"==t.type?i("v-uni-view",{staticClass:"action",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.add.apply(void 0,arguments)}}},[i("v-uni-button",{staticClass:"cu-btn round bg-blue"},[t._v("提交")])],1):t._e()],1)],1),i("v-uni-view",{style:[{padding:t.NavBar+"px 10px 0px 10px"}]}),i("v-uni-form",[i("v-uni-view",{staticClass:"user-edit-header margin-top"},[i("v-uni-image",{attrs:{src:t.avatar}}),i("v-uni-text",{staticClass:"cu-btn bg-blue radius",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toAvatar.apply(void 0,arguments)}}},[t._v("设置大模型图标")])],1),"edit"==t.type?i("v-uni-view",{staticClass:"cu-form-group margin-top"},[i("v-uni-view",{staticClass:"title"},[t._v("ID")]),i("v-uni-input",{attrs:{name:"input",disabled:!0,value:t.id}})],1):t._e(),i("v-uni-view",{staticClass:"cu-form-group"},[i("v-uni-view",{staticClass:"title"},[t._v("名称")]),i("v-uni-input",{attrs:{name:"input",type:"text"},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}})],1),i("v-uni-view",{staticClass:"cu-form-group align-start"},[i("v-uni-view",{staticClass:"title"},[t._v("简介")]),i("v-uni-textarea",{attrs:{placeholder:"请输入大模型角色简介"},model:{value:t.intro,callback:function(e){t.intro=e},expression:"intro"}})],1),"add"==t.type?i("v-uni-view",{staticClass:"cu-form-group"},[i("v-uni-view",{staticClass:"title"},[t._v("类型")]),i("v-uni-view",{staticClass:"action"},[i("v-uni-text",{staticClass:"meta-type",class:0==t.gptType?"act":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gptType=0}}},[t._v("聊天大模型")]),i("v-uni-text",{staticClass:"meta-type",class:1==t.gptType?"act":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gptType=1}}},[t._v("AI应用")])],1)],1):t._e(),i("v-uni-view",{staticClass:"cu-form-group align-start"},[i("v-uni-view",{staticClass:"title"},[t._v("Prompt")]),i("v-uni-textarea",{attrs:{maxlength:"10000",placeholder:1==t.gptType?"请输入AI应用Prompt":"请输入聊天大模型的系统指令，定义AI的行为和回复风格"},model:{value:t.prompt,callback:function(e){t.prompt=e},expression:"prompt"}})],1),i("v-uni-view",{staticClass:"padding-sm text-sm text-grey text-right"},[t._v("已输入: "+t._s(t.prompt.length)+" / 10000 字符")]),0==t.gptType?i("v-uni-view",{staticClass:"cu-bar bg-white solid-bottom"},[i("v-uni-view",{staticClass:"action"},[i("v-uni-text",{staticClass:"cuIcon-info text-blue"}),i("v-uni-text",{staticClass:"text-sm text-blue"},[t._v("提示：系统指令可以定义AI角色和行为")])],1)],1):t._e(),0==t.gptType?i("v-uni-view",{staticClass:"padding bg-white text-sm text-grey"},[i("v-uni-view",[t._v("示例1：你是一位专业的程序员，熟悉多种编程语言。请用简洁专业的方式回答问题。")]),i("v-uni-view",{staticClass:"margin-top-sm"},[t._v("示例2：你是一位亲切的心理咨询师，善于倾听和提供建议，回答要温和有耐心。")])],1):t._e(),i("v-uni-view",{staticClass:"cu-form-group"},[i("v-uni-view",{staticClass:"title"},[t._v("模型源")]),i("v-uni-view",{staticClass:"picker",attrs:{"data-target":"sourceList"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showModal.apply(void 0,arguments)}}},[t._v(t._s(t.sourceText)),i("v-uni-text",{staticClass:"cuIcon-right"})],1)],1),i("v-uni-view",{staticClass:"cu-form-group margin-top"},[i("v-uni-view",{staticClass:"title"},[t._v("仅VIP可用")]),i("v-uni-switch",{class:t.switchVip?"checked":"",attrs:{checked:!!t.switchVip},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.SwitchVIP.apply(void 0,arguments)}}})],1),i("v-uni-view",{staticClass:"cu-form-group"},[i("v-uni-view",{staticClass:"title"},[t._v("单次请求价格（整数）")]),i("v-uni-input",{attrs:{name:"input",type:"Number"},model:{value:t.price,callback:function(e){t.price=e},expression:"price"}})],1),i("v-uni-view",{staticClass:"cu-form-group align-start"},[i("v-uni-view",{staticClass:"title"},[t._v("AI模型名称")]),i("v-uni-textarea",{attrs:{placeholder:"请输入喵小算平台的AI模型名称，如：deepseek-chat"},model:{value:t.model_name,callback:function(e){t.model_name=e},expression:"model_name"}})],1),i("v-uni-view",{staticClass:"cu-form-group align-start"},[i("v-uni-view",{staticClass:"title"},[t._v("API密钥")]),i("v-uni-textarea",{attrs:{placeholder:"请输入喵小算平台的API密钥"},model:{value:t.api_key,callback:function(e){t.api_key=e},expression:"api_key"}})],1)],1),i("v-uni-view",{staticClass:"cu-modal",class:"sourceList"==t.modalName?"show":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideModal.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"cu-dialog",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("v-uni-radio-group",{staticClass:"block",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.RadioChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"cu-list menu text-left"},t._l(t.sourceList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"cu-item"},[i("v-uni-label",{staticClass:"flex justify-between align-center flex-sub",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.setSource(e)}}},[i("v-uni-view",{staticClass:"flex-sub"},[t._v(t._s(e.name))]),i("v-uni-radio",{staticClass:"round",class:t.source==e.id?"checked":"",attrs:{checked:t.source==e.id}})],1)],1)})),1)],1)],1)],1)],1)},a=[]},e815:function(t,e,i){"use strict";i.r(e);var n=i("01791"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},eed7:function(t,e,i){"use strict";i.r(e);var n=i("65e4"),a=i("e815");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var s=i("828b"),u=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"db66bba0",null,!1,n["a"],void 0);e["default"]=u.exports}}]);