<template>
	<view :class="$store.state.AppStyle" style="background-color: #f6f6f6;height: 100vh;">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="toSearch">
					<text class="cuIcon-search"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					所有圈子
				</view>
				<!--  #ifdef H5 || APP-PLUS -->
				<view class="action header-btn">
					
					<text class="cuIcon-notice" @tap="toLink('/pages/inbox/home')">
						<text class="noticeSum bg-red" v-if="noticeSum>0">{{noticeSum > 99?'99+':noticeSum}}</text>
					</text>
				</view>
				<!--  #endif -->
			</view>
		</view>
		<view :style="[{padding:CustomBar+10 + 'px 10px 0px 10px'}]"></view>
		<view class="ads-banner" v-if="bannerAdsInfo!=null">
			<image :src="bannerAdsInfo.img" mode="widthFix" @tap="goAds(bannerAdsInfo)"></image>
		</view>
		<forumIndex :sectionList="sectionList" :swiperList="swiperList" :isSwiper="0"></forumIndex>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
		<view class="full-noLogin" v-if="noLogin">
			<view class="full-noLogin-main">
				<view class="full-noLogin-text">
					您需要登录后才可以查看内容哦！
				</view>
				<view class="full-noLogin-btn">
					<view class="cu-btn bg-blue" @tap="goLogin()">
						立即登录
					</view>
				</view>
			</view>
		</view>
		
		<view style="width: 100%; height: 130upx;"></view>
	</view>
</template>

<script>
	import waves from '@/components/xxley-waves/waves.vue';
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		props: {
			curPage: {
			  type: Number,
			  default: 0
			}
		},
		name: "allSection",
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
				AppStyle:this.$store.state.AppStyle,
				
				userInfo:null,
				token:"",
				isLoading:0,
				
				noticeSum:0,
				
				
				uid:0,
				dataLoad:false,
				submitStatus1:false,
				sectionList:[],
				
				noLogin:false,
				
				myPurview:0,
				swiperList:[],
				
				ads:"",
				bannerAds:[],
				bannerAdsInfo:null,
				
				noLogin:false,
				
				
			}
		},
		mounted() {
			var that = this;
			uni.$on('onShow', function(data) {
				if(Number(data)!=Number(that.curPage)){
					return false;
				}
				console.log("触发Tab-"+data+"||页面下标"+that.curPage);
				that.getAdsCache();
				that.getSectionList();
				that.unreadNum();
			});
			
			uni.$on('onReachBottom', function(data) {
				if(Number(data)!=Number(that.curPage)){
					return false;
				}
				console.log("触发触底刷新");
			});
			
			uni.$on('onPullDownRefresh', function(data) {
				if(Number(data)!=Number(that.curPage)){
					return false;
				}
				console.log("触发下拉刷新");
				that.getSectionList();
			});
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif	
		},
		beforeDestroy(){
			var that = this;
			uni.$off('onReachBottom')
			uni.$off('onShow')
			uni.$off('onPullDownRefresh')
		},
		methods:{
			getAdsCache(){
				var that = this;
				if(localStorage.getItem('bannerAds')){
					that.bannerAds = JSON.parse(localStorage.getItem('bannerAds'));
					
					var num = that.bannerAds.length;
					if(num>0){
						var rand = Math.floor(Math.random()*num);
						that.bannerAdsInfo = that.bannerAds[rand];
					}
				}
			},
			searchClose(){
				var that = this;
				that.searchText = "";
				that.page = 1;
				that.getUserList(false);
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				// 获取年月日时分秒值  slice(-2)过滤掉大于10日期前面的0
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				//second = ("0" + date.getSeconds()).slice(-2);
				// 拼接
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				// 返回
				return result;
			},
			toSearch(){
				var that = this;
				if(that.noLogin){
					uni.navigateTo({
					    url: '/pages/user/login'
					});
					return false;
				}
				uni.navigateTo({
				    url: '/pages/contents/search'
				});
			},
			toLink(text){
				var that = this;
				
				if(!localStorage.getItem('token')||localStorage.getItem('token')==""){
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			unreadNum() {
				var that = this;
				if(localStorage.getItem('noticeSum')){
					that.noticeSum = Number(localStorage.getItem('noticeSum'));
				}
			},

			getSectionList(isLogin){
				var that = this;
				if(that.submitStatus1){
					return false;
				}
				that.submitStatus1 = true;
				var token = "";
				if(!isLogin){
					localStorage.setItem('isbug','1');
				}
				
				that.$Net.request({
					url: that.$API.sectionList(),
					data:{
						"limit":50,
						"page":1,
						"searchKey":that.searchText,
						"token":token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if(!isLogin){
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						uni.stopPullDownRefresh()
						that.isLoad=0;
						if(res.data.code==1){
							that.noLogin = false;
							var list = res.data.data;
							var parentList = [];
							for(var i in list){
								if(list[i].parent==0){
									list[i].subList = [];
									parentList.push(list[i]);
								}
							}
							for(var j in list){
								if(list[j].parent!=0){
									for(var p in parentList){
										if(list[j].parent == parentList[p].id){
											parentList[p].subList.push(list[j]);
										}
									}
								}
							}
							that.sectionList = parentList;
						}else{
							if (res.data.msg == "用户未登录或Token验证失败") {
								if (isLogin) {
									that.noLogin = true
								}else{
									that.getSectionList(true);
								}
							}
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						if(!isLogin){
							localStorage.removeItem('isbug');
						}
						that.submitStatus1 = false;
						uni.stopPullDownRefresh()
						that.moreText="加载更多";
						that.isLoad=0;
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			subText(text,num){
				if(text.length < null){
					return text.substring(0,num)+"……"
				}else{
					return text;
				}
				
			},
			goAds(data){
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if(type==1){
					plus.runtime.openURL(url);
				}
				if(type==0){
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			}
		},
		// #ifdef APP-PLUS
		components: {
			waves
		},
		// #endif
		
		// #ifdef H5 || MP
		components: {
			waves
		},
		// #endif
		
	}
</script>

<style>
</style>
