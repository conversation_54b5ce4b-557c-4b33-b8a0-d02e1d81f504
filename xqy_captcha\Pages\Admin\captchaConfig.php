<?php include 'Head.php'; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="card-title">行为验证配置</h4>
                <p class="card-title-desc">配置第三方行为验证服务</p>

                <form id="captchaConfigForm">
                    <div class="form-group row">
                        <label for="captcha_type" class="col-md-2 col-form-label">验证类型</label>
                        <div class="col-md-10">
                            <select class="form-control" id="captcha_type" name="captcha_type">
                                <option value="geetest">极验验证</option>
                                <option value="cloudflare">Cloudflare Turnstile</option>
                                <option value="recaptcha">Google reCAPTCHA</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="enabled" class="col-md-2 col-form-label">启用状态</label>
                        <div class="col-md-10">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="enabled" name="enabled" value="1">
                                <label class="custom-control-label" for="enabled">启用行为验证</label>
                            </div>
                        </div>
                    </div>

                    <!-- 极验配置 -->
                    <div id="geetest_config" class="captcha-config">
                        <h5 class="mt-4">极验配置</h5>
                        <div class="form-group row">
                            <label for="geetest_id" class="col-md-2 col-form-label">极验ID</label>
                            <div class="col-md-10">
                                <input class="form-control" type="text" id="geetest_id" name="geetest_id" placeholder="请输入极验ID">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="geetest_key" class="col-md-2 col-form-label">极验KEY</label>
                            <div class="col-md-10">
                                <input class="form-control" type="text" id="geetest_key" name="geetest_key" placeholder="请输入极验KEY">
                            </div>
                        </div>
                    </div>

                    <!-- Cloudflare配置 -->
                    <div id="cloudflare_config" class="captcha-config" style="display: none;">
                        <h5 class="mt-4">Cloudflare Turnstile配置</h5>
                        <div class="form-group row">
                            <label for="cloudflare_site_key" class="col-md-2 col-form-label">站点密钥</label>
                            <div class="col-md-10">
                                <input class="form-control" type="text" id="cloudflare_site_key" name="cloudflare_site_key" placeholder="请输入Cloudflare站点密钥">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="cloudflare_secret_key" class="col-md-2 col-form-label">私钥</label>
                            <div class="col-md-10">
                                <input class="form-control" type="text" id="cloudflare_secret_key" name="cloudflare_secret_key" placeholder="请输入Cloudflare私钥">
                            </div>
                        </div>
                    </div>

                    <!-- reCAPTCHA配置 -->
                    <div id="recaptcha_config" class="captcha-config" style="display: none;">
                        <h5 class="mt-4">Google reCAPTCHA配置</h5>
                        <div class="form-group row">
                            <label for="recaptcha_site_key" class="col-md-2 col-form-label">站点密钥</label>
                            <div class="col-md-10">
                                <input class="form-control" type="text" id="recaptcha_site_key" name="recaptcha_site_key" placeholder="请输入reCAPTCHA站点密钥">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="recaptcha_secret_key" class="col-md-2 col-form-label">私钥</label>
                            <div class="col-md-10">
                                <input class="form-control" type="text" id="recaptcha_secret_key" name="recaptcha_secret_key" placeholder="请输入reCAPTCHA私钥">
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-md-10 offset-md-2">
                            <button type="submit" class="btn btn-primary">保存配置</button>
                            <button type="button" class="btn btn-secondary" onclick="loadConfig()">重新加载</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadConfig();
    
    // 验证类型切换
    $('#captcha_type').change(function() {
        var type = $(this).val();
        $('.captcha-config').hide();
        $('#' + type + '_config').show();
    });
    
    // 表单提交
    $('#captchaConfigForm').submit(function(e) {
        e.preventDefault();
        saveConfig();
    });
});

function loadConfig() {
    $.ajax({
        url: '/Plugins/xqy_captcha/index.php',
        type: 'POST',
        data: {
            plugin: 'xqy_captcha',
            action: 'getConfig',
            admin: '1'
        },
        dataType: 'json',
        success: function(response) {
            if (response.code === 200) {
                var config = response.data;
                $('#captcha_type').val(config.captcha_type).trigger('change');
                $('#enabled').prop('checked', config.enabled == 1);
                $('#geetest_id').val(config.geetest_id || '');
                $('#geetest_key').val(config.geetest_key || '');
                $('#cloudflare_site_key').val(config.cloudflare_site_key || '');
                $('#cloudflare_secret_key').val(config.cloudflare_secret_key || '');
                $('#recaptcha_site_key').val(config.recaptcha_site_key || '');
                $('#recaptcha_secret_key').val(config.recaptcha_secret_key || '');
            } else {
                alert('加载配置失败：' + (response.msg || '未知错误'));
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX错误:', {xhr: xhr, status: status, error: error});
            alert('网络错误，请重试：' + error);
        }
    });
}

function saveConfig() {
    var formData = {
        plugin: 'xqy_captcha',
        action: 'setConfig',
        captcha_type: $('#captcha_type').val(),
        enabled: $('#enabled').is(':checked') ? 1 : 0,
        geetest_id: $('#geetest_id').val(),
        geetest_key: $('#geetest_key').val(),
        cloudflare_site_key: $('#cloudflare_site_key').val(),
        cloudflare_secret_key: $('#cloudflare_secret_key').val(),
        recaptcha_site_key: $('#recaptcha_site_key').val(),
        recaptcha_secret_key: $('#recaptcha_secret_key').val()
    };

    $.ajax({
        url: '/Plugins/xqy_captcha/index.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.code === 200) {
                alert('配置保存成功');
                // 重新加载配置以确保数据同步
                setTimeout(function() {
                    loadConfig();
                }, 300);
            } else {
                alert('配置保存失败：' + (response.msg || '未知错误'));
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX错误:', {xhr: xhr, status: status, error: error});
            alert('网络错误，请重试：' + error);
        }
    });
}
</script>

<?php include 'Footer.php'; ?>
