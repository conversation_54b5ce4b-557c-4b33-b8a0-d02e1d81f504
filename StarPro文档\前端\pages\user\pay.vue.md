<template>
	<view class="user" :class="$store.state.AppStyle" style="background-color: #f6f6f6;">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					提现账户设置
				</view>
				<view class="action" @tap="userEdit">
					<text class="cuIcon-upload"></text>
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		
		<form>
			<view class="cu-form-group margin-top">
				<view class="title">账户类型</view>
				<view class="picker" @tap="showModal" data-target="payModal">
					<block v-if="type==''">
						选择账户类型
					</block>
					<block v-else>
						{{type}}
					</block>
					<text class="cuIcon-right"></text>
				</view>
			</view>
			<view class="cu-form-group margin-top">
				<view class="title">真实姓名</view>
				<input placeholder="请输入真实姓名" name="input" type="text" v-model="realname"></input>
			</view>
			<view class="cu-form-group">
				<view class="title">账户信息</view>
				<input placeholder="请输入账户信息" name="input" type="text" v-model="info"></input>
			</view>
			<view class="cu-form-group">
				<view class="title">收款二维码</view>
				<view class="action">
					<text class="cu-btn bg-blue radius" @tap="upload">上传图片</text>
				</view>
			</view>
			<view class="pay-codeImg" v-if="imgurl!=''">
				<image :src="imgurl"></image>
			</view>
			
		</form>
		<!--  #ifdef MP -->
		<view class="post-update bg-blue" @tap="userEdit">
			<text class="cuIcon-upload"></text>
		</view>
		<!--  #endif -->
		<!--分类选择控件-->
		<view class="cu-modal" :class="modalName=='payModal'?'show':''" @tap="hideModal">
			<view class="cu-dialog" @tap.stop="">
				<radio-group class="block">
					<view class="cu-list menu text-left">
						<view class="cu-item" v-for="(item,index) in payList" :key="index" @tap="toPay(item.name)" >
							<label class="flex justify-between align-center flex-sub">
								<view class="flex-sub">{{item.name}}</view>
								<radio class="round"></radio>
							</label>
						</view>
					</view>
				</radio-group>
			</view>
		</view>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar,
			AppStyle:this.$store.state.AppStyle,
				
				uid:0,
				payList:[
					{
					"name":"支付宝",
					},
					{
					"name":"微信",
					}
				],
				type:"",
				realname:"",
				info:"",
				
				token:'',
				userInfo:"",
				isHuaWei: this.$API.isHuaWei(),
				isTy: false,
				
				modalName:"",
				
				imgurl:"",
				
				isLoading:0,
			}
		},
		onPullDownRefresh(){
			var that = this;
			
		},
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			if(localStorage.getItem('token')){
				
				that.token = localStorage.getItem('token');
			}
			that.userStatus();
			that.getCacheInfo();
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
		},
		methods: {
			back(){
				uni.navigateBack({
					delta: 1
				});
			},
			PickerChange(e) {
				this.index = e.detail.value
			},
			showModal(e) {
				this.modalName = e.currentTarget.dataset.target
			},
			hideModal() {
				this.modalName = null
			},
			toPay(name){
				var that = this;
				that.type = name;
				that.hideModal();
			},
			getCacheInfo(){
				var that = this;
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					that.uid=userInfo.uid;
					that.userInfo = userInfo;
				}
			},
			userEdit() {
				var that = this;
				if(that.type==""||that.realname==""||that.info==""||that.imgurl==""){
					uni.showToast({
						title: "请完成表单输入",
						icon: 'none'
					})
					return false;
				}
				var pay = that.type+"|"+that.realname+"|"+that.info+"|"+that.imgurl;
				var data = {
					uid:that.uid,
					name:that.userInfo.name,
					pay:pay
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.userEdit(),
					data:{
						"params":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"token":that.token,
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==1){
							uni.showToast({
								title: "操作成功！",
								icon: 'none'
							})
							setTimeout(function () {
								that.back();
							}, 1000);
							
							
						}
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			
			// #ifdef APP-PLUS
						//权限检测
						showTC(){
							var that = this;
							var tc1 = false;
							let _permissionID = 'android.permission.WRITE_EXTERNAL_STORAGE';
							let _permissionID2 = 'android.permission.CAMERA';
							plus.android.checkPermission(_permissionID2,
								granted => {
									if (granted.checkResult == -1) {
										console.log('无相机权限');
										uni.showModal({
											title: '权限申请说明',
											content: '我们将申请获取相机权限和访问相册权限，便于您使用拍照上传您的照片/视频及用于更换头像、意见反馈、保存相册、发布动态等场景。',
											cancelText: "取消",
											confirmText: "同意",
											showCancel: true,
											confirmColor: '#000',
											cancelColor: '#666',
											success: (res) => {
												if (res.confirm) {
													console.log('弹窗同意');
													tc1 = true;
													that.requestPermissions();
												}else{
													console.log('弹窗取消');
													that.isTy = false
													tc1 = true;
												}
											}
										})
										//还未授权当前查询的权限，打开权限申请目的自定义弹框
										that.$nextTick(() => {
											setTimeout(() => {
												that.ani = 'uni-' + that.type
											},30)
										})
									}
								},
								error => {
									console.log(error.message);
								}
							);
							plus.android.checkPermission(_permissionID,
								granted => {
									if (granted.checkResult == -1) {
										console.log('无相册权限');
										if(!tc1){
											uni.showModal({
												title: '权限申请说明',
												content: '我们将申请获取相机权限和访问相册权限，便于您使用拍照上传您的照片/视频及用于更换头像、意见反馈、保存相册、发布动态等场景。',
												cancelText: "取消",
												confirmText: "同意",
												showCancel: true,
												confirmColor: '#000',
												cancelColor: '#666',
												success: (res) => {
													if (res.confirm) {
														console.log('弹窗同意');
														return that.requestPermissions();
													}else{
														console.log('弹窗取消');
														that.isTy = false
													}
												}
											})
										}
										
										//还未授权当前查询的权限，打开权限申请目的自定义弹框
										that.$nextTick(() => {
											setTimeout(() => {
												that.ani = 'uni-' + that.type
											},30)
										})
									}
								},
								error => {
									console.log(error.message);
								}
							);
							
						},
						requestPermissions() {
							let _this = this;
						
								let _permissionID = 'android.permission.WRITE_EXTERNAL_STORAGE';
								let _permissionID2 = 'android.permission.CAMERA';
								
								plus.android.checkPermission(_permissionID2,
									granted => {
										if (granted.checkResult == -1) {
											//还未授权当前查询的权限，打开权限申请目的自定义弹框
											
											_this.$nextTick(() => {
												setTimeout(() => {
													_this.ani = 'uni-' + _this.type
												},30)
											})
										}
									},
									error => {
										console.log(error.message);
									}
								);
								plus.android.requestPermissions([_permissionID2],
									(e) => {
										//关闭权限申请目的自定义弹框
										_this.ani = '';
										_this.$nextTick(() => {
											
										})
										console.log(e,'kkkkk')
										if (e.granted.length > 0) {
											//当前查询权限已授权
											console.log('1已同意');
											plus.android.checkPermission(_permissionID,
												granted => {
													if (granted.checkResult == -1) {
														//还未授权当前查询的权限，打开权限申请目的自定义弹框
														
														_this.$nextTick(() => {
															setTimeout(() => {
																_this.ani = 'uni-' + _this.type
															},30)
														})
													}
												},
												error => {
													console.log(error.message);
												}
											);
											plus.android.requestPermissions([_permissionID],
												(e) => {
													//关闭权限申请目的自定义弹框
													_this.ani = '';
													_this.$nextTick(() => {
														
													})
													console.log(e,'kkkkk')
													if (e.granted.length > 0) {
														//当前查询权限已授权
														console.log('2已同意');
														_this.isTy = true
													}
													if (e.deniedAlways.length > 0) {
														//当前查询权限已被永久禁用，此时需要引导用户跳转手机系统设置去开启
														uni.showModal({
															title: '温馨提示',
															content: '您没有给予访问相册权限，不给予权限将无法使用该功能，立即去设置开启？',
															cancelText: "取消",
															confirmText: "去设置",
															showCancel: true,
															confirmColor: '#000',
															cancelColor: '#666',
															success: (res) => {
																if (res.confirm) {
																	_this.goSetting();
																}else{
																	_this.isTy = false
																}
															}
														})
													}
												})
										}
										if (e.deniedAlways.length > 0) {
											//当前查询权限已被永久禁用，此时需要引导用户跳转手机系统设置去开启
											uni.showModal({
												title: '温馨提示',
												content: '您没有给予使用相机权限，不给予权限将无法使用该功能，立即去设置开启？',
												cancelText: "取消",
												confirmText: "去设置",
												showCancel: true,
												confirmColor: '#000',
												cancelColor: '#666',
												success: (res) => {
													if (res.confirm) {
														_this.goSetting();
													}else{
														_this.isTy = false
													}
												}
											})
										}
									})
							
						},
						//跳转手机系统设置
						goSetting() {
								var Intent = plus.android.importClass("android.content.Intent");
								var Settings = plus.android.importClass("android.provider.Settings");
								var Uri = plus.android.importClass("android.net.Uri");
								var mainActivity = plus.android.runtimeMainActivity();
								var intent = new Intent();
								intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
								var uri = Uri.fromParts("package", mainActivity.getPackageName(), null);
								intent.setData(uri);
								mainActivity.startActivity(intent);
						},
						// #endif
			
			upload(){
				let that = this		
						if(that.isTy==false&&that.isHuaWei==1){
											that.showTC()
										}
				uni.chooseImage({
					count: 1,  // 最多可以选择的图片张数，默认9
					sourceType: ['album', 'camera'], 
				    success: function (res) {						
						uni.showLoading({
							title: "加载中"
						});
						const tempFilePaths = res.tempFilePaths;
						const uploadTask = uni.uploadFile({
						  url : that.$API.upload(),
						  filePath: tempFilePaths[0],
						  name: 'file',
						  formData: {
						   'token': that.token
						  },
						  success: function (uploadFileRes) {
							  setTimeout(function () {
							  	uni.hideLoading();
							  }, 1000);
							var data = JSON.parse(uploadFileRes.data);
							//var data = uploadFileRes.data;
							uni.showToast({
								title: data.msg,
								icon: 'none'
							})
							if(data.code==1){
								that.imgurl = data.data.url;
							}
							},fail:function(){
								setTimeout(function () {
									uni.hideLoading();
								}, 1000);
							}
							
						   
						});
					 
						uploadTask.onProgressUpdate(function (res) {
						  
						 });
					}
				})
			},
			userStatus() {
				var that = this;
				that.$Net.request({
					
					url: that.$API.userStatus(),
					data:{
						"token":that.token
					},
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if(res.data.code==1){
							var pay = res.data.data.pay;
							if(pay){
								var arr = pay.split("|");
								that.type=arr[0];
								that.realname=arr[1];
								that.info=arr[2];
								that.imgurl=arr[3];
							}
							
						}
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						var timer = setTimeout(function() {
							that.isLoading=1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
		}
	}
</script>

<style>
</style>
