<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action">
					<text class="cuIcon-back" @tap="back"></text>
					<!--  #ifdef MP-WEIXIN -->
					<text class="cuIcon-moreandroid" @click="show = true,commentsAdd = false"></text>
					<!--  #endif -->
				</view>
				
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					<view class="section-sub" style="line-height: 30px;">
						<text style="font-size:30upx">文章详情</text>
					</view>
				</view>
				<view class="action info-btn">
					<!--  #ifdef H5 || APP-PLUS -->
					<text class="cuIcon-moreandroid" @click="show = true,commentsAdd = false"></text>
					<!--  #endif -->
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<tn-popup v-model="show" mode="bottom" :zIndex="500" :closeBtn="true" height="30%" :borderRadius="20">
			<view class="center-container tn-margin-top-xxl">
				<view class="">
					<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
						@tap="toJb(title),show=false" v-if="authorId!=uid">
						<text class="tn-icon-warning" style="margin-right: 5px;"></text>举报文章
					</view>
					<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
						v-if="isFollow==0&&authorId!=uid" @tap="follow(1,userInfo.uid),show=false">
						<text class="tn-icon-my-add" style="margin-right: 5px;"></text>立即关注
					</view>
					<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg" v-if="isFollow==1"
						@tap="follow(0,userInfo.uid),show=false">
						<text class="tn-icon-my-reduce" style="margin-right: 5px;"></text>取消关注
					</view>
					<block v-if="actStyle==2&&group=='administrator'||group=='editor'&&actStyle==2">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="setFields(cid),show=false">
							<text class="tn-icon-notebook" style="margin-right: 5px;"></text>外显类型
						</view>
					</block>

					<block v-if="authorId==uid||group=='administrator'||group=='editor'">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="goPost(cid)">
							<text class="tn-icon-edit-write" style="margin-right: 5px;"></text>编辑帖子
						</view>
					</block>
					<block v-if="authorId==uid&&group!='administrator'">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="toDelete(cid)" v-if="allowDelete==1">
							<text class="tn-icon-delete" style="margin-right: 5px;"></text>删除帖子
						</view>
					</block>
					<block v-if="group=='administrator'">
						<view class="tn-padding-sm tn-radius bg-flex-shadow popup-content sy-text-lg"
							@tap="toAdminDelete(cid)" v-if="allowDelete==1">
							<text class="tn-icon-delete" style="margin-right: 5px;"></text>删除帖子
						</view>
					</block>

				</view>
			</view>
		</tn-popup>
		<view style="margin-top: 35upx;margin-left: 15upx;margin-right: 15upx;">
			<view class="info" style="margin-top: 20upx;">
				<view class="info-title" style="border-radius: 20upx 20upx 0 0;">
					{{title}}
				</view>
				<view class="forum-author">
					<view class="forum-list-user" @tap="toUserInfo(userInfo)">
						<view class="forum-avatar tn-margin-bottom-sm user-rz">			
							<image class="forum-avatar-image" :src="userInfo.avatar"></image>
							<!-- 头像框层 -->
							<image v-if="xqy_avatarframe && frameUrl" class="avatar-frame" :src="frameUrl" mode="aspectFill" @error="onFrameLoadError"></image>
							<!-- 认证图标 -->
							<image class="user-rz-icon" width="34upx" height="34upx" :src="rzImg" mode="aspectFill" v-if="lvrz==1"></image>
						</view>
						<view class="forum-userinfo">
							<view class="forum-userinfo-name">
								<text :class="userInfo.isvip>0?'name-vip':''">
									<block v-if="userInfo.screenName!=''">
										{{userInfo.screenName}}
									</block>
									<block v-else>
										{{userInfo.name || "用户已注销"}}
									</block>
									<image v-if="userInfo.isvip>0" :src="vipImg"
										style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;"
										mode="widthFix"></image>
									<image :src="lvImg+getLv(userInfo.experience)+'.png'"
										style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 45upx;height: 22upx;"
										mode="widthFix"></image>
									<!-- 勋章组件 -->
									<medal-item v-if="authorId" :uid="authorId" @loaded="onMedalLoaded" style="margin-left: 6upx;"></medal-item>
								</text>

							</view>
							<view class="forum-userinfo-date">
								{{formatDate(created)}} <text class="margin-left-sm"
									v-if="$API.localOf()">{{getLocal(userInfo.local)}}</text>
							</view>
							<!-- <view class="cu-btn xs forum-follow" isFollow>
							<text class="cuIcon-add"></text>
							关注
						</view> -->
						</view>
					</view>
				</view>
				<view class="info-content">
					
					<mp-html :content="html" :selectable="true" :show-img-menu="true" :scroll-table="true"
						:markdown="true" :lazyLoad="false" />


					<view class="shop-value" v-if="shopValue!=''">
						<view class="shop-value-title">
							付费内容
						</view>
						<block v-if="shopIsMd==1">
							<mp-html :content="shopValue" :selectable="true" :show-img-menu="true" :scroll-table="true"
								:markdown="true" />
						</block>
						<block v-if="shopIsMd==0">
							<mp-html :content="shopValue" :selectable="true" :show-img-menu="true" :scroll-table="true"
								:markdown="false" />
						</block>

					</view>
					<view class="content-shop" v-if="shopValue==''">
						<view class="cu-card article no-card" v-for="(item,index) in shopList" :key="index">
							<block v-if="item.type==1">
								<view class="shop-tool text-center">
									<view class="shop-name">
										实体商品
									</view>
									<image :src="item.imgurl" mode="aspectFill"></image>
									<view class="text-content">{{item.title}}</view>
									<view class="tool-price" v-if="isBuy==0">
										<text class="text-red text-bold">{{item.price}} {{currencyName}}</text><text
											class="margin-left-sm text-sm">VIP只需</text><text
											class="text-yellow text-bold">{{parseInt(item.price * item.vipDiscount)}}{{currencyName}}</text>
									</view>
									<view class="tool-price">
										<text class="cu-btn bg-blue" @tap="shopBuy(item.id,item.type)">立即下单</text>
										<text class="cu-btn text-red" @tap="shopInfo(item)">商品详情</text>
									</view>
								</view>
							</block>
							<!--源码-->
							<block v-if="item.type==2">
								<view class="shop-tool text-center">
									<view class="shop-name">
										源码
									</view>
									<image :src="item.imgurl" mode="aspectFill"></image>
									<view class="text-content">{{item.title}}</view>
									<view class="tool-price" v-if="isBuy==0">
										<text class="text-red text-bold">{{item.price}} {{currencyName}}</text><text
											class="margin-left-sm text-sm">VIP只需</text><text
											class="text-yellow text-bold">{{parseInt(item.price * item.vipDiscount)}}{{currencyName}}</text>
									</view>
									<view class="tool-price" v-if="isBuy==1">
										<text class="cu-btn bg-blue" @tap="toShopValue(item.id,item.type)">查看收费内容</text>
										<text class="cu-btn text-red" @tap="shopInfo(item)">商品详情</text>
									</view>
									<view class="tool-price" v-else>
										<text class="cu-btn bg-blue" @tap="shopBuy(item.id,item.type)">购买后下载</text>
										<text class="cu-btn text-red" @tap="shopInfo(item)">商品详情</text>
									</view>
								</view>
							</block>
							<!--工具-->
							<block v-if="item.type==3">
								<view class="shop-tool text-center">
									<view class="shop-name">
										软件工具
									</view>
									<image :src="item.imgurl" mode="aspectFill"></image>
									<view class="text-content">{{item.title}}</view>
									<view class="tool-price" v-if="isBuy==0">
										<text class="text-red text-bold">{{item.price}} {{currencyName}}</text><text
											class="margin-left-sm text-sm">VIP只需</text><text
											class="text-yellow text-bold">{{parseInt(item.price * item.vipDiscount)}}{{currencyName}}</text>
									</view>
									<view class="tool-price" v-if="isBuy==1">
										<text class="cu-btn bg-blue" @tap="toShopValue(item.id,item.type)">查看收费内容</text>
										<text class="cu-btn text-red" @tap="shopInfo(item)">商品详情</text>
									</view>
									<view class="tool-price" v-else>
										<text class="cu-btn bg-blue" @tap="shopBuy(item.id,item.type)">购买后下载</text>
										<text class="cu-btn text-red" @tap="shopInfo(item)">商品详情</text>
									</view>
								</view>
							</block>
							<!--付费阅读-->
							<block v-if="item.type==4">
								<view class="text-left">
									<text class="left-title"></text>付费后查看
								</view>
								<view class="tool-price" v-if="isBuy==0">
									<text class="text-red text-bold">{{item.price}} {{currencyName}}</text><text
										class="margin-left-sm text-sm">会员价</text><text
										class="text-yellow text-bold">{{parseInt(item.price * item.vipDiscount)}}{{currencyName}}</text>
								</view>
								<br />
								<view class="tool-price" v-if="isBuy==1" @tap="toShopValue(item.id,item.type)">
									<text class="cu-btn bg-blue" style="border-radius: 50upx;">查看订单详情</text>
								</view>
								<view class="tool-price" v-if="isBuy==0" @tap="shopBuy(item.id,item.type)">
									<text class="cu-btn bg-blue" style="border-radius: 50upx;">立即购买</text>
								</view>
							</block>
						</view>
					</view>

				</view>
				<view class="reward-log" v-if="rewardLog.length > 0&&dsstyle==1">
					<view class="reward-log-main">
						<tn-avatar-group :lists="rewardLog" :dsof="true" :badge="true" badgeBgColor="white" txNum="7"
							badgeColor="#333" :rewardTotal="rewardTotal"></tn-avatar-group>
					</view>
					<view class="reward-log-btn" @tap="goReward(cid)">
						<text class="cuIcon-more"></text>
					</view>

				</view>
				<view class="tags text-center bg-white" v-if="tagList.length>0">

					<text class="text-shojo margin-xs" v-for="(item,index) in tagList"
						@tap='toTagsContents("#"+item.name+"#",item.mid)' :key="index">
						#{{item.name}}#
					</text>
				</view>
				<view class="bg-white flex justify-center tn-padding-top-lg tn-padding-bottom-lg"
					style="border-radius: 0 0 20upx 20upx;">
					<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
						<view class="section-sub cu-btn" style="padding: 0 60upx;" @tap="toCategoryContents(category)">
							<block v-if="category.length>0">
								{{category[0].name}}
							</block>
							<block v-else>
								暂无分类
							</block>
						</view>
					</view>
				</view>
				<view class="ads-banner" style="border-radius:20upx;" v-if="bannerAdsInfo!=null">
					<image :src="bannerAdsInfo.img" mode="widthFix" @tap="goAds(bannerAdsInfo)"></image>
				</view>
				<view class="data-box" style="border-radius: 20upx;">
					<view class="cu-bar bg-white" style="border-radius: 20upx 20upx 0 0;">
						<view class="action data-box-title">
							<text class="cuIcon-titles"></text>全部评论</text>
						</view>
						<view class="comNum-right" v-if="commentsNum>0">
							共<text>{{formatNumber(commentsNum)}}条</text>
						</view>
					</view>
					<view class="no-data" v-if="commentsList.length==0">
						暂时没有评论
					</view>
					<view class="cu-card dynamic no-card info-comment" style="margin-top: 20upx;">
						<block v-for="(item,index) in commentsList" :key="index" v-if="commentsList.length>0">
							<commentItem :item="item" :isContent="true" @coAdd="showCommentsAdd"></commentItem>
						</block>
					</view>

					<view class="load-more" @tap="loadMore" v-if="commentsList.length>0">
						<text>{{moreText}}</text>
					</view>
					<view style="height: 100upx"></view>
				</view>

			</view>
		</view>
		<!--打赏选择-->
		<tn-popup v-model="dsShow" mode="bottom" :zIndex="500" :closeBtn="true" height="25%" :borderRadius="20">
			<view class="grid col-3 padding-sm tn-margin-top-xxl"
				style="display: flex;justify-content: center;margin-top: 40rpx;">
				<view v-for="(item,index) in checkbox" class="padding-xs" :key="index">
					<button class="cu-btn cyan lg block" :class="item.checked?'bg-cyan':'line-cyan'"
						@tap="ChooseCheckbox(index)"> {{item.num}}{{currencyName}}
						<view class="cu-tag sm round" :class="item.checked?'bg-white text-cyan':'bg-cyan'"
							v-if="item.hot">HOT</view>
					</button>
				</view>
				<view style="display: flex;justify-content: center;margin-top: 20rpx;">
					<tn-button :backgroundColor="isDark?'#2c2c2c':'#1cbbb4'" style="padding: 0 60upx;" fontColor="#fff"
						@tap="toReward(),dsShow=false">立即打赏</tn-button>
				</view>
			</view>

		</tn-popup>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
		<view class="cu-modal" :class="modalName=='RadioModal'?'show':''" @tap="hideModal">
			<view class="cu-dialog" @tap.stop="">
				<radio-group class="block" @change="RadioChange">
					<view class="cu-list menu text-left">
						<view class="cu-item" v-for="(item,index) in abcimgList" :key="index">
							<label class="flex justify-between align-center flex-sub">
								<view class="flex-sub">{{item.name}}</view>
								<radio class="round" :class="abcimg==item.value?'checked':''"
									:checked="abcimg==item.value?true:false" :value="item.value"></radio>
							</label>
						</view>
					</view>
				</radio-group>
			</view>
		</view>
		<view class="info-operate-bg" :class="isShare?'show':''" @tap="isShare=false"></view>
		<view class="info-operate" :class="isShare?'show':''">
			<view class="info-operate-main grid col-2">
				<view class="index-sort-box" @tap="goImgShare">
					<view class="index-sort-main">
						<view class="index-sort-i" style="background: rgb(158 158 158 / 20%)">
							<text class="cuIcon-picfill" style="color: rgba(19, 19, 19);"></text>
						</view>
						<view class="index-sort-text">
							分享海报
						</view>
					</view>
				</view>
				<view class="index-sort-box">
					<view class="index-sort-main" @tap="ToShare">
						<view class="index-sort-i" style="background: rgb(158 158 158 / 20%)">
							<text class="tn-icon-share-triangle" style="color: rgba(19, 19, 19);"></text>
						</view>
						<view class="index-sort-text">
							分享到其他应用
						</view>
					</view>
				</view>
			</view>


		</view>
		<!-- #ifdef APP-PLUS || H5 -->
		
		<u-popup v-model="commentsAdd" mode="bottom" height="64%" z-index="999" border-radius="40">
			<view>
				<form>
					<view class="cu-form-group">
						<textarea maxlength="5000" :adjust-position="false" :placeholder="coTitle" v-model="pltext"
							placeholder-class="textarea-placeholder"></textarea>
					</view>
					<view class="pl-tool-box">
						<view class="pl-tool-box-2">
							<view class="pl-tool">
								<text class="tn-icon-image" @tap="upimgf()"></text>
							</view>
						</view>
						<view>
							<tn-button @tap="commentsadd" blockTime class="pl-btn" height="54rpx" padding="0 22rpx" :plain="true"
								:border="true" shape="round" :backgroundColor="isDark?'#2c2c2c':'none'" fontColor="#525252">
								发送
							</tn-button>
						</view>
						
					</view>
					<view style="padding: 10rpx 30rpx;border-top: 1px solid #eee;">
						<block v-if="upimg">
						 <u-upload
						      ref="uUpload"
							  del-bg-color="#00000080"
							   :show-progress="true"
						      :action="uploadUrl" 
						      :form-data="{token: token}" 
						      :max-count="9" 
						      :auto-upload="true" 
						      :before-upload="beforeUpload" 
						      @on-success="handleSuccess"
							  @on-remove="handleRemove"
							  @on-uploaded="alluploaded"
							  @on-choose-complete="choosecomplet"
						    />
						</block>
					</view>
				</form>
				<!-- 添加支撑元素 -->
				<view :style="{'background-color':isDark?'#2c2c2c':'#f6f6f6', 'height':'900rpx'}"></view>
			</view>
		</u-popup>
		<!-- #endif -->
		<view class="info-footer grid col-2" :style="{'padding-bottom': paddingBottomHeight + 'upx'}">
			<view class="info-footer-input" :style="shareof!=1&&dsof!=1?'width: 55%;':'width: 50%;'">
				<!-- #ifdef APP-PLUS || H5 -->
				<view class="info-input-box" @tap="showCommentsAdd('pl')">
					善语结善缘, 恶言伤人心
				</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="info-input-box" @tap="commentsAddMp(title,0,0,uid)">
					善语结善缘, 恶言伤人心
				</view>
				<!-- #endif -->
			</view>
			<view class="info-footer-btn" :style="shareof!=1&&dsof!=1?'width: 45%;':'width: 50%;'"
				style="display: flex;justify-content: space-around;">
				<span class="user-rz" v-if="dsof!=1||shareof!=1"><text class="tn-icon-eye"></text><text class="foot-num"
						v-if="views>0">{{formatNumber(views)}}</text></span>
				<!-- #ifdef APP-PLUS || H5 -->
				<span class="user-rz"><text class="tn-icon-comment" @tap="showCommentsAdd('pl')"></text><text
						class="foot-num" v-if="commentsNum>0">{{formatNumber(commentsNum)}}</text></span>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<span class="user-rz"><text class="tn-icon-comment" @tap="commentsAddMp(title,0,0,uid)"></text><text
						class="foot-num" v-if="commentsNum>0">{{formatNumber(commentsNum)}}</text></span>
				<!-- #endif -->
				
						
						
				<span class="user-rz" v-if="isLikes==0"><text class="tn-icon-praise" @tap="toLikes"></text><text
						class="foot-num" v-if="likes>0">{{formatNumber(likes)}}</text></span>
				<span class="user-rz" v-else><text class="tn-icon-praise-fill text-blue" @tap="toLikes"></text><text
						class="foot-num" v-if="likes>0">{{formatNumber(likes)}}</text></span>
				<text class="tn-icon-star" @tap="toMark" v-if="isMark==0"></text>
				<text class="tn-icon-star-fill text-orange" @tap="rmMark" v-else></text>
				<!-- <text class="cuIcon-recharge"  @tap="toReward"></text> -->
				<text class="tn-icon-money" v-if="dsof==1" @tap="dsShow=true"></text>
				<text class="tn-icon-share-circle" v-if="shareof==1" @tap="isShare=!isShare"></text>
			</view>
		</view>
		<view class="cu-modal" style="z-index: 1999;" :class="modalName=='kaptcha'?'show':''">
			<view class="cu-dialog kaptcha-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">操作验证</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>
				<view class="kaptcha-form">
					<view class="kaptcha-image">
						<image :src="kaptchaUrl" mode="widthFix" @tap="reloadCode()"></image>
					</view>
					<view class="kaptcha-input">
						<input name="input" v-model="verifyCode" placeholder="请输入验证码"></input>
						<view class="cu-btn bg-blue" @tap="commentsadd">确定</view>
					</view>
				</view>
			</view>
		</view>
		<template v-if="isImgShare">
			<Share :name="imgShare.name" :title="imgShare.title" :intro="imgShare.intro" :time="imgShare.time"
				:href="imgShare.href" :imgUrl="imgShare.imgUrl" :webName="imgShare.webName"
				@closeImgShare="closeImgShare" />
		</template>
	</view>
</template>
<script>
	import mpHtml from '@/components/mp-html/mp-html'
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	// #ifdef APP-PLUS
	import owo from '../../static/app-plus/owo/OwO.js'
	// #endif
	// #ifdef H5
	import owo from '../../static/h5/owo/OwO.js'
	// #endif
	// #ifdef MP
	var owo = [];
	// #endif
	import darkModeMixin from '@/utils/darkModeMixin.js'
	import medalItem from '../components/medalItem.vue'
	import avatarItem from '../components/avatarItem.vue'
	export default {
		mixins: [darkModeMixin],
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,
				paddingBottomHeight: 0, //苹果X以上手机底部适配高度
				cid: 0,
				uid: 0,
				title: "",
				html: "",
				commentsNum: 0,
				uploaded: false,
				views: 0,
				isImgShare: false,
				imgShare: {
					name: "",
					title: "",
					intro: "",
					time: "",
					imgUrl: "",
					href: "",
					webName: "",
				},
				category: [],
				created: '',
				abcimg: '',
				abcimgList: [{
						name: "单图",
						value: "able"
					},
					{
						name: "三图",
						value: "mable"
					},
					{
						name: "大图",
						value: "bable"
					},
				],
				markdown: -1,
				markdownData: {},
				userInfo: {},
				slug: "",
				tagList: [],
				commentsList: [],
				shopIsMd: -1,
				shareof: 0,
				moreText: "加载更多",
				page: 1,
				isVip: 0,
				isLoad: 0,

				isLoading: 0,

				isMark: 0,
				logid: -1,

				token: "",
				rzImg: this.$API.SPRz(),
				lvImg: this.$API.SPLv(),
				likes: 0,
				isLikes: 0,

				type: "post",

				shopList: [],
				shopID: -1,
				owo: owo,
				owoList: [],

				isCommnet: 0,
				allowDelete: 0,
				modalName: null,
				actStyle: 0,
				checkbox: [{
					value: 0,
					name: '5金币',
					num: 5,
					checked: false,
					hot: false,
				}, {
					value: 1,
					name: '10金币',
					num: 10,
					checked: false,
					hot: false,
				}, {
					value: 2,
					name: '30金币',
					num: 30,
					checked: false,
					hot: false,
				}, {
					value: 3,
					name: '50金币',
					num: 50,
					checked: false,
					hot: false,
				}, {
					value: 4,
					name: '100金币',
					num: 100,
					checked: false,
					hot: false,
				}, {
					value: 5,
					name: '200金币',
					num: 200,
					checked: false,
					hot: false,
				}],
				ads: "",
				
				userlvStyle: "",
				vipDiscount: 0,
				vipPrice: 0,
				scale: 0,
				vipImg: this.$API.SPvip(),
				isBuy: 0,
				shopValue: "",
				show: false,
				bannerAds: [],
				bannerAdsInfo: null,
				dsShow: false,
				images: [],
				identifyCompany: 0,
				identifyConsumer: 0,
				Rz: false,
				group: "",
				dsstyle: 1,
				dsof: 0,
				authorId: 0,
				isFollow: 0,
				lvrz: 0,
				currencyName: "",
				isvip: 0,
				isShare: false,

				rewardLog: [],
				rewardTotal: 0,
				chooesed: false,
				//评论
				commentsAdd: false,
				submitStatus: false,
				coid: 0,
				kaptchaUrl: "",
				verifyCode: "",
				verifyLevel: 0,
				pltext: "",
				coTitle: "",
				pic: "",
				upimg:false,
				uploadUrl: this.$API.upload(),
				// 头像框相关
				frameUrl: null, // 用户头像框URL
				xqy_avatarframe: false, // 头像框插件状态
				// 勋章相关
				userMedals: [], // 用户佩戴的勋章

			}
		},
		components: {
			mpHtml,
			medalItem,
			avatarItem
		},
		onReachBottom() {
			//触底后执行的方法，比如无限加载之类的
			var that = this;
			that.loadMore();
		},
		// #ifdef MP
		onShareAppMessage(res) {
			var that = this;
			var appname = that.$API.GetAppName();
			if (res.from === 'button') {
				// 来自页面内分享按钮
			}
			if (res.from === 'menu') {
				// 来自页面内分享按钮
			}
			var data = {
				title: that.title + ' - ' + appname,
				path: '/page/contents/info?cid=' + that.cid
			}
			if (that.images.lenght > 0) {
				data.imageUrl = that.images[0];
			}

		},
		onShareTimeline() {
			var that = this;
			var appname = that.$API.GetAppName();

			var data = {
				title: that.title + ' - ' + appname,
				path: '/page/contents/info?cid=' + that.cid
			}
			if (that.images.lenght > 0) {
				data.imageUrl = that.images[0];
			}

			return data;
		},
		// #endif
		onShow() {
			var that = this;
			if (localStorage.getItem('userinfo')) {

				var userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.uid = userInfo.uid;
				that.group = userInfo.group;
				that.isvip = userInfo.isvip;
			}
			// #ifdef MP-BAIDU
			//预留百度小程序TDK

			// #endif
			if (that.isvip == 0) {
				that.getAdsCache();
			}
			// #ifdef APP-PLUS


			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			that.isLoad = 0;
			that.page = 1;

			if (that.cid != 0) {
				that.getInfo(that.cid);

				// if (localStorage.getItem('isReplyCid' + that.cid)) {
				// 	that.isCommnet = 1;
				// 	that.getCommentsList(false, that.cid);
				// }
			}

			if (localStorage.getItem('token')) {
				that.token = localStorage.getItem('token');
				that.toIsMark();
			}
			//that.allCache();
			that.getVipInfo();

		},
		onPullDownRefresh() {
			var that = this;
			that.isLoad = 0;
			that.page = 1;
			var timer = setTimeout(function() {
				that.getInfo(that.cid);
				// #ifdef H5 || APP-PLUS
				that.getCommentsList(false, that.cid);
				// #endif
			}, 1000)
		},
		onLoad(res) {
			var that = this;
			uni.getSystemInfo({
				success: function(res) {
					let model = ['X', 'XR', 'XS', '11', '12', '13', '14', '15'];
					console.log("当前设备型号：" + res.model)
					model.forEach(item => {

						if (res.model.indexOf(item) != -1 && res.model.indexOf('iPhone') != -1) {
							that.paddingBottomHeight = 40;
						}
					})
				}
			});
			that.kaptchaUrl = that.$API.getKaptcha();
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			if (res.cid && res.title) {

				that.cid = res.cid;
				that.title = res.title;
			}

			// #ifdef APP-PLUS || H5
			var owo = that.owo.data;
			var owoList = [];
			for (var i in owo) {
				owoList = owoList.concat(owo[i].container);
			}
			that.owoList = owoList;
			// #endif

			if (localStorage.getItem('likeDate_' + that.cid)) {
				var data = localStorage.getItem('likeDate_' + that.cid);
				var cur_date = new Date().getTime();
				var c = Number(cur_date) - Number(data);
				if (c >= 86400000) {
					that.isLikes = 0;
					localStorage.removeItem('likeDate_' + that.cid)
				} else {
					that.isLikes = 1;
				}
			}

			that.allCache();
			that.getInfo(that.cid);
			that.userStatus();
			that.getRewardLog(that.cid);

			that.getShopList();
			that.getCommentsList(false, that.cid);
			that.getSet();


			var ctx = this.$refs.article;
		},
		mounted() {
			this.getopset()
			this.contentConfig()
			
			// 检查头像框插件是否启用
			var cachedPlugins = localStorage.getItem('getPlugins');
			if (cachedPlugins) {
				const pluginList = JSON.parse(cachedPlugins);
				// 检查插件是否存在于插件列表中
				this.xqy_avatarframe = pluginList.includes('xqy_avatar_frame');
			}
		},
		methods: {
			// 加载用户头像框
			loadUserFrame(uid) {
				if (!uid || !this.xqy_avatarframe) return;
				
				const that = this;
				that.$Net.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": uid
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame) {
								that.frameUrl = wearingFrame.frame_url;
							}
						}
					}
				});
			},
			
			// 头像框加载错误处理
			onFrameLoadError() {
				console.error('头像框加载失败');
				this.frameUrl = null;
			},
			
			// 勋章加载完成回调
			onMedalLoaded(medals) {
				this.userMedals = medals;
			},
			upimgf(){
				var that = this;
				that.upimg = true;
			},
			beforeUpload(index, list) {
			  return true; 
			},
			handleRemove(index) {
			  let urls = this.pic.split('||');
			  if (index >= 0 && index < urls.length) {
			    urls.splice(index, 1);
			    
			    this.pic = urls.join('||');
			
			  } else {
			  }
			},
			handleSuccess(data, index, lists) {
			  if (data.code === 1) {
				const url = data.data.url; 
				this.pic += (this.pic ? '||' : '') + url;
			  } else {
				  uni.showToast({
				  	title: data.msg ,
				  	icon: 'none',
				  	duration: 1000,
				  });
				  console.log(data.msg);
			  }
			},
			alluploaded(lists, name){
				setTimeout(function() {
					uni.hideLoading();
				}, 500);
				uni.showToast({
					title: '上传完成' ,
					icon: 'success',
					duration: 1000,
				});
				this.uploaded = true
			},
			choosecomplet(lists, name){
				
				this.uploaded = false
				this.chooesed = true
			},
			commentsadd() {
				var that = this;
				if (that.submitStatus) {
					return false;
				}
				that.submitStatus = true;
				if (that.token == "") {
					uni.showToast({
						title: "请先登录",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					var timer = setTimeout(function() {
						uni.navigateTo({
							url: '/pages/user/login'
						});
						clearTimeout('timer')
					}, 1000)
					return false
				}
			
				var data = {
					"cid": that.cid,
					"parent": that.coid,
			
				}
				if(!that.uploaded&&that.chooesed){
					uni.showToast({
						title: '请等待图片上传完成',
						icon: 'none',
						duration: 1000,
					});
					that.submitStatus = false;
					return false
				}
				if (that.verifyLevel > 1) {
					if (that.verifyCode == "") {
						that.modalName = 'kaptcha'
						return false
					}
				}
				
				if (that.pic === '' && that.pltext.length < 4) {
					uni.showToast({
						title: '文字评论不少于4字',
						icon: 'none',
						duration: 1000,
					});
					that.submitStatus = false;
					that.modalName = null;
					that.verifyCode = "";
					return false
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
			
					url: that.$API.setComments(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"token": that.token,
						"text": that.pltext,
						"pic": that.pic,
						'verifyCode': that.verifyCode
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.submitStatus = false;
						that.modalName = null;
						that.verifyCode = "";
						that.pltext = "";
						that.chooesed = false;
						that.uploaded = true;
						that.upimg = false;
						that.pic = "";
						that.coid = 0;
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
			
						if (res.data.code == 1) {
							// uni.request({
							// 	url: that.$API.SPpinglun(),
							// 	method: 'GET',
							// 	data: {
							// 		uid: that.uid,
							// 	},
							// 	dataType: "json",
							// 	success(res) {},
							// 	fail() {
							// 		setTimeout(function() {
							// 			uni.hideLoading();
							// 		}, 500);
							// 		uni.showToast({
							// 			title: "网络不太好哦",
							// 			icon: 'none'
							// 		})
							// 	}
							// })
							that.isCommnet = 1;
							that.getCommentsList(false, that.cid);
							that.commentsAdd = false;
						}
					},
					fail: function(res) {
						that.submitStatus = false;
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh()
					}
				})
			},
			showCommentsAdd(type, author, coid){
				var that = this;
				if (that.token == "") {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				} 
				if(type=='pl'){
					that.coTitle = "输入评论内容"
					that.commentsAdd = true;
				}
				if(type=='hf'){
					that.commentsAdd = true;
					that.coTitle = "回复：@"+ author
					that.coid = coid
				}
				
			},
			closeImgShare() {
				var that = this;
				that.isImgShare = false;
			},
			subIntroText(text, num) {
				var that = this;
				// 检查 text 是否为 undefined 或 null，若是则赋值为空字符串
				if (typeof text !== 'string') {
					text = '';
				}

				// 去除 HTML 标签
				text = text.replace(/<\/?[^>]+(>|$)/g, "");

				// 替换特殊字符
				text = that.replaceSpecialChar(text);

				// 截断字符串并添加省略号
				if (text.length > num) {
					return text.substring(0, num) + "……";
				} else {
					return text;
				}
			},
			reloadCode() {
				var that = this;
				var kaptchaUrl = that.$API.getKaptcha();
				var num = Math.ceil(Math.random() * 10);
				kaptchaUrl += "?" + num;
				that.kaptchaUrl = kaptchaUrl;
			},
			goImgShare() {
				var that = this;
				var linkRule = that.$API.GetLinkRule();


				var url = linkRule.replace("{cid}", that.cid);
				if (linkRule.indexOf("{category}") != -1) {
					var category = that.category[0].slug;
					url = url.replace("{category}", category);
				}
				//console.log(url);
				if (that.type != "post") {
					var pageRule = that.$API.GetPageRule();
					url = pageRule.replace("{slug}", that.slug);
				}
				that.imgShare.href = url;
				that.imgShare.title = that.title;

				var name = that.userInfo.name;
				if (that.userInfo.screenName) {
					name = that.userInfo.screenName;
				}
				that.imgShare.name = name;

				if (that.images.length > 0) {
					that.imgShare.imgUrl = that.images[0];
				}
				that.imgShare.time = that.formatDate(that.created);
				that.imgShare.webName = that.$API.GetAppName();
				that.imgShare.intro = that.subIntroText(that.html);
				that.isShare = false;
				that.isImgShare = true;

			},
			getopset() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.currencyName = res.data.assetsname;
						that.dsstyle = res.data.dsstyle;
						that.dsof = res.data.dsof;
						that.shareof = res.data.shareof;

					},
					fail(error) {
						console.log(error);
					}

				})
			},
			getAdsCache() {
				var that = this;
				if (localStorage.getItem('bannerAds')) {
					that.bannerAds = JSON.parse(localStorage.getItem('bannerAds'));

					var num = that.bannerAds.length;
					if (num > 0) {
						var rand = Math.floor(Math.random() * num);
						that.bannerAdsInfo = that.bannerAds[rand];
					}
				}
			},
			backHome() {
				uni.redirectTo({
					url: '/pages/home/<USER>'
				});
			},
			back() {

				const pages = getCurrentPages()
				if (pages.length === 1) {
					uni.redirectTo({
						url: '/pages/home/<USER>'
					});
				} else {
					uni.navigateBack({
						delta: 1
					});
				}
			},
			
			
			allCache() {
				var that = this;
				var cid = that.cid;
				if (localStorage.getItem('postInfo_' + cid)) {
					var postInfo = JSON.parse(localStorage.getItem('postInfo_' + cid));
					that.category = postInfo.category;
					that.created = postInfo.created;
					that.views = postInfo.views;
					that.lvrz = postInfo.lvrz;
					that.commentsNum = postInfo.commentsNum;
					that.images = postInfo.images;
					that.markdown = postInfo.markdown;
					if (postInfo.markdown == 1) {
						that.html = that.markHtml(postInfo.text);
					} else {
						that.html = that.quillHtml(postInfo.text);
					}

					that.tagList = postInfo.tag;
					that.slug = postInfo.slug;
					that.authorId = postInfo.authorId
					that.getUserInfo(postInfo.authorId);
					that.getIsFollow(postInfo.authorId);
				}
				if (localStorage.getItem('commentsList_' + cid)) {
					that.commentsList = JSON.parse(localStorage.getItem('commentsList_' + cid));
				}

			},
			getSet() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.actStyle = res.data.actStyle;
					},
					fail(error) {}
				})
			},
			markExpand(text) {
				var that = this;
				//视频
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|type=video"[^>]*?>/g,
					(match, src, poster) => {
						return `<div style="border-radius:10px"><video src="${src}" poster="${poster}" object-fit width="100%" style="border-radius:10px" /></div>`;
					}
				);
				//超链接
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|text=([^"]+)\|type=url"[^>]*?>/g,
					(match, src, poster, text) => {
						return `<div><a href="${src}">${text}</a></div>`;
					}
				);
				//音乐
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|name=([^"]+)\|author=([^"]+)\|type=audio"[^>]*?>/g,
					(match, src, poster, name, author) => {
						return `<div><audio src="${src}" poster="${poster}" name="${name}" author="${author}" loop width="100%"></audio></div>`;
					}
				);
				//附件
				text = text.replace(
					/<img[^>]*?alt="src=([^"]+)\|poster=([^"]+)\|pw=([^"]+)\|name=([^"]+)\|type=file"[^>]*?>/g,
					(match, src, poster, pw, name) => {
						var tqm = ''
						if (pw == '无') {
							tqm = ''
						} else {
							tqm = '提取码：' + pw
						}
						return `
						<div style='background: #f0f0f0;width:100%;padding:15px 15px;color:#666666;border:solid 1px black;box-sizing: border-box;border-radius: 20px;word-break:break-all;'/>
							<div style="display: flex;justify-content: space-between;align-items: center;">
								<div>
									<div style="font-size: 16px;font-weight: bold;color: black;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">${name}</div>
									<div style="font-size: 15px;">${tqm}</div>
								</div>
								<a href='${src}' style="color:#000000">
								<div>
									<span style='font-size:30px' class='tn-icon-download-simple' />
								</div>
								</a>
							</div>
						</div>
						`;
					}
				);
				//评论可见
				if (that.isCommnet == 1) {
					text = text.replace(
						/\[hide\]([\s\S]*?)\[\/hide\]/g, // /<p[^>]*>\[hide\]<\/p>([\s\S]*?)<p[^>]*>\[\/hide\]<\/p>/g,
						(match, content) => {
							return `
							<div style='width:100%;padding:15px 15px;color:#666666;border:solid 1px #3cc9a4;box-sizing: border-box;border-radius: 20px;word-break:break-all;'>
								<p>${content}
							</div>
							`;
						}
					);
				} else {
					text = text.replace(/\[hide(([\s\S])*?)\[\/hide\]/g,
						"<div style='width:100%;padding:15px 15px;background:#cbffea;color:#3cc9a4;border:solid 1px #3cc9a4;box-sizing: border-box;border-radius: 20px;text-align: center;'>该内容评论后显示！</div>"
					);
				}

				if (that.isVip > 0) {
					text = text.replace(
						/\[vip\]([\s\S]*?)\[\/vip\]/g, // /<p[^>]*>\[vip\]<\/p>([\s\S]*?)<p[^>]*>\[\/vip\]<\/p>/g,
						(match, content) => {
							return `
					        <div style='width:100%;padding:15px 15px;color:#666666;border:solid 1px #fb7299;box-sizing: border-box;border-radius: 20px;word-break:break-all;'>
					            <p>${content}
					        </div>
					        `;
						}
					);
				} else {
					text = text.replace(
						/\[vip\]([\s\S]*?)\[\/vip\]/g,
						"<div style='width:100%;padding:15px 15px;background:#ffecee;color:#fb7299;border:solid 1px #fb7299;box-sizing: border-box;border-radius: 20px;text-align: center;'>该内容仅会员可查看！</div>"
					);
				}
				//表情包
				// #ifdef APP-PLUS || H5
				var owoList = that.owoList;
				for (var i in owoList) {

					if (that.replaceSpecialChar(text).indexOf(owoList[i].data) != -1) {
						text = that.replaceAll(that.replaceSpecialChar(text), owoList[i].data, "<img src='" + owoList[i]
							.icon + "' class='tImg' />")

					}
				}
				// #endif
				//链接自动识别
				text = text.replace(/(?![^<>]*>)(src=['"]|poster=['"]|https?:\/\/[^\s'"<>]+)/gi, function(match, group1) {
					if (group1 && !/(src=['"]|poster=['"])/i.test(group1)) {
						var lastChar = match.charAt(match.length - 1);
						if (!/['"<>]/.test(match.charAt(4)) && !/[^a-zA-Z0-9\/]/.test(lastChar)) {
							return '<a href="' + match + '" target="_blank">' + match + '</a>';
						} else {
							return match;
						}
					} else {
						return match;
					}
				});

				return text;
			},
			userStatus() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				that.$Net.request({

					url: that.$API.userStatus(),
					data: {
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 0) {

							if (that.userInfo != null) {}
							localStorage.removeItem('userinfo');
							localStorage.removeItem('token');
						} else {
							that.isVip = res.data.data.isvip;
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			RadioChange(e) {
				var abcimg = e.detail.value;
				var cid = this.curCid;
				this.setFields(cid, abcimg);
			},
			
			setFields(id, type) {
				var that = this;
				that.curCid = id;
				if (type) {
					that.abcimg = type;
				}
				if (that.modalName == null) {
					that.modalName = "RadioModal";
					return false;
				}

				var token;
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"cid": id,
					"name": "abcimg",
					"strvalue": that.abcimg,
					"token": token
				}
				that.modalName = null;
				uni.showLoading({
					title: "加载中"
				});

				that.$Net.request({
					url: that.$API.setFields(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.modalName = null;
						that.abcimg = "able";
						that.curCid = "";
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							that.page = 1;
							that.moreText = "加载更多";
							that.isLoad = 0;
							setTimeout(function() {
								that.getContentsList();
							}, 1000)
						}

					},
					fail: function(res) {
						that.modalName = null;
						that.abcimg = "able";
						that.curCid = "";
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			markHtml(text) {
				var that = this;
				//下面奇怪的代码是为了解决可执行代码区域问题
				text = that.replaceAll(text, "@!!!", "@@@@");

				text = that.replaceAll(text, "!!!", "");
				text = that.replaceAll(text, "@@@@", "@!!!");
				text = that.markExpand(text);
				//text = text.replace(/(?<!\r)\n(?!\r)/g, "\n\n");
				//兼容垃圾的Safari浏览器
				text = text.replace(/([^\r])\n([^\r])/g, "$1\n\n$2");
				text = that.replaceAll(text, "||rn||", "\n\n");
				return text;

			},

			markCommentHtml(text) {
				var that = this;
				// #ifdef APP-PLUS || H5
				var owoList = that.owoList;
				for (var i in owoList) {

					if (that.replaceSpecialChar(text).indexOf(owoList[i].data) != -1) {
						text = that.replaceAll(that.replaceSpecialChar(text), owoList[i].data, "<img src='/" + owoList[i]
							.icon + "' class='tImg' />")

					}
				}
				// #endif
				return text;
			},
			getUserLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			getUserLvStyle(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle = "color:#fff;background-color: " + rankStyle[i];
				return userlvStyle;
			},
			replaceAll(string, search, replace) {
				return string.split(search).join(replace);
			},
			getLocal(local) {
				var that = this;
				if (local && local != '') {
					var local_arr = local.split("|");
					if (!local_arr[3] || local_arr[3] == 0) {
						return local_arr[2];
					} else {
						return local_arr[3];
					}

				} else {
					return "未知"
				}

			},

			contentConfig() {
				var that = this;
				that.$Net.request({

					url: that.$API.contentConfig(),
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.allowDelete = res.data.data.allowDelete;
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			
			toAdminDelete(id) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"key": id,
					"token": token
				}
				uni.showModal({
					title: '确定要删除该文章吗',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.contentsDelete(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 1) {
										uni.request({
											url: that.$API.SPwzpostremove(),
											method: 'GET',
											data: {
												cid: id,
											},
											dataType: "json",
											success(res) {},
											fail() {
												setTimeout(function() {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}


										})
										that.back();
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toDelete(id) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"key": id,
					"token": token
				}
				uni.showModal({
					title: '确定要删除该文章吗',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.contentsDelete(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 1) {
										uni.request({
											url: that.$API.SPwzpostmyremove(),
											method: 'GET',
											data: {
												key: id,
											},
											dataType: "json",
											success(res) {},
											fail() {
												setTimeout(function() {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}


										})
										that.back();
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toUserInfo(data) {
				var that = this;
				var name = data.name;
				var title = data.name + "的信息";
				if (data.screenName) {
					title = data.screenName + " 的信息";
					name = data.screenName
				}
				var id = data.uid;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
			toTagsContents(title, id) {
				var that = this;
				var type = "meta";
				uni.navigateTo({
					url: '/pages/contents/contentlist?title=' + title + "&type=" + type + "&id=" + id
				});
			},
			toCategoryContents(data) {
				var that = this;
				var title = data[0].name;
				var id = data[0].mid;
				var type = "meta";
				uni.navigateTo({
					url: '/pages/contents/contentlist?title=' + title + "&type=" + type + "&id=" + id
				});
			},
			loadMore() {
				var that = this;
				that.moreText = "正在加载中...";
				if (that.isLoad == 0) {
					that.getCommentsList(true, that.cid);
				}

			},
			getVipInfo() {
				var that = this;
				that.$Net.request({
					url: that.$API.getVipInfo(),
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.vipDiscount = res.data.data.vipDiscount;
							that.vipPrice = res.data.data.vipPrice;
							that.scale = res.data.data.scale;
						}
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					},
					fail: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
					}
				})
			},
			quillHtml(text) {
				var that = this;
				text = that.replaceAll(text, "hljs", "hl");
				text = that.replaceAll(text, "ql-syntax", "hl-pre");

				text = that.markExpand(text);
				return text;
			},
			getInfo(cid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"key": that.cid,
					"isMd": 0,
					"token": token
				}

				that.$Net.request({
					url: that.$API.getContentsInfo(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						if (res.data.title) {
							that.title = res.data.title;
							that.category = res.data.category;
							that.created = res.data.created;
							that.views = res.data.views;
							that.commentsNum = res.data.commentsNum;
							that.images = res.data.images;
							that.markdown = res.data.markdown;
							var html = res.data.text;
							if (res.data.markdown == 1) {
								html = that.markHtml(res.data.text);
							} else {
								html = that.quillHtml(res.data.text);
							}
							that.html = html;
							that.tagList = res.data.tag;
							that.slug = res.data.slug;
							that.type = res.data.type;
							that.likes = res.data.likes;
							that.authorId = res.data.authorId
							that.getUserInfo(res.data.authorId);
							that.getIsFollow(res.data.authorId);
							localStorage.removeItem('postInfo_' + that.cid);
							localStorage.setItem('postInfo_' + that.cid, JSON.stringify(res.data));
							var timer = setTimeout(function() {
								that.allCache();
							}, 200);
							var timer = setTimeout(function() {
								that.isLoading = 1;
								clearTimeout('timer')
							}, 300)

						}
					},
					fail: function(res) {
						uni.stopPullDownRefresh();
					}
				})
			},
			getUserInfo(id) {
				var that = this;
				var data = {
					"key": id,
				}

				that.$Net.request({
					url: that.$API.getUserInfo(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.userInfo = res.data.data;
							that.userInfo.style = "background-image:url(" + res.data.data.avatar + ");"
							
							if (that.xqy_avatarframe && that.authorId) {
								that.loadUserFrame(that.authorId);
							}
						} else {
							that.userInfo.name = "用户已注销"
						}
					},
					fail: function(res) {}
				});
			},
			toJb(title) {
				var that = this;

				uni.navigateTo({
					url: '/pages/user/help?type=text&title=' + title
				});
			},
			getCommentsList(isPage, id) {
				var that = this;
				var data = {
					"cid": id,
					"status": "approved"
				}
				var page = that.page;
				if (isPage) {
					page++;
				}
				that.$Net.request({
					url: that.$API.getCommentsList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 4,
						"page": page,
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						that.isLoad = 0;
						if (res.data.code == 1) {
							var list = res.data.data;
							if (list.length > 0) {
								var commentsList = [];
								for (var i in list) {
									var arr = list[i];
									arr.style = "background-image:url(" + list[i].avatar + ");"
									commentsList.push(arr);
								}
								if (isPage) {
									that.page++;
									that.commentsList = that.commentsList.concat(commentsList);
								} else {
									that.commentsList = commentsList;
								}
								localStorage.setItem('commentsList_' + that.cid, JSON.stringify(that
									.commentsList));

							} else {
								that.moreText = "没有更多评论了";
								if (that.page == 1 && !isPage) {
									localStorage.removeItem('commentsList_' + that.cid);
									that.commentsList = [];
								}

							}

						}

					},
					fail: function(res) {
						uni.stopPullDownRefresh();
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						that.isLoad = 0;
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)

						that.moreText = "加载更多";
					}
				})
			},
			//#ifdef MP
			commentsAddMp(title, coid, reply, uid) {
				var that = this;

				if (!localStorage.getItem('userinfo')) {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				} else {
					var cid = that.cid;
					uni.navigateTo({
						url: '/pages/contents/commentsadd?cid=' + cid + "&coid=" + coid + "&title=" + title +
							"&isreply=" + reply + "&uid=" + uid
					});
				}

			},
			//#endif
			toReward() {
				var that = this;
				var rewardList = that.checkbox;
				var num = 10;
				for (var i in rewardList) {
					if (rewardList[i].checked) {
						num = rewardList[i].num;
					}
				}
				var data = {
					"type": "reward",
					"cid": that.cid,
					"num": num,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.addLog(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.hideModal();
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							uni.request({
								url: that.$API.SPdashang(),
								method: 'GET',
								data: {
									uid: that.uid,
								},
								dataType: "json",
								success(res) {},
								fail() {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络不太好哦",
										icon: 'none'
									})
								}


							})
							uni.showToast({
								title: "成功打赏 " + num + " " + that.currencyName,
								icon: 'none'
							})
						}

					},
					fail: function(res) {
						that.hideModal();
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toLikes() {
				var that = this;
				var data = {
					"type": "likes",
					"cid": that.cid,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.addLog(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})

						if (res.data.code == 1) {
							var timestamp = new Date().getTime();
							that.isLikes = 1;
							localStorage.setItem('likeDate_' + that.cid, timestamp);
							that.likes++;
							//that.getInfo(that.cid);
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			showModal(e) {
				this.modalName = e.currentTarget.dataset.target
			},
			hideModal(e) {
				this.modalName = null
			},
			ChooseCheckbox(j) {
				let items = this.checkbox;
				for (let i = 0, lenI = items.length; i < lenI; ++i) {
					this.checkbox[i].checked = false;
				}
				this.checkbox[j].checked = !this.checkbox[j].checked;
			},
			toIsMark() {
				var that = this;
				that.$Net.request({

					url: that.$API.getIsMark(),
					data: {
						"token": that.token,
						"cid": that.cid
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {

						if (res.data.code == 1) {
							that.isMark = res.data.data.isMark;
							that.logid = res.data.data.logid;
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			toMark() {

				var that = this;
				var data = {
					"type": "mark",
					"cid": that.cid,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.addLog(),
					data: {
						"params": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							that.toIsMark();
							that.isMark = 1;
							//that.toIsMark();
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			rmMark() {
				var that = this;
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.removeLog(),
					data: {
						"key": that.logid,
						"token": that.token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if (res.data.code == 1) {
							that.isMark = 0;
							that.toIsMark();
							//that.toIsMark();
						}

					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 500);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				// 获取年月日时分秒值  slice(-2)过滤掉大于10日期前面的0
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				//second = ("0" + date.getSeconds()).slice(-2);
				// 拼接
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				// 返回
				return result;
			},
			getLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var lv = that.$API.getLever(i);
				return lv;
			},
			ToCopy(text) {
				var that = this;
				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: text,
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: "链接复制成功"
						})
					}
				});
				// #endif
				// #ifdef H5 
				let textarea = document.createElement("textarea");
				textarea.value = text;
				textarea.readOnly = "readOnly";
				document.body.appendChild(textarea);
				textarea.select();
				textarea.setSelectionRange(0, text.length);
				uni.showToast({ //提示
					title: "链接复制成功"
				})
				var result = document.execCommand("copy")
				textarea.remove();
				// #endif
			},
			goAds(data) {
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if (type == 1) {
					plus.runtime.openURL(url);
				}
				if (type == 0) {
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			copyShare() {
				var that = this;
				var linkRule = that.$API.GetLinkRule();

				var appname = that.$API.GetAppName()

				var url = linkRule.replace("{cid}", that.cid);
				if (linkRule.indexOf("{category}") != -1) {
					var category = that.category[0].slug;
					url = url.replace("{category}", category);
				}
				//console.log(url);
				if (that.type != "post") {
					var pageRule = that.$API.GetPageRule();
					url = pageRule.replace("{slug}", that.slug);
				}
				var text = that.title + ' - ' + appname + " | 链接：" + url
				that.ToCopy(text);
				that.isShare = false
			},
			ToShare() {

				var that = this;
				var linkRule = that.$API.GetLinkRule();
				var appname = that.$API.GetAppName()

				var url = linkRule.replace("{cid}", that.cid);
				if (linkRule.indexOf("{category}") != -1) {
					var category = that.category[0].slug;
					url = url.replace("{category}", category);
				}
				//console.log(url);
				if (that.type != "post") {
					var pageRule = that.$API.GetPageRule();
					url = pageRule.replace("{slug}", that.slug);
				}

				// #ifdef APP-PLUS
				uni.shareWithSystem({
					href: url,
					summary: that.title + ' | ' + appname,
					success() {
						// 分享完成，请注意此时不一定是成功分享

					},
					fail() {
						// 分享失败
					}
				});
				// #endif
				// #ifdef H5
				var text = that.title + ' - ' + appname + " | 链接：" + url
				that.ToCopy(text);
				// #endif
				that.isShare = false
			},
			formatNumber(num) {
				return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' :
					num
			},

			getShopList() {
				var that = this;
				var uid = 0;
				if (localStorage.getItem('userinfo')) {

					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					uid = userInfo.uid;

				}
				var data = {
					"cid": that.cid,
				}
				that.$Net.request({
					url: that.$API.shopList(),
					data: {
						"searchParams": JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit": 1,
						"page": 1,
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res));
						if (res.data.code == 1) {
							var list = res.data.data;
							that.shopList = list;
							if (list.length > 0) {
								that.shopID = list[0].id;
								that.isBuyShop(that.shopID, list[0].type);
							}

						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			getIsCommnet() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {

					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;

				}
				if (token == "") {
					return false;
				}
				var data = {
					"key": that.cid,
					"token": token
				}
				that.$Net.request({
					url: that.$API.isCommnet(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						if (res.data.code == 1) {
							that.isCommnet = 1;
							that.getInfo(that.cid);
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})

					}
				})
			},
			shopInfo(data) {
				var that = this;
				var sid = data.id;
				if (data.status != 1) {
					uni.showToast({
						title: "该商品未上架",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/shop/shopinfo?sid=' + sid
				});
			},
			toSearch() {
				var that = this;

				uni.redirectTo({
					url: '/pages/contents/search'
				});
			},
			toAds(url) {
				// #ifdef APP-PLUS
				plus.runtime.openURL(url)
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			replaceSpecialChar(text) {
				if (!text) {
					return false;
				}
				text = text.replace(/&quot;/g, '"');
				text = text.replace(/&amp;/g, '&');
				text = text.replace(/&lt;/g, '<');
				text = text.replace(/&gt;/g, '>');
				text = text.replace(/&nbsp;/g, ' ');
				return text;
			},
			isBuyShop(sid, type) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"sid": sid,
					"token": token
				}
				that.$Net.request({
					url: that.$API.isBuyShop(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res));
						if (res.data.code == 1) {
							that.isBuy = 1;
							if (type == 4) {
								that.toShopValue(sid, type);
							}
						}

					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			shopBuy(sid, type) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none',
						duration: 1000,
						position: 'bottom',
					});
					var timer = setTimeout(function() {
						uni.navigateTo({
							url: '../user/login'
						});
						clearTimeout('timer')
					}, 1000)
					return false
				}
				//因为增加了金币抵扣，所以跳转订单确认
				if (type != 4) {
					uni.navigateTo({
						url: '/pages/shop/orderpay?sid=' + sid
					});
					return false
				}

				var data = {
					"token": token,
					"sid": sid
				}
				uni.showModal({
					title: '确定购买此商品吗?',
					content: ' ',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							that.$Net.request({
								url: that.$API.buyShop(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 1) {

										if (type != 4) {
											//跳转订单页面
											var timer = setTimeout(function() {
												uni.navigateTo({
													url: '/pages/user/order'
												});
												clearTimeout('timer')
											}, 1000)
										} else {
											that.toShopValue(sid, type);
										}

									} else {
										if (res.data.msg == "购买实体商品前，需要先设置收货地址") {
											var timer = setTimeout(function() {
												uni.redirectTo({
													url: '/pages/user/address'
												});
												clearTimeout('timer')
											}, 1000)
										}
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						}
					}
				});

			},
			toShopValue(id, type) {
				var that = this;
				if (type == 1) {
					uni.showToast({
						title: "实体商品请留意快递信息",
						icon: 'none'
					})
				} else if (type == 4) {
					var that = this;
					var token = "";
					if (localStorage.getItem('token')) {
						token = localStorage.getItem('token');
					}
					var data = {
						"key": id,
						"token": token
					}
					that.$Net.request({
						url: that.$API.shopInfo(),
						data: data,
						header: {
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						method: "get",
						dataType: 'json',
						success: function(res) {

							uni.stopPullDownRefresh();
							if (res.data.value) {
								that.shopIsMd = res.data.isMd;
								that.shopValue = that.quillHtml(res.data.value);
							}


							var timer = setTimeout(function() {
								that.isLoading = 1;
								clearTimeout('timer')
							}, 300)
						},
						fail: function(res) {
							uni.stopPullDownRefresh();
							uni.showToast({
								title: "网络开小差了哦",
								icon: 'none'
							})
							var timer = setTimeout(function() {
								that.isLoading = 1;
								clearTimeout('timer')
							}, 300)
						}
					})
				} else {
					uni.navigateTo({
						url: '/pages/shop/shoptext?sid=' + id
					});
				}
			},
			subText(text, num) {
				if (text) {
					if (text.length > num) {
						text = text.substring(0, num);
						return text + "……";
					} else {
						return text;
					}
				} else {
					return "Ta还没有个人介绍哦"
				}
			},
			toUserContents(data) {
				var that = this;
				var name = data.author;
				var title = data.author + "的信息";
				var id = data.authorId;
				var type = "user";
				uni.navigateTo({
					url: '/pages/contents/userinfo?title=' + title + "&name=" + name + "&uid=" + id + "&avatar=" +
						encodeURIComponent(data.avatar)
				});
			},
			toBan(uid) {
				if (!uid) {
					uni.showToast({
						title: "该用户不存在",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/manage/banuser?uid=' + uid
				});
			},
			toDelete(id) {
				var that = this;
				var token = "";

				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					"key": id,
					"token": token
				}
				uni.showModal({
					title: '确定要删除该文章吗',
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});

							that.$Net.request({
								url: that.$API.contentsDelete(),
								data: data,
								header: {
									'Content-Type': 'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: res.data.msg,
										icon: 'none'
									})
									if (res.data.code == 1) {
										uni.request({
											url: that.$API.SPwzpostmyremove(),
											method: 'GET',
											data: {
												key: id,
											},
											dataType: "json",
											success(res) {},
											fail() {
												setTimeout(function() {
													uni.hideLoading();
												}, 1000);
												uni.showToast({
													title: "网络不太好哦",
													icon: 'none'
												})
											}


										})
										that.back();
									}

								},
								fail: function(res) {
									setTimeout(function() {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			getIsFollow(uid) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				var data = {
					token: token,
					touid: uid,
				}
				that.$Net.request({

					url: that.$API.isFollow(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isFollow = res.data.code;
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			follow(type) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				} else {
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				var data = {
					token: token,
					touid: that.authorId,
					type: type,
				}
				that.isFollow = type;
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({

					url: that.$API.follow(),
					data: data,
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						if (res.data.code == 0) {
							if (type == 1) {
								uni.request({
									url: that.$API.SPguanzhu(),
									method: 'GET',
									data: {
										uid: that.uid,
									},
									dataType: "json",
									success(res) {},
									fail() {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络不太好哦",
											icon: 'none'
										})
									}


								})
							} else {
								uni.request({
									url: that.$API.SPquguan(),
									method: 'GET',
									data: {
										uid: that.uid,
									},
									dataType: "json",
									success(res) {},
									fail() {
										setTimeout(function() {
											uni.hideLoading();
										}, 1000);
										uni.showToast({
											title: "网络不太好哦",
											icon: 'none'
										})
									}


								})
							}
						}
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						that.getIsFollow(that.authorId);
					},
					fail: function(res) {
						setTimeout(function() {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						uni.stopPullDownRefresh();
						that.getIsFollow(that.authorId);
					}
				})
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			goPost(cid) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}

				var markdown = that.markdown;
				if (markdown == 1) {
					//MarkDown编辑器
					uni.navigateTo({
						url: '/pages/edit/articlePost?type=edit' + '&cid=' + cid
					});
				} else {
					//富文本编辑器
					uni.navigateTo({
						url: '/pages/edit/articlePost?type=edit' + '&cid=' + cid
					});
				}
			},
			getRewardLog(id) {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;
				}
				that.$Net.request({
					url: that.$API.rewardList(),
					data: {
						"limit": 4,
						"page": 1,
						"id": id,
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						uni.stopPullDownRefresh();
						if (res.data.code == 1) {
							that.rewardLog = res.data.data;
							var list = res.data.data;
							var rewardTotal = 0;
							for (var i in list) {
								rewardTotal = rewardTotal + list[i].num;
							}
							that.rewardTotal = rewardTotal;
						}
					},
					fail: function(res) {
						uni.stopPullDownRefresh();
					}
				})
			},
			goReward(id) {
				var that = this;

				uni.navigateTo({
					url: '/pages/contents/rewardLog?id=' + id
				});
			},
		}
	}
</script>

<style>
	.comNum-right {
		float: right;
		margin: 0 30upx;
		font-size: 11px !important;
		color: #333;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.data-box-title {
		font-size: 14px !important;
	}

	.comPxOn {
		color: #333;
	}

	.comPxOff {
		color: #878787;
	}

	.foot-num {
		position: absolute;
		right: -22upx;
		top: 22upx;
		width: 28upx;
		height: 18upx;
		font-size: 22upx;
		background: #fff;
		line-height: 18upx;
		padding: 2upx 4upx;
		border-radius: 16upx;
	}

	.info-input-box {
		height: 64upx;
		border-radius: 100upx;
		line-height: 64upx;
		margin: 4upx 0;
	}

	.info-footer-btn {
		padding: 0 20upx;
	}

	.info-footer-btn text {
		margin: 0 25upx 0 0;
	}

	.name-vip {
		color: #ff6c3e;
	}

	.user-rz {
		position: relative;
		display: inline-block;
	}

	.user-rz-icon {
		position: absolute;
		right: -4upx;
		bottom: -4upx;
		width: 34upx;
		height: 34upx;
		z-index: 10; /* 确保蓝V图标在最上层 */
	}
	
	/* 头像框样式 */
	.avatar-frame {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 100%;
		height: 100%;
		transform: translate(-50%, -50%) scale(1.15);
		z-index: 1;
		pointer-events: none; /* 允许点击穿透到头像 */
		max-width: initial;
	}


	.tn-margin-top-xxl {
		margin-top: 60upx;
	}

	.center-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.index-sort-i {
		border-radius: 15px;
	}

	.pl-title {
		font-size: 30rpx;
	}

	.pl-tool-box {
		display: flex;
		margin: 0 32rpx 16rpx 32rpx;
		
		align-items: center;
		justify-content: space-between;
	}
	.pl-tool-box-2 {
		display: flex;
		justify-content: flex-start;
	}
	.pl-tool {
		font-size: 48rpx;
		margin-right: 30rpx;
	}

	.pl-btn {
		font-size: 40rpx;
		border-radius: 50px;
		border: 2px solid #525252;
	}
	.cu-form-group {
	    background-color: #ffffff;
	    padding: 2rpx 20rpx;
	}
	textarea {
		border-radius: 40rpx;
		margin: 20rpx 0;
		background-color: #eeeeee;
		padding: 20rpx;
	}
</style>