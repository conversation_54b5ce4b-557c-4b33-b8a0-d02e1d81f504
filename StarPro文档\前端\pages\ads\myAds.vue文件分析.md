# myAds.vue 文件分析

## 概述

`myAds.vue` 页面用于**展示当前登录用户自己发布的广告列表**。它允许用户按状态（已发布、待审核、已到期）筛选广告，并提供了查看广告详情、预计到期时间以及进入编辑页面的入口。

## 文件信息
- **文件路径**：`APP前端部分/pages/ads/myAds.vue.md`
- **主要功能**：查看和管理用户个人发布的广告列表。

## 主要组成部分分析

### 1. 模板 (`<template>`)
   - **导航栏**: 
     - 使用 `cu-bar` 构建自定义导航栏。
     - 标题固定为"我的广告"。
     - 左侧为返回按钮 (`cuIcon-back`)。
   - **状态筛选栏**: 
     - 使用 `grid col-3` 布局创建三个状态切换按钮：已发布、待审核、已到期。
     - 通过 `status` 数据属性和 `:class` 动态绑定 `active` 类来高亮当前选中的状态。
     - 点击按钮触发 `setStatus` 方法切换状态并重新加载列表。
   - **广告列表 (`myAds`)**: 
     - 使用 `v-for` 遍历 `adsList` 数组展示广告项。
     - **无数据提示**: 如果 `adsList` 为空，显示"暂时没有数据"的提示 (`no-data`)。
     - **广告项 (`myAds-box`)**: 
       - 显示广告标题 (`item.name`)。
       - 显示预计到期时间 (`formatDate(item.close)`) 和广告类型 (`getType(item.type)`)。
       - 显示广告缩略图 (`item.img`) 和简介 (`item.intro`)。
       - 整个广告项绑定了 `tap` 事件，触发 `goEdit(item)` 方法。
   - **加载更多 (`load-more`)**: 
     - 当 `adsList` 不为空时显示。
     - 显示加载状态文本 (`moreText`)。
     - 点击触发 `loadMore` 方法加载下一页数据。

### 2. 脚本 (`<script>`)
   - **依赖**: 引入 `localStorage`。
   - **`data` 属性**: 
     - `StatusBar`, `CustomBar`, `NavBar`, `AppStyle`: 同 `adsPost.vue`，用于UI适配和样式。
     - `status`: 当前选中的广告状态 (1:已发布, 0:待审核, 2:已到期)，默认为1。
     - `page`: 当前加载的页码，默认为1。
     - `moreText`: "加载更多"按钮的文本。
     - `isLoad`: 加载状态标志 (0:允许加载, 1:正在加载中)。
     - `token`: 用户登录凭证。
     - `adsList`: 存储从后端获取的广告列表数组。
   - **生命周期函数**: 
     - `onLoad(res)`: 
       - 从 `localStorage` 获取 `token`。
       - 调用 `getAdsList(false)` 加载第一页数据。
     - `onShow()`:
       - 重置 `page` 为 1。
       - 检查 `token` 是否存在，如果存在则调用 `getAdsList(false)` 刷新数据（确保每次进入页面都显示最新列表）。
     - `onReachBottom()`: 触底时，如果不在加载中 (`isLoad == 0`)，调用 `loadMore()` 加载下一页。
     - `onPullDownRefresh()`: (已定义但内容为空，未实现下拉刷新逻辑)。
   - **`methods` 方法**: 
     - `back()`: 返回上一页。
     - `getType(type)`: 根据广告类型ID返回对应的中文名称。
     - `loadMore()`: 设置加载状态文本和标志，调用 `getAdsList(true)` 加载下一页。
     - `formatDate(datetime)`: 将时间戳格式化为 `YYYY-MM-DD HH:mm` 格式。
     - `setStatus(status)`: 
       - 清空 `adsList`。
       - 更新 `status` 状态。
       - 重置 `page` 为 1。
       - 调用 `getAdsList(false)` 加载新状态下的第一页数据。
     - `getAdsList(isPage)`: 
       - 核心数据获取函数。
       - 检查 `token` 是否存在，不存在则提示登录。
       - 从 `localStorage` 获取 `uid`。
       - 构造请求参数，包含 `status`, `uid`, `limit` (固定为8), `page`。使用 `$API.removeObjectEmptyKey` 清理空参数，并通过 `JSON.stringify` 序列化为 `searchParams`。
       - 调用 `$Net.request` (注意这里调用的是 `$Net.request` 而不是 `$API.GetRequest`，可能使用了 `net.js` 中的封装) 发起 GET 请求到 `$API.adsList()` 接口。
       - **成功回调**: 
         - 重置加载状态和文本。
         - 处理返回的列表数据 `res.data.data`。
         - 如果是加载更多 (`isPage`)，则将新数据拼接到 `adsList` 后面 (`concat`)，并增加 `page` 页码。
         - 如果是刷新或切换状态，则直接替换 `adsList`。
         - 根据返回列表长度更新 `moreText` 为"没有更多数据了"或保持"加载更多"。
       - **失败回调**: 重置加载状态。
     - `goEdit(data)`:
       - 弹出确认框提示用户修改广告需要重新审核。
       - 如果用户确认，则跳转到 `adsPost` 页面进行编辑，并传递 `post=edit`, `type` 和 `aid` 参数。

## 总结与注意事项

-   `myAds.vue` 页面清晰地展示了用户发布的广告，并按状态进行了分类。
-   核心功能是调用 `$API.adsList()` 接口，根据用户ID (`uid`) 和状态 (`status`) 拉取广告列表，并支持分页加载。
-   页面使用了 `$Net.request` 进行网络请求，这与 `api.js` 中定义的 `$API.GetRequest` 不同，需要注意这两者之间的区别和使用场景。
-   提供了编辑广告的入口 (`goEdit`)，跳转前有风险提示。
-   UI 风格与 `adsPost.vue` 类似，可能同样依赖 ColorUI 或类似库。
-   时间戳的格式化使用了自定义的 `formatDate` 方法。

## 后续分析建议

-   **API 依赖**: 查看 `$API.adsList()` 接口的后端实现，了解其过滤和分页逻辑。
-   **网络请求封装**: 确认为何此处使用 `$Net.request` 而不是 `$API.GetRequest`，研究两者实现上的差异。
-   **用户身份获取**: 确认从 `localStorage` 获取 `uid` 的逻辑是否可靠。
-   **UI组件库**: 明确项目使用的具体UI组件库。
