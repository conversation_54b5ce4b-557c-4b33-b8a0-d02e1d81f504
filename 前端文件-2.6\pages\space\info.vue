<template>
	<view class="user" :class="[isDark?'dark':'', $store.state.AppStyle]" :style="{'background-color':isDark?'#1c1c1c':'#f6f6f6','min-height':isDark?'100vh':'auto'}">
		<view class="header" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					<block v-if="replyType==0">
						动态详情
					</block>
					<block v-else>
						动态评论详情
					</block>
				</view>
				<!--  #ifdef H5 || APP-PLUS -->
				<view class="action" @tap="toSearch">
					<text class="cuIcon-search"></text>
				</view>
				<!--  #endif -->
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="cu-card dynamic no-card space-info">
			<view class="cu-item info">

				<view class="forum-author" style="margin: 20upx;padding: 0;">
					<view class="forum-list-user" style="margin: 0;padding: 0;" @tap="toUserInfo(spaceInfo.userJson)">
						<view class="forum-avatar tn-margin-bottom-sm user-rz">
							<image class="forum-avatar-image" :src="spaceInfo.userJson && spaceInfo.userJson.avatar ? spaceInfo.userJson.avatar : '../../static/user/default.png'"></image>
							<image v-if="frameUrl" class="avatar-frame" :src="frameUrl" mode="aspectFit" @error="onFrameLoadError"></image>
							<image class="user-rz-icon" width="34upx" height="34upx" :src="rzImg"
								mode="aspectFill" v-if="spaceInfo.lvrz==1"></image>
				
						</view>
						<view class="forum-userinfo">
							<view class="forum-userinfo-name">
								<text :class="spaceInfo.userJson && spaceInfo.userJson.isvip>0?'name-vip':''">{{spaceInfo.userJson && spaceInfo.userJson.name || "用户已注销"}}</text>
								<image v-if="spaceInfo.userJson && spaceInfo.userJson.isvip>0" :src="vipImg"
										style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;"
										mode="widthFix"></image>
								<image v-if="spaceInfo.userJson" :src="lvImg+getLv(spaceInfo.userJson.experience)+'.png'"
										style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 45upx;"
										mode="widthFix"></image>
								<text class="userlv customize"
									style="border: 3upx solid black;color:black;padding: 2upx 10upx;border-radius: 40upx;background-color: transparent;"
									v-if="spaceInfo.userJson && spaceInfo.userJson.customize && spaceInfo.userJson.customize!=''">{{spaceInfo.userJson.customize}}</text>
								<medal-item v-if="spaceInfo.userJson && spaceInfo.userJson.uid" :uid="spaceInfo.userJson.uid" @medal-loaded="onMedalLoaded"></medal-item>
							</view>
							<view class="forum-userinfo-date">
								{{formatDate(spaceInfo.created)}} <text
									class="margin-left-sm margin-right-sm" v-if="$API.localOf() && spaceInfo.userJson">
									{{getLocal(spaceInfo.userJson.local)}}
								</text>
							</view>
							<!-- <view class="cu-btn xs forum-follow" isFollow>
						<text class="cuIcon-add"></text>
						关注
					</view> -->
						</view>
					</view>
					<block v-if="group=='administrator'||group=='editor'">
						<text class="text-blue margin-left-sm" @tap="edit(spaceInfo.id)">编辑</text>
						<text class="text-black margin-left-sm" @tap="toBan(spaceInfo.userJson.uid)">封禁</text>
						<text class="text-red margin-left-sm" @tap="toDelete(spaceInfo.id)">删除</text>
					</block>

				<block v-else>
					<block v-if="spaceInfo.userJson && spaceInfo.userJson.uid!=0 && spaceInfo.userJson.uid==uid">
						<!-- <text class="text-blue margin-left-sm" @tap="edit(spaceInfo.id)">编辑</text> -->
						<text class="text-red margin-left-sm" @tap="toDelete(spaceInfo.id)">删除</text>
					</block>
					<block v-else>
						<text class="text-red margin-left-sm" @tap="toJb(markHtml(spaceInfo.text))">举报</text>
					</block>
				</block>
				</view>
				
				<view class="text-content break-all" style="display: flex;" >
					
					<rich-text :nodes="markHtml(spaceInfo.text)"></rich-text>
				</view>
				
				<block  v-if="spaceInfo.type==0">
					<view class="image-container" v-if="spaceInfo.picList.length>0">
						<!-- 1张图片：等宽长方形 -->
						<view class="image-layout-1" v-if="spaceInfo.picList.length === 1">
							<view class="image-item large-rect" 
								:style="'background-image:url('+spaceInfo.picList[0]+');'"
								@tap="previewImage(spaceInfo.picList,spaceInfo.picList[0])">
							</view>
						</view>
						
						<!-- 2张图片：左右等分 -->
						<view class="image-layout-2" v-if="spaceInfo.picList.length === 2">
							<view class="image-item half-width" 
								v-for="(img, i) in spaceInfo.picList" :key="i"
								:style="'background-image:url('+img+');'"
								@tap="previewImage(spaceInfo.picList,img)">
							</view>
						</view>
						
						<!-- 3张图片：6宫格布局 -->
						<view class="image-layout-3" v-if="spaceInfo.picList.length === 3">
							<view class="image-grid-3">
								<!-- 第一张图占1,2,4,5位置 -->
								<view class="image-item grid-main" style="margin: 0;" 
									:style="'background-image:url('+spaceInfo.picList[0]+');'"
									@tap="previewImage(spaceInfo.picList,spaceInfo.picList[0])">
								</view>
								<!-- 第二张图占3位置 -->
								<view class="image-item grid-top-right" style="margin: 0;" 
									:style="'background-image:url('+spaceInfo.picList[1]+');'"
									@tap="previewImage(spaceInfo.picList,spaceInfo.picList[1])">
								</view>
								<!-- 第三张图占6位置 -->
								<view class="image-item grid-bottom-right" style="margin: 0;" 
									:style="'background-image:url('+spaceInfo.picList[2]+');'"
									@tap="previewImage(spaceInfo.picList,spaceInfo.picList[2])">
								</view>
							</view>
						</view>
						
						<!-- 4张图片：2x2宫格 -->
						<view class="image-layout-4" v-if="spaceInfo.picList.length === 4">
							<view class="image-item quarter" 
								v-for="(img, i) in spaceInfo.picList" :key="i"
								:style="'background-image:url('+img+');'"
								@tap="previewImage(spaceInfo.picList,img)">
							</view>
						</view>
						
						<!-- 5张图片：上3下2布局 -->
						<view class="image-layout-5" v-if="spaceInfo.picList.length === 5">
							<view class="image-row-top">
								<view class="image-item third-width" 
									v-for="(img, i) in spaceInfo.picList.slice(0, 3)" :key="i"
									:style="'background-image:url('+img+');'"
									@tap="previewImage(spaceInfo.picList,img)">
								</view>
							</view>
							<view class="image-row-bottom">
								<view class="image-item half-width-5" 
									v-for="(img, i) in spaceInfo.picList.slice(3)" :key="i+3"
									:style="'background-image:url('+img+');'"
									@tap="previewImage(spaceInfo.picList,img)">
								</view>
							</view>
						</view>
						
						<!-- 6张图片：9宫格布局 -->
						<view class="image-layout-6" v-if="spaceInfo.picList.length === 6">
							<view class="image-grid-6">
								<!-- 第一张图占1,2,3,4位置 -->
								<view class="image-item grid-main-6" style="margin: 0;" 
									:style="'background-image:url('+spaceInfo.picList[0]+');'"
									@tap="previewImage(spaceInfo.picList,spaceInfo.picList[0])">
								</view>
								<!-- 其他图片占5,6,7,8,9位置 -->
								<view class="image-item grid-small-6" style="margin: 0;" 
									v-for="(img, i) in spaceInfo.picList.slice(1)" :key="i+1"
									:style="'background-image:url('+img+');'"
									@tap="previewImage(spaceInfo.picList,img)">
								</view>
							</view>
						</view>
					</view>
				</block>
				<block  v-if="spaceInfo.type==1">
					<view class="grid flex-sub padding-lr">
						<view class="user-post-info" @tap="goContentInfo(spaceInfo)">
							<view class="user-post-pic" v-if="spaceInfo.contentJson.images.length>0">
								<image :src="spaceInfo.contentJson.images[0]" mode="widthFix"></image>
							</view>
							<view class="user-post-text">
								<view class="user-post-title">
									{{spaceInfo.contentJson.title}}
								</view>
								<view class="user-post-intro">
									{{spaceInfo.contentJson.text}}
								</view>
							</view>
						</view>
					</view>
				</block>
				<block  v-if="spaceInfo.type==2">
					<view class="grid flex-sub padding-lr">
						
													<view class="user-space-info" @tap="toInfo(spaceInfo.forwardJson.id)">
								<view class="user-space-text">
									<text class="text-blue">@{{spaceInfo.forwardJson.username}}：</text>{{spaceInfo.forwardJson.text}}
								</view>
								
								<view class="image-container" v-if="spaceInfo.forwardJson.picList.length > 0">
									<!-- 1张图片：等宽长方形 -->
									<view class="image-layout-1" v-if="spaceInfo.forwardJson.picList.length === 1">
										<view class="image-item large-rect"
											:style="'background-image:url(' + spaceInfo.forwardJson.picList[0] + ');'"
											@tap="previewImage(spaceInfo.forwardJson.picList, spaceInfo.forwardJson.picList[0])">
										</view>
									</view>

									<!-- 2张图片：左右等分 -->
									<view class="image-layout-2" v-if="spaceInfo.forwardJson.picList.length === 2">
										<view class="image-item half-width"
											v-for="(img, i) in spaceInfo.forwardJson.picList" :key="i"
											:style="'background-image:url(' + img + ');'"
											@tap="previewImage(spaceInfo.forwardJson.picList, img)">
										</view>
									</view>

									<!-- 3张图片：6宫格布局 -->
									<view class="image-layout-3" v-if="spaceInfo.forwardJson.picList.length === 3">
										<view class="image-grid-3">
											<!-- 第一张图占1,2,4,5位置 -->
											<view class="image-item grid-main" style="margin: 0;"
												:style="'background-image:url(' + spaceInfo.forwardJson.picList[0] + ');'"
												@tap="previewImage(spaceInfo.forwardJson.picList, spaceInfo.forwardJson.picList[0])">
											</view>
											<!-- 第二张图占3位置 -->
											<view class="image-item grid-top-right" style="margin: 0;"
												:style="'background-image:url(' + spaceInfo.forwardJson.picList[1] + ');'"
												@tap="previewImage(spaceInfo.forwardJson.picList, spaceInfo.forwardJson.picList[1])">
											</view>
											<!-- 第三张图占6位置 -->
											<view class="image-item grid-bottom-right" style="margin: 0;"
												:style="'background-image:url(' + spaceInfo.forwardJson.picList[2] + ');'"
												@tap="previewImage(spaceInfo.forwardJson.picList, spaceInfo.forwardJson.picList[2])">
											</view>
										</view>
									</view>

									<!-- 4张图片：2x2宫格 -->
									<view class="image-layout-4" v-if="spaceInfo.forwardJson.picList.length === 4">
										<view class="image-item quarter"
											v-for="(img, i) in spaceInfo.forwardJson.picList" :key="i"
											:style="'background-image:url(' + img + ');'"
											@tap="previewImage(spaceInfo.forwardJson.picList, img)">
										</view>
									</view>

									<!-- 5张图片：上3下2布局 -->
									<view class="image-layout-5" v-if="spaceInfo.forwardJson.picList.length === 5">
										<view class="image-row-top">
											<view class="image-item third-width"
												v-for="(img, i) in spaceInfo.forwardJson.picList.slice(0, 3)" :key="i"
												:style="'background-image:url(' + img + ');'"
												@tap="previewImage(spaceInfo.forwardJson.picList, img)">
											</view>
										</view>
										<view class="image-row-bottom">
											<view class="image-item half-width-5"
												v-for="(img, i) in spaceInfo.forwardJson.picList.slice(3)" :key="i + 3"
												:style="'background-image:url(' + img + ');'"
												@tap="previewImage(spaceInfo.forwardJson.picList, img)">
											</view>
										</view>
									</view>

									<!-- 6张图片：9宫格布局 -->
									<view class="image-layout-6" v-if="spaceInfo.forwardJson.picList.length === 6">
										<view class="image-grid-6">
											<!-- 第一张图占1,2,3,4位置 -->
											<view class="image-item grid-main-6" style="margin: 0;"
												:style="'background-image:url(' + spaceInfo.forwardJson.picList[0] + ');'"
												@tap="previewImage(spaceInfo.forwardJson.picList, spaceInfo.forwardJson.picList[0])">
											</view>
											<!-- 其他图片占5,6,7,8,9位置 -->
											<view class="image-item grid-small-6" style="margin: 0;"
												v-for="(img, i) in spaceInfo.forwardJson.picList.slice(1)" :key="i + 1"
												:style="'background-image:url(' + img + ');'"
												@tap="previewImage(spaceInfo.forwardJson.picList, img)">
											</view>
										</view>
									</view>
								</view>
							</view>
					</view>
				</block>
				<block  v-if="spaceInfo.type==4">
					<view class="padding-lr spaceVideo">
						<video :src="spaceInfo.pic"></video>
					</view>
				</block>
				

			</view>
		</view>
		<view class="space-reply" style="margin: 0 20upx 20upx 20upx;border-radius: 20upx;">
			<view class="space-reply-head">
				<text @tap="setInfoType(1)" class="margin-right-xl" :class="infoType==1?'cur':''" v-if="replyType==0">
					转发 <block v-if="spaceInfo.forward>0">{{formatNumber(spaceInfo.forward)}}</block></text>
				<text  @tap="setInfoType(0)" :class="infoType==0?'cur':''">评论 <block v-if="spaceInfo.reply>0">{{formatNumber(spaceInfo.reply)}}</block></text>
				
				<text class="space-reply-likes">赞 <block v-if="spaceInfo.likes>0">{{formatNumber(spaceInfo.likes)}}</block></text>
			</view>
			<block v-if="infoType==0">
				<view class="space-reply-list">
					<view class="cu-list menu-avatar comment" v-for="(item,index) in replyList" :key="index">
						<view class="cu-item">
							<view class="cu-avatar round user-rz" :style="'background-image:url('+(item.userJson && item.userJson.avatar ? item.userJson.avatar : '../../static/user/default.png')+');'" @tap="toUserContents(item.userJson)">
								<image v-if="getUserFrame(item.userJson.uid)" class="avatar-frame" :src="getUserFrame(item.userJson.uid)" mode="aspectFit" @error="onFrameLoadError"></image>
								<image class="user-rz-icon" width="34upx" height="34upx" :src="rzImg" mode="aspectFill" v-if="item.lvrz==1"></image>
							</view>
							<view class="content">
								<view class="text-grey">
									<text>{{item.userJson.name}}</text>
									<image v-if="item.userJson.isvip>0" :src="vipImg" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;" mode="widthFix"></image>
									<image :src="lvImg+getLv(item.userJson.experience)+'.png'" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 44upx;height: 22upx;" mode="widthFix"></image>
									<text class="userlv customize"
										style="border: 3upx solid black;color:black;padding: 2upx 10upx;border-radius: 40upx;background-color: transparent;"
										v-if="item.userJson.customize&&item.userJson.customize!=''">{{item.userJson.customize}}</text>
									<medal-item :uid="item.userJson.uid"></medal-item>
								</view>
								<view class="text-content text-df break-all" style="display: flex;">
									<rich-text :nodes="markHtml(item.text)"></rich-text>
								</view>
								<view class="space-reply-num padding-xs radius margin-top-sm  text-sm" v-if="item.reply>0" @tap="toReplyInfo(item.id)">
									<text class="text-blue">共{{item.reply}}条回复<text class="cuIcon-right margin-left-xs"></text></text>
								</view>
								<view class="margin-top-sm flex justify-between">
									<view class="text-gray text-df">
									{{formatDate(item.created)}}
									<text class="margin-left-sm" v-if="$API.localOf()">{{getLocal(item.userJson.local)}}</text>								
									</view>
									<view>
										<text class="cuIcon-message text-gray margin-left-xl" @tap="goReply(item.id)">
											{{formatNumber(item.reply) || ''}}
										</text>
										<text class="cuIcon-appreciate  margin-left-xl" @tap="toListLike(item.id,'reply',index)" :class="item.isLikes==1?'text-red':'text-gray'">
											{{formatNumber(item.likes) || ''}}
										</text>
									</view>
								</view>
								<view class="comment-operation">
									<block v-if="group=='administrator'||group=='editor'">
										<!-- <text class="text-blue margin-left-sm" @tap="edit(item.id)">编辑</text> -->
										<text class="text-black" @tap="toBan(item.userJson.uid)">封禁</text>
										<text class="text-red margin-left-sm" @tap="toDelete(item.id)">删除</text>
									</block>
									<block v-else>
										<block v-if="item.userJson.uid!=0&&item.userJson.uid==uid">
											<!-- <text class="text-blue margin-left-sm" @tap="edit(item.id)">编辑</text> -->
											<text class="text-red" @tap="toDelete(item.id)">删除</text>
										</block>
										
									</block>
								</view>
							</view>
						</view>
					</view>
					<view class="no-data" v-if="replyList.length==0">
						<text class="cuIcon-text"></text>
						
						暂时没有消息
						<view class="text-center margin-top-sm">
							<text class="cu-btn bg-blue" @tap="goReply(spaceInfo.id)">我要抢首评</text>
						</view>
						
					</view>
					<view class="load-more" @tap="loadMore" v-if="replyList.length>0">
						<text>{{moreText}}</text>
					</view>
					
				</view>
			</block>
			<block v-if="infoType==1">
				<view class="space-reply-list">
					<view class="cu-list menu-avatar comment" v-for="(item,index) in forwardList" :key="index">
						<view class="cu-item">
							<view class="cu-avatar round user-rz" :style="'background-image:url('+(item.userJson && item.userJson.avatar ? item.userJson.avatar : '../../static/user/default.png')+');'" @tap="toUserContents(item.userJson)">
								<image v-if="getUserFrame(item.userJson.uid)" class="avatar-frame" :src="getUserFrame(item.userJson.uid)" mode="aspectFit" @error="onFrameLoadError"></image>
								<image class="user-rz-icon" width="34upx" height="34upx" :src="rzImg" mode="aspectFill" v-if="item.lvrz==1"></image>
							</view>
							<view class="content">
								<view class="text-grey">
									<text>{{item.userJson.name}}</text>
									<image v-if="item.userJson.isvip>0" :src="vipImg" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 70upx;height: 35upx;" mode="widthFix"></image>
									<image :src="lvImg+getLv(item.userJson.experience)+'.png'" style="border-radius: 0px;margin-left: 6upx;margin-right: 0upx;width: 44upx;height: 22upx;" mode="widthFix"></image>
									<text class="userlv customize"
										style="border: 3upx solid black;color:black;padding: 2upx 10upx;border-radius: 40upx;background-color: transparent;"
										v-if="item.userJson.customize&&item.userJson.customize!=''">{{item.userJson.customize}}</text>
									<medal-item :uid="item.userJson.uid"></medal-item>
								</view>
								<view class="text-content text-df break-all" style="display: flex;">
									<rich-text :nodes="markHtml(item.text)"></rich-text>
								</view>
								<view class="margin-top-sm flex justify-between">
									<view class="text-gray text-df">
									{{formatDate(item.created)}}
									<text class="margin-left-sm" v-if="$API.localOf()">{{getLocal(item.userJson.local)}}</text>
									</view>
									<view>
										<text class="cuIcon-message text-gray margin-left-xl" @tap="goReply(item.id)">
											{{formatNumber(item.reply) || ''}}
										</text>
										<text class="cuIcon-appreciate text-gray margin-left-xl" @tap="toListLike(item.id,'forward',index)"  :class="item.isLikes==1?'text-red':'text-gray'">
											{{formatNumber(item.likes) || ''}}
										</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="no-data" v-if="forwardList.length==0">
						<text class="cuIcon-text"></text>
						
						暂时还没有人转发

						
					</view>
					<view class="load-more" @tap="loadMore" v-if="forwardList.length>0">
						<text>{{moreText}}</text>
					</view>
					
				</view>
			</block>
			
		</view>
		<view class="space-footer grid " :class="replyType==0?'col-3':'col-3'">
			<view class="space-footer-box" @tap="forward(spaceInfo.id)">
				<text class="cuIcon-forward"></text>
				转发
			</view>
			<view class="space-footer-box" @tap="goReply(spaceInfo.id)">
				<text class="cuIcon-message"></text>
				评论
			</view>
			<view class="space-footer-box" @tap="toLike(spaceInfo.id)">
				<text class="cuIcon-appreciate"  :class="spaceInfo.isLikes==1?'text-cyan':''"></text>
				点赞
			</view>
		</view>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
	</view>
</template>

<script>
	import { localStorage } from '../../js_sdk/mp-storage/mp-storage/index.js'
	import darkModeMixin from '@/utils/darkModeMixin.js'
	// 引入勋章组件
	import medalItem from '../components/medalItem.vue'
	// #ifdef APP-PLUS
	import owo from '../../static/app-plus/owo/OwO.js'
	// #endif
	// #ifdef H5
	import owo from '../../static/h5/owo/OwO.js'
	// #endif
	// #ifdef MP
	var owo = [];
	// #endif
	export default {
		mixins: [darkModeMixin],
		// 注册勋章组件
		components: {
			medalItem
		},
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar:this.StatusBar +  this.CustomBar + 10,
				AppStyle:this.$store.state.AppStyle,
				
				replyType:0,
				id:0,
				token:'',
				
				spaceInfo:{
					created: 0,
					forward: 0,
					id: 3,
					likes: 0,
					reply: 0,
					text: "",
					toid: 0,
					type: 0,
					uid: 1,
					modified:0,
					picList:[]
				},
				replyList:[],
				forwardList:[],
				isLoad:0,
				isLoading:0,
				vipImg: this.$API.SPvip(),
				lvImg: this.$API.SPLv(),
				rzImg: this.$API.SPRz(),
				identifyCompany: 0,
				identifyConsumer: 0,
				Rz: false,
				page:1,
				pageSize:10,
				moreText:"加载更多",
				dataLoad:false,
				
				currencyName:"",
				
				owo:owo,
				owoList:[],
				
				infoType:0,
				group:"",
				uid:0,
				frameUrl: null, // 用户头像框URL
				fanstey_avatarframe: false, // 头像框插件状态
				userMedals: null, // 用户勋章数据
				userFrameCache: {}, // 用于缓存用户头像框
			}
		},
		onPullDownRefresh(){
			var that = this;
			if(that.id!=0){
				that.getSpaceInfo();
				that.getReplyList(false)
			}
			var timer = setTimeout(function() {
				
				uni.stopPullDownRefresh();
			}, 1000)
		},
		onReachBottom() {
		    //触底后执行的方法，比如无限加载之类的
			var that = this;
			that.loadMore();
		},
		onHide() {
			localStorage.removeItem('getuid')
		},
		onShow(){
			var that = this;
			// #ifdef APP-PLUS
			that.isLoad=0;
			that.page=1;
			//plus.navigator.setStatusBarStyle("dark")
			// #endif
			if(localStorage.getItem('token')){
				
				that.token = localStorage.getItem('token');
			}
			if(localStorage.getItem('getuid')){
				that.toid = localStorage.getItem('getuid');
			}
			if(that.id!=0){
				that.getSpaceInfo();
				that.getReplyList(false)
			}
			
		},
		onLoad(res) {
			var that = this;
			if(localStorage.getItem('userinfo')){
				var userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.group = userInfo.group;
				that.uid = userInfo.uid;
			}
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			// #ifdef APP-PLUS || H5
			var owo = that.owo.data;
			var owoList=[];
			for(var i in owo){
				owoList = owoList.concat(owo[i].container);
			}
			that.owoList = owoList;
			// #endif
			if(res.replyType){
				that.replyType = res.replyType;
			}
			if(res.id){
				that.id = res.id;
				that.getSpaceInfo();
				that.getReplyList(false);
			}
		},
		mounted() {
			var that = this;
			that.getleiji();
			
			// 检查头像框插件是否启用
			var cachedPlugins = localStorage.getItem('getPlugins');
			if (cachedPlugins) {
				const pluginList = JSON.parse(cachedPlugins);
				// 检查插件是否存在于插件列表中 
				this.fanstey_avatarframe = pluginList.includes('xqy_avatar_frame');
			}
		},
		
		methods: {
			getleiji() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.currencyName = res.data.assetsname;
					},
					fail(error) {
						console.log(error);
					}
		
				})
			},
			loadMore(){
				var that = this;
				that.moreText="正在加载中...";
				if(that.isLoad==0){
					that.getReplyList(true);
					if(that.infoType==0){
						that.getReplyList(false)
					}
					if(that.infoType==1){
						that.getForwardList(false);
					}
				}
			},
			back(){
				// 设置标记，表示从详情页返回，避免重新加载数据
				localStorage.setItem('isinfoback', 1);
				uni.navigateBack({
					delta: 1
				});
			},
			getLv(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var lv  = that.$API.getLever(i);
				return lv;
			},
			getSpaceInfo(){
				var that = this;
				var token = "";
				if(localStorage.getItem('token')){
					
					token = localStorage.getItem('token');
				}
				var data = {
					"id":that.id,
					"token":token
				}
				that.$Net.request({
					url: that.$API.spaceInfo(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isLoading=1;
						if(res.data.code==1){
							that.spaceInfo = res.data.data;
							if(res.data.data.pic && res.data.data.pic!=""){
								that.spaceInfo.picList = res.data.data.pic.split("||");
							}else{
								that.spaceInfo.picList = [];
							}
							if(res.data.data.forwardJson){
								if(res.data.data.forwardJson.pic && res.data.data.forwardJson.pic!=""){
									that.spaceInfo.forwardJson.picList = res.data.data.forwardJson.pic.split("||");
								}else{
									that.spaceInfo.forwardJson.picList = [];
								}
							}
							
							// 使用setTimeout确保方法在下一个事件循环中调用，避免可能的作用域问题
							setTimeout(function() {
								// 检查方法是否存在
								if (typeof that.loadUserFrame === 'function' && that.spaceInfo.userJson && that.spaceInfo.userJson.uid) {
									that.loadUserFrame(that.spaceInfo.userJson.uid);
								} else {
									console.error('loadUserFrame方法未定义或无法访问');
								}
							}, 0);
						}
					},
					fail: function(res) {
						that.isLoading=1;
					}
				});
				
			},
			previewImage(imageList,image) {
				//预览图片
				uni.previewImage({
					urls: imageList,
					current: image
				});
			},
			subText(text,num){
				if(text.length < null){
					return text.substring(0,num)+"……"
				}else{
					return text;
				}
				
			},
			replaceSpecialChar(text) {
			  text = text.replace(/&quot;/g, '"');
			  text = text.replace(/&amp;/g, '&');
			  text = text.replace(/&lt;/g, '<');
			  text = text.replace(/&gt;/g, '>');
			  text = text.replace(/&nbsp;/g, ' ');
			  return text;
			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				var now = new Date();
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
			
				var timeDiff = now - datetime;
				var seconds = Math.floor(timeDiff / 1000);
				var minutes = Math.floor(timeDiff / (1000 * 60));
				var hours = Math.floor(timeDiff / (1000 * 60 * 60));
				var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
				var months = Math.floor(days / 30);
			
				var result = "";
			
				if (seconds < 60) {
					result = seconds + "秒前";
				} else if (minutes < 60) {
					result = minutes + "分钟前";
				} else if (hours < 24) {
					result = hours + "小时前";
				} else if (days < 7) {
					result = days + "天前";
				} else if (year === now.getFullYear()) {
					result = month + "月" + date + "日";
				} else {
					result = year + "年" + month + "月" + date + "日";
				}
			
				return result;
			},
			formatNumber(num) {
			    return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' : num
			},
			toInfo(id){
				var that = this;
				
				uni.navigateTo({
				    url: '/pages/space/info?id='+id
				});
			},
			goAds(data){
				var that = this;
				var url = data.url;
				var type = data.urltype;
				// #ifdef APP-PLUS
				if(type==1){
					plus.runtime.openURL(url);
				}
				if(type==0){
					plus.runtime.openWeb(url);
				}
				// #endif
				// #ifdef H5
				window.open(url)
				// #endif
			},
			formatNumber(num) {
			    return num >= 1e3 && num < 1e4 ? (num / 1e3).toFixed(1) + 'k' : num >= 1e4 ? (num / 1e4).toFixed(1) + 'w' : num
			},
			getUserLv(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			markHtml(text){
				var that = this;
				text = that.replaceAll(text,"<","&lt;");
				text = that.replaceAll(text,">","&gt;");
				var owoList=that.owoList;
				for(var i in owoList){
				
					if(that.replaceSpecialChar(text).indexOf(owoList[i].data) != -1){
						text = that.replaceAll(that.replaceSpecialChar(text),owoList[i].data,"<img src='/"+owoList[i].icon+"' class='tImg' />")
						
					}
				}
				text = that.replaceAll(text,"/r/n","<br>");
				text =that.replaceAll(text,"||rn||","<br>");
				text = that.TransferString(text);
				return "<div style=\"display:flex;flex-wrap: wrap;\">" + text + "</div>";
			},
			TransferString(content)
			{  
			    var string = content;  
			    try{  
			        string=string.replace(/\r\n/g,"<br>")  
			        string=string.replace(/\n/g,"<br>");  
					
			    }catch(e) {  
			        return content;
			    }  
			    return string;  
			},
			replaceAll(string, search, replace) {
			  return string.split(search).join(replace);
			},
			getUserLv(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var rankList = that.$API.GetRankList();
				return rankList[i];
			},
			
			getUserLvStyle(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle ="color:#fff;background-color: "+rankStyle[i];
				return userlvStyle;
			},
			getLvStyle(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var lv  = that.$API.getLever(i);
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle ="color:#fff;background-color: "+rankStyle[lv];
				return userlvStyle;
			},
			goReply(id){
				uni.navigateTo({
				    url: '/pages/space/reply?id='+id
				});
			},
			forward(id){
				var that = this;
				uni.navigateTo({
				    url: '/pages/space/post?type=2&id='+id
				});
			},
			toUserInfo(data){
				var that = this;
				var name = data.name;
				var title = data.name+"的信息";
				var id= data.uid;
				var type="user";
				uni.navigateTo({
				    url: '/pages/contents/userinfo?title='+title+"&name="+name+"&uid="+id+"&avatar="+encodeURIComponent(data.avatar)
				});
			},
			getReplyList(isPage){
				var that = this;
				var page = that.page;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"toid":that.id,
					"type":3
				}
				if(isPage){
					page++;
				}
				that.$Net.request({
					url: that.$API.spaceList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":that.pageSize,
						"page":page,
						"order":"created",
						"token":token
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isLoad=0;
						that.moreText="加载更多";
						if(!isPage){
							that.dataLoad = true;
						}
						if(res.data.code==1){
							var list = res.data.data;
							var replyList = [];
							for(var i in list){
								if(list[i].type==0){
									if(list[i].pic){
										var pic = list[i].pic;
										list[i].picList = pic.split("||");
									}else{
										list[i].picList = [];
									}
									
								}
								if(list[i].type==2){
									if(list[i].forwardJson.pic){
										var pic = list[i].forwardJson.pic;
										list[i].forwardJson.picList = pic.split("||");
									}else{
										list[i].forwardJson.picList = [];
									}
									
								}
							}
							replyList = list;
							if(list.length>0){
								if(isPage){
									that.page++;
									that.replyList = that.replyList.concat(replyList);
								}else{
									that.replyList = replyList;
								}
								
							}else{
								that.moreText="没有更多数据了";
							}
							
							// 预加载评论用户的头像框
							if (that.fanstey_avatarframe) {
								for (let i = 0; i < list.length; i++) {
									if (list[i].userJson && list[i].userJson.uid) {
										that.loadUserFrameForComment(list[i].userJson.uid);
									}
								}
							}
						}
					},
					fail: function(res) {
						
						that.moreText="加载更多";
						that.isLoad=0;
					}
				})
			},
			

			getLv(i) {
				var that = this;
				if (!i) {
					var i = 0;
				}
				var lv = that.$API.getLever(i);
				return lv;
			},
			getForwardList(isPage){
				var that = this;
				var page = that.page;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"toid":that.id,
					"type":2
				}
				if(isPage){
					page++;
				}
				that.$Net.request({
					url: that.$API.spaceList(),
					data:{
						"searchParams":JSON.stringify(that.$API.removeObjectEmptyKey(data)),
						"limit":that.pageSize,
						"page":page,
						"order":"created",
						"token":token
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						that.isLoad=0;
						that.moreText="加载更多";
						if(!isPage){
							that.dataLoad = true;
						}
						if(res.data.code==1){
							var list = res.data.data;
							var forwardList = [];
							for(var i in list){
								if(list[i].type==0){
									if(list[i].pic){
										var pic = list[i].pic;
										list[i].picList = pic.split("||");
									}else{
										list[i].picList = [];
									}
									
								}
								if(list[i].type==2){
									if(list[i].forwardJson.pic){
										var pic = list[i].forwardJson.pic;
										list[i].forwardJson.picList = pic.split("||");
									}else{
										list[i].forwardJson.picList = [];
									}
									
								}
							}
							forwardList = list;
							if(list.length>0){
								if(isPage){
									that.page++;
									that.forwardList = that.forwardList.concat(forwardList);
								}else{
									that.forwardList = forwardList;
								}
								
							}else{
								that.moreText="没有更多数据了";
							}
							
							// 预加载转发用户的头像框
							if (that.fanstey_avatarframe) {
								for (let i = 0; i < list.length; i++) {
									if (list[i].userJson && list[i].userJson.uid) {
										that.loadUserFrameForComment(list[i].userJson.uid);
									}
								}
							}
						}
					},
					fail: function(res) {
						
						that.moreText="加载更多";
						that.isLoad=0;
					}
				})
			},
			follow(type,uid){
				var that = this;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}else{
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				that.spaceInfo.isFollow = type;
				var data = {
					token:token,
					touid:uid,
					type:type,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.follow(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==0){
							that.spaceInfo.isFollow = 0;
						}
						
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						
					}
				})
			},
			toLike(id){
				var that = this;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}else{
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				if(that.spaceInfo.isLikes==1){
					uni.showToast({
						title: "你已经点赞过了",
						icon: 'none'
					});
					return false;
				}else{
					that.spaceInfo.isLikes = 1;
				}
				
				that.spaceInfo.likes += 1;
				var data = {
					token:token,
					id:id,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.spaceLikes(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==0){
							that.spaceInfo.isLikes = 0;
						}
						
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						
					}
				})
			},
			toJb(title) {
				var that = this;
			
				uni.navigateTo({
					url: '/pages/user/help?type=text&title='+title
				});
			},
			toListLike(id,type,index){
				var that = this;
				var token = "";
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}else{
					uni.showToast({
						title: "请先登录",
						icon: 'none'
					})
					uni.navigateTo({
						url: '/pages/user/login'
					});
					return false;
				}
				if(type=="forward"){
					if(that.forwardList[index].isLikes==1){
						uni.showToast({
							title: "你已经点赞过了",
							icon: 'none'
						});
						return false;
					}else{
						that.forwardList[index].isLikes = 1;
					}
					that.forwardList[index].likes += 1;
				}
				if(type=="reply"){
					if(that.replyList[index].isLikes==1){
						uni.showToast({
							title: "你已经点赞过了",
							icon: 'none'
						});
						return false;
					}else{
						that.replyList[index].isLikes = 1;
					}
					that.replyList[index].likes += 1;
				}
				
				
				var data = {
					token:token,
					id:id,
				}
				uni.showLoading({
					title: "加载中"
				});
				that.$Net.request({
					
					url: that.$API.spaceLikes(),
					data:data,
					header:{
						'Content-Type':'application/x-www-form-urlencoded'
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						//console.log(JSON.stringify(res))
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						if(res.data.code==0){
							if(type=="forward"){
								that.forwardList[index].isLikes = 0;
							}
							if(type=="reply"){
								that.replyList[index].isLikes = 1;
							}
						}
						
					},
					fail: function(res) {
						setTimeout(function () {
							uni.hideLoading();
						}, 1000);
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
						
					}
				})
			},
			setInfoType(type){
				var that = this;
				that.page = 1;
				if(type==0){
					that.getReplyList(false)
				}
				if(type==1){
					that.getForwardList(false);
				}
				that.infoType = type;
			},
			toSearch(){
				var that = this;
				uni.navigateTo({
				    url: '/pages/contents/search'
				});
			},
			getLvStyle(i){
				var that = this;
				if(!i){
					var i = 0;
				}
				var lv  = that.$API.getLever(i);
				var rankStyle = that.$API.GetRankStyle();
				var userlvStyle ="color:#fff;background-color: "+rankStyle[lv];
				return userlvStyle;
			},
			toUserContents(data){
				var that = this;
				var name = data.name;
				var title = data.name+"的信息";
				var id= data.uid;
				if(id==0){
					uni.showToast({
						title: "用户不存在或已注销",
						icon: 'none'
					})
					return false
				}
				var type="user";
				uni.navigateTo({
				    url: '/pages/contents/userinfo?title='+title+"&name="+name+"&uid="+id+"&avatar="+encodeURIComponent(data.avatar)
				});
			},
			toReplyInfo(id){
				var that = this;
				
				uni.navigateTo({
				    url: '/pages/space/info?id='+id+'&replyType=1'
				});
			},
			toBan(uid){
				if(uid==0){
					uni.showToast({
						title: "该用户不存在",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: '/pages/manage/banuser?uid='+uid
				});
			},
			edit(id){
				var that = this;
				uni.navigateTo({
				    url: '/pages/space/post?postType=edit&id='+id
				});
			},
			toDelete(id){
				var that = this;
				var token = "";
				
				if(localStorage.getItem('userinfo')){
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token=userInfo.token;
				}
				var data = {
					"id":id,
					"token":token
				}
				uni.showModal({
					title: '确定要删除该动态吗',
					success: function (res) {
						if (res.confirm) {
							uni.showLoading({
								title: "加载中"
							});
							
							that.$Net.request({
								url: that.$API.spaceDelete(),
								data:data,
								header:{
									'Content-Type':'application/x-www-form-urlencoded'
								},
								method: "get",
								dataType: 'json',
								success: function(res) {
									setTimeout(function () {
										uni.hideLoading();
									}, 1000);
									if(res.data.code==1){
										uni.showToast({
											title: res.data.msg+"，刷新数据后生效",
											icon: 'none'
										})
									}else{
										uni.showToast({
											title: res.data.msg,
											icon: 'none'
										})
									}
									
									
								},
								fail: function(res) {
									setTimeout(function () {
										uni.hideLoading();
									}, 1000);
									uni.showToast({
										title: "网络开小差了哦",
										icon: 'none'
									})
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			getLocal(local){
				var that = this;
				if(local&&local!=''){
					var local_arr = local.split("|");
					if(!local_arr[3]||local_arr[3]==0){
						return local_arr[2];
					}else{
						return local_arr[3];
					}
					
				}else{
					return "未知"
				}
				
			},
			onFrameLoadError(event) {
				console.error('头像框加载失败', event);
				this.frameUrl = null;
			},
			onMedalLoaded(medals) {
				this.userMedals = medals;
			},
			// 获取用户头像框
			getUserFrame(uid) {
				if (!uid || !this.fanstey_avatarframe) return null;
				
				// 如果缓存中有，直接返回
				if (this.userFrameCache && this.userFrameCache[uid]) {
					return this.userFrameCache[uid];
				}
				
				// 否则加载头像框
				this.loadUserFrameForComment(uid);
				return null;
			},
			
			// 为评论/转发加载用户头像框
			loadUserFrameForComment(uid) {
				if (!uid || !this.fanstey_avatarframe) return;
				
				const that = this;
				that.$Net.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": uid
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame) {
								// 确保userFrameCache已初始化
								if (!that.userFrameCache) that.userFrameCache = {};
								// 缓存用户头像框
								that.$set(that.userFrameCache, uid, wearingFrame.frame_url);
							}
						}
					}
				});
			},
			loadUserFrame(uid) {
				if (!uid || !this.fanstey_avatarframe) return;
				
				const that = this;
				that.$Net.request({
					url: that.$API.PluginLoad('xqy_avatar_frame'),
					data: {
						"action": "get_user_frames",
						"op": "list",
						"type": "view",
						"uid": uid
					},
					method: "post",
					dataType: 'json',
					success: function(res) {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.awarded && res.data.data.awarded.length > 0) {
							const wearingFrame = res.data.data.awarded.find(frame => frame.is_wearing);
							if (wearingFrame) {
								that.frameUrl = wearingFrame.frame_url;
							}
						}
					}
				});
			},
		}
	}
</script>

<style>
	.cu-card.no-card>.cu-item {
	    margin: 20upx;
	    border-radius: 20upx;
	}
	.grid.col-3.grid-square>view {
		margin-right: 10upx;
		margin-bottom: 10upx;
	    border-radius: 20upx;
	}
	.square-text {
	    width: calc(100% - 120upx);
	    padding: 0 20upx;
	    float: left;
	    margin: 0 20upx;
	}
	.user-rz {
		position: relative;
		display: inline-block;
	}
	
	.user-rz-icon {
		position: absolute;
		right: -4upx;
		bottom: -4upx;
		width: 34upx;
		height: 34upx;
		z-index: 3;
	}
	
	.avatar-frame {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 120%;
		height: 120%;
		z-index: 2;
		transform: translate(-50%, -50%) scale(1.1);
	}

	/* 图片容器样式 */
	.image-container {
		padding: 0 20upx;
		margin-top: 20upx;
	}
	
	/* 基础图片样式 */
	.image-item {
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		border-radius: 20upx;
		margin: 8upx;
	}
	
	/* 1张图片：等宽长方形 */
	.image-layout-1 {
		display: flex;
		width: 100%;
	}
	
	.large-rect {
		width: calc(100% - 20upx);
		height: 400upx;
	}
	
	/* 2张图片：左右等分 */
	.image-layout-2 {
		display: flex;
		width: 100%;
	}
	
	.half-width {
		width: calc(50% - 20upx);
		height: 340upx;
	}
	
	/* 3张图片：6宫格布局 */
	.image-layout-3 {
		display: flex;
		width: 100%;
		height: 370upx;
	}
	
	.image-grid-3 {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		grid-template-rows: 1fr 1fr;
		width: 100%;
		height: 100%;
		gap: 15upx;
		padding: 0 10upx;
	}
	
	.grid-main {
		grid-column: 1 / 3;
		grid-row: 1 / 3;
	}
	
	.grid-top-right {
		grid-column: 3 / 4;
		grid-row: 1 / 2;
	}
	
	.grid-bottom-right {
		grid-column: 3 / 4;
		grid-row: 2 / 3;
	}
	
	/* 4张图片：2x2宫格 */
	.image-layout-4 {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		height: 480upx;
	}
	
	.quarter {
		width: calc(50% - 20upx);
		height: calc(50% - 20upx);
	}
	
	/* 5张图片：上3下2布局 */
	.image-layout-5 {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 480upx;
	}
	
	.image-row-top {
		display: flex;
		width: 100%;
		height: 50%;
	}
	
	.image-row-bottom {
		display: flex;
		width: 100%;
		height: 50%;
	}
	
	.third-width {
		width: calc(33.33% - 20upx);
		height: calc(100% - 20upx);
	}
	
	.half-width-5 {
		width: calc(50% - 20upx);
		height: calc(100% - 20upx);
	}
	
	/* 6张图片：9宫格布局 */
	.image-layout-6 {
		display: flex;
		width: 100%;
		height: 480upx;
	}
	
	.image-grid-6 {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		grid-template-rows: 1fr 1fr 1fr;
		width: 100%;
		height: 100%;
		gap: 15upx;
		padding: 0 10upx;
	}
	
	.grid-main-6 {
		grid-column: 1 / 3;
		grid-row: 1 / 3;
	}
	
	.grid-small-6 {
		width: 100%;
		height: 100%;
	}
	
	.grid-small-6:nth-child(2) {
		grid-column: 3 / 4;
		grid-row: 1 / 2;
	}
	
	.grid-small-6:nth-child(3) {
		grid-column: 3 / 4;
		grid-row: 2 / 3;
	}
	
	.grid-small-6:nth-child(4) {
		grid-column: 1 / 2;
		grid-row: 3 / 4;
	}
	
	.grid-small-6:nth-child(5) {
		grid-column: 2 / 3;
		grid-row: 3 / 4;
	}
	
	.grid-small-6:nth-child(6) {
		grid-column: 3 / 4;
		grid-row: 3 / 4;
	}
</style>
