<?php
initializeActions();

// 设置验证配置 - 仅管理员可用
if (!adminVerify()) {
    send_json(403, "权限不足");
    exit;
}

$captcha_type = isset($_POST['captcha_type']) ? $_POST['captcha_type'] : '';
$geetest_id = isset($_POST['geetest_id']) ? $_POST['geetest_id'] : '';
$geetest_key = isset($_POST['geetest_key']) ? $_POST['geetest_key'] : '';
$cloudflare_site_key = isset($_POST['cloudflare_site_key']) ? $_POST['cloudflare_site_key'] : '';
$cloudflare_secret_key = isset($_POST['cloudflare_secret_key']) ? $_POST['cloudflare_secret_key'] : '';
$recaptcha_site_key = isset($_POST['recaptcha_site_key']) ? $_POST['recaptcha_site_key'] : '';
$recaptcha_secret_key = isset($_POST['recaptcha_secret_key']) ? $_POST['recaptcha_secret_key'] : '';
$enabled = isset($_POST['enabled']) ? intval($_POST['enabled']) : 1;

// 验证必填字段
if (empty($captcha_type)) {
    send_json(400, "验证类型不能为空");
    exit;
}

// 根据验证类型验证对应的配置
switch ($captcha_type) {
    case 'geetest':
        if (empty($geetest_id) || empty($geetest_key)) {
            send_json(400, "极验配置不完整");
            exit;
        }
        break;
    case 'cloudflare':
        if (empty($cloudflare_site_key) || empty($cloudflare_secret_key)) {
            send_json(400, "Cloudflare配置不完整");
            exit;
        }
        break;
    case 'recaptcha':
        if (empty($recaptcha_site_key) || empty($recaptcha_secret_key)) {
            send_json(400, "reCAPTCHA配置不完整");
            exit;
        }
        break;
}

$updated_time = time();

$sql = "UPDATE Xqy_Plugin_captcha_config SET 
        captcha_type = '$captcha_type',
        geetest_id = '$geetest_id',
        geetest_key = '$geetest_key',
        cloudflare_site_key = '$cloudflare_site_key',
        cloudflare_secret_key = '$cloudflare_secret_key',
        recaptcha_site_key = '$recaptcha_site_key',
        recaptcha_secret_key = '$recaptcha_secret_key',
        enabled = $enabled,
        updated_time = $updated_time
        WHERE id = 1";

if (mysqli_query($connect, $sql)) {
    send_json(200, "配置更新成功");
} else {
    send_json(500, "配置更新失败：" . mysqli_error($connect));
}
?>
