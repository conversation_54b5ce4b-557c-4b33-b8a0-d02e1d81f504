<template>
	<view class="user">
		<view class="header" :style="[{height:CustomBar + 'px'}]" :class="AppStyle">
			<view class="cu-bar bg-white" :style="{'height': CustomBar + 'px','padding-top':StatusBar + 'px'}">
				<view class="action" @tap="back">
					<text class="cuIcon-back"></text>
				</view>
				<view class="content text-bold" :style="[{top:StatusBar + 'px'}]">
					邀请好友
				</view>
				<view class="action">
					<text class="text-blue" @tap="toLink('/pages/user/inviteList')">邀请记录</text>
				</view>
			</view>
		</view>
		<view :style="[{padding:NavBar + 'px 10px 0px 10px'}]"></view>
		<view class="data-box">
			<view class="menu margin-top">
				<!--  #ifdef APP-PLUS -->
				<block v-if="adsVideoType==0 || adsVideoType==1">
					<view class="menu-item" v-if="adpid!=''&&adpid">
						<view class="content">
							<text class="text-lg">观看广告获取奖励</text>
							<text class="text-gray text-sm">可随机获得{{currencyName}}</text>
						</view>
						<view class="action">
							<text class="cu-btn round bg-gradual-orange" @tap="show">
								<text class="cuIcon-videofill margin-right-xs"></text>获取
							</text>
						</view>
					</view>
				</block>
				<!--  #endif -->
			</view>
		</view>
		<view class="data-box">
			<block v-if="islock==false">
				<view class="invite margin">
					<view class="invite-title text-xl padding-bottom">
						邀请好友得{{currencyName}}
						<view class="text-sm text-gray tn-margin-sm">好友注册时输入邀请码即可</view>
					</view>
					<view class="invite-main padding-tb-xl">
						<view class="invite-text text-center">
							<text class="text-price text-xxl text-bold text-blue">{{invitationCode}}</text>
						</view>
					</view>
					<view class="invite-btn flex justify-center padding-bottom">
						<text class="cu-btn round bg-gradual-blue" @tap="copy(invitationCode)">
							<text class="cuIcon-copy margin-right-xs"></text>复制邀请码
						</text>
					</view>
				</view>
				
				<view class="padding-lr">
					<view class="text-content" v-html="yqtext"></view>
				</view>
			</block>
			
			<block v-if="islock==true">
				<view class="invite margin text-center padding-xl">
					<text class="text-grey">邀请码功能已关闭</text>
				</view>
			</block>
		</view>
		<!--加载遮罩-->
		<view class="loading" v-if="isLoading==0">
			<view class="loading-main">
				<image src="../../static/loading.gif"></image>
			</view>
		</view>
		<!--加载遮罩结束-->
	</view>

</template>

<script>
	const ERROR_CODE = [-5001, -5002, -5003, -5004, -5005, -5006];
	import waves from '@/components/xxley-waves/waves.vue';
	import {
		localStorage
	} from '../../js_sdk/mp-storage/mp-storage/index.js'
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				NavBar: this.StatusBar + this.CustomBar,
				AppStyle: this.$store.state.AppStyle,


				userInfo: null,
				token: "",

				isLoading: 0,

				modalName: null,

				currencyName: "",
				invitationCode: "--",
				codeImg: null,
				islock: false,
				yqtext: "",
				title: '激励视频广告',
				loading: false,
				loadError: false,
				adpid: '',
				adsLogid: '',

				//服务端回调相关配置
				urlCallback: {
					userId: '0',
					extra: 'RuleApp'
				},
				adsVideoType: 1,

			}
		},
		onPullDownRefresh() {
			var that = this;
			if (localStorage.getItem('token')) {

				that.token = localStorage.getItem('token');
			}
			var timer = setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000)
		},
		onReady() {
			// #ifdef APP-PLUS
			this.adsVideoType = this.$API.GetAdsVideoType();
			this.adpid = this.$API.GetAdpid();
			if (this.adpid != "" && this.adpid) {
				this.adOption = {
					adpid: this.adpid
				};
				this.createAd();
			}

			// #endif

		},
		onShow() {
			var that = this;
			// #ifdef APP-PLUS

			//plus.navigator.setStatusBarStyle("dark")

			// #endif
			if (localStorage.getItem('userinfo')) {

				that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.userInfo.style = "background-image:url(" + that.userInfo.avatar + ");"
				that.urlCallback.userId = that.userInfo.uid;
			}
			if (localStorage.getItem('token')) {

				that.token = localStorage.getItem('token');
				that.getInvitationCode();
			}
		},
		onLoad() {
			var that = this;
			// #ifdef APP-PLUS || MP
			that.NavBar = this.CustomBar;
			// #endif
			if (localStorage.getItem('userinfo')) {

				that.userInfo = JSON.parse(localStorage.getItem('userinfo'));
				that.urlCallback.userId = that.userInfo.uid;
			}
		},
		mounted() {
			this.getset();
		},
		methods: {
			getset() {
				var that = this;
				uni.request({
					url: that.$API.SPset(),
					method: 'GET',
					dataType: "json",
					success(res) {
						that.currencyName = res.data.assetsname;
						that.yqtext = res.data.yqtext;
					},
					fail(error) {
						console.log(error);
					}

				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			onadRewardedLoad() {
				console.log('激励视频广告数据加载成功');
			},
			onaderror(e) {
				// 广告加载失败
				console.log(e.detail, '广告加载失败');
				uni.showToast({
					title: "广告加载失败，请稍后重试",
					icon: "none"
				});
			},
			onadRewardedClose(e) {
				const detail = e.detail;
				// 用户点击了【关闭广告】按钮
				if (detail && detail.isEnded) {
					// 正常播放结束
					console.log('onadclose---正常播放结束 ' + detail.isEnded);
					uni.showToast({
						title: "播放完成，奖励已发放！",
						icon: "none"
					});
				} else {
					console.log('onadclose--播放中途退出 ' + detail.isEnded);
					uni.showToast({
						title: "你未完成广告播放，无法获得奖励！",
						icon: "none"
					});
				}
			},
			showModal(e) {
				this.modalName = e.currentTarget.dataset.target
			},
			hideModal(e) {
				this.modalName = null
			},
			toLink(text) {
				var that = this;

				if (!localStorage.getItem('token') || localStorage.getItem('token') == "") {
					uni.showToast({
						title: "请先登录哦",
						icon: 'none'
					})
					return false;
				}
				uni.navigateTo({
					url: text
				});
			},
			userrecharge() {
				var that = this;
				uni.navigateTo({
					url: '/pages/user/userrecharge'
				});

			},
			formatDate(datetime) {
				var datetime = new Date(parseInt(datetime * 1000));
				// 获取年月日时分秒值  slice(-2)过滤掉大于10日期前面的0
				var year = datetime.getFullYear(),
					month = ("0" + (datetime.getMonth() + 1)).slice(-2),
					date = ("0" + datetime.getDate()).slice(-2),
					hour = ("0" + datetime.getHours()).slice(-2),
					minute = ("0" + datetime.getMinutes()).slice(-2);
				//second = ("0" + date.getSeconds()).slice(-2);
				// 拼接
				var result = year + "-" + month + "-" + date + " " + hour + ":" + minute;
				// 返回
				return result;
			},

			tovipDiscount(num) {
				if (Number(num) <= 0) {
					return 0;
				} else {
					num = num.toString();
					num = num.replace("0.", "");
					return num;
				}

			},
			//激励视频
			createAd() {
				var that = this;
				var rewardedVideoAd = this.rewardedVideoAd = uni.createRewardedVideoAd(this.adOption);
				rewardedVideoAd.onLoad(() => {
					this.loading = false;
					this.loadError = false;
					console.log('onLoad event')
				});
				rewardedVideoAd.onClose((res) => {
					this.loading = true;
					// 用户点击了【关闭广告】按钮
					if (res && res.isEnded) {
						// 正常播放结束
						console.log("onClose " + res.isEnded);
						console.log("开始广告")
						that.adsGiftNotify();
					} else {
						// 播放中途退出
						console.log("onClose " + res.isEnded);
						setTimeout(() => {
							uni.showToast({
								title: "您未完成广告播放，无法获得奖励！",
								duration: 10000,
								position: 'bottom'
							})
						}, 500)
					}
					uni.hideLoading();

				});
				rewardedVideoAd.onError((err) => {
					uni.hideLoading();
					this.loading = false;
					if (err.code && ERROR_CODE.indexOf(err.code) !== -1) {
						this.loadError = true;
					}
					console.log('onError event', err)
					uni.showToast({
						title: err.errMsg,
						duration: 10000,
						position: 'bottom'
					})
				});
				this.loading = true;
			},
			show() {
				var that = this;
				uni.showLoading();
				that.$Net.request({

					url: that.$API.adsGift(),
					data: {
						"token": that.token,
						"appkey": that.$API.getAppKey()
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						console.log(JSON.stringify(res));
						uni.hideLoading();
						if (res.data.code == 1) {
							that.adsLogid = res.data.data.logid;
							const rewardedVideoAd = that.rewardedVideoAd;
							rewardedVideoAd.show().catch(() => {
								rewardedVideoAd.load()
									.then(() => rewardedVideoAd.show())
									.catch(err => {
										console.log('激励视频 广告显示失败', err)
										uni.showToast({
											title: err.errMsg || err.message,
											duration: 5000,
											position: 'bottom'
										})
									})
							})
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})

			},
			adsGiftNotify() {
				var that = this;
				console.log("进来了")

				that.$Net.request({

					url: that.$API.adsGiftNotify(),
					data: {
						"token": that.token,
						"logid": that.adsLogid
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						console.log(JSON.stringify(res));
						if (res.data.code == 1) {
							uni.showToast({
								title: "已获得" + res.data.data.award + that.currencyName + "奖励",
								duration: 3000,
								position: 'bottom'
							})
						}
					},
					fail: function(res) {
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			reloadAd() {
				this.loading = true;
				this.rewardedVideoAd.load();
			},
			copy(text) {
				var that = this;
				// #ifndef H5
				uni.setClipboardData({
					data: text,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						})
					}
				});
				// #endif

				// #ifdef H5 
				let textarea = document.createElement("textarea")
				textarea.value = text
				textarea.readOnly = "readOnly"
				document.body.appendChild(textarea)
				textarea.select() // 选中文本内容
				textarea.setSelectionRange(0, text.length)
				uni.showToast({ //提示
					title: '复制成功',
					icon: 'success'
				})
				result = document.execCommand("copy")
				textarea.remove()
				// #endif
			},
			getInvitationCode() {
				var that = this;
				var token = "";
				if (localStorage.getItem('userinfo')) {
					var userInfo = JSON.parse(localStorage.getItem('userinfo'));
					token = userInfo.token;

				}
				that.$Net.request({

					url: that.$API.getInvitationCode(),
					data: {
						"token": token
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					method: "get",
					dataType: 'json',
					success: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
						if (res.data.code == 1) {
							that.invitationCode = res.data.data;
							var inviteJson = {
								"type": "Invite",
								"code": that.invitationCode
							}

							// that.codeImg = that.$API.qrCode() + "?codeContent=" + encodeURIComponent(JSON
							// 	.stringify(inviteJson));
						}else if(res.data.msg=="邀请码功能已关闭"){
							that.islock = true;
						}
					},
					fail: function(res) {
						var timer = setTimeout(function() {
							that.isLoading = 1;
							clearTimeout('timer')
						}, 300)
						uni.showToast({
							title: "网络开小差了哦",
							icon: 'none'
						})
					}
				})
			},
			dtImg() {
				var that = this;
				var url = that.codeImg;
				// #ifdef APP-PLUS
				uni.downloadFile({
					url: url,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.saveImageToPhotosAlbum({
								filePath: res.tempFilePath,
								success: function() {
									if (that.payType == 1) {
										uni.showToast({
											title: "图片已保存，微信不支持相册识别支付，请通过正常扫码完成。",
											icon: "none"
										});
									} else {
										uni.showToast({
											title: "保存成功",
											icon: "none"
										});
									}

								},
								fail: function() {
									uni.showToast({
										title: "保存失败，请稍后重试",
										icon: "none"
									});
								}
							});
						}
					}
				})
				// #endif
				// #ifdef H5
				uni.showToast({
					title: "请长按二维码图片保存",
					icon: "none"
				});
				// #endif
			},
		},
		components: {
			waves
		}
	}
</script>

<style>
.menu-item {
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.invite {
	padding: 30rpx;
}

.invite-title {
	text-align: center;
}

.text-price::before {
	content: "邀请码：";
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}

.text-content {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
}

.cu-btn.round {
	border-radius: 50rpx;
	padding: 0 40rpx;
}

.bg-gradual-orange {
	background-image: linear-gradient(45deg, #ff9700, #ed1c24);
	color: #ffffff;
}

.bg-gradual-blue {
	background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
	color: #ffffff;
}
</style>