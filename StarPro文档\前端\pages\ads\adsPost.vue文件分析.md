# adsPost.vue 文件分析

## 概述

`adsPost.vue` 页面是 StarPro 前端项目中用于**发布或编辑付费广告**的功能界面。它提供了一个表单，允许用户输入广告的标题、内容、类型、缩略图、跳转链接等信息，并能计算和显示所需的费用（在发布模式下）。页面还包含了图片上传功能和跳转类型的选择。

## 文件信息
- **文件路径**：`APP前端部分/pages/ads/adsPost.vue.md`
- **主要功能**：创建新的付费广告或修改已存在的广告。

## 主要组成部分分析

### 1. 模板 (`<template>`)
   - **导航栏**: 
     - 使用 `cu-bar` (可能是ColorUI或类似库的组件) 构建自定义导航栏。
     - 标题根据 `post` 模式显示"发布广告"或"编辑广告"。
     - 右侧动作按钮：发布模式下为"提交"按钮，编辑模式下为"保存"按钮 (按钮仅在 H5 或 App 端显示)。
     - 左侧为返回按钮 (`cuIcon-back`)。
   - **表单 (`<form>`)**: 
     - 使用 `cu-form-group` 构建表单项。
     - **主要字段**:
       - `广告标题 (name)`: 输入框。
       - `广告类型 (type)`: 显示当前类型名称 (从 `typeList` 获取)，似乎不可直接在此修改，可能由上个页面传入。
       - `广告内容 (intro)`: 多行文本输入框 (`textarea`)。
       - `缩略图 (imgurl)`: 显示图片URL的输入框 (禁用)，旁边有"上传图片"按钮 (`imgUpload`)。
       - `广告跳转链接 (url)`: 输入框。
       - `链接跳转类型 (urltype)`: 点击后弹出模态框 (`urltypeModal`) 选择"APP内打开"或"浏览器打开"。
       - `购买天数 (day)` (仅发布模式): 数字输入框，有输入限制 (`limit` 函数)，旁边显示单价 (`price`) 和货币名称 (`currencyName`)。
       - **费用预览** (仅发布模式): 显示根据天数和单价计算的总金额 (`total` 函数)。
   - **模态框 (`cu-modal`)**: 
     - 用于选择"链接跳转类型"，包含一个 `radio-group`。
   - **加载遮罩**: 
     - 通过 `isLoading` 控制显示，用于在数据加载或提交时提供用户反馈。

### 2. 脚本 (`<script>`)
   - **依赖**: 引入 `localStorage`。
   - **`data` 属性**: 
     - `StatusBar`, `CustomBar`, `NavBar`: 用于适配不同设备状态栏和导航栏高度。
     - `AppStyle`: 从 Vuex store 获取全局样式类。
     - `modalName`: 控制模态框显示。
     - `token`: 用户登录凭证。
     - `aid`: 广告ID (编辑模式下使用)。
     - `name`, `intro`, `url`, `imgurl`: 表单字段对应的数据。
     - `type`, `urltype`, `day`: 表单字段对应的数据 (类型、跳转类型、天数)。
     - `price`: 广告单价。
     - `typeList`: 广告类型选项 (文章推流、横幅、启动图)。
     - `urltypeList`: 跳转类型选项 (APP内打开、浏览器打开)。
     - `post`: 页面模式，'add' 或 'edit'。
     - `isLoading`: 加载状态标志。
     - `currencyName`: 货币名称。
     - `isHuaWei`, `isTy`: 特定环境或状态标志。
   - **生命周期函数**: 
     - `onLoad(res)`: 
       - 获取页面参数 `res.type`, `res.post`, `res.aid`。
       - 如果是编辑模式 (`post == 'edit'`)，调用 `getAdsInfo()` 获取广告详情。
       - 调用 `getAdsConfig()` 获取广告配置（可能包含价格 `price` 和货币名称 `currencyName`）。
       - 从 `localStorage` 获取 `token`。
     - `mounted()`: 调用 `getleiji()` (可能是获取累计数据或其他相关信息，函数名不太明确)。
     - `onShow()`: 可能用于设置状态栏样式 (被注释掉了)。
   - **`methods` 方法**: 
     - `back()`: 返回上一页。
     - `hideModal()`: 关闭模态框。
     - `RadioChange(e)`: 处理跳转类型选择模态框的选择结果，更新 `urltype`。
     - `getAdsConfig()`: 调用 `API.adsConfig()` 获取广告配置信息 (价格、货币名等)。
     - `getAdsInfo()`: (编辑模式) 调用 `API.adsInfo()` 获取指定 `aid` 的广告详情，填充表单。
     - `imgUpload()`: 
       - **权限检查 (APP-PLUS)**: 调用 `showTC()` 检查并请求相机和存储权限。
       - 调用 `uni.chooseImage` 选择图片。
       - 调用 `uni.uploadFile` 将图片上传到 `API.upload()` 指定的接口，并携带 `token`。
       - 上传成功后更新 `imgurl`。
     - `limit(value, min)`: 输入限制函数，确保值不小于 `min`。
     - `total(day, price)`: 计算总价。
     - `submit()`: (发布模式) 
       - 表单校验 (标题、内容、缩略图、链接、天数不能为空)。
       - 调用 `API.addAds()` 提交广告数据。
       - 成功后提示并返回。
     - `edit()`: (编辑模式)
       - 表单校验 (同上，但不校验天数)。
       - 调用 `API.editAds()` 提交修改后的广告数据。
       - 成功后提示并返回。
     - `getleiji()`: 调用 `API.SPleiji()` (具体作用不明，可能与用户积分或统计有关)。
     - **权限相关 (APP-PLUS)**: `showTC()`, `requestPermissions()`, `gotoAppPermissionSetting()` 包含复杂的 Android 权限请求逻辑，包括弹窗说明、请求权限、引导用户去设置页面等。

## 总结与注意事项

-   `adsPost.vue` 是一个功能相对完整的广告发布/编辑页面，包含了表单输入、图片上传、类型选择、价格计算和提交逻辑。
-   页面通过 `onLoad` 的 `post` 参数区分是新建 (`add`) 还是编辑 (`edit`) 模式。
-   核心的提交/保存操作分别调用后端 `API.addAds()` 和 `API.editAds()` 接口。
-   图片上传使用了 `uni.uploadFile`，目标接口由 `API.upload()` 提供。
-   包含了针对 App 平台的详细权限请求逻辑 (相机、存储)。
-   UI组件可能依赖 ColorUI 或类似库 (`cu-bar`, `cu-form-group`, `cu-modal` 等)。
-   页面逻辑与后端 API 紧密耦合，依赖 `api.js` 中定义的大量接口函数。

## 后续分析建议

-   **API 依赖**: 查看 `api.js` 中 `adsConfig`, `adsInfo`, `addAds`, `editAds`, `upload`, `SPleiji` 等接口的具体实现或后端对应逻辑。
-   **数据流**: 确认广告类型 (`type`) 是如何从上一个页面传递过来的。
-   **权限逻辑**: 在 App 端详细测试权限请求和处理流程。
-   **费用计算**: 确认 `currencyName` 和 `price` 的来源 (`getAdsConfig`) 是否准确。
-   **`getleiji()` 的作用**: 明确 `getleiji()` 方法的具体功能和对页面的影响。
